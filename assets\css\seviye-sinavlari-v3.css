/* Akt<PERSON> */
.active-exam-alert {
    background: linear-gradient(135deg, #ffff00 0%, #ffeb3b 100%);
    border: 3px solid #ffd700;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(255, 255, 0, 0.4);
    animation: activeAlertPulse 3s ease-in-out infinite;
    position: relative;
    overflow: hidden;
    padding: 25px;
}



@keyframes activeAlertPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.03);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }
}

@keyframes neonBorder {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes borderGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.5), 0 0 40px rgba(40, 167, 69, 0.3), 0 0 60px rgba(40, 167, 69, 0.1);
    }
    50% {
        box-shadow: 0 0 30px rgba(40, 167, 69, 0.8), 0 0 60px rgba(40, 167, 69, 0.5), 0 0 90px rgba(40, 167, 69, 0.2);
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

.alert-icon {
    font-size: 4rem;
    animation: bounce 2s ease-in-out infinite;
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3));
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    40% {
        transform: translateY(-15px) rotate(5deg);
    }
    60% {
        transform: translateY(-8px) rotate(-3deg);
    }
}

.active-exam-alert h4 {
    color: #000 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 5px;
}

.active-exam-alert p {
    color: #000 !important;
    font-weight: 600;
    margin-bottom: 15px;
}

/* Sınav Detayları */
.exam-details {
    background: rgba(40, 167, 69, 0.05);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

/* Açıklayıcı Metin Stilleri */
.exam-description {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 12px 15px;
    border-left: 4px solid #007bff;
    margin-top: 10px;
}

.exam-description p {
    margin: 0;
    font-weight: 500;
    line-height: 1.4;
}

.exam-description .uk-text-success {
    color: #28a745 !important;
    border-left-color: #28a745;
}

.exam-description .uk-text-danger {
    color: #dc3545 !important;
    border-left-color: #dc3545;
}

.exam-description .uk-text-warning {
    color: #ffc107 !important;
    border-left-color: #ffc107;
}

.exam-description .uk-text-primary {
    color: #007bff !important;
    border-left-color: #007bff;
}

.exam-description .uk-text-muted {
    color: #6c757d !important;
    border-left-color: #6c757d;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 10px;
    background: #ff5e00;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.3);
}

.detail-item:hover {
    background: rgba(255, 0, 0, 0.9);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 0, 0, 0.3);
}

.detail-icon {
    font-size: 1.5rem;
    margin-bottom: 5px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.detail-label {
    color: #fff;
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 3px;
}

.detail-value {
    color: #000 !important;
    font-size: 1.1rem;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(40, 167, 69, 0.2);
}



/* Responsive Tasarım */
@media (max-width: 959px) {
    .active-exam-alert {
        padding: 20px;
    }
    
    .alert-icon {
        font-size: 3rem;
    }
    
    .exam-details {
        padding: 15px;
    }
    
    .detail-item {
        padding: 8px;
    }
    
    .detail-icon {
        font-size: 1.2rem;
    }
    
    .detail-label {
        font-size: 0.8rem;
    }
    
    .detail-value {
        font-size: 1rem;
    }
}

@media (max-width: 639px) {
    .active-exam-alert {
        padding: 15px;
    }
    
    .alert-icon {
        font-size: 2.5rem;
    }
    
    .exam-details {
        padding: 10px;
    }
    
    .detail-item {
        padding: 6px;
        margin-bottom: 10px;
    }
    
    .active-exam-alert .exam-btn {
        width: 100%;
        padding: 10px 0px;
        font-size: 1rem;
    }
}

/* Sınav Kartları - Odevlerim.php Assignment Card Stilinde */
.exam-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 22px;
    transition: all 0.3s ease;
    position: relative;
    overflow: visible;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.exam-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Sınav Durumu Badge'leri */
.exam-status-badge {
    position: absolute;
    top: -12px;
    right: 20px;
    z-index: 10;
}

.exam-status-text {
    color: white;
    padding: 10px 22px;
    border-radius: 25px;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.exam-status-text.active {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    animation: pulse 2s ease-in-out infinite;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
}

.exam-status-text.success {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
}

.exam-status-text.failed {
    background: linear-gradient(135deg, #ff4757, #ff3742);
}

.exam-status-text.completed {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
}

.exam-status-text {
    background: linear-gradient(135deg, #6c757d, #495057);
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Sınav İkonları */
.exam-icon {
    font-size: 4rem;
    margin-right: 20px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Sınav Başlığı */
.exam-header {
    margin-bottom: 15px;
}

.exam-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
}

/* Sekme Stilleri */
.exam-tabs {
    margin-bottom: 20px;
}

.exam-tabs .uk-subnav {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 25px;
    padding: 5px;
    display: inline-flex;
    width: 100%;
}

.exam-tabs .uk-subnav > li {
    flex: 1;
}

.exam-tabs .uk-subnav > li > a {
    color: #6c757d;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
    text-align: center;
    display: block;
    background: transparent;
}

.exam-tabs .uk-subnav > li.uk-active > a {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.exam-tabs .uk-subnav > li > a:hover {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.exam-tab-content {
    min-height: 150px;
    display: block !important;
}

.uk-subnav-pill {
    margin-left: 0px !important;
}

.uk-subnav-pill>* {
    padding-left: 0px;
}

/* UIkit switcher için özel stiller */
.uk-switcher > * {
    display: block !important;
}

.uk-switcher > *:not(.uk-active) {
    display: none !important;
}

/* Sekme içeriklerinin görünürlüğü için */
.exam-tab-content {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Sekme meta bilgilerinin daha iyi görünmesi için */
.exam-tab-content .exam-meta {
    margin-bottom: 15px;
}

.exam-tab-content .exam-meta-item {
    margin-bottom: 8px;
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
}

.exam-subtitle {
    color: #7f8c8d;
    font-style: italic;
    margin-bottom: 10px;
}

/* Sınav Meta Bilgileri */
.exam-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.exam-meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(0, 0, 0, 0.05);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
}

.exam-meta-icon {
    font-size: 1rem;
}

.exam-meta-text {
    font-weight: 500;
    color: #34495e;
}

/* Sınav Butonları */
.exam-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 15px;
    padding: 10px 20px;
    min-height: 30px;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}


@keyframes buttonPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

.exam-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
    text-decoration: none;
}

.exam-btn.success {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.exam-btn.success:hover {
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.exam-btn.failed {
    background: linear-gradient(135deg, #dc3545, #c82333);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.exam-btn.failed:hover {
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.exam-btn.pink {
    background: linear-gradient(135deg, #e91e63, #ad1457);
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
}

.exam-btn.pink:hover {
    box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
}

.exam-btn.certificate {
    background: linear-gradient(135deg, #ffa61d, #ff5e00);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.exam-btn.certificate:hover {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    transform: translateY(-2px);
}

.exam-btn.disabled {
    background: linear-gradient(135deg, #6c757d, #495057);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    cursor: not-allowed;
    opacity: 0.7;
}

.exam-btn.disabled:hover {
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    transform: none;
}

/* Aktif Sınav Animasyonu */
.exam-card.active-exam {
    border: 3px solid #ff6b35;
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
    position: relative;
    background: rgba(255, 255, 255, 0.95);
}

.exam-card.active-exam::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: white;
    background-size: 400% 400%;
    border-radius: 19px;
    z-index: -1;
    animation: borderGlow 3s ease-in-out infinite;
    border: 3px solid #ff6b35;
}

.exam-card.active-exam.pulse {
    animation: activePulse 2s infinite;
}

@keyframes activePulse {
    0% {
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 107, 53, 0.6);
    }
    100% {
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
    }
}

@keyframes borderGlow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Başarılı Sınav */
.exam-card.success-exam {
    border: 2px solid #28a745;
    box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
}

/* Başarısız Sınav */
.exam-card.failed-exam {
    border: 2px solid #dc3545;
    box-shadow: 0 0 20px rgba(220, 53, 69, 0.3);
}

/* Modal Stilleri */
.uk-modal-dialog {
    border-radius: 16px;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    text-align: center;
}

.modal-body {
    padding: 30px;
}

/* Responsive Tasarım */
@media (max-width: 959px) {
    .exam-title {
        font-size: 1.25rem;
    }
    
    .exam-meta {
        gap: 10px;
    }
    
    .exam-meta-item {
        font-size: 0.8rem;
        padding: 5px 10px;
    }
    
    .exam-btn {
        padding: 12px 24px;
        font-size: 14px;
    }
    
    .exam-tabs .uk-subnav > li > a {
        padding: 6px 12px;
        font-size: 0.9rem;
    }
}

@media (max-width: 639px) {
    .exam-title {
        font-size: 1.125rem;
    }
    
    .exam-meta {
        flex-direction: column;
        gap: 8px;
    }
    
    .exam-btn {
        width: 100%;
        justify-content: center;
        padding: 15px 20px;
    }
    
    .exam-icon {
        font-size: 3rem;
        margin-right: 15px;
    }
    
    .exam-tabs .uk-subnav > li > a {
        padding: 5px 8px;
        font-size: 0.8rem;
    }
    
    .exam-tab-content {
        min-height: 120px;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.3);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Boş Durum */
.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
}

.empty-state-text {
    color: #7f8c8d;
    font-size: 1rem;
}


.uk-accordion-title {color: red !important}

/* Sonuç Modal Stilleri */
.sonuc-ozet-kutu {
    background: linear-gradient(135deg, #ff00ff 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.sonuc-ozet-kutu:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.sonuc-ozet-baslik {
    font-size: 0.9rem;
    color: #fff !important;
    font-weight: 500;
    margin-bottom: 8px;
    opacity: 0.9;
}

.sonuc-ozet-deger {
    font-size: 1.8rem;
    color:white !important;
    font-weight: 500;
    margin: 0;
}

.yuzdeli-progress {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto;
    border-radius: 50%;
    background: conic-gradient(#667eea 0deg, #667eea var(--progress), #e9ecef var(--progress), #e9ecef 360deg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.yuzdeli-progress::before {
    content: '';
    position: absolute;
    width: 120px;
    height: 120px;
    background: white;
    border-radius: 50%;
}

.yuzde-deger {
    position: relative;
    z-index: 1;
    font-size: 2rem;
    font-weight: 400;
    color: #667eea;
}

/* Soru Sonuç Stilleri */
.soru-sonuc {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 5px solid #e9ecef;
    transition: all 0.3s ease;
}

.soru-sonuc.dogru {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #afffba 0%, #77ff77 100%);
}

.soru-sonuc.yanlis {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #ffe8e8 0%, #ff7373 100%);
}

.soru-sonuc:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.soru-sonuc-baslik {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.soru-no {
    font-size: 1.1rem;
    font-weight: 400;
    color: #495057;
}

.sonuc-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dogru-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.yanlis-badge {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.soru-metin {
    font-size: 1rem;
    line-height: 1.6;
    color: #495057;
    margin-bottom: 15px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 10px;
    border-left: 4px solid #007bff;
}

.cevap-bilgi {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.cevap-bilgi strong {
    color: #495057;
    font-weight: 400;
}

/* Koyu Modal Stilleri */
.dark-modal {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
}

.dark-modal .uk-modal-title {
    color: white !important;
}

.dark-modal .soru-sonuc {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 10px;
    margin-bottom: 15px;
}

.dark-modal .soru-sonuc.dogru {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(32, 201, 151, 0.2) 100%);
    border-left: 4px solid #28a745;
}

.dark-modal .soru-sonuc.yanlis {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(253, 126, 20, 0.2) 100%);
    border-left: 4px solid #dc3545;
}

.dark-modal .soru-metin {
    background: rgba(255, 255, 255, 0.05);
    border-left: 4px solid #667eea;
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
}

.dark-modal .cevap-bilgi {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin-top: 10px;
}

.dark-modal .cevap-bilgi strong {
    color: #ffffff;
    font-weight: 600;
}

.dark-modal .uk-text-muted {
    color: #e9ecef !important;
}

.dark-modal .uk-text-success {
    color: #28a745 !important;
}

.dark-modal .uk-alert {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 8px;
}

.dark-modal .uk-alert-warning {
    background: rgba(255, 193, 7, 0.2);
    border-color: #ffc107;
}

.dark-modal .uk-alert-danger {
    background: rgba(220, 53, 69, 0.2);
    border-color: #dc3545;
}

/* Modal Responsive */
@media (max-width: 768px) {
    .sonuc-ozet-kutu {
        padding: 15px;
    }
    
    .sonuc-ozet-deger {
        font-size: 1.5rem;
    }
    
    .yuzdeli-progress {
        width: 120px;
        height: 120px;
    }
    
    .yuzdeli-progress::before {
        width: 90px;
        height: 90px;
    }
    
    .yuzde-deger {
        font-size: 1.5rem;
    }
    
    .soru-sonuc {
        padding: 15px;
    }
    
    .soru-metin {
        font-size: 0.9rem;
        padding: 12px;
    }
}

.uk-text-black {color: #000 !important}

.uk-text-dogru {
    color: #005a08 !important;
}