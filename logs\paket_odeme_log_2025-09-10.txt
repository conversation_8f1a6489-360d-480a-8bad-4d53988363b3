=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-10 09:17:14
İşlem No: ORDER******************
Ödeme ID: 4085
Kullanıcı ID: 4251
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 5a62de94-56dc-4c97-a5ff-1b17be674089
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=5a62de94-56dc-4c97-a5ff-1b17be674089" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER******************
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-10 13:10:17
İşlem No: ORDER202509101310159772
Ödeme ID: 4088
Kullanıcı ID: 2467
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => abfd72f2-9868-47e0-b4ee-65431b2a9d15
            [UCD_HTML] => <html>
 <head>
  <!-- troyStartSuccess.htm -->
  <title>GO</title>
  <meta http-equiv="Content-Language" content="tr">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
  <script type="text/javascript" language="javascript" nonce="r0e3Xag4cRgiArLqzmOt6/tSHOZg6AaPlhhJ7d1xjOE=">
	window.onload = function() {
		document.returnform.submit();
	}
</script>
 </head>
 <body>
  <form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post">
   <input type="hidden" name="goreq" value="********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
   <noscript>
    <center>
     Devam etmek icin tiklayiniz.
     <br>
     <br>
     <input type="submit" name="submit" value="Submit" id="btnSbmt">
    </center>
   </noscript>
  </form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=abfd72f2-9868-47e0-b4ee-65431b2a9d15" />
 </body>
</html>
            [UCD_MD] => ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509101310159772
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-10 13:13:15
İşlem No: ORDER202509101313122752
Ödeme ID: 4089
Kullanıcı ID: 1805
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4772,70
Taksit: 2

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => ********-30dc-4bdf-a2a7-000528a34a77
            [UCD_HTML] => <html class="no-js" lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
<META http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta charset="utf-8">
<title>3D Secure Processing</title>
<link href="https://mpi.yapikredi.com.tr/mdpaympi/static/mpi.css" rel="stylesheet" type="text/css">
</head>
<body>
<div id="main">
<div id="content">
<div id="order">
<h2>3D Secure Processing</h2>
<script src="https://mpi.yapikredi.com.tr/mdpaympi/static/red.js" defer>/* needed for xsl to xhtml */</script>
<div id="spinner">
<img src="https://mpi.yapikredi.com.tr/mdpaympi/static/preloader.gif" alt="Please wait.."></div>
<img src="https://mpi.yapikredi.com.tr/mdpaympi/static/verifiedbyvisa.png" alt="Verified by VISA"><div id="formdiv">
<div>
<form id="webform0" name="red2ACSv2" method="POST" action="https://emvacs.bkm.com.tr/acs/creq" accept_charset="UTF-8">
<input type="hidden" name="creq" value="ewogICAiYWNzVHJhbnNJRCIgOiAiZjI1ODFlOTQtNWVmMy00YjNjLTkzMzgtOGFmMzY0ZGNiNzA1IiwKICAgImNoYWxsZW5nZVdpbmRvd1NpemUiIDogIjAzIiwKICAgIm1lc3NhZ2VUeXBlIiA6ICJDUmVxIiwKICAgIm1lc3NhZ2VWZXJzaW9uIiA6ICIyLjIuMCIsCiAgICJ0aHJlZURTU2VydmVyVHJhbnNJRCIgOiAiODIwNDgwZWQtZDBlYi01ZjYyLTgwMDAtMDAwMDBiNGU2NTE2Igp9"><input type="hidden" name="threeDSSessionData" value="0161010697916295993"><input type="submit" name="submitBtn" value="Please click here to continue">
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=********-30dc-4bdf-a2a7-000528a34a77" />
</div>
</div>
<noscript>
<div align="center">
<b>Javascript is turned off or not supported!</b>
<br>
</div>
</noscript>
</div>
<div id="content-footer"></div>
</div>
</div>
</body>
</html>
<script id="f5_cspm">(function(){var f5_cspm={f5_p:'HDDELIKJJCAIHBPAGLGPMOGHDKFLJKEEIAIMPHFMFLBEDGAJGFIACLCICJAIHNLBNEABMKNLAAMLKKMHFBPAPHKFAALHMHLEKHBBFBMADNEGDFLFAMBPOHPKBICIGNPE',setCharAt:function(str,index,chr){if(index>str.length-1)return str;return str.substr(0,index)+chr+str.substr(index+1);},get_byte:function(str,i){var s=(i/16)|0;i=(i&15);s=s*32;return((str.charCodeAt(i+16+s)-65)<<4)|(str.charCodeAt(i+s)-65);},set_byte:function(str,i,b){var s=(i/16)|0;i=(i&15);s=s*32;str=f5_cspm.setCharAt(str,(i+16+s),String.fromCharCode((b>>4)+65));str=f5_cspm.setCharAt(str,(i+s),String.fromCharCode((b&15)+65));return str;},set_latency:function(str,latency){latency=latency&0xffff;str=f5_cspm.set_byte(str,40,(latency>>8));str=f5_cspm.set_byte(str,41,(latency&0xff));str=f5_cspm.set_byte(str,35,2);return str;},wait_perf_data:function(){try{var wp=window.performance.timing;if(wp.loadEventEnd>0){var res=wp.loadEventEnd-wp.navigationStart;if(res<60001){var cookie_val=f5_cspm.set_latency(f5_cspm.f5_p,res);window.document.cookie='f5avr1841486501aaaaaaaaaaaaaaaa_cspm_='+encodeURIComponent(cookie_val)+';path=/;'+'';}
return;}}
catch(err){return;}
setTimeout(f5_cspm.wait_perf_data,100);return;},go:function(){var chunk=window.document.cookie.split(/\s*;\s*/);for(var i=0;i<chunk.length;++i){var pair=chunk[i].split(/\s*=\s*/);if(pair[0]=='f5_cspm'&&pair[1]=='1234')
{var d=new Date();d.setTime(d.getTime()-1000);window.document.cookie='f5_cspm=;expires='+d.toUTCString()+';path=/;'+';';setTimeout(f5_cspm.wait_perf_data,100);}}}}
f5_cspm.go();}());</script>
            [UCD_MD] => 77B95B5FA78DFF52BF22D3695CAEBA675F1486D24E30016A2DEA46B932EEA9A6D543290EBCD0FFAE72C387B8E3801CA0C67AF1F57A5DF73CBE55E5F2F227B35A4470BFA57991352293B5409AED9DF4D00194937CF41E58AE11B305170F6D80B2D3A36C7A08B2789EA45E2430
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509101313122752
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-10 13:14:52
İşlem No: ORDER202509101314516057
Ödeme ID: 4090
Kullanıcı ID: 1805
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 562b42a7-1af7-420e-80f7-7d59f1c7e91e
            [UCD_HTML] => <!DOCTYPE html SYSTEM "about:legacy-compat">
<html class="no-js" lang="en" xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <meta charset="utf-8"/>
        <title>3-D Secure Processing</title>
        <link href="https://3d.payten.com.tr/mdpaympi/static/mpi.css" rel="stylesheet" type="text/css"/>
    </head>
    <body>
        <div id="main">
            <div id="content">
                <div id="order">
                    <h2>3-D Secure Processing</h2>
                    <div style="padding-bottom: 15px">
                        <img src="https://3d.payten.com.tr/mdpaympi/static/preloader.gif" alt="Please wait.."/>
                    </div>
                    <img src="https://3d.payten.com.tr/mdpaympi/static/verifiedbyvisa.png" alt="Verified by VISA"/>
                    <div id="formdiv">
                        <script type="text/javascript">
				function hideAndSubmitTimed(formid)
				{
				var timer=setTimeout("hideAndSubmit('"+formid+"');",2500);
				}

				function hideAndSubmit(formid)
				{
				var formx=document.getElementById(formid);
				if (formx!=null)
				{
				formx.style.visibility="hidden";
				formx.submit();
				}
				}
			</script>
                        <div>
                            <form id="webform0" name="ddcoll" method="POST" action="https://3d.payten.com.tr/mdpaympi/MerchantServer" accept_charset="UTF-8">
                                <input type="hidden" name="txid" value="1629280199"/>
                                <input type="hidden" name="TDS2_Navigator_language" value=""/>
                                <input type="hidden" name="TDS2_Navigator_javaEnabled" value=""/>
                                <input type="hidden" name="TDS2_Navigator_jsEnabled" value=""/>
                                <input type="hidden" name="TDS2_Screen_colorDepth" value=""/>
                                <input type="hidden" name="TDS2_Screen_height" value=""/>
                                <input type="hidden" name="TDS2_Screen_width" value=""/>
                                <input type="hidden" name="TDS2_Screen_PixelDepth" value=""/>
                                <input type="hidden" name="TDS2_TimezoneOffset" value=""/>
                                <input type="text" name="digest" value="uXEbLH5UNErdqnxDKkaFE2DBjnhM88ElqJdrWV0dSI8=" readonly="true" style="display:none;"/>
                                <input type="hidden" name="transientData" value="X+7G1tUemiYOOz2H/rR4FEGZZZcY6mWF+9J87oqEFxbyjNBD/cqeutDbbdDGvm526qlhui3gllyaixBjHLbt2JNenh7Z4I5uBw5ZI+5tGUA1i5CaofXZCNjVoXtOn+Uu705gCLOARDWqYYTsjDH6feoF4q4cX9tFiFYbXlEniV/7Jk3nXyiitXGHUOhc7an5S5aA+FPt3n0tqXQmxFqJVVxuykfbZvnjOF/9NclL5hQJOhxCe0fxhLFWV3LgEmlOp5tezz+X7dkgyzafJAwDpHLMzAEdhFKgKp72NJxZ2nguaKMaxZ6lQLWbXa+bNqghstr06VYgaZ09CsCK2t7F46uRP65G25x+cSmUCxQIcc4e3Z8n2dzt5VyHTTfLgQDEBQjsSmzGevnLWlcPCbCXj2Pb6VZGxy1/YbIg77xzSA1X5L91WTLn1zFgq9cKY2owO/8JmxdvkgRBpUVQdNI/4Q=="/>
                                <noscript>
                                    <input type="submit" name="submitBtn" value="Please click here to continue"/>
                                </noscript>
                            </form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=562b42a7-1af7-420e-80f7-7d59f1c7e91e" />
                        </div>
                    </div>
                    <script type="text/javascript">
			hideAndSubmitTimed('webform0');
		</script>
                    <noscript>
                        <div align="center">
                            <b>Javascript is turned off or not supported!</b>
                            <br/>
                        </div>
                    </noscript>
                    <script type="text/javascript">
			function populateData()
			{
				var form = document.getElementById("webform0");
				if (form != null)
				{
					if (form["TDS2_Navigator_language"]!=null)
					{
						form["TDS2_Navigator_language"].value=(navigator.language || '');
					}
					if (form["TDS2_Navigator_jsEnabled"]!=null)
					{
						// if this scipt runs js is enabled
						form["TDS2_Navigator_jsEnabled"].value="true";
					}
					if (form["TDS2_Navigator_javaEnabled"]!=null)
					{
						form["TDS2_Navigator_javaEnabled"].value=navigator.javaEnabled();
					}
					if (form["TDS2_Screen_colorDepth"]!=null)
					{
						form["TDS2_Screen_colorDepth"].value=screen.colorDepth;
					}
					if (form["TDS2_Screen_height"]!=null)
					{
						form["TDS2_Screen_height"].value=screen.height;
					}
					if (form["TDS2_Screen_width"]!=null)
					{
						form["TDS2_Screen_width"].value=screen.width;
					}
					if (form["TDS2_Screen_pixelDepth"]!=null)
					{
						form["TDS2_Screen_pixelDepth"].value=screen.pixelDepth;
					}

					var timezoneOffsetField = form["TDS2_TimezoneOffset"];
					if (timezoneOffsetField!=null)
					{
						timezoneOffsetField.value=new Date().getTimezoneOffset();
					}
				}
			}
			populateData();
		</script>
                </div>
                <div id="content-footer"/>
            </div>
        </div>
    </body>
</html>


            [UCD_MD] => X+7G1tUemiYOOz2H/rR4FEGZZZcY6mWF+9J87oqEFxbyjNBD/cqeutDbbdDGvm526qlhui3gllyaixBjHLbt2JNenh7Z4I5uBw5ZI+5tGUA1i5CaofXZCNjVoXtOn+Uu705gCLOARDWqYYTsjDH6feoF4q4cX9tFiFYbXlEniV/7Jk3nXyiitXGHUOhc7an5S5aA+FPt3n0tqXQmxFqJVVxuykfbZvnjOF/9NclL5hQJOhxCe0fxhLFWV3LgEmlOp5tezz+X7dkgyzafJAwDpHLMzAEdhFKgKp72NJxZ2nguaKMaxZ6lQLWbXa+bNqghstr06VYgaZ09CsCK2t7F46uRP65G25x+cSmUCxQIcc4e3Z8n2dzt5VyHTTfLgQDEBQjsSmzGevnLWlcPCbCXj2Pb6VZGxy1/YbIg77xzSA1X5L91WTLn1zFgq9cKY2owO/8JmxdvkgRBpUVQdNI/4Q==
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509101314516057
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-10 13:55:29
İşlem No: ORDER202509101355274046
Ödeme ID: 4092
Kullanıcı ID: 4778
Paket ID: 2
Paket Adı: Standart paket
Tutar: 5400,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 668672b5-2f58-40fe-b6f5-01205b7571ef
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=668672b5-2f58-40fe-b6f5-01205b7571ef" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509101355274046
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-10 15:28:59
İşlem No: ORDER202509101528588788
Ödeme ID: 4093
Kullanıcı ID: 4780
Paket ID: 1
Paket Adı: 4 Aylık paket
Tutar: 16969,60
Taksit: 2

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 2f417e56-e422-47ee-a5a5-d10aa16d28db
            [UCD_HTML] => <html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Redirect</title>

</head>
<body>
<div class="content"><!DOCTYPE html SYSTEM "about:legacy-compat">
<html class="no-js" lang="en" xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <meta charset="utf-8"/>
        <title>3-D Secure Processing</title>
        <link href="https://3d.payten.com.tr/mdpaympi/static/mpi.css" rel="stylesheet" type="text/css"/>
    </head>
    <body>
        <div id="main">
            <div id="content">
                <div id="order">
                    <h2>3-D Secure Processing</h2>
                    <div style="padding-bottom: 15px">
                        <img src="https://3d.payten.com.tr/mdpaympi/static/preloader.gif" alt="Please wait.."/>
                    </div>
                    <div id="formdiv">
                        <script type="text/javascript">
				function hideAndSubmitTimed(formid)
				{
				var timer=setTimeout("hideAndSubmit('"+formid+"');",2500);
				}

				function hideAndSubmit(formid)
				{
				var formx=document.getElementById(formid);
				if (formx!=null)
				{
				formx.style.visibility="hidden";
				formx.submit();
				}
				}
			</script>
                        <div>
                            <form id="webform0" name="ddcoll" method="POST" action="https://3d.payten.com.tr/mdpaympi/MerchantServer" accept_charset="UTF-8">
                                <input type="hidden" name="txid" value="1629333979"/>
                                <input type="hidden" name="TDS2_Navigator_language" value=""/>
                                <input type="hidden" name="TDS2_Navigator_javaEnabled" value=""/>
                                <input type="hidden" name="TDS2_Navigator_jsEnabled" value=""/>
                                <input type="hidden" name="TDS2_Screen_colorDepth" value=""/>
                                <input type="hidden" name="TDS2_Screen_height" value=""/>
                                <input type="hidden" name="TDS2_Screen_width" value=""/>
                                <input type="hidden" name="TDS2_Screen_PixelDepth" value=""/>
                                <input type="hidden" name="TDS2_TimezoneOffset" value=""/>
                                <input type="text" name="digest" value="vo6w4Wke1Fsvhu2yfAcrX1hWEepo7+PiMd7xTypljnQ=" readonly="true" style="display:none;"/>
                                <input type="hidden" name="transientData" value="MKg/ls0ctAQQQyGrm+nwVSSVSfIlu6B/ygY3xXpK+Xp2EjJpH3jBmMOTg6uYIIY5ttOKdjfnEsMW9O1UzKY7L08+FsnKBxcQlBaT9iuwS3v/NwbYgCoLAPd0tBX6Ru23N1mHkVhzeztz5+a7uhSYzdzW5Z4+v0ttmWXiorfxD8SUHi8i/Pvmd4rRWu2Qrfk9zx0DRWKW4XyogJwgBicTBd521d+0SSZxsAwBDcDD/dWYamdvfJVqBFKRvXCSUtK4vca9kWaMZM31rLZGRHxaKGAMvJYJtaLDPFJ57WyIr3G6cjC9LGKlbI3nQBkwmYLP0p96FSByjG5aWP/F67c3LQBo39H/jso5NI8tddxIXQs="/>
                                <noscript>
                                    <input type="submit" name="submitBtn" value="Please click here to continue"/>
                                </noscript>
                            </form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=2f417e56-e422-47ee-a5a5-d10aa16d28db" />
                        </div>
                    </div>
                    <script type="text/javascript">
			hideAndSubmitTimed('webform0');
		</script>
                    <noscript>
                        <div align="center">
                            <b>Javascript is turned off or not supported!</b>
                            <br/>
                        </div>
                    </noscript>
                    <script type="text/javascript">
			function populateData()
			{
				var form = document.getElementById("webform0");
				if (form != null)
				{
					if (form["TDS2_Navigator_language"]!=null)
					{
						form["TDS2_Navigator_language"].value=(navigator.language || '');
					}
					if (form["TDS2_Navigator_jsEnabled"]!=null)
					{
						// if this scipt runs js is enabled
						form["TDS2_Navigator_jsEnabled"].value="true";
					}
					if (form["TDS2_Navigator_javaEnabled"]!=null)
					{
						form["TDS2_Navigator_javaEnabled"].value=navigator.javaEnabled();
					}
					if (form["TDS2_Screen_colorDepth"]!=null)
					{
						form["TDS2_Screen_colorDepth"].value=screen.colorDepth;
					}
					if (form["TDS2_Screen_height"]!=null)
					{
						form["TDS2_Screen_height"].value=screen.height;
					}
					if (form["TDS2_Screen_width"]!=null)
					{
						form["TDS2_Screen_width"].value=screen.width;
					}
					if (form["TDS2_Screen_pixelDepth"]!=null)
					{
						form["TDS2_Screen_pixelDepth"].value=screen.pixelDepth;
					}

					var timezoneOffsetField = form["TDS2_TimezoneOffset"];
					if (timezoneOffsetField!=null)
					{
						timezoneOffsetField.value=new Date().getTimezoneOffset();
					}
				}
			}
			populateData();
		</script>
                </div>
                <div id="content-footer"/>
            </div>
        </div>
    </body>
</html>
</div>
<!--<div th:remove="tag" th:utext="${n}"></div>-->
</body>
</html>
            [UCD_MD] => MKg/ls0ctAQQQyGrm+nwVSSVSfIlu6B/ygY3xXpK+Xp2EjJpH3jBmMOTg6uYIIY5ttOKdjfnEsMW9O1UzKY7L08+FsnKBxcQlBaT9iuwS3v/NwbYgCoLAPd0tBX6Ru23N1mHkVhzeztz5+a7uhSYzdzW5Z4+v0ttmWXiorfxD8SUHi8i/Pvmd4rRWu2Qrfk9zx0DRWKW4XyogJwgBicTBd521d+0SSZxsAwBDcDD/dWYamdvfJVqBFKRvXCSUtK4vca9kWaMZM31rLZGRHxaKGAMvJYJtaLDPFJ57WyIr3G6cjC9LGKlbI3nQBkwmYLP0p96FSByjG5aWP/F67c3LQBo39H/jso5NI8tddxIXQs=
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509101528588788
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
