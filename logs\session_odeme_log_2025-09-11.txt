=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-11 09:01:11
İşlem No: SESSION202509110901109984
Ödeme ID: 4096
Kullanıcı ID: 1151
Tutar: 4950,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 2461f2cb-7489-4ad3-b541-d8fa827f73e0
            [UCD_HTML] => <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="tr" xmlns="http://www.w3.org/1999/xhtml">
<head>
<script type="text/javascript" src="/ruxitagentjs_ICA27NVfqrux_10263230921131557.js" data-dtconfig="rid=RID_-**********|rpid=*********|domain=denizbank.com|reportUrl=/rb_d0ac9e6b-b11a-4747-9900-8db5d8d1e8c3|app=600f804e2f46f262|cuc=w2s3jly9|owasp=1|mel=100000|featureHash=ICA27NVfqrux|dpvc=1|lastModification=*************|tp=500,50,0,1|rdnt=1|uxrgce=1|srbbv=2|agentUri=/ruxitagentjs_ICA27NVfqrux_10263230921131557.js"></script></head>
<body>
<form id="form1" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="POST">
<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=2461f2cb-7489-4ad3-b541-d8fa827f73e0" />
<script type="text/javascript">
 document.getElementById("form1").submit(); 
</script>
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509110901109984
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-11 09:32:34
İşlem No: SESSION202509110932323690
Ödeme ID: 4097
Kullanıcı ID: 959
Tutar: 4950,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 7a5c38fe-dd17-480a-beb1-cd28b96f4086
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=7a5c38fe-dd17-480a-beb1-cd28b96f4086" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509110932323690
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-11 09:57:13
İşlem No: SESSION202509110957121899
Ödeme ID: 4098
Kullanıcı ID: 4106
Tutar: 17678,40
Taksit: 4

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 87e3913c-d44b-4a02-a42e-d0a43de69b34
            [UCD_HTML] => <html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Redirect</title>

</head>
<body>
<div class="content"><!DOCTYPE html SYSTEM "about:legacy-compat">
<html class="no-js" lang="en" xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <meta charset="utf-8"/>
        <title>3-D Secure Processing</title>
        <link href="https://3d.payten.com.tr/mdpaympi/static/mpi.css" rel="stylesheet" type="text/css"/>
    </head>
    <body>
        <div id="main">
            <div id="content">
                <div id="order">
                    <h2>3-D Secure Processing</h2>
                    <div style="padding-bottom: 15px">
                        <img src="https://3d.payten.com.tr/mdpaympi/static/preloader.gif" alt="Please wait.."/>
                    </div>
                    <div id="formdiv">
                        <script type="text/javascript">
				function hideAndSubmitTimed(formid)
				{
				var timer=setTimeout("hideAndSubmit('"+formid+"');",2500);
				}

				function hideAndSubmit(formid)
				{
				var formx=document.getElementById(formid);
				if (formx!=null)
				{
				formx.style.visibility="hidden";
				formx.submit();
				}
				}
			</script>
                        <div>
                            <form id="webform0" name="ddcoll" method="POST" action="https://3d.payten.com.tr/mdpaympi/MerchantServer" accept_charset="UTF-8">
                                <input type="hidden" name="txid" value="1629620732"/>
                                <input type="hidden" name="TDS2_Navigator_language" value=""/>
                                <input type="hidden" name="TDS2_Navigator_javaEnabled" value=""/>
                                <input type="hidden" name="TDS2_Navigator_jsEnabled" value=""/>
                                <input type="hidden" name="TDS2_Screen_colorDepth" value=""/>
                                <input type="hidden" name="TDS2_Screen_height" value=""/>
                                <input type="hidden" name="TDS2_Screen_width" value=""/>
                                <input type="hidden" name="TDS2_Screen_PixelDepth" value=""/>
                                <input type="hidden" name="TDS2_TimezoneOffset" value=""/>
                                <input type="text" name="digest" value="tWjG+mHQ1t2VCcky4c1u7/xzYIUDC9aJg677iuzZEOA=" readonly="true" style="display:none;"/>
                                <input type="hidden" name="transientData" value="w0QgmihukO697+lvJCEvGQxCxB50yqht/HkUsuD5E2sSuQcjEFCyRVVJ3XYM5j0qxkwytzTKdmUb3kwRSY5CkJLpQFfxxug1Y9XUrqtrSsP8bjr8UpXDes0QUXRG89pIikQefdVoTjesORu+rIr8m7tmnErrzHckSS7Pg9jBaeJ+HSP1SebSTvQEUN0f/fhkzuxi7btTLCgZvUhoj5v0fhFOnTkpKd8nH0uPSXatJk8dqRL9R8M32tt7yfjQrEfOI5s2GTXTzYcclE+9/3Z1kRTGzt3ItuzSE1LXpifup31OXazCvZ8wsfTg0zFCSWRVHre4U3edgieYHclRnfksRNOjYKXNxd9nDGvDKeywATM="/>
                                <noscript>
                                    <input type="submit" name="submitBtn" value="Please click here to continue"/>
                                </noscript>
                            </form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=87e3913c-d44b-4a02-a42e-d0a43de69b34" />
                        </div>
                    </div>
                    <script type="text/javascript">
			hideAndSubmitTimed('webform0');
		</script>
                    <noscript>
                        <div align="center">
                            <b>Javascript is turned off or not supported!</b>
                            <br/>
                        </div>
                    </noscript>
                    <script type="text/javascript">
			function populateData()
			{
				var form = document.getElementById("webform0");
				if (form != null)
				{
					if (form["TDS2_Navigator_language"]!=null)
					{
						form["TDS2_Navigator_language"].value=(navigator.language || '');
					}
					if (form["TDS2_Navigator_jsEnabled"]!=null)
					{
						// if this scipt runs js is enabled
						form["TDS2_Navigator_jsEnabled"].value="true";
					}
					if (form["TDS2_Navigator_javaEnabled"]!=null)
					{
						form["TDS2_Navigator_javaEnabled"].value=navigator.javaEnabled();
					}
					if (form["TDS2_Screen_colorDepth"]!=null)
					{
						form["TDS2_Screen_colorDepth"].value=screen.colorDepth;
					}
					if (form["TDS2_Screen_height"]!=null)
					{
						form["TDS2_Screen_height"].value=screen.height;
					}
					if (form["TDS2_Screen_width"]!=null)
					{
						form["TDS2_Screen_width"].value=screen.width;
					}
					if (form["TDS2_Screen_pixelDepth"]!=null)
					{
						form["TDS2_Screen_pixelDepth"].value=screen.pixelDepth;
					}

					var timezoneOffsetField = form["TDS2_TimezoneOffset"];
					if (timezoneOffsetField!=null)
					{
						timezoneOffsetField.value=new Date().getTimezoneOffset();
					}
				}
			}
			populateData();
		</script>
                </div>
                <div id="content-footer"/>
            </div>
        </div>
    </body>
</html>
</div>
<!--<div th:remove="tag" th:utext="${n}"></div>-->
</body>
</html>
            [UCD_MD] => w0QgmihukO697+lvJCEvGQxCxB50yqht/HkUsuD5E2sSuQcjEFCyRVVJ3XYM5j0qxkwytzTKdmUb3kwRSY5CkJLpQFfxxug1Y9XUrqtrSsP8bjr8UpXDes0QUXRG89pIikQefdVoTjesORu+rIr8m7tmnErrzHckSS7Pg9jBaeJ+HSP1SebSTvQEUN0f/fhkzuxi7btTLCgZvUhoj5v0fhFOnTkpKd8nH0uPSXatJk8dqRL9R8M32tt7yfjQrEfOI5s2GTXTzYcclE+9/3Z1kRTGzt3ItuzSE1LXpifup31OXazCvZ8wsfTg0zFCSWRVHre4U3edgieYHclRnfksRNOjYKXNxd9nDGvDKeywATM=
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509110957121899
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-11 10:45:19
İşlem No: SESSION202509111045175554
Ödeme ID: 4099
Kullanıcı ID: 1715
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 4e988e11-1dcd-4a6b-bd43-f9d878c2f044
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=4e988e11-1dcd-4a6b-bd43-f9d878c2f044" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509111045175554
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-11 11:41:56
İşlem No: SESSION202509111141558588
Ödeme ID: 4102
Kullanıcı ID: 3715
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => b235c652-521b-47b1-af23-249fcbd3c01c
            [UCD_HTML] => <html>
<head>
    <title>GO</title>
    <meta http-equiv="Content-Language" content="tr">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="now">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
    <script type="text/javascript" nonce="6e95oqgmlaa6eo638fatttkda">
        window.onload = function () {
            document.returnform.submit();
        }
    </script>
</head>
<body>
<form action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" name="returnform">
    <input name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" type="hidden"/>
    <noscript>
        <center>Devam etmek icin tiklayiniz.<br><br>
            <input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
    </noscript>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=b235c652-521b-47b1-af23-249fcbd3c01c" />
</body>
</html>
            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509111141558588
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-11 11:50:11
İşlem No: SESSION202509111150091006
Ödeme ID: 4103
Kullanıcı ID: 4526
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => acc0d634-e4ba-4e00-8945-6d43f68d1e66
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=acc0d634-e4ba-4e00-8945-6d43f68d1e66" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509111150091006
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-11 11:55:00
İşlem No: SESSION202509111154581630
Ödeme ID: 4104
Kullanıcı ID: 1658
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 32a72459-db2c-4137-9143-121bc19253e0
            [UCD_HTML] => <!DOCTYPE html SYSTEM "about:legacy-compat">
<html class="no-js" lang="en" xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <meta charset="utf-8"/>
        <title>3-D Secure Processing</title>
        <link href="https://3d.payten.com.tr/mdpaympi/static/mpi.css" rel="stylesheet" type="text/css"/>
    </head>
    <body>
        <div id="main">
            <div id="content">
                <div id="order">
                    <h2>3-D Secure Processing</h2>
                    <div style="padding-bottom: 15px">
                        <img src="https://3d.payten.com.tr/mdpaympi/static/preloader.gif" alt="Please wait.."/>
                    </div>
                    <img src="https://3d.payten.com.tr/mdpaympi/static/verifiedbyvisa.png" alt="Verified by VISA"/>
                    <div id="formdiv">
                        <script type="text/javascript">
				function hideAndSubmitTimed(formid)
				{
				var timer=setTimeout("hideAndSubmit('"+formid+"');",2500);
				}

				function hideAndSubmit(formid)
				{
				var formx=document.getElementById(formid);
				if (formx!=null)
				{
				formx.style.visibility="hidden";
				formx.submit();
				}
				}
			</script>
                        <div>
                            <form id="webform0" name="ddcoll" method="POST" action="https://3d.payten.com.tr/mdpaympi/MerchantServer" accept_charset="UTF-8">
                                <input type="hidden" name="txid" value="1629652399"/>
                                <input type="hidden" name="TDS2_Navigator_language" value=""/>
                                <input type="hidden" name="TDS2_Navigator_javaEnabled" value=""/>
                                <input type="hidden" name="TDS2_Navigator_jsEnabled" value=""/>
                                <input type="hidden" name="TDS2_Screen_colorDepth" value=""/>
                                <input type="hidden" name="TDS2_Screen_height" value=""/>
                                <input type="hidden" name="TDS2_Screen_width" value=""/>
                                <input type="hidden" name="TDS2_Screen_PixelDepth" value=""/>
                                <input type="hidden" name="TDS2_TimezoneOffset" value=""/>
                                <input type="text" name="digest" value="7QaR8DqoX1p/BUinD0Z0pJwZ7TWpQ49mB9M2CPy8R+Q=" readonly="true" style="display:none;"/>
                                <input type="hidden" name="transientData" value="BVAMP2b0jarqoBZftmqDAOb2wca3EcW3m0Es7uOteKPTaP1w1l7g2ErStoHped+CgC4t1rrl8afZIZ4/VVWnkgEIE8B4xx42VUoXS/LtI0TgK+0hF1CzD59XyP152mb4xfkfhvbQ0hWTZ7FwnACiDDyFhxS6JeSfVuQkX5TNNQsZiXJuSsq9v3rPj0jvb3QtcQiGu1LkmWpTTgN3WazTQ9FB2qx6d1GsOJT66LApYodor5kTKCeLrOA51fWXTuNrb82U7seWymQPu0bs21Jn1Qx4XW6dDAx7wVkIKaHcJFKkQ429hj06gk1MCZo3kYFKIpEb8I7lNdvHfQHhqOAjlSloCgzpcDYVRlwpUO6fjLLEPHUK3LCBLCfYtVvObRMQjuusG0bRJbsf+0bpOyn6dxYdA23h2L6uttKg0N7lvqfUwTBQMvj5qzn13ax4HRd218T9GyXX99AtWYVp8hymGA=="/>
                                <noscript>
                                    <input type="submit" name="submitBtn" value="Please click here to continue"/>
                                </noscript>
                            </form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=32a72459-db2c-4137-9143-121bc19253e0" />
                        </div>
                    </div>
                    <script type="text/javascript">
			hideAndSubmitTimed('webform0');
		</script>
                    <noscript>
                        <div align="center">
                            <b>Javascript is turned off or not supported!</b>
                            <br/>
                        </div>
                    </noscript>
                    <script type="text/javascript">
			function populateData()
			{
				var form = document.getElementById("webform0");
				if (form != null)
				{
					if (form["TDS2_Navigator_language"]!=null)
					{
						form["TDS2_Navigator_language"].value=(navigator.language || '');
					}
					if (form["TDS2_Navigator_jsEnabled"]!=null)
					{
						// if this scipt runs js is enabled
						form["TDS2_Navigator_jsEnabled"].value="true";
					}
					if (form["TDS2_Navigator_javaEnabled"]!=null)
					{
						form["TDS2_Navigator_javaEnabled"].value=navigator.javaEnabled();
					}
					if (form["TDS2_Screen_colorDepth"]!=null)
					{
						form["TDS2_Screen_colorDepth"].value=screen.colorDepth;
					}
					if (form["TDS2_Screen_height"]!=null)
					{
						form["TDS2_Screen_height"].value=screen.height;
					}
					if (form["TDS2_Screen_width"]!=null)
					{
						form["TDS2_Screen_width"].value=screen.width;
					}
					if (form["TDS2_Screen_pixelDepth"]!=null)
					{
						form["TDS2_Screen_pixelDepth"].value=screen.pixelDepth;
					}

					var timezoneOffsetField = form["TDS2_TimezoneOffset"];
					if (timezoneOffsetField!=null)
					{
						timezoneOffsetField.value=new Date().getTimezoneOffset();
					}
				}
			}
			populateData();
		</script>
                </div>
                <div id="content-footer"/>
            </div>
        </div>
    </body>
</html>


            [UCD_MD] => BVAMP2b0jarqoBZftmqDAOb2wca3EcW3m0Es7uOteKPTaP1w1l7g2ErStoHped+CgC4t1rrl8afZIZ4/VVWnkgEIE8B4xx42VUoXS/LtI0TgK+0hF1CzD59XyP152mb4xfkfhvbQ0hWTZ7FwnACiDDyFhxS6JeSfVuQkX5TNNQsZiXJuSsq9v3rPj0jvb3QtcQiGu1LkmWpTTgN3WazTQ9FB2qx6d1GsOJT66LApYodor5kTKCeLrOA51fWXTuNrb82U7seWymQPu0bs21Jn1Qx4XW6dDAx7wVkIKaHcJFKkQ429hj06gk1MCZo3kYFKIpEb8I7lNdvHfQHhqOAjlSloCgzpcDYVRlwpUO6fjLLEPHUK3LCBLCfYtVvObRMQjuusG0bRJbsf+0bpOyn6dxYdA23h2L6uttKg0N7lvqfUwTBQMvj5qzn13ax4HRd218T9GyXX99AtWYVp8hymGA==
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509111154581630
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-11 12:05:13
İşlem No: SESSION202509111205125279
Ödeme ID: 4105
Kullanıcı ID: 85
Tutar: 17678,40
Taksit: 4

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => b2641902-54a0-412a-83a9-aabb19ba812e
            [UCD_HTML] => 

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1"><title>
	Payment MPI Service
</title></head>
<body>
    <form method="post" action="./Default.aspx" id="step1Form">
<div class="aspNetHidden">
<input type="hidden" name="goreq" id="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />

</div>

<script type='text/javascript'>var frm = document.getElementById('step1Form');frm.action = 'https://goguvenliodeme.bkm.com.tr/troy/approve' ;frm.method = "POST";frm.submit();</script>
    
<div class="aspNetHidden">

	<input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="BFED9D85" />
</div></form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=b2641902-54a0-412a-83a9-aabb19ba812e" />
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509111205125279
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
