/* Modern Admin Dictation Styling */

/* Modal Backdrop Fix */
.modal {
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(5px);
}

/* Modal Dialog Styling */
.modal-dialog {
    max-width: 900px !important;
}

.modal-content {
    background: white !important;
    border-radius: 20px !important;
    box-shadow: 0 25px 50px rgba(0,0,0,0.25) !important;
    border: none !important;
    overflow: hidden !important;
}

/* Modal Header */
.modal-header {
    background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
    color: white !important;
    border-bottom: none !important;
    padding: 25px 30px !important;
}

.modal-title {
    color: white !important;
    font-weight: 700 !important;
    font-size: 1.5rem !important;
    margin: 0 !important;
}

.btn-close {
    color: white !important;
    opacity: 0.8 !important;
    transition: opacity 0.3s ease !important;
    filter: invert(1) !important;
}

.btn-close:hover {
    opacity: 1 !important;
}

/* Modal Body */
.modal-body {
    padding: 30px !important;
    background: white !important;
}

/* Card Styling */
.card {
    border-radius: 16px !important;
    border: none !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 30px rgba(0,0,0,0.12) !important;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding: 20px 25px !important;
    font-weight: 600 !important;
}

.card-body {
    padding: 25px !important;
}

/* Table Styling */
.table {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05) !important;
}

.table thead th {
    background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
    color: white !important;
    border: none !important;
    padding: 15px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    font-size: 0.85rem !important;
}

.table tbody tr {
    transition: all 0.3s ease !important;
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05) !important;
    transform: scale(1.01) !important;
}

.table tbody td {
    padding: 15px !important;
    border-color: #f1f3f4 !important;
    vertical-align: middle !important;
}

/* Form Styling */
.form-control, .form-select {
    border: 2px solid #e1e5e9 !important;
    border-radius: 12px !important;
    padding: 12px 16px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: #f8f9fa !important;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    background: white !important;
}

.form-label {
    font-weight: 600 !important;
    color: #495057 !important;
    margin-bottom: 8px !important;
}

/* Button Styling */
.btn {
    border-radius: 25px !important;
    padding: 10px 25px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
}

.btn-success:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4) !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3) !important;
}

.btn-warning:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4) !important;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3) !important;
}

.btn-danger:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4) !important;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3) !important;
}

.btn-info:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4) !important;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
}

.btn-secondary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4) !important;
}

/* Badge Styling */
.badge {
    border-radius: 20px !important;
    padding: 6px 12px !important;
    font-weight: 600 !important;
    font-size: 0.75rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* Input Group Styling */
.input-group {
    margin-bottom: 15px !important;
}

.input-group-text {
    background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
    color: white !important;
    border: none !important;
    font-weight: 600 !important;
    border-radius: 12px 0 0 12px !important;
}

/* Sentence Item Styling */
.sentence-item {
    background: #f8f9fa !important;
    border-radius: 12px !important;
    padding: 15px !important;
    margin-bottom: 15px !important;
    border: 2px solid #e9ecef !important;
    transition: all 0.3s ease !important;
}

.sentence-item:hover {
    border-color: #667eea !important;
    background: white !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1) !important;
}

/* Alert Styling */
.alert {
    border-radius: 12px !important;
    border: none !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%) !important;
    color: #0c5460 !important;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
    color: #155724 !important;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    color: #856404 !important;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
    color: #721c24 !important;
}

/* Pagination Styling */
.pagination .page-link {
    border-radius: 8px !important;
    margin: 0 2px !important;
    border: none !important;
    padding: 10px 15px !important;
    color: #667eea !important;
    transition: all 0.3s ease !important;
}

.pagination .page-link:hover {
    background: #667eea !important;
    color: white !important;
    transform: translateY(-2px) !important;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

/* Animation for modal appearance */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-content {
    animation: modalSlideIn 0.3s ease-out !important;
}

/* Responsive */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 10px !important;
        max-width: calc(100% - 20px) !important;
    }
    
    .modal-header, .modal-body {
        padding: 20px !important;
    }
    
    .table-responsive {
        border-radius: 12px !important;
    }
    
    .btn {
        padding: 8px 20px !important;
        font-size: 0.9rem !important;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}
