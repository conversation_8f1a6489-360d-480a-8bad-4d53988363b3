=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 09:15:55
İşlem No: SESSION******************
Ödeme ID: 4064
Kullanıcı ID: 1335
Tutar: 2700,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 9df58ee3-c0f7-4645-bd87-d2d9e7f7ea65
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=9df58ee3-c0f7-4645-bd87-d2d9e7f7ea65" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION******************
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 11:12:04
İşlem No: SESSION202509091112032440
Ödeme ID: 4066
Kullanıcı ID: 3660
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => cd0f2046-9395-4fad-8779-2281372aa410
            [UCD_HTML] => <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="tr" xmlns="http://www.w3.org/1999/xhtml">
<head>
<script type="text/javascript" src="/ruxitagentjs_ICA27NVfqrux_10263230921131557.js" data-dtconfig="rid=RID_-**********|rpid=-**********|domain=denizbank.com|reportUrl=/rb_d0ac9e6b-b11a-4747-9900-8db5d8d1e8c3|app=600f804e2f46f262|cuc=w2s3jly9|owasp=1|mel=100000|featureHash=ICA27NVfqrux|dpvc=1|lastModification=*************|tp=500,50,0,1|rdnt=1|uxrgce=1|srbbv=2|agentUri=/ruxitagentjs_ICA27NVfqrux_10263230921131557.js"></script></head>
<body>
<form id="form1" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="POST">
<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=cd0f2046-9395-4fad-8779-2281372aa410" />
<script type="text/javascript">
 document.getElementById("form1").submit(); 
</script>
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509091112032440
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 11:25:57
İşlem No: SESSION202509091125568223
Ödeme ID: 4067
Kullanıcı ID: 1301
Tutar: 2700,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 15714d3f-ec74-4649-bcd7-fd718c12337f
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=15714d3f-ec74-4649-bcd7-fd718c12337f" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509091125568223
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 15:22:03
İşlem No: SESSION202509091522035689
Ödeme ID: 4070
Kullanıcı ID: 1296
Tutar: 2700,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => 0
            [Sonuc] => -201
            [Sonuc_Str] => Oran tanım hatası.
            [Banka_Sonuc_Kod] => -1
        )

)


ÖDEME HATASI:
Sonuç Kodu: -201
Hata Mesajı: Oran tanım hatası.
İşlem ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 15:22:27
İşlem No: SESSION202509091522266186
Ödeme ID: 4071
Kullanıcı ID: 1296
Tutar: 2700,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => 0
            [Sonuc] => -201
            [Sonuc_Str] => Oran tanım hatası.
            [Banka_Sonuc_Kod] => -1
        )

)


ÖDEME HATASI:
Sonuç Kodu: -201
Hata Mesajı: Oran tanım hatası.
İşlem ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 15:23:50
İşlem No: SESSION202509091523497276
Ödeme ID: 4072
Kullanıcı ID: 1191
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => e2c21bc2-cebe-4f24-b821-1e972d36b3a9
            [UCD_HTML] => <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="tr" xmlns="http://www.w3.org/1999/xhtml">
<head>
<script type="text/javascript" src="/ruxitagentjs_ICA27NVfqrux_10263230921131557.js" data-dtconfig="rid=RID_-**********|rpid=-**********|domain=denizbank.com|reportUrl=/rb_d0ac9e6b-b11a-4747-9900-8db5d8d1e8c3|app=600f804e2f46f262|cuc=w2s3jly9|owasp=1|mel=100000|featureHash=ICA27NVfqrux|dpvc=1|lastModification=*************|tp=500,50,0,1|rdnt=1|uxrgce=1|srbbv=2|agentUri=/ruxitagentjs_ICA27NVfqrux_10263230921131557.js"></script></head>
<body>
<form id="form1" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="POST">
<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=e2c21bc2-cebe-4f24-b821-1e972d36b3a9" />
<script type="text/javascript">
 document.getElementById("form1").submit(); 
</script>
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509091523497276
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 15:24:51
İşlem No: SESSION202509091524516065
Ödeme ID: 4073
Kullanıcı ID: 1296
Tutar: 2700,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => 0
            [Sonuc] => -201
            [Sonuc_Str] => Oran tanım hatası.
            [Banka_Sonuc_Kod] => -1
        )

)


ÖDEME HATASI:
Sonuç Kodu: -201
Hata Mesajı: Oran tanım hatası.
İşlem ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 15:33:29
İşlem No: SESSION202509091533284267
Ödeme ID: 4074
Kullanıcı ID: 1296
Tutar: 2700,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => 0
            [Sonuc] => -201
            [Sonuc_Str] => Oran tanım hatası.
            [Banka_Sonuc_Kod] => -1
        )

)


ÖDEME HATASI:
Sonuç Kodu: -201
Hata Mesajı: Oran tanım hatası.
İşlem ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 15:40:14
İşlem No: SESSION202509091540132797
Ödeme ID: 4075
Kullanıcı ID: 1296
Tutar: 2700,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => 0
            [Sonuc] => -201
            [Sonuc_Str] => Oran tanım hatası.
            [Banka_Sonuc_Kod] => -1
        )

)


ÖDEME HATASI:
Sonuç Kodu: -201
Hata Mesajı: Oran tanım hatası.
İşlem ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 15:49:01
İşlem No: SESSION202509091548598783
Ödeme ID: 4076
Kullanıcı ID: 1296
Tutar: 2700,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => e75bb7c2-1809-4d70-9858-8fbb95844e6c
            [UCD_HTML] => <html lang="en">
<head>
<title>Redirect Bkm Go Page</title>
</head>
<body>
	<script>
	window.onload = function(){
		  document.forms['autosubmit'].submit();
		}
	</script>
	<form name="autosubmit"  action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post">
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"> 
		<input type="submit" value="Submit" style="display:none">
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=e75bb7c2-1809-4d70-9858-8fbb95844e6c" />
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509091548598783
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 15:50:43
İşlem No: SESSION202509091550426714
Ödeme ID: 4077
Kullanıcı ID: 1296
Tutar: 2700,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => b08e6dfd-b3fb-44ac-bff6-6117089fd776
            [UCD_HTML] => <html lang="en">
<head>
<title>Redirect Bkm Go Page</title>
</head>
<body>
	<script>
	window.onload = function(){
		  document.forms['autosubmit'].submit();
		}
	</script>
	<form name="autosubmit"  action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post">
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"> 
		<input type="submit" value="Submit" style="display:none">
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=b08e6dfd-b3fb-44ac-bff6-6117089fd776" />
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509091550426714
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 16:45:11
İşlem No: SESSION202509091645108866
Ödeme ID: 4079
Kullanıcı ID: 1326
Tutar: 2700,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 0cf35147-f036-4719-b8b0-9e062d6d3696
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=0cf35147-f036-4719-b8b0-9e062d6d3696" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509091645108866
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 21:54:11
İşlem No: SESSION202509092154108391
Ödeme ID: 4084
Kullanıcı ID: 851
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => d452b3ba-fd10-4cd9-bef9-eb7e2a019311
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=d452b3ba-fd10-4cd9-bef9-eb7e2a019311" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509092154108391
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
