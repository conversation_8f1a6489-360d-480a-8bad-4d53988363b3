=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 09:27:34
İşlem No: ORDER202509090927322986
Ödeme ID: 4065
Kullanıcı ID: 2256
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => fea09664-265b-48a1-ac71-64d5955cf1eb
            [UCD_HTML] => <html>
<head>
    <title>GO</title>
    <meta http-equiv="Content-Language" content="tr">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="now">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
    <script type="text/javascript" nonce="2sflsm4enowqeb0evzioxy617">
        window.onload = function () {
            document.returnform.submit();
        }
    </script>
</head>
<body>
<form action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" name="returnform">
    <input name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" type="hidden"/>
    <noscript>
        <center>Devam etmek icin tiklayiniz.<br><br>
            <input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
    </noscript>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=fea09664-265b-48a1-ac71-64d5955cf1eb" />
</body>
</html>
            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509090927322986
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 12:00:21
İşlem No: ORDER202509091200192921
Ödeme ID: 4068
Kullanıcı ID: 4072
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 8e62c5b3-4bd9-4af1-ae4a-8c7620f54e93
            [UCD_HTML] => <html>
 <head>
  <!-- troyStartSuccess.htm -->
  <title>GO</title>
  <meta http-equiv="Content-Language" content="tr">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
  <script type="text/javascript" language="javascript" nonce="XIVPG6+6uCR0UM8aFlnnpkvkN+nqFq5NX+d7Ks6b/bk=">
	window.onload = function() {
		document.returnform.submit();
	}
</script>
 </head>
 <body>
  <form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post">
   <input type="hidden" name="goreq" value="********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
   <noscript>
    <center>
     Devam etmek icin tiklayiniz.
     <br>
     <br>
     <input type="submit" name="submit" value="Submit" id="btnSbmt">
    </center>
   </noscript>
  </form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=8e62c5b3-4bd9-4af1-ae4a-8c7620f54e93" />
 </body>
</html>
            [UCD_MD] => ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509091200192921
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 12:39:17
İşlem No: ORDER202509091239158672
Ödeme ID: 4069
Kullanıcı ID: 4072
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 859eca62-6446-4e33-8fbc-16e2bd1f1629
            [UCD_HTML] => <html>
 <head>
  <!-- troyStartSuccess.htm -->
  <title>GO</title>
  <meta http-equiv="Content-Language" content="tr">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
  <script type="text/javascript" language="javascript" nonce="QigwyIkHwWPFrgqSd92ADieP033nQJFK/RnTC9LMxLI=">
	window.onload = function() {
		document.returnform.submit();
	}
</script>
 </head>
 <body>
  <form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post">
   <input type="hidden" name="goreq" value="********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
   <noscript>
    <center>
     Devam etmek icin tiklayiniz.
     <br>
     <br>
     <input type="submit" name="submit" value="Submit" id="btnSbmt">
    </center>
   </noscript>
  </form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=859eca62-6446-4e33-8fbc-16e2bd1f1629" />
 </body>
</html>
            [UCD_MD] => ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509091239158672
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 18:30:14
İşlem No: ORDER202509091830132001
Ödeme ID: 4081
Kullanıcı ID: 4433
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 4289b3f8-1c22-45d8-8707-d4a30aa74f92
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=4289b3f8-1c22-45d8-8707-d4a30aa74f92" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509091830132001
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-09 20:01:42
İşlem No: ORDER202509092001418239
Ödeme ID: 4083
Kullanıcı ID: 1940
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4772,70
Taksit: 2

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 0bc19bc3-3528-42e9-8623-d6f7e8a3a2e3
            [UCD_HTML] => <html>
                          <head>
                            <meta name="viewport" content="width=device-width" />
                            <title>ParamPOS Connector</title>
                            <script>
                              function postPage() {
                              document.forms["frmParamPOS"].submit();
                              }
                            </script>
                          </head>
                          <body onload="javascript:postPage();">
                            <form action="https://posws.param.com.tr/api/bank/Vakifbank/start3d?d=&g=5c1b8a0c-9688-4042-8a1d-e2ed3d881a65&SID=0bc19bc3-3528-42e9-8623-d6f7e8a3a2e3" method="post" id="frmParamPOS" name="frmParamPOS">
                              <input type="hidden" name="guid" value="0bc19bc3-3528-42e9-8623-d6f7e8a3a2e3" />
                              <input type="hidden" name="d" value="" />
                              <input type="hidden" name="g" value="5c1b8a0c-9688-4042-8a1d-e2ed3d881a65" />
                              <noscript>
                                <input type="submit" id="btnSubmit" value="Gönder" />
                              </noscript>
                            </form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=0bc19bc3-3528-42e9-8623-d6f7e8a3a2e3" />
                          </body>
                        </html>
            [UCD_MD] => 0015474d6dba-ae51-4c33-b9b3-d121e61f4500#001550c83ad0-d0e7-4823-8fb2-3a2a3d5ed106#eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************.hxRoODRjbbi9l1ks_EBTf4-lU1iVFZhkbX3Pm5Q37H1E9vD-tLGUPeY7QpiWnwzCvAJhlLuJfre059uOWZD5Mg#https://goguvenliodeme.bkm.com.tr/troy/approve
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509092001418239
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
