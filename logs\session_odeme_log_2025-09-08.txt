=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 08:46:08
İşlem No: SESSION202509080846063841
Ödeme ID: 4040
Kullanıcı ID: 1980
Tutar: 4870,35
Taksit: 3

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 66af4b7b-0a44-4d69-9134-3fddd7c20071
            [UCD_HTML] => 

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1"><title>
	Payment MPI Service
</title></head>
<body>
    <form method="post" action="./Default.aspx" id="step1Form">
<div class="aspNetHidden">
<input type="hidden" name="goreq" id="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />

</div>

<script type='text/javascript'>var frm = document.getElementById('step1Form');frm.action = 'https://goguvenliodeme.bkm.com.tr/troy/approve' ;frm.method = "POST";frm.submit();</script>
    
<div class="aspNetHidden">

	<input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="BFED9D85" />
</div></form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=66af4b7b-0a44-4d69-9134-3fddd7c20071" />
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509080846063841
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 10:41:05
İşlem No: SESSION202509081041046569
Ödeme ID: 4047
Kullanıcı ID: 508
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => aa7b28da-2b48-485c-abf5-6da4a46758f7
            [UCD_HTML] => <html>
<head>
    <script type="text/javascript" nonce="06lispm5b9cbe8x73kehdy8ec">
        window.onload = function () {
            document.returnform.submit();
        };
    </script>
</head>
<body>
<form action="https://posws.param.com.tr/api/bank/payten/pre?SID=AA7B28DA-2B48-485C-ABF5-6DA4A46758F7&amp;g=5c1b8a0c-9688-4042-8a1d-e2ed3d881a65" method="post" name="returnform">
    <div>
        <input name="amount" value="4500,00" type="hidden"/>
    </div>
    <div>
        <input name="clientid" value="*********" type="hidden"/>
    </div>
    <div>
        <input name="currency" value="949" type="hidden"/>
    </div>
    <div>
        <input name="Ecom_Payment_Card_ExpDate_Month" value="06" type="hidden"/>
    </div>
    <div>
        <input name="Ecom_Payment_Card_ExpDate_Year" value="28" type="hidden"/>
    </div>
    <div>
        <input name="ErrMsg" value="Girilen kart numarası doğru formatta değildir. Kart numarasını kontrol ederek tekrar işlem deneyiniz." type="hidden"/>
    </div>
    <div>
        <input name="ErrorCode" value="HPP-1001" type="hidden"/>
    </div>
    <div>
        <input name="failUrl" value="https://posws.param.com.tr/api/bank/payten/pre?SID=AA7B28DA-2B48-485C-ABF5-6DA4A46758F7&amp;g=5c1b8a0c-9688-4042-8a1d-e2ed3d881a65" type="hidden"/>
    </div>
    <div>
        <input name="HASH" value="txVCOatcovUKT4vywWOKXZzVxC5jgxNhrk/lXrGzu5NF9svu13OVIdTdL3MuQx8GE+PTZHeAxQxLM6HX4J3ioQ==" type="hidden"/>
    </div>
    <div>
        <input name="hashAlgorithm" value="ver3" type="hidden"/>
    </div>
    <div>
        <input name="islemtipi" value="Auth" type="hidden"/>
    </div>
    <div>
        <input name="lang" value="tr" type="hidden"/>
    </div>
    <div>
        <input name="maskedCreditCard" value="4345 28** **** 9854" type="hidden"/>
    </div>
    <div>
        <input name="MaskedPan" value="434528***9854" type="hidden"/>
    </div>
    <div>
        <input name="oid" value="1008**********" type="hidden"/>
    </div>
    <div>
        <input name="okUrl" value="https://posws.param.com.tr/api/bank/payten/pre?SID=AA7B28DA-2B48-485C-ABF5-6DA4A46758F7&amp;g=5c1b8a0c-9688-4042-8a1d-e2ed3d881a65" type="hidden"/>
    </div>
    <div>
        <input name="refreshtime" value="1" type="hidden"/>
    </div>
    <div>
        <input name="Response" value="Error" type="hidden"/>
    </div>
    <div>
        <input name="rnd" value="wxJjJdxy+zXusLS4pnYF" type="hidden"/>
    </div>
    <div>
        <input name="storetype" value="3D" type="hidden"/>
    </div>
    <div>
        <input name="SUBMERCHANTID" value="PARAM/CAGLA AYDOGDU DIL A" type="hidden"/>
    </div>
    <div>
        <input name="SUBMERCHANTMCC" value="8299" type="hidden"/>
    </div>
    <div>
        <input name="SUBMERCHANTNAME" value="" type="hidden"/>
    </div>
    <div>
        <input name="SUBMERCHANTNUMBER" value="***************" type="hidden"/>
    </div>
    <div>
        <input name="taksit" value="" type="hidden"/>
    </div>
    <div>
        <input name="traceId" value="4a540b6893b713316bc8778e25bb2ae0" type="hidden"/>
    </div>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=aa7b28da-2b48-485c-abf5-6da4a46758f7" />
</body>
</html>
            [UCD_MD] => 
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509081041046569
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 10:42:22
İşlem No: SESSION202509081042205083
Ödeme ID: 4048
Kullanıcı ID: 508
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 52f6e40b-71d2-4210-9090-053ddf840876
            [UCD_HTML] => <html>
<head>
    <title>GO</title>
    <meta http-equiv="Content-Language" content="tr">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="now">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
    <script type="text/javascript" nonce="zfvrsz0i6ss2ki8fncbrlc4uy">
        window.onload = function () {
            document.returnform.submit();
        }
    </script>
</head>
<body>
<form action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" name="returnform">
    <input name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" type="hidden"/>
    <noscript>
        <center>Devam etmek icin tiklayiniz.<br><br>
            <input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
    </noscript>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=52f6e40b-71d2-4210-9090-053ddf840876" />
</body>
</html>
            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509081042205083
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 12:12:17
İşlem No: SESSION202509081212168947
Ödeme ID: 4050
Kullanıcı ID: 3972
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 4b46b3f1-83e5-478d-a945-331432f178be
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=4b46b3f1-83e5-478d-a945-331432f178be" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509081212168947
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== SESSION ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 15:54:47
İşlem No: SESSION202509081554462059
Ödeme ID: 4057
Kullanıcı ID: 3977
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => c30def2b-da73-4f5e-8b80-7ce0e3f365c9
            [UCD_HTML] => <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="tr" xmlns="http://www.w3.org/1999/xhtml">
<head>
<script type="text/javascript" src="/ruxitagentjs_ICA27NVfqrux_10263230921131557.js" data-dtconfig="rid=RID_-**********|rpid=*********|domain=denizbank.com|reportUrl=/rb_d0ac9e6b-b11a-4747-9900-8db5d8d1e8c3|app=600f804e2f46f262|cuc=w2s3jly9|owasp=1|mel=100000|featureHash=ICA27NVfqrux|dpvc=1|lastModification=*************|tp=500,50,0,1|rdnt=1|uxrgce=1|srbbv=2|agentUri=/ruxitagentjs_ICA27NVfqrux_10263230921131557.js"></script></head>
<body>
<form id="form1" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="POST">
<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=c30def2b-da73-4f5e-8b80-7ce0e3f365c9" />
<script type="text/javascript">
 document.getElementById("form1").submit(); 
</script>
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => SESSION202509081554462059
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
