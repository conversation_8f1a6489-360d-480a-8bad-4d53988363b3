{"version": 3, "sources": ["../scss/_variables.scss", "transparent-style.scss"], "names": [], "mappings": "AAKA;AAqCA;AASA;AAeA;AAaA;AAOA;AAQA;AC7FA;EACI,ODgCG;EC/BH,kBD+Fc;;;AC5FlB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAOI;EACI;;;AAKZ;AAAA;EAEI;;;AAGJ;AAwEI;AAiBA;AA0BA;AAOA;AAeA;AA2IA;AAWA;AAmCA;AAeA;AAMA;AA2BA;AAkBA;AAoCA;AAoCA;AAqCA;AA+CA;AAoHA;AAyCA;AA0KA;AAOA;AAkEA;AAOA;AA0KA;AAgIA;AAoJA;AACA;AAmKA;AAwEA;AAOA;AA2FA;AAkPA;AAgBA;AAkQA;AA4MA;AAmMA;AAmJA;AAqBA;AAsBA;AA8DA;AAwDA;AAOA;AA4BA;AAgFA;AAsIA;AA6EA;AAAA;AAAA;AAGA;AAgCA;AAyBA;AACA;AAwHA;AAmDA;AAiNA;AACA;AACA;AAsHA;AAgBA;AAyBA;AA6BA;AAwGA;AA4CA;AAsWA;AACA;AACA;AACA;AAoCA;AAqGA;AACA;AAsDA;AA0BA;AA6GA;AA4HA;AA2nDA;AAwDA;AAqDA;AAgGA;AAgIA;;AA9sNA;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;AAAA;EAEI;;AAEJ;EACI;;AAEJ;EACI,ODpED;ECqEC,kBDzEF;;AC2EF;EACI,ODxED;;AC0EH;EACI;AAAA;IAEI;;EAEJ;IACI,cD/EL;;ECiFC;AAAA;AAAA;AAAA;IAII;;EAEJ;AAAA;AAAA;AAAA;IAII;;;AAIJ;EAEI,YDnCO;;ACsCf;EACI;EACA;EACA,OD/EC;;ACiFL;EACI,ODpFC;ECqFD,kBD7CW;EC8CX;;AAEJ;EACI;EACA,cDhDY;;ACmDhB;EACI,cDpDY;;ACqDZ;EACI,ODvHL;;ACyHC;EACI,YD3DO;;AC6DX;EACI,YD9DO;;ACgEX;EACI;EACA,qBDhEQ;;ACoEhB;EACI,cDrEY;ECsEZ;;AAEJ;EACI,OD1ID;;AC2IC;EACI,cD3EQ;;AC6EZ;EACI,OD/IL;ECgJK,YDhFM;;ACkFV;EACI,OD1HH;;AC8HD;EACI;;AAEJ;EACI;;AAIR;EACI;;AAEJ;EACI;;AAGJ;EACI,YDxGW;ECyGX,OD/IC;;ACiJL;EACI,ODlJC;;ACoJL;EACI,ODrJC;ECsJD,cD9GY;;ACgHhB;EACI,cDjHY;;ACqHZ;EACI,OD9JH;;ACgKD;EACI,ODnKH;;ACsKL;EACI,kBD9HU;;ACiIV;EACI,OD7ND;;AC+NH;EACI,ODjLH;;ACmLD;EACI,OD/KH;;ACkLL;EACI,OD5OO;EC6OP;EACA;;AACA;EACI,ODhPG;ECiPH;EACA;;AAEJ;EAEI;;AAEJ;EAEI,OD1PG;EC2PH;EACA;;AAGA;EAEI,ODjQD;ECkQC;EACA;;AAIZ;EACI,ODxOD;ECyOC,kBD1KW;EC2KX,cDzKY;;AC0KZ;EACI,OD5OL;EC6OK,kBD9KO;EC+KP,cD7KQ;;AC+KZ;EAEI;;AAEJ;EAEI,ODtPL;ECuPK,kBDxLO;ECyLP,cDvLQ;ECwLR;;AAGA;EAEI;EACA,kBDhMG;ECiMH,cD/LI;;ACmMhB;EACI,ODrQD;ECsQC;EACA,cDtMY;;ACuMZ;EACI,ODzSG;EC0SH;;AAGR;EACI,OD9QD;EC+QC,kBDhNW;ECiNX,cD/MY;;ACgNZ;EACI,ODlRL;ECmRK,kBDpNO;ECqNP,cDnNQ;;ACqNZ;EAEI;;AAEJ;EAEI;EACA,kBD9NO;EC+NP,cD9RL;;ACiSK;EAEI;EACA;EACA,cDpOI;;ACwOhB;EACI;EACA;EACA,cD3OY;;AC8OZ;EAEI,kBD5UD;EC6UC,cD7UD;;ACgVP;EACI,cD9RC;EC+RD,OD/RC;;ACgSD;EAGI,kBDtVD;ECuVC,cDvVD;;AC2VP;EACI,kBDlQW;ECmQX;;AAGJ;EAEI,kBDtQY;ECuQZ;;AAGJ;EACI,cD3QY;EC4QZ;EACA;;AAEJ;EACI,kBDhRY;;ACkRhB;EACI,qBDnRY;;ACsRZ;EACI;;AAGR;EACI;;AAEJ;EACI,kBD9RY;;ACgShB;EACI,kBDjSY;;ACmShB;EACI;;AACA;EACI,OD9UH;;AC+UG;EACI;;AAKZ;EACI;;AAGA;EACI;;AACA;EACI,ODrXT;;ACwXC;EACI;;AAIR;EAEI,kBD9TY;EC+TZ;;AAGJ;EACI,kBDnUY;;ACsUZ;EACI;;AAGR;EACI;;AAEJ;EACI,kBD9UY;;ACgVhB;EACI,kBDjVY;;ACmVhB;EACI;;AACA;EACI,OD9XH;;AC+XG;EACI;;AAKZ;EACI;;AAGA;EACI;;AACA;EACI,ODraT;;ACwaC;EACI;;AAGR;EACI,qBD5WY;;AC+WhB;AAAA;EAEI,ODlbD;;ACqbC;EAEI,ODvbL;;AC2bC;EAEI,OD7bL;;ACscC;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI,kBD1cL;;AC6cH;EACI,OD9cD;;ACgdH;EACI;;AAGJ;AAAA;EAEI,ODtdD;;ACydC;EAEI,OD3dL;;AC+dC;EAEI,ODjeL;;AC0eC;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI,kBD9eL;;ACifH;EACI,ODlfD;;ACofH;EACI;;AAGJ;EACI,ODheC;ECieD,kBD1bU;EC2bV,cD1bY;;AC4bhB;EACI,kBD7bY;;AC+bhB;EACI,ODjgBD;;ACmgBH;EACI,OD3eC;;AC8eD;EAII,kBD5cO;;AC8cX;EAEI,OD/gBL;;ACkhBH;EACI,cDldY;ECmdZ;;AAEJ;EACI,OD9fC;;ACggBL;EACI,OD1hBD;;AC6hBH;AAAA;EAEI;EACA;EACA;EACA;;AAEJ;EACI,kBDhkBG;ECikBH;EACA;;AACA;EACI,ODziBL;;AC0iBK;EAEI,ODnhBP;ECohBO,kBD9eG;;ACifX;EACI,kBDhfQ;;ACkfZ;EACI;EACA;EACA;;AAGR;EACI,qBDzfY;;AC0fZ;EACI,ODniBH;;ACsiBL;AAAA;EAEI,YD5lBG;EC6lBH,kBDjgBY;ECkgBZ,mBDlgBY;;ACogBhB;EACI;;AAEJ;EACI,kBD1gBW;;AC6gBf;EACI,OD7kBD;EC8kBC;EACA;EACA;;AACA;EACI;;AAEJ;EACI,OD5jBH;EC6jBG,kBDvhBO;ECwhBP,cDthBQ;;ACwhBZ;EACI;;AAEJ;EAII;;AAEJ;EAEI,kBDpiBM;;ACsiBV;EACI,ODvmBL;ECwmBK,kBDxiBM;;AC0iBV;EACI,kBD5iBO;;AC+iBf;EACI,ODtlBC;;ACwlBL;EACI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;AAAA;AAAA;EAII;EACA;;AAEJ;AAAA;AAAA;AAAA;EAII;;AAEJ;AAAA;AAAA;AAAA;EAII;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI;;AAEJ;EACI,YDzlBW;EC0lBX;EACA,OD1pBD;;AC2pBC;EACI,OD5pBL;;AC8pBC;EAEI;;AAGR;EACI;;AAEJ;EACI,cDlsBG;;ACosBP;EACI,YDvpBC;ECwpBD,cD1mBY;;AC4mBhB;EACI,OD9qBD;EC+qBC,kBD1sBG;EC2sBH,cD/mBY;;ACinBhB;EACI,cDlnBY;;AConBhB;EACI,ODtrBD;ECurBC;;AAEJ;EACI;;AAEJ;EACI,cD5nBY;;AC+nBhB;EACI,kBD5tBG;EC6tBH,cDjoBY;ECkoBZ;;AAEJ;EACI,kBDrsBD;;ACusBH;EACI,qBDxoBY;;AC0oBhB;EACI,kBD3oBY;;AC8oBZ;EACI;EACA;;AAEJ;EACI,ODptBL;;ACstBC;EACI;EACA;;AACA;EACI,OD1tBT;;AC6tBC;EACI;;AACA;EACI,ODhuBT;;ACmuBC;EACI;EACA,kBDpqBQ;;ACwqBhB;EACI,qBDzqBY;;AC4qBZ;AAAA;EAEI,OD/uBL;;ACkvBH;EACI;;AAEJ;EACI,ODtvBD;;ACwvBH;EACI,YDzrBU;;AC4rBV;EAEI,OD9vBL;;ACiwBH;EACI,ODlwBD;;ACmwBC;EACI,OD/xBD;;ACiyBH;EACI,ODlvBH;;ACqvBL;EACI,qBD1sBY;;AC4sBR;EACI;;AAEJ;EACI;EACA,ODzvBP;EC0vBO;;AACA;EAEI,ODtxBb;;AC0xBC;EACI;;AACA;EACI;;AAEJ;EACI;EACA;;AAIZ;EACI,ODj0BG;;ACo0BC;EACI;;AAIZ;EACI,OD/yBD;;ACizBH;EACI;;AAEJ;EACI,ODpzBD;;ACqzBC;EACI;EACA,OD/xBH;;ACgyBG;EAEI,OD3zBT;;AC6zBK;EACI;EACA;EACA;;AAGR;EACI,YDrwBO;ECswBP,cDpwBQ;ECqwBR;EACA;;AACA;EACI;;AACA;EACI;EACA;EACA;;AAKhB;EACI,kBDpxBW;ECqxBX,cDnxBY;ECoxBZ,OD5zBC;;AC6zBD;EACI,kBDxxBO;;AC4xBX;EACI,OD51BL;;AC81BC;EACI;EACA,kBDjyBO;ECkyBP,cDhyBQ;;ACmyBhB;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI,kBDjzBW;ECkzBX,kBDhzBY;ECizBZ,mBDjzBY;ECkzBZ,oBDlzBY;;ACozBhB;AAAA;EAEI,cDtzBY;;ACwzBhB;EACI,kBD1zBU;EC2zBV,cD1zBY;;AC2zBZ;EACI;;AAGR;EACI,cDj4BD;;ACk4BC;EACI;;AAGR;EACI,YDx0BW;;AC00Bf;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAIA;EAEI,ODt5BL;;AC05BH;EACI;EACA;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI,qBDn2BY;;ACq2BhB;EACI;EACA;;AAEJ;EACI,kBD32BU;EC42BV;EACA;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI,kBDr3BU;;ACu3Bd;AAAA;EAEI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI,mBDl5BW;;ACo5Bf;EACI;EACA,kBDt5BW;ECu5BX;;AAEJ;EACI;;AAGJ;EACI;;AAEJ;EACI,ODh+BD;;ACm+BH;EACI,ODp+BD;;ACq+BC;EACI,qBDr6BQ;;ACw6BhB;EACI,qBDz6BY;;AC26BhB;AAAA;EAEI;;AAEJ;AAAA;AAAA;EAGI,kBDp7BW;;ACu7BX;AAAA;EAEI,kBDz7BO;;AC47Bf;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI,cDh8BY;;ACk8BhB;EACI;;AAGA;AAAA;EAEI,kBDz8BM;EC08BN,OD1gCL;;AC6gCH;EACI;;AAEI;AAAA;EAEI;;AAIZ;EACI;;AAEI;AAAA;EAEI;;AAIZ;AAAA;EAEI,ODjiCD;ECkiCC,kBDliCD;ECmiCC;;AAEJ;AAAA;EAEI;EACA,kBDz+BW;EC0+BX,cDx+BY;;AC0+BhB;EACI,cD3+BY;;AC4+BZ;EACI;;AACA;EACI,qBD/+BI;;ACk/BJ;EAEI;;AAGR;EACI,YD1/BG;;AC8/Bf;AAAA;EAEI,OD/jCD;;ACikCH;AAAA;EAEI,ODnmCO;;ACsmCP;AAAA;EAEI,kBDvgCQ;;AC2gCZ;AAAA;AAAA;AAAA;EAII;;AAIJ;AAAA;AAAA;AAAA;EAII;;AAIJ;EACI,OD7lCL;;AC+lCC;EACI,kBDjiCO;;ACoiCf;EACI;EACA;;AACA;EACI;;AAEJ;EACI,OD1mCL;EC2mCK;;AACA;EACI,YD9iCG;;ACkjCf;AAAA;AAAA;EAGI;;AAII;EACI,qBDxjCI;;AC2jCZ;EACI,cD5jCQ;;AC+jChB;EACI,ODzkCI;;AC2kCR;EACI,kBDnkCY;;ACqkChB;EACI,OD/kCI;;ACilCR;EACI;;AAGJ;EACI,OD9oCD;EC+oCC,kBD/kCU;;ACilCd;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA,OD9pCD;;ACgqCH;EACI;EACA,ODlqCD;;ACoqCH;EACI;EACA,ODtqCD;;ACwqCH;EACI;EACA,OD1qCD;;AC4qCH;EACI;EACA,OD9qCD;;ACgrCH;EACI;EACA,ODlrCD;;ACorCH;EACI;EACA,ODtrCD;;ACwrCH;EACI;EACA,OD1rCD;;AC4rCH;EACI;EACA,OD9rCD;;ACgsCH;EACI;EACA,ODlsCD;;ACosCH;EACI,kBDtoCW;ECuoCX,ODtsCD;;ACwsCH;EACI;EACA,OD1sCD;;AC4sCH;EACI;EACA,OD9sCD;;ACgtCH;EACI;EACA,ODltCD;;ACotCH;EACI;EACA,ODttCD;;ACwtCH;EACI;EACA,OD1tCD;EC2tCC;EACA,OD5tCD;;AC8tCH;EACI;EACA,ODhuCD;;ACkuCH;EACI;EACA,ODpuCD;;ACsuCH;EACI,kBDxuCD;ECyuCC,ODxuCD;;AC0uCH;EACI;EACA,OD5uCD;;AC8uCH;EACI;EACA,ODhvCD;;ACkvCH;EACI;EACA,ODpvCD;;ACsvCH;EACI;EACA,ODxvCD;;AC0vCH;EACI,kBD3vCD;;AC6vCH;EACI;EACA,ODvwCF;ECwwCE,cDxwCF;;ACywCE;EACI,cD1wCN;;AC6wCF;EACI,cDrsCY;;ACusChB;EACI,cDxsCY;ECysCZ;;AAGJ;EACI,kBD/sCW;ECgtCX,cD9sCY;;ACgtChB;AAAA;EAEI,kBDntCU;;ACqtCd;AAAA;EAEI,oBDvtCU;;ACytCd;AAAA;EAEI;;AAEJ;AAAA;EAEI,mBD/tCU;;ACiuCd;EACI,kBDluCU;;ACouCd;EACI,kBDruCU;ECsuCV,cDruCY;;ACwuCZ;EACI;EACA,OD3yCL;;AC6yCC;EAEI,kBD10CD;;AC40CH;EAEI,qBD90CD;;ACg1CH;EAEI,mBDl1CD;;ACo1CH;EAEI,oBDt1CD;;ACy1CP;EACI,kBD/vCU;ECgwCV;;AAEJ;AAAA;EAEI,kBDpwCU;;ACswCd;AAAA;EAEI,kBDxwCU;;AC0wCd;AAAA;EAEI,oBD5wCU;;AC8wCd;AAAA;EAEI,oBDhxCU;;ACkxCd;AAAA;EAEI;;AAEJ;AAAA;EAEI,qBDzxCW;;AC2xCf;AAAA;EAEI,qBD3xCY;;AC6xChB;AAAA;EAEI;EACA,mBDjyCU;;ACmyCd;AAAA;EAEI;EACA,mBDtyCU;;ACwyCd;EACI,ODz2CD;EC02CC,kBD3yCW;EC4yCX,cD1yCY;;AC4yChB;EACI,ODv1CC;;AC01CD;EACI,ODl3CL;ECm3CK;;AAEJ;EAEI;;AAIJ;EACI,OD53CL;EC63CK;;AAEJ;EAEI;;AAGR;AAAA;EAEI;;AAEJ;EACI;;AACA;EACI;EACA;EACA,OD74CL;;AC+4CC;EAEI;;AAEJ;EAEI;;AAEJ;EAEI;;AAEJ;EAEI;;AAMJ;EACI,ODp6CL;;ACs6CC;EACI,ODv6CL;ECw6CK;;AAEJ;EACI,ODl5CH;;ACm5CG;EACI,kBD52CI;;ACg3ChB;EACI,kBDj3CY;;ACm3ChB;EACI;;AAEJ;EACI,OD/5CC;ECg6CD,cDx3CY;ECy3CZ;;AACA;EACI,cD33CQ;EC43CR;;AACA;EACI,ODt6CP;ECu6CO,kBDj4CG;;ACo4CX;EACI;EACA,kBDt4CO;;ACy4Cf;EACI,ODh7CC;ECi7CD,kBD34CW;EC44CX,cD14CY;;AC24CZ;EACI,OD78CL;EC88CK,mBD74CQ;;ACi5CZ;EACI,YDp5CO;ECq5CP;;AACA;EACI;;AAEJ;EACI;;AAGR;EACI,kBD55CQ;EC65CR;;AAEJ;EACI,YDl6CO;;ACm6CP;EACI;;AAEJ;EACI;;AAGR;EACI,kBDz6CQ;EC06CR;;AAEJ;EACI,YD/6CO;ECg7CP;;AACA;EACI;;AAEJ;EACI;;AAGR;EACI,kBDv7CQ;ECw7CR;;AAGR;EACI,cD57CY;EC67CZ;;AAII;EAGI;EACA;;AAGR;EACI;;AAEJ;EACI;EACA,kBD78CQ;;ACg9ChB;EACI,ODz/CC;;AC2/CL;EACI,OD5/CC;;AC8/CL;EACI,YDjgDC;ECkgDD,cDx9CY;;ACy9CZ;EACI,YD3hDL;EC4hDK;;AAGR;EACI;;AAEJ;AAAA;EAEI,kBDn+CY;;ACu+CZ;AAAA;EACI,kBDx+CQ;;AC0+CZ;AAAA;EACI;;AAGR;EACI;;AAEJ;EACI,YD9kDG;;ACglDP;AAAA;EAEI,YDhiDC;ECiiDD,cDv/CY;;ACw/CZ;AAAA;EACI,YD1jDL;EC2jDK;;AAGR;EACI,YDhgDW;;ACkgDf;EACI,ODlkDD;;ACqkDH;EACI,YDvgDW;ECwgDX,mBDtgDY;ECugDZ;;AAEJ;EACI;IACI,qBD3gDQ;;;AC8gDhB;EACI;AAAA;IAEI,cDjhDQ;;;ACohDhB;EACI;IACI,ODvlDL;;;AC0lDH;EACI;IACI,OD5lDL;;;AC+lDH;EACI;IACI;;EAEJ;IACI;;EAEJ;IACI;;;AAGR;EACI,OD3mDD;EC4mDC;;AAEJ;EACI,cD9iDY;;ACgjDhB;EACI;;AAEJ;EACI;EACA,ODtnDD;ECunDC;;AAEJ;EACI,cDzjDY;EC0jDZ;;AAEJ;EACI,OD9nDD;;AC+nDC;EACI;;AAGR;EACI;;AAEJ;EACI,ODvoDD;;ACyoDH;EACI;;AAGJ;EACI,YD/kDW;;ACglDX;EACI,YDjlDO;;ACqlDf;EACI;;AAEJ;EACI;;AAEJ;EACI,OD3pDD;;AC4pDC;EACI;;AAIJ;EACI;;AAGR;EAEQ;IAEI;;;AAIZ;EACI;IACI,YDhnDO;;ECknDX;IACI,ODjrDL;;ECqrDS;IACI;;EAEJ;IACI;;EAGR;IACI,OD9rDT;;;ACksDH;EACI,ODnsDD;;ACqsDH;EACI,YDvoDW;ECwoDX,cDtoDY;;ACwoDhB;EACI;AAAA;IAEI,YD7oDO;;;ACkpDP;EACI,YDnpDG;ECopDH;;AAEJ;EACI,YDtpDE;ECupDF;;AAIJ;EAEI;;AAIZ;EACI;;AAEJ;EACI;;AAEJ;AAAA;EAEI,ODntDC;;ACqtDL;EACI,cD3qDY;;AC8qDhB;EACI;IACI;IACA;;;AAGR;EACI;IACI;IACA;;;AAGR;EACI;;AACA;EACI;EACA;;AAGR;EACI;;AACA;EACI;EACA;;AAIJ;EACI;;AAEJ;EACI;;AAGR;EACI,ODlxDD;;ACoxDH;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI,YDruDW;;ACuuDf;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGA;EACI,YDxvDO;ECyvDP,ODxzDL;;AC0zDC;EACI,OD3zDL;;AC6zDC;EACI,YDzyDH;;AC2yDD;EACI;;AAEJ;EACI;;AACA;EACI;;AAIZ;EACI,OD32DO;;AC62DX;EACI,YDz2DG;;AC22DP;EACI;;AAEJ;EACI,kBDxzDE;;AC2zDF;EACI,ODx1DL;;AC01DC;EAKI,OD92DD;;AC02DC;EACI;EACA,OD71DT;;ACi2DC;EAKI,OD/2DJ;;AC22DI;EACI;EACA,ODp2DT;;ACw2DC;EACI,ODp3DH;;ACq3DG;EACI;EACA,OD52DT;;AC+2DC;EACI;;AACA;EACI;EACA,ODn3DT;;ACu3DH;EACI,ODx3DD;;ACy3DC;EACI,mBDt2DH;;ACy2DL;EACI;;AAGA;EACI,OD92DH;;AC+2DG;EACI;;AAGR;EACI;;AAIJ;AAAA;EAEI,cD70DQ;EC80DR,OD/4DL;;ACi5DC;EACI;;AAGR;EACI,cDr1DY;ECs1DZ,OD93DC;;ACg4DL;EAII,kBD91DW;EC+1DX,cD71DY;;ACy1DZ;EACI,cD11DQ;;AC+1DhB;EACI,cDh2DY;;ACk2DhB;EAKI,cDv2DY;ECw2DZ,ODz6DD;;ACo6DC;EACI,YDh8DD;ECi8DC,ODt6DL;;AC26DH;AAAA;AAAA;EAGI,OD96DD;;ACg7DH;EACI,YDl3DW;ECm3DX,cDj3DY;;ACm3DhB;EACI,YDt3DW;;ACw3Df;EACI,OD/5DC;;ACi6DL;EACI;;AAEJ;EACI,OD97DD;;ACg8DH;EACI;;AACA;EACI,OD16DH;;AC66DG;EACI;;AAEJ;EAGI,OD58DT;;ACg9DK;EACI;;AAEJ;EAGI,ODt9DT;;AC09DH;EACI,QD55DW;;AC85Df;EACI;;AAGJ;EACI,YDl6DU;;ACo6Dd;EACI,oBDp6DY;;ACs6DhB;EACI,mBDv6DY;;ACy6DhB;EACI,kBD16DY;;AC46DhB;EACI,qBD76DY;;ACg7DhB;EACI,YDn7DW;ECo7DX,cDl7DY;ECm7DZ;;AACA;EACI,YDv7DO;ECw7DP,ODv/DL;ECw/DK,qBDv7DQ;;AC27DZ;EACI;;AAGR;EACI;;AAEJ;EACI,kBDr8DW;ECs8DX;;AAEJ;EACI,ODxgED;;ACygEC;EACI;;AAEJ;EACI;;AAGR;EACI;;AAEJ;EACI,YDp9DU;ECq9DV,cDp9DY;ECq9DZ;;AAEJ;EACI,cDx9DY;ECy9DZ,YD39DW;;AC69Df;EAMI,ODzgEC;;ACogED;EAGI,YDj+DO;;ACq+Df;EACI;IACI;;;AAGR;EACI;;AAEJ;EACI,cD5+DY;;AC8+DR;EACI,ODzhEP;;AC2hEG;EACI,ODnjET;;ACujEH;EACI;;AAEJ;AAAA;AAAA;AAAA;EAII,YD9/DU;;ACggEd;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;EACA;;AAEJ;EACI,YD3gEW;EC4gEX,cD1gEY;;AC6gEZ;EAEI,ODhlEL;;ACmlEH;EACI,ODplED;;ACslEH;EAII,cDzhEY;;ACshEZ;EACI,cDvhEQ;;AC2hEhB;EAKI,cDhiEY;;AC4hEZ;EACI;EACA,OD1nED;;AC+nEH;EAEI,kBDriEQ;;ACuiEZ;EACI;;AAGR;EACI,OD7mED;;AC+mEK;EACI;EACA;EACA;;AAEJ;EACI;;AAIZ;EACI;;AAEJ;EACI;;AAEJ;EACI,cD3pEG;EC4pEH;EACA,OD7pEG;;AC+pEP;EACI,cDnpEF;ECopEE;EACA,ODrpEF;;ACupEF;EACI,cDzpEG;EC0pEH;EACA,OD3pEG;;AC6pEP;EACI,cD1qEG;;AC4qEP;EACI,kBDnlEW;;ACslEX;EACI,MDtpEL;;ACwpEC;EACI,MDzpEL;;AC6pEC;EACI,YD3oEH;;AC8oEG;AAAA;EAEI,OD9oEP;;ACipED;EACI,YDvmEM;ECwmEN,ODxqEL;ECyqEK;;AAEJ;EACI;EACA,YD9mEO;;AC+mEP;EACI;EACA,oBDjnEG;;ACqnEf;EACI,ODhqEC;;ACkqEL;EACI,cDvnEY;ECwnEZ;EACA;EACA,OD3rED;;AC4rEC;EACI;EACA;;AAGR;EACI;EACA,YDpoEW;;ACuoEX;EAEI;;AAGR;EACI,cD3oEY;EC4oEZ;;AAEJ;EACI,ODhtED;;ACmtEC;EACI;;AAEJ;EACI;;AAGR;EACI,OD3tED;;AC6tEH;EACI,MDvsEC;;ACysEL;EACI,ODjuED;;ACkuEC;EAGI,ODruEL;;ACwuEH;EACI,ODzuED;;AC2uEH;EACI;;AAEJ;EACI;EACA;;AAKI;EACI;;AAGR;EACI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI,cDnsEY;ECosEZ;EACA,YDvsEW;;ACwsEX;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;AAAA;EAEI;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;AAAA;AAAA;AAAA;AAAA;EAKI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI,OD32EL;;AC62EC;EACI;;AAEJ;AAAA;AAAA;AAAA;EAII;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;AAAA;AAAA;EAGI;;AAEJ;EACI;;AAGA;EACI;;AACA;EACI;;AAGR;EACI;;AAGR;AAAA;AAAA;EAGI;;AAGR;EACI;EACA,OD56ED;;AC86EH;EACI,YD17EC;EC27ED,ODh7ED;;ACk7EH;EACI;EACA,ODp7ED;;ACs7EH;EACI,YDj8EC;ECk8ED,ODx7ED;;AC07EH;EACI,YDn8EF;ECo8EE,OD57ED;;AC+7EH;EACI,kBDj4EW;ECk4EX,ODj8ED;ECk8EC,cDj4EY;;ACm4EhB;EACI,OD56EC;;AC66ED;EAGI,ODh7EH;ECi7EG,kBD14EM;;AC64Ed;AAAA;AAAA;AAAA;AAAA;EAKI;EACA,cDl5EY;ECm5EZ,OD37EC;;AC87ED;EAEI,ODr8EH;ECs8EG;;AAGR;EACI;EACA;;AAEI;EAEI;EACA;;AAEJ;EACI,ODv+ET;ECw+ES;EACA;;AAIZ;EACI;EACA;;AAEI;EAEI;EACA;;AAEJ;EACI,ODv/ET;ECw/ES;EACA;;AAIZ;EACI;EACA;;AAEI;EAEI,OD9gFP;EC+gFO;;AAEJ;EACI,ODvgFT;ECwgFS,kBDnhFP;ECohFO,cDphFP;;ACwhFL;EACI;EACA;;AAEI;EAEI,OD3hFV;EC4hFU;;AAEJ;EACI,ODvhFT;ECwhFS;EACA;;AAIZ;EACI;EACA;;AAEI;EAEI,OD7iFP;EC8iFO;;AAEJ;EACI,ODviFT;ECwiFS,kBDljFP;ECmjFO,cDnjFP;;ACujFL;EACI;EACA;;AAEI;EAEI,OD5jFR;EC6jFQ;;AAEJ;EACI,ODvjFT;ECwjFS,kBDjkFR;ECkkFQ,cDlkFR;;ACskFJ;EACI;EACA;;AAEI;EAEI;EACA;;AAEJ;EACI,ODvkFT;ECwkFS;EACA;;AAIZ;EACI;EACA;;AAEI;EAEI;EACA;;AAEJ;EACI,ODvlFT;ECwlFS;EACA;;AAIZ;EACI,cD7hFY;EC8hFZ,ODtkFC;;ACukFD;EACI;;AAEJ;EACI;;AAKA;EACI,YD3iFG;EC4iFH,OD3mFT;;AC8mFC;EACI,OD/mFL;;ACknFH;EACI,OD3jFI;;AC6jFR;EACI,OD9jFI;;ACgkFR;EACI,cDxjFY;;AC0jFhB;EACI,ODpkFI;;ACskFR;EACI,cD9jFY;;ACikFhB;EACI,OD1mFC;;AC2mFD;EAEI;;AAGR;EACI;EACA;;AAEJ;EACI;IACI;;;AAGR;EACI;;AAGA;EACI,ODvpFL;;ACwpFK;EAEI,OD1pFT;;AC8pFK;EACI;;AACA;EAEI;;AAEJ;EACI;;AAGR;AAAA;EAEI;;AAGA;EAEI;;AAIZ;EACI;EACA;;AAEJ;EACI;;AACA;EACI;;AACA;EAEI;;AAMZ;EACI,ODpsFL;;ACqsFK;EAEI,ODvsFT;;AC2sFK;EACI;;AACA;EAEI;;AAEJ;EACI;;AAGR;AAAA;EAEI,ODvtFT;;AC0tFS;EAEI,OD5tFb;;ACguFC;EACI;EACA;;AAEJ;EACI;;AACA;EACI,ODvuFT;;ACwuFS;EAEI,OD1uFb;;AC+uFH;EAGQ;IACI;;EAKA;IACI;;EAMJ;IACI;;EAKR;IACI,ODtwFT;;;AC0wFH;EACI;IACI,YD5sFM;IC6sFN;IACA,kBD7sFQ;;EC+sFZ;IACI;;;AAIR;EACI,oBDrtFY;ECstFZ,mBDttFY;ECutFZ,qBDvtFY;;ACytFhB;EACI,cD1tFY;;AC4tFhB;EACI,OD9xFD;;AC+xFC;EACI,ODhyFL;;ACoyFC;EAEI,ODj0FD;;ACq0FP;EACI,OD3yFD;;AC6yFH;EACI;;AAGA;EACI;;AAIJ;EACI,qBDtvFQ;;ACyvFhB;EACI;EACA;EACA;;AAGJ;EACI;EACA,YDnwFW;;ACqwFf;EACI;EACA,YDvwFW;;ACwwFX;EACI,ODx0FL;;AC40FC;EACI;;AAEJ;EACI;;AAGR;EACI,ODp1FD;ECq1FC;EACA,YDj3FG;ECk3FH;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAIA;EACI;EACA,kBDl0FO;ECm0FP,cDj0FQ;;ACq0FZ;EACI,OD92FH;;ACg3FD;EACI,ODr6FD;;ACw6FP;EACI,OD94FD;;ACg5FH;EACI;;AAGA;EACI;;AAEJ;EACI;;AAGR;AAAA;EAEI,ODp4FC;;ACu4FD;EACI;EACA;;AAEJ;EACI;;AAGR;EACI,cDx2FY;;AC22FZ;EACI,OD76FL;EC86FK,qBD72FQ;;AC+2FZ;EACI,cDh3FQ;ECi3FR;;AAIR;EACI;;AAEJ;EACI,kBD33FW;;AC83Ff;EACI;;AAGA;EAEI,YDp4FO;;ACu4Ff;EACI,cDv8FD;ECw8FC;;AAEJ;EACI;;AAEJ;EACI;;AAGA;EACI;;AAEJ;EACI;;AAIR;EACI,kBDz5FY;;AC45FZ;EACI,YD/5FO;ECg6FP;;AACA;EACI;EACA;EACA;EACA;;AAGR;EACI;;AACA;EACI,OD1+FT;;AC6+FC;EACI;EACA;;AAIJ;AAAA;EAEI,YDhhGD;;ACohGH;AAAA;EAEI,YD1gGD;;AC8gGH;AAAA;EAEI,YD5gGH;;ACghGD;AAAA;EAEI,YDngGL;;ACugGC;AAAA;EAEI;;AAIJ;AAAA;EAEI;;AAIJ;AAAA;EAEI,YD19FO;;AC89FX;AAAA;EAEI;;AAGR;EACI;IACI;IACA;;;AAIR;EACI;EACA;;AACA;EACI,cD5+FQ;EC6+FR,YD9+FM;EC++FN,OD/iGL;;ACmjGC;EACI,OD3hGH;;AC6hGD;EACI,OD9hGH;;AC+hGG;EACI,ODhiGP;;ACmiGD;EACI,cD5/FQ;;AC6/FR;EACI;;AAIZ;EACI;;AAEJ;EACI;EACA,kBDvgGY;;ACygGhB;EACI,YD5gGW;EC6gGX,qBD3gGY;;AC6gGhB;AAAA;AAAA;EAGI;;AAEJ;EACI,kBD/mGG;ECgnGH;;AAEJ;EACI;IACI,qBDxhGQ;ICyhGR;;EAEJ;IACI;IACA;;;AAKA;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA,OD5mGT;;ACgnGH;EACI;IACI,ODlnGL;;EConGC;IACI;IACA,ODrnGL;;ECwnGK;IACI,ODjmGP;;ECmmGG;IACI,OD7nGT;;EC+nGK;IACI;;;AAIZ;EAEQ;IAEI,YDxqGD;;EC4qGH;IAEI,YD9qGD;;;ACkrGX;EACI;IACI,kBDrlGO;;;ACwlGf;EACI;;AAGA;EACI;;AAEJ;EACI;;AAGR;EACI,ODnqGD;;ACqqGH;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGJ;EACI;IACI;;EAEJ;IACI,kBDpnGM;ICqnGN;;;AAIJ;EACI;EACA,cD1nGQ;;AC2nGR;EACI,qBD5nGI;;AC+nGZ;AAAA;AAAA;EAGI,ODnsGL;;ACqsGC;AAAA;EAEI,OD9qGH;;ACgrGD;EACI;EACA,cD1oGQ;;AC2oGR;EACI,qBD5oGI;;AC+oGZ;EACI,OD5uGD;;AC6uGC;EACI;;AAKR;AAAA;EAEI,OD1tGL;;AC4tGC;AAAA;EAEI,ODrsGH;;ACusGD;EACI;EACA,cDjqGQ;;ACkqGR;EACI,qBDnqGI;;ACsqGZ;EACI,OD/sGH;;ACgtGG;EACI;;AAKR;EACI,ODttGH;;ACwtGD;EACI;;AAGR;AAAA;AAAA;EAGI,kBD1rGW;;ACgsGf;EAptGJ;AAqtGQ;AACA;AACA;AAWA;AAWA;AAIA;;EAzBA;IACI;IACA;;EAEJ;IACI;;EACA;IACI;;EAIR;IACI,YDtvGH;;ECuvGG;IAEI,YDzvGP;;EC4vGD;IACI;;EAGJ;IACI;;;AAKR;EACI,YDjuGW;ECkuGX,qBDhuGY;;ACkuGhB;EACI,cDnuGY;;ACouGZ;EACI,OD/wGH;;ACkxGL;EACI;;AAEJ;AAAA;EAEI;;AAEJ;EACI;;AACA;EACI;EACA;;AAKR;EACI,kBD1vGU;EC2vGV,cD1vGY;;AC2vGZ;EACI,OD7zGL;;AC8zGK;AAAA;EAEI;EACA,ODxyGP;;AC2yGO;EAEI,OD10Gd;;AC80GU;EAEI,ODh1Gd;;ACm1GM;AAAA;EAEI,ODxzGP;;AC4zGG;EACI,OD7zGP;;AC+zGG;EACI,cDxxGI;ECyxGJ;;AACA;EACI,kBD7xGD;EC8xGC,OD71Gb;;AC+1GS;EACI,kBDjyGD;ECkyGC,ODx0GX;;ACy0GW;EACI,kBDpyGL;ECqyGK,ODp2GjB;;ACw2GK;EACI,kBD1yGG;EC2yGH,OD12GT;;AC62GC;EACI,OD92GL;;ACi3GH;EACI,YDl3GD;ECm3GC;;AAEJ;AAAA;AAAA;EAGI,YDxzGU;ECyzGV;;AAEJ;EACI;EACA;;AAGA;EACI;EACA;;AAEJ;EACI,OD92GH;;ACg3GD;EACI,kBDz0GO;EC00GP;EACA;;AAEJ;EACI,kBD90GO;EC+0GP;;AAEJ;EACI;EACA,YDn1GO;;ACs1GP;AAAA;EAEI;;AAGR;EACI;;AAEJ;EACI,OD95GL;;ACi6GH;EACI,YDn2GW;;ACq2Gf;EACI,cDp2GY;ECq2GZ,OD74GC;;ACg5GD;EACI,YD32GO;;AC62GX;EACI;;AAIR;EACI;;AAEJ;EACI;EACA,YDv3GW;;ACw3GX;EACI;;AAGR;EACI,cD33GY;;AC43GZ;EACI,kBD/3GO;;ACi4GX;EACI;EACA;;AAIJ;EACI;;AAGR;EACI,cD14GY;EC24GZ,YD74GW;;AC84GX;EACI;;AAEJ;EACI;EACA,YDn5GO;;ACq5GX;EACI;EACA;;AACA;EACI;;AAGR;EACI;;AAGR;AAAA;EAEI;;AAGJ;EACI;IACI;;;AAGR;EACI;IACI,YD36GM;;;AC86Gd;EACI;IACI,kBDj7GO;;;ACo7Gf;EACI,qBDn7GY;ECo7GZ,YDt7GW;;ACw7Gf;EACI,ODx/GD;;AC0/GH;EACI;EACA;;AAEJ;EACI,OD//GD;;ACggHC;EACI;;AAGR;EACI,ODrgHD;ECsgHC;EACA;EACA;EACA,oBDx8GY;;ACy8GZ;EACI;;AAGR;EACI;;AACA;EACI;EACA;EACA;;AAGR;AAAA;EAEI;;AAEJ;EACI,ODjgHC;;ACkgHD;EACI;;AACA;EAEI,OD3jHL;;AC8jHH;EAEI,ODhkHD;;ACmkHC;AAAA;EAEI;;AAIJ;AAAA;EAEI,ODhjHT;;ACqjHC;EAGI;;AAGR;EACI,ODvlHG;;AC4lHH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;;AAGR;AAAA;EAEI,OD/iHC;;ACsjHD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI,ODnlHL;;ACslHH;EACI,ODvlHD;;ACylHH;EACI;;AAEJ;EACI,OD7lHD;;AC+lHH;EACI;;AAEJ;EAEQ;IACI;IACA;;EAEJ;IACI;;EAEJ;IACI;;EAKA;IACI;IACA;;EAGR;AAAA;AAAA;IAGI,kBDzjHE;IC0jHF,cDzjHI;IC0jHJ;;;AAIZ;EACI,kBDjkHW;ECkkHX;EACA;;AAEJ;EACI;;AAGA;EACI;;AAEJ;EACI;;AAGR;EACI,qBD7nHC;EC8nHD,oBD9nHC;;ACgoHL;EACI,YDrlHW;;ACulHf;EACI;;AAGA;EACI;;AAEJ;EACI,YD/lHO;;ACimHX;AAAA;EAEI,cDjmHQ;;ACkmHR;AAAA;EACI,MD5mHJ;;ACgnHR;EACI;;AAEJ;AAAA;AAAA;EAGI,ODppHC;;ACspHL;AAAA;EAEI,OD1nHI;;AC+nHR;EACI;EACA;;AACA;EACI;;AACA;EACI;;AAIZ;AAAA;AAAA;AAAA;EAII,ODrsHD;;ACysHK;EACI,mBDzoHI;EC0oHJ,kBD1oHI;EC2oHJ,oBD3oHI;EC4oHJ,ODrpHJ;;ACupHA;AAAA;EAEI,ODjtHT;;ACmtHK;AAAA;EAEI,qBDppHI;;ACupHJ;AAAA;AAAA;AAAA;EAII,mBD3pHA;EC4pHA,kBD5pHA;EC6pHA,ODrsHX;;ACusHO;EACI,mBDhqHA;ECiqHA,kBDjqHA;;ACkqHA;EAEI;;AAEJ;EACI,oBDvqHJ;;ACyqHA;EACI,YD3qHN;;ACgrHV;EACI,cDhrHQ;ECirHR;;AAGA;AAAA;EAEI,ODvvHT;;AC4vHC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUI,cDrsHQ;;ACwsHhB;AAAA;EAEI;;AAGA;AAAA;AAAA;EAGI,YDhtHQ;;ACotHZ;AAAA;EAEI;;AAGR;EACI;EACA;;AAEJ;EACI;;AAGA;AAAA;AAAA;AAAA;EAII,YDvuHO;;AC0uHf;EACI,OD1yHD;;AC6yHH;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAGJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAGJ;EACI,qBDnzHY;;ACszHZ;EACI,cDvzHQ;;ACwzHR;EACI,kBDzzHI;;AC2zHR;EACI;;AACA;EACI,OD/3Hb;;ACi4HS;EACI;;AAGR;EAEI,kBDx0HG;ECy0HH,kBDv0HI;ECw0HJ,qBDx0HI;;AC00HR;EAEI;;AAEJ;EACI,kBDj1HG;ECk1HH,kBDh1HI;ECi1HJ,qBDj1HI;;ACk1HJ;EACI;;AAEJ;EACI,ODv5Hb;;ACy5HS;EACI,ODj4HX;;ACq4HD;EACI,OD/5HL;ECg6HK,kBD/6HD;ECg7HC;;AAEJ;EACI,OD34HH;;AC84HG;EACI,ODx6HT;;AC06HK;EACI,ODl5HP;;ACs5HL;EACI,qBD/2HY;;ACg3HZ;EACI,ODz5HH;;AC45HL;AAAA;EAEI,OD95HC;;ACi6HD;EACI;;AAEJ;EAEI,kBD93HQ;;ACi4HhB;EACI,kBDl4HY;ECm4HZ,kBDr4HW;;ACs4HX;EACI,OD76HH;;AC+6HD;EACI,cDx4HQ;;ACy4HR;EAEI;;AAIZ;EACI;;AAEJ;EACI,kBDp5HU;;ACs5Hd;EACI,kBDx5HW;ECy5HX,ODx9HD;;AC29HH;EACI,ODn8HC;;ACq8HL;EACI,cD95HY;;ACi6HZ;EAEI;;AAIJ;EACI;;AAEJ;EACI;;AAEJ;EACI,ODt9HH;;ACy9HL;EACI,YDp7HW;ECq7HX;;AAEJ;EACI,ODv/HD;;ACy/HH;EACI;EACA;EACA,cD37HY;;AC67HhB;AAAA;EAEI,ODv+HC;;ACy+HL;EACI;EACA,ODpgID;;ACugIH;EACI;EACA;EACA;EAqEA;EACA;EACA;EAqEA;EACA;EACA;EAqEA;EACA;EACA;EAqEA;EACA;EACA;;AA3RA;EACI;;AACA;EAEI;EACA;;AAGR;EACI;EACA;;AAII;EAEI,kBDvhIb;;AC0hIK;EACI,kBD3hIT;;AC6hIK;EACI,eD9hIT;;ACmiIS;EAEI,kBD1iIb;;AC6iIK;EACI,kBD9iIT;;ACgjIK;EACI,eDjjIT;;ACsjIS;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAKA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAMR;EACI;;AACA;EAEI;EACA;;AAGR;EACI;EACA;;AAII;EAEI,kBD9lIb;;ACimIK;EACI,kBDlmIT;;AComIK;EACI,eDrmIT;;AC0mIS;EAEI,kBDjnIb;;AConIK;EACI,kBDrnIT;;ACunIK;EACI,eDxnIT;;AC6nIS;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAKA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAMR;EACI;;AACA;EAEI;EACA;;AAGR;EACI;EACA;;AAII;EAEI,kBDrqIb;;ACwqIK;EACI,kBDzqIT;;AC2qIK;EACI,eD5qIT;;ACirIS;EAEI,kBDxrIb;;AC2rIK;EACI,kBD5rIT;;AC8rIK;EACI,eD/rIT;;ACosIS;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAKA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAMR;EACI;;AACA;EAEI;EACA;;AAGR;EACI;EACA;;AAII;EAEI,kBD5uIb;;AC+uIK;EACI,kBDhvIT;;ACkvIK;EACI,eDnvIT;;ACwvIS;EAEI,kBD/vIb;;ACkwIK;EACI,kBDnwIT;;ACqwIK;EACI,eDtwIT;;AC2wIS;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAKA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAMR;EACI;;AACA;EAEI;EACA;;AAGR;EACI;EACA;;AAII;EAEI,kBDnzIb;;ACszIK;EACI,kBDvzIT;;ACyzIK;EACI,eD1zIT;;AC+zIS;EAEI,kBDt0Ib;;ACy0IK;EACI,kBD10IT;;AC40IK;EACI,eD70IT;;ACk1IS;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAKA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAQZ;EACI,ODj3ID;;ACk3IC;EACI;EACA;EACA;;AAEJ;EACI;;AACA;EACI;;AAEJ;EACI;;AAIZ;EACI,ODl4ID;ECm4IC;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AAIR;EACI,cDp1IY;ECq1IZ,kBDv1IW;;AC01IH;EACI,ODr7IT;;ACs7IS;EAEI,ODx7Ib;;AC07IS;EAGI;;AAGR;EACI,OD74IX;;AC84IW;EAEI,ODh5If;;ACm5IO;EACI,ODx8IT;;ACy8IS;EAEI,OD38Ib;;AC68IS;EAGI,kBDl6If;ECm6Ie,ODt7IjB;;ACy7IS;EACI,ODr8IX;;ACs8IW;EAEI,ODx8If;;AC08IW;EAGI,kBD78If;EC88Ie,ODn8IjB;;ACu8IK;EACI,kBDv4II;ECw4IJ,qBDx4II;;AC04IA;EACI,OD5+IT;;AC8+IK;EACI;;AAKR;EACI,kBDh+IX;;ACk+IO;EACI,kBDn/IT;ECo/IS,ODz9Ib;;AC09Ia;EAEI,kBDv/Ib;ECw/Ia,OD79IjB;;ACg+IS;EACI,kBDj6IF;ECk6IE,ODz8IX;;AC08IW;EAEI,kBDr6IN;ECs6IM,OD78If;;ACm9IL;EACI;IACI,mBD76IQ;IC86IR,oBD96IQ;;;ACi7IhB;EACI;IACI,mBDn7IQ;ICo7IR,oBDp7IQ;;;AC27IR;EAGI;EACA;;AAIJ;EAGI,kBDliJL;ECmiJK,ODxgJT;;AC4gJK;EAGI;EACA,ODhhJT;;ACshJK;EAGI;EACA;EACA,kBD59IG;;ACg+IP;EAGI,cD7jJL;EC8jJK,OD9jJL;;ACkkJC;EAGI;EACA;;AAKZ;EACI;;AAEJ;EACI;;AAEJ;EACI,ODllJG;;AColJP;EACI;;AAEJ;EACI;;AAGA;EAEI;EACA;;AAGR;EACI,ODvkJD;;AC0kJH;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGJ;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGJ;EACI;EACA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;EACA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;EACA;;AAEJ;EACI,YDzpMF;EC0pME;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,YDjrMA;ECkrMA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;EACA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;EACA;;AAEJ;EACI;EACA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,YDxuMH;ECyuMG;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,YDzvMA;EC0vMA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,kBD5vMA;EC6vMA;;AAEJ;EACI,YDhwMA;ECiwMA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,kBDlxMD;ECmxMC;;AAEJ;EACI,YDtxMD;ECuxMC;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;EACA;;AAGA;EAEI;;AAIJ;EAEI;;AAIJ;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,YDp3MD;;ACu3MC;EAEI;;AAIJ;EAEI;;AAGR;EACI,YD94MA;;ACg5MJ;EACI,YD14MF;;AC64ME;EAEI;;AAIJ;EAEI;;AAGR;EACI,ODp5MD;ECq5MC;;AAEJ;EACI,YDz1MW;;AC21Mf;EACI;;AAEJ;EACI;;AAEJ;EACI,kBDl2MW;;ACo2Mf;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA,OD36MD;;AC86MH;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA,ODngNO;;ACsgNX;EACI;EACA,cDv6MY;;ACy6MhB;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGJ;AAAA;EAEI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAGJ;EACI;EACA,OD7nND;;AC+nNH;EACI;EACA,ODjoND;;ACmoNH;EACI,kBDroND;ECsoNC,ODroND;;ACuoNH;EACI;EACA,ODzoND;;AC2oNH;EACI;EACA,OD7oND;;AC+oNH;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAGJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAIA;EACI,kBDpyNH;;ACyyND;EACI,kBDvyNN;;AC4yNE;EACI,kBD/yNH;;ACozND;EACI,kBDpzNJ;;;ACyzNR;EACI,YD50NO;EC60NP,cDjvNgB;ECkvNhB;;;AAIA;EACI;;AAEJ;EACI;;;AAIR;EACI,ODtyNK;;;ACyyNT;EACI;;;AAGJ;EACI;;;AAGJ;EACI,kBD5wNc;EC6wNd;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EAII;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EAII;;;AAGJ;EAII;;;AAGJ;EAII;;;AAGJ;EACI;;;AAGJ;EAII;;;AAGJ;EAII;;;AAGJ;EAGI;;;AAGJ;EAII;;;AAGJ;EAGI;;;AAGJ;EAEQ;IACI;;;AAOR;EACI,cD33NY;;AC63NhB;AAAA;EAEI,cD/3NY;;ACi4NhB;EACI;EACA;EACA;;AAEJ;EACI,ODx8ND;;AC08NH;EACI,kBD54NW;;AC+4NX;EACI,cD94NQ;;ACg5NZ;EACI,YDl5NM;;ACq5Nd;EACI,YDt5NU;;ACw5Nd;EACI;;AAEJ;AAAA;EAEI,OD79ND;;AC+9NH;AAAA;EAEI,ODj+ND;;;ACu+NH;EACI,YDx6NU;;AC26NV;EACI,OD5+NL;;AC8+NC;EACI,ODt9NH;;ACy9NL;EACI,kBDn7NU;;;ACy7Nd;AAAA;AAAA;EAGI,kBD57NU;;AC87Nd;AAAA;AAAA;EAGI,YDj8NU;;ACm8Nd;AAAA;AAAA;EAGI,ODtgOD;;ACwgOH;AAAA;EAEI,OD1gOD;EC2gOC,YD38NU;;AC68Nd;EACI,ODr/NC;;ACu/NL;AAAA;EAEI,kBDl9NU;;ACo9Nd;EACI,YDhjOG;;ACkjOP;EACI,kBDnjOG;;ACqjOP;EACI,cDtjOG;;;AC4jOH;EACI,ODliOL;;ACmiOK;EACI,ODpiOT;;ACsiOK;EACI;;AAIJ;EACI,mBDthOP;;ACwhOG;EACI,kBDzhOP;;AC6hOL;EACI,ODpjOD;;;AC2jOC;EACI,cD3/NQ;;AC4/NR;EACI,qBD7/NI;EC8/NJ,kBD9/NI;;ACggOR;EACI;EACA,kBD9iOP;EC+iOO,mBD/iOP;;ACijOG;EACI,cDtgOI;;AC0gOR;EACI,cD3gOI;;AC6gOR;EAOI,mBDphOI;;AC8gOJ;EACI,cD/gOA;;ACihOJ;EACI,YDlhOA;;ACqhOJ;EACI;;AAIZ;EACI;;AAEJ;EACI,cD9hOQ;;AC+hOR;EACI;;;AAQZ;EACI,OD1mOD;EC2mOC,kBD5iOW;EC6iOX,cD3iOY;;AC4iOZ;EACI,kBD/iOO;;ACkjOf;EACI,ODzlOC;;AC2lOL;EACI;;AAYA;EAEI,kBDpkOO;ECqkOP,cDnkOQ;;ACskOhB;EACI,cDvkOY;ECwkOZ,kBD1kOW;;AC2kOX;EACI,kBD5kOO;EC6kOP,cD3kOQ;;AC8kOhB;EACI,cD/kOY;ECglOZ,YDjlOU;ECklOV,ODlpOD;;ACmpOC;EAEI,cDplOQ;ECqlOR,ODtpOL;;ACypOH;EACI,YD1lOU;EC2lOV;EACA,cD3lOY;;AC6lOhB;EACI;;AAEJ;EACI,YDlmOU;ECmmOV;EACA;;AAEJ;EACI;;AAGA;AAAA;AAAA;EAGI;;AAGR;AAAA;AAAA;AAAA;EAII;;AAGA;EAEI,kBDznOM;;AC4nOd;AAAA;EAEI,ODrqOC;;ACwqOD;EACI,cDjoOQ;ECkoOR;EACA,OD3qOH;;AC6qOD;EACI,cDtoOQ;ECuoOR,YDzoOO;EC0oOP;;AAIJ;EACI,YD9oOM;;ACgpOV;EACI,cDhpOQ;;ACipOR;EACI,qBDlpOI;;ACmpOJ;EACI;;AAMZ;EACI,YD7pOO;EC8pOP,ODpsOH;;ACssOD;EACI,kBDjqOO;ECkqOP,qBDhqOQ;;ACmqOR;EACI;EACA,kBDvqOG;;ACyqOP;EACI;;AAMJ;EAEI;;AAGR;EAEI;;AAIJ;EAEI;EACA;;AAGR;AAAA;EAEI,YDnsOW;;ACqsOf;AAAA;EAEI;EACA;;AAEJ;EACI,qBDzsOY;;AC2sOhB;EACI,qBD5sOY;;AC8sOhB;EACI,ODvvOC;ECwvOD;EACA,cDjtOY;;ACktOZ;EACI,kBDrtOO;ECstOP,cDptOQ;;ACutOhB;EAII;EACA,kBD7tOU;EC8tOV,cD7tOY;;ACwtOZ;EACI;;AAMR;EACI,kBDjuOU;ECkuOV,cDjuOY;;ACouOZ;EACI,kBDvuOO;;ACyuOX;EACI,kBD1uOO;;AC6uOf;EACI,YD9uOW;EC+uOX,cD7uOY;;AC+uOhB;EACI;;AAEJ;EAII,qBDtvOY;;ACmvOZ;EACI,YDtvOO;;AC0vOf;AAAA;EAEI,cD1vOY;;AC4vOhB;EACI,kBD7vOY;;AC+vOhB;EACI,cDhwOY;ECiwOZ,kBDnwOW;;ACqwOf;EACI,kBDtwOW;;ACwwOf;EACI,kBDzwOW;EC0wOX,cDxwOY;;AC0wOhB;EACI,qBD3wOY;;AC6wOhB;EACI,cD9wOY;EC+wOZ,kBDjxOW;;ACmxOf;EACI,ODn1OD;ECo1OC,YDrxOW;ECsxOX,cDpxOY;;ACsxOhB;EAKI,cD3xOY;;ACuxOZ;EACI,qBDxxOQ;ECyxOR;;AAGJ;EACI,OD91OL;EC+1OK,YDhyOO;;ACkyOX;AAAA;EAEI,YDpyOO;;ACuyOf;EACI,YDxyOW;ECyyOX,oBDvyOY;;ACyyOhB;EACI,kBD5yOW;EC6yOX,cD3yOY;EC4yOZ,OD72OD;;AC82OC;EAEI,cD/yOQ;ECgzOR,kBDlzOO;ECmzOP,oBDjzOQ;ECkzOR,ODn3OL;;ACs3OH;EACI,oBDtzOY;;ACwzOhB;EACI,kBD3zOW;EC4zOX,kBD1zOY;;AC4zOhB;EACI,cD7zOY;EC8zOZ,kBD/zOU;ECg0OV,oBD/zOY;;ACi0OhB;EACI,kBDp0OW;;ACs0Of;EACI,qBDr0OY;;;AC20OhB;EACI;EACA,qBD70OY;;AC+0OhB;EACI,kBDj1OU;;ACm1Od;EACI;EACA,cDp1OY;ECq1OZ,ODt5OD;;ACw5OH;EACI,kBDz1OU;;AC21Od;AAAA;EAEI,cD51OY;;AC81OhB;EACI,kBDj2OW;ECk2OX,cDh2OY;ECi2OZ,ODl6OD;;ACo6OH;EACI,kBDr2OU;ECs2OV,ODt6OD;ECu6OC,cDt2OY;;ACw2OhB;AAAA;EAEI,kBD32OU;EC42OV,ODn5OC;;;AC05OD;EACI;EACA;;AAEJ;EACI,qBDv3OQ;;ACy3OZ;EACI,OD37OL;;AC47OK;EACI,kBD93OG;;ACg4OP;EACI,kBDh4OE;ECi4OF,cDh4OI;;ACi4OJ;EACI,YDn4OF;ECo4OE,OD36OX;;ACi7OO;EAGI,cD54OA;EC64OA,YD/4OD;ECg5OC,OD/8Ob;;ACk9OK;EACI,cDl5OI;ECm5OJ,YDr5OG;ECs5OH,ODr9OT;;ACw9OC;EAII,qBD35OQ;;ACw5OR;EACI,YD35OG;;AC+5OX;AAAA;EAEI,oBD/5OQ;;;ACs6OhB;EAMI,cD56OY;;ACu6OZ;EACI,cDx6OQ;ECy6OR,YD16OM;EC26ON,OD3+OL;;AC++OH;EACI,cD/6OY;;ACk7OZ;AAAA;EAEI,cDp7OQ;ECq7OR,YDt7OM;ECu7ON,ODv/OL;;ACy/OC;AAAA;EAEI,oBD17OQ;;AC67OhB;EACI,kBD/7OU;;ACi8Od;EACI,cDj8OY;;ACm8OhB;EACI,cDp8OY;;ACs8OhB;EACI,ODxgPD;;AC0gPH;EACI,kBD38OU;EC48OV,cD38OY;EC48OZ;EACA,OD9gPD;EC+gPC;;AAEJ;EACI,QD3/OC;;AC6/OL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAcI,ODzgPC;;AC6gPG;EAGI,cDx+OI;ECy+OJ,YD3+OG;EC4+OH,OD3iPT;;AC+iPH;EACI,kBDj/OW;;;ACw/OX;EACI;EACA,cDx/OQ;;ACy/OR;EACI,kBD3/OE;;AC6/ON;EACI,YD9/OE;;ACggPN;EACI,YDxiPP;;AC0iPG;EACI,ODpkPT;;ACukPC;EACI;;AAEJ;EACI;;AAGR;EACI,cD9gPY;EC+gPZ;EACA;;AAEI;EACI,kBDnhPI;ECohPJ,qBDphPI;;ACqhPJ;EACI;;AAGR;EACI,kBDxkPP;ECykPO;;AAII;EAGI,kBD9nPb;;ACkoPS;EAGI,kBDvlPf;;AC6lPG;AAAA;EAEI,mBDjjPI;;ACsjPJ;EAGI;;AAIJ;EAGI,kBD9mPX;;;ACunPL;EACI;EACA;;;AAMJ;EACI;;AAEJ;EACI;;AAGA;EACI,MDjoPH;;ACmoPD;EACI;;AAEJ;AAAA;EAEI,MDjqPL;;ACoqPH;EACI,YDrmPU;ECsmPV;EACA,ODvqPD;;;AC2qPP;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAIA;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAGA;EACI;EACA;;;AAMR;EACI;;;AAGR;AAAA;AAAA;AAAA;EAII,ODruPG;;;ACuuPP;EACI,YDxqPc;;;AC0qPlB;AAAA;AAAA;AAAA;EAII,OD9uPG;;;ACgvPP;EACI;;;AAGA;EACI;;AAEJ;AAAA;EAEI,ODzvPD;;AC2vPH;EACI,OD5vPD;;AC+vPC;EACI;;;AAIZ;EACI;IACI;;;EAEJ;IACI;;;AAGR;EACI;;;AAEJ;EACI;IACI;;;AAGR;EACI;EACA,kBDttPc;;;ACwtPlB;EACI;EACA,kBD3tPe;;;AC6tPnB;EACI;;;AAGJ;EAEQ;IACI;;;EAIJ;IACI;;;EAIJ;IACI;;;EAIJ;IACI;;;EAIJ;IACI;;;AAMR;EACI;EACA,YD/vPU;;ACiwPd;EACI,YDnwPW;;;ACuwPf;EACI;;;AAGR;EAMI;EACA,ODj1PG;;;ACm1PP;EAKI,kBDzxPe;;;AC2xPnB;EACI,cD1xPgB;EC2xPhB,kBD7xPe;;;ACgyPf;EACI;;AAEJ;EACI;;AAEJ;EACI,mBDryPY;;ACuyPhB;EACI,ODz2PD;;AC42PC;EACI,OD72PL;;ACg3PH;EAII;;AAEJ;EACI,ODv3PD;;ACw3PC;EACI,ODz3PL;;AC43PH;EACI,OD73PD;;AC83PC;EACI,OD/3PL;;ACk4PH;EACI,ODn4PD;;ACo4PC;EACI,ODr4PL;;;AC64PK;EACI;;AAGR;EACI;;AAIJ;EACI;;;AAKR;EAMI;;;AAIJ;EACI,ODv6PD;ECw6PC;EACA;;AAGA;EACI,ODn5PH;;ACu5PD;EACI;EACA;EACA,OD53PA;;ACg4PJ;EACI;;;AAIZ;EACI;AAAA;AAAA;IAGI", "file": "transparent-style.css", "sourcesContent": ["\r\n$background: #f0f0f5;\r\n$default-color:#282f53;\r\n$border: #e9edf4;\r\n\r\n/*Color variables*/\r\n\r\n$primary-1:var(--primary-bg-color);\r\n$primary-01:var(--primary01);\r\n$primary-02:var(--primary02);\r\n$primary-03:var(--primary03);\r\n$primary-06:var(--primary06);\r\n$primary-09:var(--primary09);\r\n$primary-005:var(--primary005);\r\n$primary-hover:var(--primary-bg-hover);\r\n$primary-border:var(--primary-bg-border);\r\n$primary-transparent:var(--primary-transparentcolor);\r\n$darkprimary-transparent:var(--darkprimary-transparentcolor);\r\n$transparentprimary-transparent:var(--transparentprimary-transparentcolor);\r\n$secondary:#05c3fb;\r\n$pink:#fc5296;\r\n$teal:#1caf9f;\r\n$purple:#8927ec;\r\n$success:#09ad95;\r\n$warning:#f7b731;\r\n$danger:#e82646;\r\n$info:#1170e4;\r\n$orange:#fc7303;\r\n$red:#e73827;\r\n$lime:#7bd235;\r\n$dark:#343a40;\r\n$indigo:#6574cd;\r\n$cyan:#007ea7;\r\n$azure:#45aaf2;\r\n$white:#fff;\r\n$black:#000;\r\n$light:#f2f2f9;\r\n$gray:#5a6970;\r\n$green:#4ecc48;\r\n$blue:#3223f1;\r\n$yellow:#FBB034;\r\n\r\n/*Gradient variables*/\r\n\r\n$primary-gradient-1:linear-gradient(to bottom right, $primary-1 0%, #8e77fa 100%);\r\n$secondary-gradient:linear-gradient(to bottom right, #82cff2 0%, #28b7f9 100%);\r\n$warning-gradient:linear-gradient(to bottom right, #f66b4e 0%, #fbc434 100%);\r\n$info-gradient:linear-gradient(to bottom right, #1e63c3 0%, #00f2fe 100%);\r\n$danger-gradient:linear-gradient(to bottom right, #b51b35 0%, #fd4a68 100%);\r\n$success-gradient:linear-gradient(to bottom right, #1ea38f 0%, #5cf9e2 100%);\r\n\r\n/*white variables*/\r\n\r\n$white-1:rgba(255, 255, 255, 0.1);\r\n$white-2:rgba(255, 255, 255, 0.2);\r\n$white-3:rgba(255, 255, 255, 0.3);\r\n$white-4:rgba(255, 255, 255, 0.4);\r\n$white-5:rgba(255, 255, 255, 0.5);\r\n$white-6:rgba(255, 255, 255, 0.6);\r\n$white-7:rgba(255, 255, 255, 0.7);\r\n$white-8:rgba(255, 255, 255, 0.8);\r\n$white-9:rgba(255, 255, 255, 0.9);\r\n$white-05:rgba(255, 255, 255, 0.05);\r\n$white-08:rgba(255, 255, 255, 0.08);\r\n$white-75:rgba(255, 255, 255, 0.075);\r\n\r\n/*black variables*/\r\n\r\n$black-1:rgba(0, 0, 0, 0.1);\r\n$black-2:rgba(0, 0, 0, 0.2);\r\n$black-3:rgba(0, 0, 0, 0.3);\r\n$black-4:rgba(0, 0, 0, 0.4);\r\n$black-5:rgba(0, 0, 0, 0.5);\r\n$black-6:rgba(0, 0, 0, 0.6);\r\n$black-7:rgba(0, 0, 0, 0.7);\r\n$black-8:rgba(0, 0, 0, 0.8);\r\n$black-9:rgba(0, 0, 0, 0.9);\r\n$black-05:rgba(0, 0, 0, 0.05);\r\n\r\n/*shadow variables*/\r\n\r\n$shadow:0 5px 15px 5px rgba(80, 102, 224, 0.08);\r\n$dark-theme:#1e2448;\r\n$dark-theme2:#16192f;\r\n$dark-theme3:#181d3e;\r\n\r\n/*Dark Theme Variables*/\r\n\r\n$dark-body:#1a1a3c;\r\n$dark-theme-1:#2a2a4a;\r\n$text-color:#dedefd;\r\n$border-dark:rgba(255, 255, 255, 0.1);\r\n$dark-card-shadow:0 3px 9px 0 rgba(28, 28, 51, 0.15);\r\n\r\n/*Transparent variables*/\r\n\r\n$transparent-primary:$primary-1;\r\n$transparent-theme:rgba(0, 0, 0, 0.2);\r\n$transparent-body:var(--transparent-body);\r\n$transparent-border:rgba(255, 255, 255, 0.2);", "@import \"../scss/variables\";\nbody.transparent-mode {\n    color: $white;\n    background-color: $transparent-body;\n}\n\nbody.transparent-mode.bg-img1 {\n    background: url(../images/media/bg-img1.jpg);\n    background-blend-mode: overlay;\n    background-size: cover;\n    background-position: center;\n    background-repeat: no-repeat;\n    background-attachment: fixed;\n    content: \"\";\n    left: 0;\n    right: 0;\n    top: 0;\n    width: 100%;\n}\n\nbody.transparent-mode.bg-img2 {\n    background: url(../images/media/bg-img2.jpg);\n    background-blend-mode: overlay;\n    background-size: cover;\n    background-position: center;\n    background-repeat: no-repeat;\n    background-attachment: fixed;\n    content: \"\";\n    left: 0;\n    right: 0;\n    top: 0;\n    width: 100%;\n}\n\nbody.transparent-mode.bg-img3 {\n    background: url(../images/media/bg-img3.jpg);\n    background-blend-mode: overlay;\n    background-size: cover;\n    background-position: center;\n    background-repeat: no-repeat;\n    background-attachment: fixed;\n    content: \"\";\n    left: 0;\n    right: 0;\n    top: 0;\n    width: 100%;\n}\n\nbody.transparent-mode.bg-img4 {\n    background: url(../images/media/bg-img4.jpg);\n    background-blend-mode: overlay;\n    background-size: cover;\n    background-position: center;\n    background-repeat: no-repeat;\n    background-attachment: fixed;\n    content: \"\";\n    left: 0;\n    right: 0;\n    top: 0;\n    width: 100%;\n}\n.transparent-mode {\n    &.bg-img1,\n    &.bg-img2,\n    &.bg-img3,\n    &.bg-img4 {\n        .login-img::before {\n            background: none;\n        }\n    }\n}\n\nbody.transparent-mode *::-webkit-scrollbar-thumb,\nbody.transparent-mode *:hover::-webkit-scrollbar-thumb {\n    background: var(--transparent-body);\n}\n\n.transparent-mode {\n    table.dataTable > tbody > tr.child ul.dtr-details > li {\n        border-bottom: 1px solid $transparent-border;\n    }\n    .dtr-bs-modal .dtr-details {\n        border: 1px solid $transparent-border;\n    }\n    .dtr-bs-modal .dtr-details tr td:first-child {\n        border-right: 1px solid $transparent-border;\n    }\n    caption {\n        color: #505662;\n    }\n    hr {\n        border-top-color: rgba(255, 255, 255, 0.2);\n        background-color: transparent;\n    }\n    mark,\n    .mark {\n        background-color: #fcf8e3;\n    }\n    .list-unstyled li {\n        border-bottom-color: $transparent-border;\n    }\n    kbd {\n        color: $white;\n        background-color: $dark;\n    }\n    pre {\n        color: $white;\n    }\n    @media print {\n        pre,\n        blockquote {\n            border-color: #adb5bd;\n        }\n        .badge {\n            border-color: $black;\n        }\n        .table td,\n        .text-wrap table td,\n        .table th,\n        .text-wrap table th {\n            background-color: $transparent-theme !important;\n        }\n        .table-bordered th,\n        .text-wrap table th,\n        .table-bordered td,\n        .text-wrap table td {\n            border-color: $transparent-border !important;\n        }\n    }\n    body * {\n        &::-webkit-scrollbar-thumb,\n        &:hover::-webkit-scrollbar-thumb {\n            background: $transparent-theme;\n        }\n    }\n    code {\n        background: transparent;\n        border-color: transparent;\n        color: $white-7;\n    }\n    pre {\n        color: $white-5;\n        background-color: $transparent-theme;\n        text-shadow: 0 1px $transparent-theme;\n    }\n    .section-nav {\n        background-color: #f8f9fa;\n        border-color: $transparent-border;\n    }\n    /*------ Accordion -------*/\n    .accordionjs .acc_section {\n        border-color: $transparent-border;\n        .acc_head h3:before {\n            color: $white;\n        }\n        .acc-header {\n            background: $transparent-theme;\n        }\n        .acc_head {\n            background: $transparent-theme;\n        }\n        &.acc_active > .acc_head {\n            color: $white !important;\n            border-bottom-color: $transparent-border;\n        }\n    }\n    /* #accordion rotate icon option */\n    .accordion-item {\n        border-color: $transparent-border;\n        background: transparent;\n    }\n    .accordion-button {\n        color: $white;\n        &:focus {\n            border-color: $transparent-border;\n        }\n        &:not(.collapsed) {\n            color: $white;\n            background: $transparent-body;\n        }\n        &::after {\n            color: $white-7;\n        }\n    }\n    #accordion .panel-default {\n        > .panel-heading {\n            border: 0px solid $transparent-border;\n        }\n        .collapsing .panel-body {\n            border-top: 0px solid transparent;\n        }\n    }\n    /*----- Avatars -----*/\n    .avatar-status {\n        border: 2px solid $transparent-body;\n    }\n    .avatar-list-stacked .avatar {\n        box-shadow: 0 0 0 2px $transparent-theme;\n    }\n    /*-----Badges-----*/\n    .btn-custom {\n        background: $transparent-theme;\n        color: $white-7;\n    }\n    .blockquote-footer {\n        color: $white-7;\n    }\n    blockquote {\n        color: $white-7;\n        border-color: $transparent-border;\n    }\n    .blockquote-reverse {\n        border-color: $transparent-border;\n    }\n    /*------ Breadcrumb ------*/\n    .breadcrumb-item {\n        a {\n            color: $white-7;\n        }\n        + .breadcrumb-item::before {\n            color: $white-5;\n        }\n    }\n    .breadcrumb1 {\n        background-color: $transparent-body;\n    }\n    .breadcrumb-item1 {\n        a:hover {\n            color: $primary-1;\n        }\n        + .breadcrumb-item1::before {\n            color: $white-2;\n        }\n        &.active {\n            color: $white-7;\n        }\n    }\n    .btn-default {\n        color: $default-color;\n        background: #e9e9f1;\n        border-color: #e9e9f1;\n        &:hover {\n            color: $default-color;\n            background-color: #e3e3ef;\n            border-color: #e3e3ef;\n        }\n        &:focus,\n        &.focus {\n            box-shadow: 0 0 0 2px #e9e9f1;\n        }\n        &.disabled,\n        &:disabled {\n            color: $default-color;\n            background-color: #e9e9f1;\n            border-color: #e9e9f1;\n        }\n        &:not(:disabled):not(.disabled) {\n            &:active,\n            &.active {\n                color: $default-color;\n                background-color: #e9e9f1;\n                border-color: #e9e9f1;\n            }\n        }\n    }\n    .btn-light {\n        color: $white;\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n        &:hover {\n            color: $white;\n            background-color: $transparent-theme;\n            border-color: $transparent-border;\n        }\n        &:focus,\n        &.focus {\n            box-shadow: 0 0 0 2px rgba(248, 249, 250, 0.1);\n        }\n        &.disabled,\n        &:disabled {\n            color: $white;\n            background-color: $transparent-theme;\n            border-color: $transparent-border;\n            opacity: 0.7;\n        }\n        &:not(:disabled):not(.disabled) {\n            &:active,\n            &.active {\n                color: #495057;\n                background-color: $transparent-theme;\n                border-color: $transparent-border;\n            }\n        }\n    }\n    .btn-outline-default {\n        color: $white;\n        background: transparent;\n        border-color: $transparent-border;\n        &:hover {\n            color: $default-color;\n            background: #e9e9f1;\n        }\n    }\n    .btn-white {\n        color: $white;\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n        &:hover {\n            color: $white;\n            background-color: $transparent-theme;\n            border-color: $transparent-border;\n        }\n        &:focus,\n        &.focus {\n            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);\n        }\n        &.disabled,\n        &:disabled {\n            color: #495057;\n            background-color: $transparent-theme;\n            border-color: $white;\n        }\n        &:not(:disabled):not(.disabled) {\n            &:active,\n            &.active {\n                color: #495057;\n                background-color: #545478;\n                border-color: $transparent-border;\n            }\n        }\n    }\n    .show > .btn-white.dropdown-toggle {\n        color: #495057;\n        background-color: #545478;\n        border-color: $transparent-border;\n    }\n    .btn-check {\n        &:active + .btn-outline-primary,\n        &:checked + .btn-outline-primary {\n            background-color: $primary-1;\n            border-color: $primary-1;\n        }\n    }\n    .btn-outline-primary {\n        border-color: $white-6;\n        color: $white-6;\n        &.active,\n        &.dropdown-toggle.show,\n        &:active {\n            background-color: $primary-1;\n            border-color: $primary-1;\n        }\n    }\n    /*------ Card -------*/\n    .card {\n        background-color: $transparent-theme;\n        border: inherit !important;\n        // box-shadow: 0 5px 8px 0 rgba(0, 0, 0, 0.3);\n    }\n    .card-footer {\n        // background-color: rgba(0, 0, 0, 0.03);\n        border-top-color: $transparent-border;\n        color: #6e7687;\n    }\n    /*------ Default Card Values -------*/\n    .card .card {\n        border-color: $transparent-border;\n        background: transparent;\n        box-shadow: 0px 0px 10px $transparent-theme;\n    }\n    .card-body + .card-body {\n        border-top-color: $transparent-border;\n    }\n    .card-header {\n        border-bottom-color: $transparent-border;\n    }\n    .card-title {\n        small {\n            color: #9aa0ac;\n        }\n    }\n    .card-subtitle {\n        color: #9aa0ac;\n    }\n    .card-body + .card-table {\n        border-top-color: $transparent-border;\n    }\n    .card-body + .card-list-group {\n        border-top-color: $transparent-border;\n    }\n    .card-options {\n        color: #9aa0ac;\n        a:not(.btn) {\n            color: $white-7;\n            &:hover {\n                color: #6e7687;\n            }\n        }\n    }\n    /*Card maps*/\n    .card-map {\n        background: #e9ecef;\n    }\n    .card .box {\n        h2 {\n            color: #262626;\n            span {\n                color: $white;\n            }\n        }\n        p {\n            color: #262626;\n        }\n    }\n    /*------ Card -------*/\n    .card-footer {\n        // background-color: rgba(0, 0, 0, 0.03);\n        border-top-color: $transparent-border;\n        color: rgba(255, 255, 255, 0.5);\n    }\n    /*------ Default Card Values -------*/\n    .card-body + .card-body {\n        border-top-color: $transparent-border;\n    }\n    .card-title {\n        small {\n            color: #9aa0ac;\n        }\n    }\n    .card-subtitle {\n        color: #9aa0ac;\n    }\n    .card-body + .card-table {\n        border-top-color: $transparent-border;\n    }\n    .card-body + .card-list-group {\n        border-top-color: $transparent-border;\n    }\n    .card-options {\n        color: #9aa0ac;\n        a:not(.btn) {\n            color: $white-7;\n            &:hover {\n                color: #6e7687;\n            }\n        }\n    }\n    /*Card maps*/\n    .card-map {\n        background: #e9ecef;\n    }\n    .card .box {\n        h2 {\n            color: #262626;\n            span {\n                color: $white;\n            }\n        }\n        p {\n            color: #262626;\n        }\n    }\n    .cardheader-tabs .card-header {\n        border-bottom-color: $transparent-border;\n    }\n    /*------ Carousel -------*/\n    .carousel-control-prev,\n    .carousel-control-next {\n        color: $white;\n    }\n    .carousel-control-prev {\n        &:hover,\n        &:focus {\n            color: $white;\n        }\n    }\n    .carousel-control-next {\n        &:hover,\n        &:focus {\n            color: $white;\n        }\n    }\n    .carousel-indicators,\n    .carousel-indicators1,\n    .carousel-indicators2,\n    .carousel-indicators3,\n    .carousel-indicators4,\n    .carousel-indicators5 {\n        li {\n            background-color: rgba(255, 255, 255, 0.5);\n        }\n        .active {\n            background-color: $white;\n        }\n    }\n    .carousel-caption {\n        color: $white;\n    }\n    .carousel-item-background {\n        background: rgba(0, 0, 0, 0.5);\n    }\n    /*------ Carousel -------*/\n    .carousel-control-prev,\n    .carousel-control-next {\n        color: $white;\n    }\n    .carousel-control-prev {\n        &:hover,\n        &:focus {\n            color: $white;\n        }\n    }\n    .carousel-control-next {\n        &:hover,\n        &:focus {\n            color: $white;\n        }\n    }\n    .carousel-indicators,\n    .carousel-indicators1,\n    .carousel-indicators2,\n    .carousel-indicators3,\n    .carousel-indicators4,\n    .carousel-indicators5 {\n        li {\n            background-color: rgba(255, 255, 255, 0.5);\n        }\n        .active {\n            background-color: $white;\n        }\n    }\n    .carousel-caption {\n        color: $white;\n    }\n    .carousel-item-background {\n        background: rgba(0, 0, 0, 0.5);\n    }\n    /*------Drop Downs-------*/\n    .dropdown-menu {\n        color: $white-7;\n        background-color: $transparent-body;\n        border-color: $transparent-border;\n    }\n    .dropdown-divider {\n        border-top-color: $transparent-border;\n    }\n    .drop-heading {\n        color: $white;\n    }\n    .dropdown-item {\n        color: $white-7;\n    }\n    .dropdown-item {\n        &:hover,\n        &:focus,\n        &.active,\n        &:active {\n            background-color: $transparent-theme;\n        }\n        &.disabled,\n        &:disabled {\n            color: $white;\n        }\n    }\n    .dropdown-menu.show {\n        border-color: $transparent-border;\n        box-shadow: 0 10px 40px 0 rgba(34, 34, 61, 0.8);\n    }\n    .dropdown-header {\n        color: $white-7;\n    }\n    .dropdown-item-text {\n        color: $white;\n    }\n    /*-----List Of Dropdwons-----*/\n    btn.dropdown-toggle ~ .dropdown-menu,\n    ul.dropdown-menu li.dropdown ul.dropdown-menu {\n        background-color: #f4f4f4 !important;\n        background-color: white !important;\n        border: 0 solid #4285f4 !important;\n        box-shadow: 0px 0px 3px rgba(25, 25, 25, 0.3) !important;\n    }\n    .dropdown-menu {\n        background-color: $primary-1;\n        -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\n        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\n        > li > a {\n            color: $white;\n            &:hover,\n            &:focus {\n                color: $white-7;\n                background-color: $transparent-theme;\n            }\n        }\n        .divider {\n            background-color: $transparent-border;\n        }\n        .dropdown-plus-title {\n            color: $white !important;\n            border: 0 solid $transparent-border !important;\n            border-bottom: 1px solid $transparent-border !important;\n        }\n    }\n    .dropdown-menu-header {\n        border-bottom-color: $transparent-border;\n        label {\n            color: $white-7;\n        }\n    }\n    .dropdown-menu-arrow:before,\n    .dropdown-menu.header-search:before {\n        background: $primary-1;\n        border-top-color: $transparent-border;\n        border-left-color: $transparent-border;\n    }\n    .dropdown-menu {\n        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n    }\n    .dropdown-toggle .dropdown-label {\n        background-color: $transparent-theme;\n    }\n    /*------- Forms -------*/\n    .form-control {\n        color: $white;\n        background-color: transparent;\n        border-color: $transparent-border !important;\n        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n        &::-ms-expand {\n            background-color: transparent;\n        }\n        &:focus {\n            color: $white-7;\n            background-color: $transparent-theme;\n            border-color: $transparent-border;\n        }\n        &::placeholder {\n            color: $white-7 !important;\n        }\n        &::-webkit-input-placeholder,\n        &::-moz-placeholder,\n        &:-ms-input-placeholder,\n        &::-ms-input-placeholder {\n            color: $white !important;\n        }\n        &:disabled,\n        &[readonly] {\n            background-color: $transparent-body;\n        }\n        &::-webkit-file-upload-button {\n            color: $white;\n            background-color: $transparent-body;\n        }\n        &:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {\n            background-color: $transparent-theme;\n        }\n    }\n    .form-control-plaintext {\n        color: $white-7;\n    }\n    .form-check-input:disabled ~ .form-check-label {\n        color: #505662;\n    }\n    .was-validated .custom-control-input:valid:focus ~ .custom-control-label::before,\n    .custom-control-input.is-valid:focus ~ .custom-control-label::before {\n        box-shadow: 0 0 0 1px $transparent-theme, 0 0 0 2px rgba(9, 173, 149, 0.25);\n    }\n    .was-validated .form-control:invalid,\n    .form-control.is-invalid,\n    .was-validated .form-select:invalid,\n    .form-select.is-invalid {\n        border-color: #dc3545;\n        background-repeat: no-repeat;\n    }\n    .was-validated .form-control:invalid:focus,\n    .form-control.is-invalid:focus,\n    .was-validated .form-select:invalid:focus,\n    .form-select.is-invalid:focus {\n        border-color: #dc3545;\n    }\n    .was-validated .form-check-input:invalid ~ .form-check-label,\n    .form-check-input.is-invalid ~ .form-check-label,\n    .was-validated .custom-control-input:invalid ~ .custom-control-label,\n    .custom-control-input.is-invalid ~ .custom-control-label {\n        color: #dc3545;\n    }\n    .was-validated .custom-control-input:invalid ~ .custom-control-label::before,\n    .custom-control-input.is-invalid ~ .custom-control-label::before {\n        background-color: #ec8080;\n    }\n    .was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before,\n    .custom-control-input.is-invalid:checked ~ .custom-control-label::before {\n        background-color: #e23e3d;\n    }\n    .was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before,\n    .custom-control-input.is-invalid:focus ~ .custom-control-label::before {\n        box-shadow: 0 0 0 1px $transparent-theme, 0 0 0 2px rgba(232, 38, 70, 0.25);\n    }\n    .form-control.header-search {\n        background: $transparent-theme;\n        border-color: rgba(225, 225, 225, 0.1);\n        color: $white;\n        &::placeholder {\n            color: $white;\n        }\n        &:hover,\n        &:focus {\n            border-color: rgba(225, 225, 225, 0.1);\n        }\n    }\n    .form-required {\n        color: #dc3545;\n    }\n    .form-check-input:focus {\n        border-color: $primary-1;\n    }\n    .form-check-input {\n        background: $white-1;\n        border-color: $transparent-border;\n    }\n    .input-group-text {\n        color: $white;\n        background-color: $primary-1;\n        border-color: $transparent-border;\n    }\n    .input-indec .input-group-btn > .btn {\n        border-color: $transparent-border;\n    }\n    .input-group-text {\n        color: $white;\n        border-color: $transparent-border !important;\n    }\n    .input-group-text.bg-white {\n        background-color: transparent !important;\n    }\n    .input-indec .input-group-btn > .btn {\n        border-color: $transparent-border;\n    }\n    /*------ Modal -------*/\n    .modal-content {\n        background-color: $primary-1;\n        border-color: $transparent-border;\n        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);\n    }\n    .modal-backdrop {\n        background-color: $black;\n    }\n    .modal-header {\n        border-bottom-color: $transparent-border;\n    }\n    .modal-footer {\n        border-top-color: $transparent-border;\n    }\n    .modal.effect-just-me {\n        .modal-content {\n            background-color: #16192f;\n            border-color: #4d4e50;\n        }\n        .btn-close {\n            color: $white;\n        }\n        .modal-header {\n            background-color: transparent;\n            border-bottom-color: rgba(255, 255, 255, 0.1);\n            h6 {\n                color: $white;\n            }\n        }\n        .modal-body {\n            color: rgba(255, 255, 255, 0.8);\n            h6 {\n                color: $white;\n            }\n        }\n        .modal-footer {\n            background-color: transparent;\n            border-top-color: $transparent-border;\n        }\n    }\n    /*------ Navigation -------*/\n    .nav-tabs {\n        border-bottom-color: $transparent-border;\n    }\n    .nav-pills {\n        .nav-link.active,\n        .show > .nav-link {\n            color: $white;\n        }\n    }\n    .nav.nav-pills.nav-stacked.labels-info p {\n        color: #9d9f9e;\n    }\n    .nav.nav-pills .nav-item .nav-link-icon {\n        color: $white;\n    }\n    .nav1 {\n        background: $transparent-body;\n    }\n    .nav-item1 {\n        &:hover:not(.disabled),\n        &.active {\n            color: $white;\n        }\n    }\n    .nav-item1 .nav-link {\n        color: $white;\n        &.active {\n            color: $primary-1;\n        }\n        &.disabled {\n            color: $white-3;\n        }\n    }\n    .nav-tabs {\n        border-bottom-color: $transparent-border;\n        .nav-item1 {\n            &.nav-link {\n                border-color: transparent;\n            }\n            .nav-link {\n                color: inherit;\n                color: $white-7;\n                transition: 0.3s border-color;\n                &:hover:not(.disabled),\n                &.active {\n                    color: $white;\n                }\n            }\n        }\n        .nav-submenu .nav-item1 {\n            color: #9aa0ac;\n            &.active {\n                color: #467fcf;\n            }\n            &:hover {\n                color: #6e7687;\n                background: rgba(0, 0, 0, 0.024);\n            }\n        }\n    }\n    .nav-link {\n        color: $primary-1;\n        &.icon i {\n            // color: #f7f7f7 !important;\n            &::after {\n                background: rgba($primary-1, 0);\n            }\n        }\n    }\n    .app-header .header-right-icons .nav-link.icon {\n        color: $white;\n    }\n    .app-header .header-right-icons .nav-link.icon:hover {\n        color: $white !important;\n    }\n    .nav-tabs {\n        color: $black;\n        .nav-link {\n            color: inherit;\n            color: $white-7;\n            &:hover:not(.disabled),\n            &.active {\n                color: $white;\n            }\n            &.disabled {\n                color: #868e96;\n                background-color: transparent;\n                border-color: transparent;\n            }\n        }\n        .nav-submenu {\n            background: $transparent-theme;\n            border-color: $transparent-border;\n            border-top: none;\n            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n            .nav-item {\n                color: #9aa0ac;\n                &:hover {\n                    color: #6e7687;\n                    text-decoration: none;\n                    background: rgba(0, 0, 0, 0.024);\n                }\n            }\n        }\n    }\n    .page-link {\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n        color: $white-7;\n        &:hover {\n            background-color: $transparent-theme;\n        }\n    }\n    .page-item {\n        &.active .page-link {\n            color: $white;\n        }\n        &.disabled .page-link {\n            color: rgba(255, 255, 255, 0.3);\n            background-color: $transparent-theme;\n            border-color: $transparent-border;\n        }\n    }\n    .page-header {\n        border-color: transparent;\n    }\n    .panel-title-landing {\n        color: #1643a3 !important;\n    }\n    .panel-footer-landing {\n        border: solid 2px #1643a3 !important;\n        border-top: none !important;\n        background: #f7f7f7;\n    }\n    .panel-footer {\n        background-color: $transparent-theme;\n        border-top-color: $transparent-border;\n        border-left-color: $transparent-border;\n        border-right-color: $transparent-border;\n    }\n    .panel-group .panel,\n    .panel-group1 .panel {\n        border-color: $transparent-border;\n    }\n    .panel-default > .panel-heading {\n        background-color: $transparent-body;\n        border-color: $transparent-border;\n        + .panel-collapse > .panel-body {\n            border: 0px solid $transparent-border;\n        }\n    }\n    .panel1 {\n        border-color: $white;\n        &:last-child {\n            border-bottom: none;\n        }\n    }\n    .panel-body1 {\n        background: $transparent-theme;\n    }\n    .panel-group1 .panel-body {\n        border: 0px solid $transparent-border;\n    }\n    .panel-title1 a {\n        color: $white !important;\n    }\n    .panel-title a:hover {\n        color: $text-color !important;\n    }\n    /* #bs-collapse icon scale option */\n    .panel-title a.accordion-toggle {\n        &:before,\n        &.collapsed:before {\n            color: $white;\n        }\n    }\n    /*--------panel----------*/\n    .expanel {\n        background-color: transparent !important;\n        border-color: $transparent-border !important;\n        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.01) !important;\n    }\n    .expanel-default > .expanel-heading {\n        background-color: $transparent-theme !important;\n        border-color: $transparent-theme !important;\n    }\n    .expanel-heading {\n        border-bottom-color: $transparent-border;\n    }\n    .expanel-footer {\n        background-color: $transparent-theme !important;\n        border-top-color: $transparent-border !important;\n    }\n    .popover {\n        background-color: $transparent-body;\n        border-color: #dee3eb;\n        filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.1));\n    }\n    .bs-popover-top .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"top\"] .popover-arrow::before {\n        border-top-color: rgba(0, 0, 0, 0.25);\n    }\n    .bs-popover-top .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"top\"] .popover-arrow::after {\n        border-top-color: $transparent-body;\n    }\n    .bs-popover-end .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"right\"] .popover-arrow::before {\n        border-right-color: #dee3eb;\n    }\n    .bs-popover-end .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"right\"] .popover-arrow::after {\n        border-right-color: transparent;\n    }\n    .bs-popover-bottom .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::before {\n        border-bottom-color: #dee3eb;\n    }\n    .bs-popover-bottom .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::after {\n        border-bottom-color: transparent;\n    }\n    .bs-popover-bottom .popover-header::before,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-header::before {\n        border-bottom-color: transparent;\n    }\n    .bs-popover-start .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"left\"] .popover-arrow::before {\n        border-left-color: #dee3eb;\n    }\n    .bs-popover-start .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"left\"] .popover-arrow::after {\n        border-left-color: $transparent-theme;\n    }\n    .popover-header {\n        color: inherit;\n        background-color: $transparent-theme;\n        border-bottom-color: #ebebeb;\n    }\n    .popover-body {\n        color: #6e7687;\n    }\n    /*-----progress-----*/\n    .progress {\n        background-color: rgba(0, 0, 0, 0.1);\n    }\n    .progress-bar {\n        color: $white;\n    }\n    /*------ Tables -----*/\n    .table {\n        color: $white;\n        thead th {\n            border-bottom-color: $transparent-border;\n        }\n    }\n    .text-wrap table thead th {\n        border-bottom-color: $transparent-border;\n    }\n    .table tbody + tbody,\n    .text-wrap table tbody + tbody {\n        border-top: 2px solid $transparent-border;\n    }\n    .table .table,\n    .text-wrap table .table,\n    .table .text-wrap table {\n        background-color: $transparent-theme;\n    }\n    .text-wrap {\n        .table table,\n        table table {\n            background-color: $transparent-theme;\n        }\n    }\n    .table-bordered,\n    .text-wrap table,\n    .table-bordered th,\n    .text-wrap table th,\n    .table-bordered td,\n    .text-wrap table td {\n        border-color: $transparent-border;\n    }\n    .table-striped tbody tr:nth-of-type(odd) {\n        background-color: rgba(0, 0, 0, 0.02);\n    }\n    .table-hover tbody {\n        tr:hover,\n        th {\n            background-color: $transparent-body;\n            color: $white;\n        }\n    }\n    .table-active {\n        background-color: rgba(0, 0, 0, 0.04);\n        > {\n            th,\n            td {\n                background-color: rgba(0, 0, 0, 0.04);\n            }\n        }\n    }\n    .table-hover .table-active:hover {\n        background-color: rgba(0, 0, 0, 0.04);\n        > {\n            td,\n            th {\n                background-color: rgba(0, 0, 0, 0.04);\n            }\n        }\n    }\n    .table .thead-dark th,\n    .text-wrap table .thead-dark th {\n        color: $white;\n        background-color: $white;\n        border-color: #32383e;\n    }\n    .table .thead-light th,\n    .text-wrap table .thead-light th {\n        color: #495057;\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n    }\n    .table-inbox {\n        border-color: $transparent-border;\n        tr {\n            border-bottom-color: rgba(238, 238, 238, 0.7);\n            &:last-child {\n                border-bottom-color: $transparent-border;\n            }\n            td .fa-star {\n                &.inbox-started,\n                &:hover {\n                    color: #f78a09;\n                }\n            }\n            &.unread td {\n                background: $transparent-theme;\n            }\n        }\n    }\n    .table th,\n    .text-wrap table th {\n        color: $white;\n    }\n    .table .table-light th,\n    .transparent-mode .text-wrap table th {\n        color: $default-color;\n    }\n    .table-vcenter {\n        td,\n        th {\n            border-top-color: $transparent-border;\n        }\n    }\n    .table-secondary {\n        tbody + tbody,\n        td,\n        th,\n        thead th {\n            border-color: rgba(255, 255, 255, 0.2);\n        }\n    }\n    .table-primary {\n        tbody + tbody,\n        td,\n        th,\n        thead th {\n            border-color: rgba(255, 255, 255, 0.2);\n        }\n    }\n    .table-striped tbody tr {\n        &:nth-of-type(odd) {\n            color: $white;\n        }\n        &:nth-of-type(even) {\n            background-color: $transparent-theme;\n        }\n    }\n    .table-calendar-link {\n        background: #f8f9fa;\n        color: #495057;\n        &:before {\n            background: #467fcf;\n        }\n        &:hover {\n            color: $white;\n            background: #467fcf;\n            &:before {\n                background: $transparent-theme;\n            }\n        }\n    }\n    .table-header:hover,\n    .table-header-asc,\n    .table-header-desc {\n        color: #495057 !important;\n    }\n    .table {\n        > {\n            :not(:last-child) > :last-child > * {\n                border-bottom-color: $transparent-border;\n            }\n        }\n        tbody td {\n            border-color: $transparent-border;\n        }\n    }\n    .table-hover > tbody > tr:hover > * {\n        color: $text-color;\n    }\n    .table > :not(:first-child) {\n        border-top-color: $transparent-border;\n    }\n    .table-striped > tbody > tr:nth-of-type(odd) > * {\n        color: $text-color;\n    }\n    #data-table .text-primary span {\n        color: #b7b7d3;\n    }\n    /*---- Tags-----*/\n    .tag {\n        color: $white;\n        background-color: $transparent-body;\n    }\n    a.tag:hover {\n        background-color: rgba(110, 118, 135, 0.2);\n        color: inherit;\n    }\n    .tag-addon {\n        color: inherit;\n    }\n    a.tag-addon:hover {\n        background: inherit;\n        color: inherit;\n    }\n    .tag-blue {\n        background-color: #467fcf !important;\n        color: $white;\n    }\n    .tag-indigo {\n        background-color: $indigo !important;\n        color: $white;\n    }\n    .tag-purple {\n        background-color: #867efc !important;\n        color: $white;\n    }\n    .tag-pink {\n        background-color: #ec82ef !important;\n        color: $white;\n    }\n    .tag-red {\n        background-color: #c21a1a !important;\n        color: $white;\n    }\n    .tag-orange {\n        background-color: $orange !important;\n        color: $white;\n    }\n    .tag-yellow {\n        background-color: #ecb403 !important;\n        color: $white;\n    }\n    .tag-green {\n        background-color: $green !important;\n        color: $white;\n    }\n    .tag-teal {\n        background-color: #2bcbba !important;\n        color: $white;\n    }\n    .tag-cyan {\n        background-color: #17a2b8 !important;\n        color: $white;\n    }\n    .tag-white {\n        background-color: $transparent-theme;\n        color: $white;\n    }\n    .tag-gray {\n        background-color: #868e96 !important;\n        color: $white;\n    }\n    .tag-gray-dark {\n        background-color: #414160;\n        color: $white;\n    }\n    .tag-azure {\n        background-color: $azure !important;\n        color: $white;\n    }\n    .tag-lime {\n        background-color: $lime !important;\n        color: $white;\n    }\n    .tag-primary {\n        background-color: #467fcf;\n        color: $white;\n        background-color: $primary-1 !important;\n        color: $white;\n    }\n    .tag-secondary {\n        background-color: #868e96;\n        color: $white;\n    }\n    .tag-success {\n        background-color: $green !important;\n        color: $white;\n    }\n    .tag-info {\n        background-color: $azure;\n        color: $white;\n    }\n    .tag-warning {\n        background-color: #ecb403 !important;\n        color: $white;\n    }\n    .tag-danger {\n        background-color: #c21a1a !important;\n        color: $white;\n    }\n    .tag-light {\n        background-color: #f8f9fa;\n        color: $white;\n    }\n    .tag-dark {\n        background-color: #25253e;\n        color: $white;\n    }\n    .tag-round::before {\n        background-color: $white;\n    }\n    .tag-outline-info {\n        background-color: #c7e0fd;\n        color: $info;\n        border-color: $info;\n        &::before {\n            border-color: $info;\n        }\n    }\n    .tag-outline {\n        border-color: $transparent-border;\n    }\n    .tag-border {\n        border-color: $transparent-border;\n        background-color: transparent;\n    }\n    /*---------Thumbnails----------*/\n    .thumbnail {\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n    }\n    .bs-popover-top .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"top\"] .popover-arrow::after {\n        border-top-color: $transparent-body;\n    }\n    .bs-popover-end .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"right\"] .popover-arrow::after {\n        border-right-color: $transparent-body;\n    }\n    .bs-popover-bottom .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::after {\n        border-bottom-color: transparent;\n    }\n    .bs-popover-start .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"left\"] .popover-arrow::after {\n        border-left-color: $transparent-body;\n    }\n    .tooltip-static-demo {\n        background-color: $transparent-body;\n    }\n    .popover-static-demo {\n        background-color: $transparent-body;\n        border-color: $transparent-border;\n    }\n    .tooltip-primary {\n        .tooltip-inner {\n            background-color: $primary-1 !important;\n            color: $white;\n        }\n        &.bs-tooltip-top .tooltip-arrow::before,\n        &.bs-tooltip-auto[data-popper-placement^=\"top\"] .tooltip-arrow::before {\n            border-top-color: $primary-1;\n        }\n        &.bs-tooltip-bottom .tooltip-arrow::before,\n        &.bs-tooltip-auto[data-popper-placement^=\"bottom\"] .tooltip-arrow::before {\n            border-bottom-color: $primary-1;\n        }\n        &.bs-tooltip-start .tooltip-arrow::before,\n        &.bs-tooltip-auto[data-popper-placement^=\"left\"] .tooltip-arrow::before {\n            border-left-color: $primary-1;\n        }\n        &.bs-tooltip-end .tooltip-arrow::before,\n        &.bs-tooltip-auto[data-popper-placement^=\"right\"] .tooltip-arrow::before {\n            border-right-color: $primary-1;\n        }\n    }\n    .popover {\n        background-color: $transparent-body;\n        border: 0px solid $transparent-theme;\n    }\n    .bs-popover-top > .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"top\"] > .popover-arrow::before {\n        border-top-color: $transparent-body;\n    }\n    .bs-popover-top > .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"top\"] > .popover-arrow::after {\n        border-top-color: $transparent-body;\n    }\n    .bs-popover-end > .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"right\"] > .popover-arrow::before {\n        border-right-color: $transparent-body;\n    }\n    .bs-popover-end > .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"right\"] > .popover-arrow::after {\n        border-right-color: $transparent-body;\n    }\n    .bs-popover-bottom > .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] > .popover-arrow::before {\n        border-bottom-color: transparent;\n    }\n    .bs-popover-bottom > .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] > .popover-arrow::after {\n        border-bottom-color: $transparent-theme;\n    }\n    .bs-popover-bottom .popover-header::before,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-header::before {\n        border-bottom-color: $transparent-border;\n    }\n    .bs-popover-start > .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"left\"] > .popover-arrow::before {\n        border-width: 0.5rem 0 0.5rem 0.5rem;\n        border-left-color: $transparent-body;\n    }\n    .bs-popover-start > .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"left\"] > .popover-arrow::after {\n        border-width: 0.5rem 0 0.5rem 0.5rem;\n        border-left-color: $transparent-body;\n    }\n    .popover-header {\n        color: $white;\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n    }\n    .popover-body {\n        color: $white-5;\n    }\n    .popover-head-primary {\n        .popover-header {\n            color: $white;\n            background-color: $primary-1 !important;\n        }\n        &.bs-popover-bottom .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::after {\n            border-bottom-color: $primary-1 !important;\n        }\n    }\n    .popover-head-secondary {\n        .popover-header {\n            color: $white;\n            background-color: $secondary !important;\n        }\n        &.bs-popover-bottom .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::after {\n            border-bottom-color: $secondary !important;\n        }\n    }\n    .popover-head-primary .popover-body,\n    .popover-head-secondary .popover-body {\n        border-color: rgba(20, 17, 45, 0.2);\n    }\n    .popover-primary {\n        background-color: $primary-1 !important;\n        .popover-header {\n            background-color: $primary-1 !important;\n            border-bottom-width: 0 !important;\n            color: $white;\n        }\n        &.bs-popover-top .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"top\"] .popover-arrow::after {\n            border-top-color: $primary-1 !important;\n        }\n        &.bs-popover-bottom .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::after {\n            border-bottom-color: $primary-1 !important;\n        }\n        &.bs-popover-start .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"left\"] .popover-arrow::after {\n            border-left-color: $primary-1 !important;\n        }\n        &.bs-popover-end .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"right\"] .popover-arrow::after {\n            border-right-color: $primary-1 !important;\n        }\n    }\n    /*  ######## CUSTOM-STYLES ######## */\n    /*----- Custom control -----*/\n    .custom-control-input {\n        &:checked ~ .custom-control-label::before {\n            color: $white;\n        }\n        &:active ~ .custom-control-label::before {\n            color: $white;\n            background-color: rgba(218, 201, 232, 0.5);\n        }\n        &:disabled ~ .custom-control-label {\n            color: $white-7;\n            &::before {\n                background-color: $transparent-border;\n            }\n        }\n    }\n    .custom-control-label::before {\n        background-color: $transparent-border;\n    }\n    .custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {\n        background-color: rgba(212, 182, 228, 0.5);\n    }\n    .form-select {\n        color: $white-7;\n        border-color: $transparent-border;\n        background: transparent;\n        &:focus {\n            border-color: $transparent-border;\n            box-shadow: none;\n            &::-ms-value {\n                color: $white-7;\n                background-color: $transparent-theme;\n            }\n        }\n        &:disabled {\n            color: #9fa7af;\n            background-color: $transparent-theme;\n        }\n    }\n    .form-file-label {\n        color: $white-7;\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n        &::after {\n            color: $white;\n            border-left-color: $transparent-border;\n        }\n    }\n    .form-range {\n        &::-webkit-slider-thumb {\n            background: $transparent-theme;\n            box-shadow: none;\n            &:focus {\n                box-shadow: 0 0 0 1px $transparent-theme, 0 0 0 2px rgba(98, 58, 162, 0.25);\n            }\n            &:active {\n                background-color: #d4e1f4;\n            }\n        }\n        &::-webkit-slider-runnable-track {\n            background-color: $transparent-border;\n            background: #467fcf;\n        }\n        &::-moz-range-thumb {\n            background: $transparent-theme;\n            &:focus {\n                box-shadow: 0 0 0 1px $transparent-theme, 0 0 0 2px rgba(98, 58, 162, 0.25);\n            }\n            &:active {\n                background-color: #d4e1f4;\n            }\n        }\n        &::-moz-range-track {\n            background-color: $transparent-border;\n            background: rgba(0, 50, 126, 0.12);\n        }\n        &::-ms-thumb {\n            background: $transparent-theme;\n            border-color: rgba(0, 30, 75, 0.12);\n            &:focus {\n                box-shadow: 0 0 0 1px $transparent-theme, 0 0 0 2px rgba(98, 58, 162, 0.25);\n            }\n            &:active {\n                background-color: #d4e1f4;\n            }\n        }\n        &::-ms-fill-lower {\n            background-color: $transparent-border;\n            background: #467fcf;\n        }\n    }\n    .custom-control-label:before {\n        border-color: $transparent-border;\n        background-color: rgba(255, 255, 255, 0.02);\n    }\n    .form-range {\n        &:focus {\n            &::-webkit-slider-thumb,\n            &::-moz-range-thumb,\n            &::-ms-thumb {\n                border-color: #467fcf;\n                background-color: #467fcf;\n            }\n        }\n        &::-moz-range-progress {\n            background: #467fcf;\n        }\n        &::-ms-fill-upper {\n            background: rgba(0, 50, 126, 0.12);\n            background-color: $transparent-border;\n        }\n    }\n    .custom-switch-description {\n        color: $white-7;\n    }\n    .custom-switch-input:checked ~ .custom-switch-description {\n        color: $white-7;\n    }\n    .custom-switch-indicator {\n        background: $white-5;\n        border-color: $transparent-border;\n        &:before {\n            background: $white;\n            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.4);\n        }\n    }\n    .custom-switch-input:focus ~ .custom-switch-indicator {\n        border-color: #60529f;\n    }\n    .custom-radio-md .custom-control-label::before,\n    .custom-radio-lg .custom-control-label::before {\n        background-color: $transparent-border;\n    }\n    .custom-checkbox-md,\n    .custom-checkbox-lg {\n        .custom-control-label::before {\n            background-color: $transparent-border;\n        }\n        .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {\n            background-color: rgba(212, 182, 228, 0.5);\n        }\n    }\n    .custom-switch-input:disabled ~ .custom-switch-indicator {\n        background: #e9ecef;\n    }\n    .custom-switch-input:checked ~ .custom-switch-indicator {\n        background: $primary-1;\n    }\n    .custom-switch-indicator-md,\n    .custom-switch-indicator-lg {\n        background: $white-5;\n        border-color: $transparent-border;\n        &::before {\n            background: $white;\n            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.4);\n        }\n    }\n    .collapse:not(.show) {\n        background: $transparent-theme;\n    }\n    .notifyimg {\n        color: $white;\n    }\n    /*----- Global Loader -----*/\n    .aside {\n        background: $transparent-theme;\n        border-left-color: $transparent-border;\n        box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.05);\n    }\n    @media (max-width: 992px) {\n        .about-con {\n            border-bottom-color: $transparent-border;\n        }\n    }\n    @media (max-width: 480px) {\n        .tabs-menu ul li a,\n        .tabs-menu1 ul li {\n            border-color: $transparent-border;\n        }\n    }\n    @media (max-width: 320px) {\n        .construction .btn.btn-icon {\n            color: $white;\n        }\n    }\n    @media (max-width: 360px) {\n        .breadcrumb {\n            color: $white;\n        }\n    }\n    @media (max-width: 768px) {\n        .richText .richText-toolbar ul li a {\n            border: rgba(0, 40, 100, 0.12) solid 1px;\n        }\n        .richText .richText-toolbar ul li {\n            border-bottom: $transparent-border solid 1px;\n        }\n        .richText .richText-toolbar {\n            border-bottom: 0 !important;\n        }\n    }\n    .stamp {\n        color: $white;\n        background: #868e96;\n    }\n    .example {\n        border-color: $transparent-border;\n    }\n    .example-bg {\n        background: transparent;\n    }\n    .colorinput-color {\n        border: 3px solid $transparent-border;\n        color: $white;\n        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n    }\n    .colorinput-input:focus ~ .colorinput-color {\n        border-color: $transparent-border;\n        box-shadow: 0 0 0 2px rgba(98, 58, 162, 0.25);\n    }\n    #back-to-top {\n        color: $white;\n        &:hover {\n            background: $white !important;\n        }\n    }\n    .features span {\n        color: #43414e;\n    }\n    .feature .border {\n        color: $white;\n    }\n    .actions:not(.a-alt) > li > a > i {\n        color: #939393;\n    }\n    /* --------Added--------- css*/\n    #sidebar li a.active {\n        background: $transparent-theme;\n        a[data-toggle=\"collapse\"] {\n            background: $transparent-theme;\n        }\n    }\n    /* line 77, C:/wamp/www/github/addSlider/src/partials/_addSlider.scss */\n    .line-divide {\n        border-color: rgba(218, 216, 219, 0.2);\n    }\n    .activity {\n        border-left-color: rgba(0, 0, 0, 0.125);\n    }\n    .username {\n        color: $white;\n        + p {\n            color: #f2f2f2;\n        }\n    }\n    #user-profile {\n        .profile-details ul > li > span {\n            color: #643ba2;\n        }\n    }\n    @media (max-width: 1024px) {\n        body {\n            &.search-show:before,\n            &.sidebar-show:before {\n                background-color: rgba(0, 0, 0, 0.4);\n            }\n        }\n    }\n    @media (max-width: 575.98px) {\n        .header .form-inline .search-element .form-control {\n            background: $transparent-theme;\n        }\n        .form-control.header-search {\n            color: $black;\n        }\n        .header {\n            .form-inline {\n                .btn {\n                    color: #46494a !important;\n                }\n                .form-control::-webkit-input-placeholder {\n                    color: $white !important;\n                }\n            }\n            .navsearch i {\n                color: $white;\n            }\n        }\n    }\n    .settings {\n        color: $white;\n    }\n    .member {\n        background: $transparent-theme;\n        border-color: $transparent-border;\n    }\n    @media screen and (max-width: 998px) and (min-width: 768px) {\n        .note-popover .popover-content,\n        .card-header.note-toolbar {\n            background: $transparent-theme;\n        }\n    }\n    .material-switch > {\n        label {\n            &::before {\n                background: $transparent-theme;\n                box-shadow: inset 0px 0px 10px rgba(42, 38, 53, 0.5);\n            }\n            &::after {\n                background: $transparent-body;\n                box-shadow: 0px 0px 5px rgba(42, 38, 53, 0.9);\n            }\n        }\n        input[type=\"checkbox\"]:checked + label {\n            &::before,\n            &::after {\n                background: inherit;\n            }\n        }\n    }\n    .sw-theme-default > ul.step-anchor > li > a::after {\n        background: none !important;\n    }\n    .border-transparet {\n        border-color: rgba(255, 255, 255, 0.1);\n    }\n    .breadcrumb .breadcrumb-item.active,\n    .breadcrumb-item1 a {\n        color: $white-4;\n    }\n    .input-group.input-indec .form-control {\n        border-color: $transparent-border;\n    }\n    /*********************IE***************************/\n    @media (min-width: 992px) {\n        .main-header-center .form-control {\n            background: transparent;\n            border-color: rgba(255, 255, 255, 0.2);\n        }\n    }\n    @media (min-width: 992px) {\n        .main-header-center .btn {\n            background-color: transparent;\n            color: #b4bdce;\n        }\n    }\n    .pulse-danger {\n        background: #ee335e;\n        &:before {\n            background: rgba(238, 51, 94, 0.8);\n            box-shadow: 0 0 0 rgba(238, 51, 94, 0.9);\n        }\n    }\n    .pulse {\n        background: #22c03c;\n        &:before {\n            background: rgba(34, 192, 60, 0.6);\n            box-shadow: 0 0 0 rgba(34, 192, 60, 0.9);\n        }\n    }\n    .progress {\n        &.info1 {\n            background: #fed5db;\n        }\n        &.secondary {\n            background: #f9d1bd;\n        }\n    }\n    .avatar {\n        color: $white;\n    }\n    .badge-success-light {\n        background-color: rgba(19, 191, 27, 0.15) !important;\n        color: #13bf1b;\n    }\n    .badge-orange-light {\n        color: #e17626;\n        background-color: rgba(225, 118, 38, 0.15);\n    }\n    .badge-danger-light {\n        color: #f33819;\n        background-color: rgba(243, 56, 25, 0.15);\n    }\n    .badge.bg-white {\n        background-color: $white !important;\n    }\n    .browser-stats img {\n        background: $transparent-theme;\n    }\n    .box-shadow-primary {\n        box-shadow: 0 5px 10px $transparent-theme;\n    }\n    .box-shadow-secondary {\n        box-shadow: 0 5px 10px $transparent-theme;\n    }\n    .box-shadow-success {\n        box-shadow: 0 5px 10px $transparent-theme;\n    }\n    .box-shadow-danger {\n        box-shadow: 0 5px 10px $transparent-theme;\n    }\n    .box-shadow-pink {\n        box-shadow: 0 5px 10px $transparent-theme;\n    }\n    .sidebar {\n        .tab-menu-heading {\n            background: $transparent-theme;\n            color: $white;\n        }\n        .tabs-menu ul li .active {\n            color: $white;\n        }\n        .feeds.avatar-circle.bg-primary-transparent::before {\n            background: $white-3;\n        }\n        .feeds.avatar-circle-info::before {\n            background: #0151b3;\n        }\n        .feeds.avatar-circle-info {\n            background: rgba(142, 193, 255, 0.1) !important;\n            .text-info {\n                color: #0b54af;\n            }\n        }\n    }\n    .sidebar-right .dropdown-item h6 {\n        color: $default-color;\n    }\n    .sidebar-right .panel-primary.card {\n        background: $primary-1;\n    }\n    .sidebar-right .feeds.avatar-circle .text-primary {\n        color: $white-5 !important;\n    }\n    .sidebar-right .feeds.avatar-circle.bg-primary-transparent {\n        background-color: $white-05;\n    }\n    .nav.panel-tabs {\n        a.active {\n            color: $white;\n        }\n        &.panel-secondary a {\n            &.active {\n                background-color: $secondary !important;\n                color: $white;\n            }\n            color: $secondary;\n        }\n        &.panel-danger a {\n            &.active {\n                background-color: $danger !important;\n                color: $white;\n            }\n            color: $danger;\n        }\n        &.panel-success a {\n            color: $success;\n            &.active {\n                background-color: $success !important;\n                color: $white;\n            }\n        }\n        &.panel-info a {\n            color: #538ed7;\n            &.active {\n                background-color: $info !important;\n                color: $white;\n            }\n        }\n    }\n    .task-list {\n        color: $white;\n        &:before {\n            border-left-color: $white-2;\n        }\n    }\n    .mail-inbox .icons {\n        color: $white !important;\n    }\n    .table-inbox tr td {\n        i {\n            color: $white-2;\n            &:hover {\n                color: #f7284a;\n            }\n        }\n        .fa-star:hover {\n            color: #fbc518;\n        }\n    }\n    .mail-option {\n        .btn-group a.btn,\n        .chk-all {\n            border-color: $transparent-border;\n            color: $white;\n        }\n        .btn-group a.all {\n            box-shadow: none;\n        }\n    }\n    .inbox-pagination a.np-btn {\n        border-color: $transparent-border;\n        color: $white-7;\n    }\n    .acc-header a {\n        &.collapsed {\n            border-color: $transparent-border;\n        }\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n    }\n    .acc-body {\n        border-color: $transparent-border;\n    }\n    .card-pay .tabs-menu li a {\n        &.active {\n            background: $primary-1;\n            color: $white;\n        }\n        border-color: $transparent-border;\n        color: $white;\n    }\n    .main-content-label,\n    .card-table-two .card-title,\n    .card-dashboard-eight .card-title {\n        color: $white;\n    }\n    .social-login {\n        background: $transparent-theme;\n        border-color: $transparent-border;\n    }\n    .user-social-detail .social-profile {\n        background: $transparent-theme;\n    }\n    .txt1 {\n        color: $white-7;\n    }\n    .hor-header.sticky.stickyClass .horizontal-main.hor-menu {\n        box-shadow: 5px 7px 26px -5px rgba(104, 113, 123, 0.1);\n    }\n    .mini-stat-icon {\n        color: $white;\n    }\n    .product-grid6 {\n        overflow: hidden;\n        .price span {\n            color: $white-7;\n        }\n        .icons-wishlist li a {\n            i {\n                color: inherit;\n            }\n            &:hover,\n            &:after,\n            &:before {\n                color: $white;\n            }\n        }\n        .icons li a {\n            i {\n                color: inherit;\n            }\n            &:hover,\n            &:after,\n            &:before {\n                color: $white;\n            }\n        }\n    }\n    .apexcharts-radialbar-track.apexcharts-track path {\n        stroke: $transparent-theme;\n    }\n    .apex-charts text {\n        fill: #000200;\n    }\n    /*--- Offcanvas ---*/\n    .offcanvas {\n        background: $transparent-body;\n    }\n    .offcanvas-start {\n        border-right-color: $transparent-border;\n    }\n    .offcanvas-end {\n        border-left-color: $transparent-border;\n    }\n    .offcanvas-bottom {\n        border-top-color: $transparent-border;\n    }\n    .offcanvas-top {\n        border-bottom-color: $transparent-border;\n    }\n    /*--Toast ---*/\n    .toast {\n        background: $transparent-theme;\n        border-color: $transparent-border;\n        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5);\n        .toast-header {\n            background: $transparent-theme;\n            color: $white;\n            border-bottom-color: $transparent-border;\n        }\n    }\n    .toast.show {\n        .btn-close {\n            color: #fff;\n        }\n    }\n    .task-icon1:first-child {\n        border: 2px solid $white-5;\n    }\n    .nav.product-sale a.active {\n        background-color: $transparent-theme;\n        border-bottom: none;\n    }\n    .profile-cover__img {\n        color: $white;\n        .profile-img-1 > img {\n            border: 5px solid $white;\n        }\n        > .h3 {\n            color: #393939;\n        }\n    }\n    .profile-cover__info .nav li {\n        color: #464461;\n    }\n    .social.social-profile-buttons .social-icon {\n        background: $transparent-body;\n        border-color: $transparent-border;\n        color: $text-color !important;\n    }\n    .profile-share {\n        border-color: $transparent-border;\n        background: $transparent-theme;\n    }\n    .option-dots {\n        &:focus,\n        &:hover,\n        &[aria-expanded=\"true\"] {\n            background: $transparent-theme;\n        }\n        color: $white-7;\n    }\n    @media (min-width: 601px) {\n        .social-profile-buttons .nav {\n            color: #999;\n        }\n    }\n    .social-profile-buttons .nav li {\n        color: #464461;\n    }\n    .item2-gl-menu {\n        border-color: $transparent-border;\n        li {\n            a {\n                color: $white-5;\n            }\n            .active {\n                color: $white;\n            }\n        }\n    }\n    .product-label {\n        background: rgba(178, 177, 183, 0.1);\n    }\n    .ui-widget-header,\n    .ui-state-default,\n    .ui-widget-content .ui-state-default,\n    .ui-widget-header .ui-state-default {\n        background: $transparent-body;\n    }\n    .ui-state-hover,\n    .ui-widget-content .ui-state-hover,\n    .ui-widget-header .ui-state-hover,\n    .ui-state-focus,\n    .ui-widget-content .ui-state-focus,\n    .ui-widget-header .ui-state-focus {\n        border-color: $primary-1 !important;\n        background: $primary-1 !important;\n    }\n    .ui-widget-content {\n        background: $transparent-theme;\n        border-color: $transparent-border;\n    }\n    .product-list .icons li a {\n        &:after,\n        &:before {\n            color: $white;\n        }\n    }\n    .product-grid6 .card-footer .btn-outline-primary:hover {\n        color: $white;\n    }\n    .carousel-inner .carousel-item .thumb {\n        &.active {\n            border-color: $transparent-border;\n        }\n        border-color: $transparent-border;\n    }\n    .customer-services {\n        span {\n            background-color: transparent;\n            color: $primary-1;\n        }\n        border-color: $transparent-border;\n    }\n    .login-social-icon {\n        &::before,\n        &::after {\n            background-color: $transparent-border;\n        }\n        span {\n            background: transparent;\n        }\n    }\n    .custom-layout {\n        color: $white;\n        .nav-link.icon {\n            i {\n                color: $white !important;\n                box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);\n                background: rgba(255, 255, 255, 0.08);\n            }\n            i::after {\n                background-color: transparent;\n            }\n        }\n    }\n    .transparent-mode .custom-layout .nav-link.icon i {\n        color: $white !important;\n    }\n    .country-selector .nav-link {\n        color: #495046;\n    }\n    .theme-container .active {\n        border-color: $primary-1;\n        background: transparent;\n        color: $primary-1;\n    }\n    .theme-container1 .active {\n        border-color: $pink;\n        background: transparent;\n        color: $pink;\n    }\n    .theme-container2 .active {\n        border-color: $secondary;\n        background: transparent;\n        color: $secondary;\n    }\n    .settings-icon {\n        border-color: $primary-1;\n    }\n    .input-group-text.input-text-color {\n        background-color: $transparent-theme;\n    }\n    .payment-icon {\n        &.active svg {\n            fill: $white;\n        }\n        svg {\n            fill: $white;\n        }\n    }\n    .notification {\n        &:before {\n            background: $white-1;\n        }\n        .notification-time {\n            .date,\n            .time {\n                color: $white-3;\n            }\n        }\n        .notification-icon a {\n            background: $transparent-body;\n            color: $white;\n            border: 3px solid $white-5;\n        }\n        .notification-body {\n            box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.03);\n            background: $transparent-theme;\n            &:before {\n                border: 10px solid transparent;\n                border-right-color: $transparent-theme;\n            }\n        }\n    }\n    .notification-time-date {\n        color: $white-3;\n    }\n    .btn-country {\n        border-color: $transparent-border;\n        box-shadow: none !important;\n        box-shadow: 0px 1px 16px rgba(0, 0, 0, 0.1) !important;\n        color: $white;\n        &:hover {\n            border-color: $primary-1 !important;\n            box-shadow: 0px 1px 16px rgba(0, 0, 0, 0.1) !important;\n        }\n    }\n    .btn-check:checked + .btn-country {\n        border-color: $primary-1 !important;\n        background: $transparent-theme;\n    }\n    .btn-country {\n        &.active,\n        &:active {\n            border-color: $primary-1 !important;\n        }\n    }\n    .email-icon {\n        border-color: $transparent-border;\n        color: #5c678f;\n    }\n    .product-grid6 .card-footer .btn-outline-primary:hover .wishlist-icon {\n        color: $white;\n    }\n    .btn-outline-primary {\n        .wishlist-icon {\n            color: $white-6 !important;\n        }\n        &:hover .wishlist-icon {\n            color: $white !important;\n        }\n    }\n    #chartZoom .btn-outline-primary:hover {\n        color: $white;\n    }\n    .c3-legend-item text {\n        fill: $white-5;\n    }\n    .file-image .icons li a {\n        color: $white;\n        &:after,\n        &:before,\n        &:hover {\n            color: $white;\n        }\n    }\n    .file-name {\n        color: $white;\n    }\n    .img-1 img {\n        border: 6px solid rgba(225, 225, 225, 0.5);\n    }\n    .profile-img {\n        border-color: rgba(167, 180, 201, 0.2);\n        background: rgba(225, 225, 225, 0.2);\n    }\n    /*-----Gallery-----*/\n    .demo-gallery {\n        > ul > li {\n            a {\n                border: 3px solid $white;\n            }\n        }\n        &.dark > ul > li a {\n            border: 3px solid #04070a;\n        }\n    }\n    .gallery a img {\n        border-color: rgba(0, 0, 0, 0.2);\n    }\n    .example + .highlight {\n        border-top: none;\n    }\n    .highlight {\n        border-color: $transparent-border;\n        border-top: none;\n        background: $transparent-theme;\n        .hll {\n            background-color: #ffc;\n        }\n        .c {\n            color: #999;\n        }\n        .k {\n            color: #069;\n        }\n        .o {\n            color: #555;\n        }\n        .cm {\n            color: #999;\n        }\n        .cp {\n            color: #099;\n        }\n        .c1,\n        .cs {\n            color: #999;\n        }\n        .gd {\n            background-color: #fcc;\n            border-color: #c00;\n        }\n        .gr {\n            color: #f00;\n        }\n        .gh {\n            color: #030;\n        }\n        .gi {\n            background-color: #cfc;\n            border-color: #0c0;\n        }\n        .go {\n            color: #aaa;\n        }\n        .gp {\n            color: #009;\n        }\n        .gu {\n            color: #030;\n        }\n        .gt {\n            color: #9c6;\n        }\n        .kc,\n        .kd,\n        .kn,\n        .kp,\n        .kr {\n            color: #069;\n        }\n        .kt {\n            color: #078;\n        }\n        .m {\n            color: #f60;\n        }\n        .s {\n            color: #cc0099;\n        }\n        .na {\n            color: #00cc7a;\n        }\n        .nb {\n            color: #366;\n        }\n        .nc {\n            color: #0a8;\n        }\n        .no {\n            color: #360;\n        }\n        .nd {\n            color: #99f;\n        }\n        .ni {\n            color: #999;\n        }\n        .ne {\n            color: #c00;\n        }\n        .nf {\n            color: #c0f;\n        }\n        .nl {\n            color: #99f;\n        }\n        .nn {\n            color: #0cf;\n        }\n        .nt {\n            color: #800000;\n        }\n        .nv {\n            color: #033;\n        }\n        .ow {\n            color: $black;\n        }\n        .w {\n            color: #bbb;\n        }\n        .mf,\n        .mh,\n        .mi,\n        .mo {\n            color: #f60;\n        }\n        .sb,\n        .sc,\n        .sd,\n        .s2,\n        .se,\n        .sh {\n            color: #c30;\n        }\n        .si {\n            color: #a00;\n        }\n        .sx {\n            color: #c30;\n        }\n        .sr {\n            color: #3aa;\n        }\n        .s1 {\n            color: #c30;\n        }\n        .ss {\n            color: #fc3;\n        }\n        .bp {\n            color: #366;\n        }\n        .vc,\n        .vg,\n        .vi {\n            color: #033;\n        }\n        .il {\n            color: #f60;\n        }\n        .css {\n            .o {\n                color: #999;\n                + .nt {\n                    color: #999;\n                }\n            }\n            .nt + .nt {\n                color: #999;\n            }\n        }\n        .language-bash::before,\n        .language-sh::before,\n        .language-powershell::before {\n            color: #009;\n        }\n    }\n    .label-default {\n        background: #d5e0ec;\n        color: $white;\n    }\n    .label-success {\n        background: $success;\n        color: $white;\n    }\n    .label-danger {\n        background: #f5334f;\n        color: $white;\n    }\n    .label-warning {\n        background: $warning;\n        color: $white;\n    }\n    .label-info {\n        background: $info;\n        color: $white;\n    }\n    /*-----Lists-----*/\n    .list-group-item.active {\n        background-color: $transparent-theme;\n        color: $white;\n        border-color: $transparent-border;\n    }\n    .list-group-item-action {\n        color: $white-7;\n        &:hover,\n        &:focus,\n        &:active {\n            color: $white-7;\n            background-color: $transparent-body;\n        }\n    }\n    .list-group-item,\n    .listorder,\n    .listorder1,\n    .listunorder,\n    .listunorder1 {\n        background-color: transparent;\n        border-color: $transparent-border;\n        color: $white-7;\n    }\n    .list-group-item {\n        &.disabled,\n        &:disabled {\n            color: $white-2;\n            background-color: transparent;\n        }\n    }\n    .list-group-item-primary {\n        color: #24426c !important;\n        background-color: #cbdbf2;\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: #24426c;\n                background-color: #b7cded;\n            }\n            &.active {\n                color: $white;\n                background-color: #24426c;\n                border-color: #24426c;\n            }\n        }\n    }\n    .list-group-item-secondary {\n        color: #464a4e !important;\n        background-color: #dddfe2;\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: #464a4e;\n                background-color: #cfd2d6;\n            }\n            &.active {\n                color: $white;\n                background-color: #464a4e;\n                border-color: #464a4e;\n            }\n        }\n    }\n    .list-group-item-success {\n        color: $success !important;\n        background-color: rgba(9, 173, 149, 0.4);\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: $success;\n                background-color: rgba(9, 173, 149, 0.2);\n            }\n            &.active {\n                color: $white;\n                background-color: $success;\n                border-color: $success;\n            }\n        }\n    }\n    .list-group-item-info {\n        color: $info !important;\n        background-color: rgba(17, 112, 228, 0.4);\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: $info;\n                background-color: rgba(17, 112, 228, 0.2);\n            }\n            &.active {\n                color: $white;\n                background-color: rgba(17, 112, 228, 0.2);\n                border-color: rgba(17, 112, 228, 0.2);\n            }\n        }\n    }\n    .list-group-item-warning {\n        color: $warning !important;\n        background-color: rgba(247, 183, 49, 0.4);\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: $warning;\n                background-color: rgba(247, 183, 49, 0.2);\n            }\n            &.active {\n                color: $white;\n                background-color: $warning;\n                border-color: $warning;\n            }\n        }\n    }\n    .list-group-item-danger {\n        color: $danger !important;\n        background-color: rgba(232, 38, 70, 0.4);\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: $danger;\n                background-color: rgba(232, 38, 70, 0.2);\n            }\n            &.active {\n                color: $white;\n                background-color: $danger;\n                border-color: $danger;\n            }\n        }\n    }\n    .list-group-item-light {\n        color: #818182;\n        background-color: #fdfdfe;\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: #818182;\n                background-color: #ececf6;\n            }\n            &.active {\n                color: $white;\n                background-color: #818182;\n                border-color: #818182;\n            }\n        }\n    }\n    .list-group-item-dark {\n        color: #1b1e21;\n        background-color: #c6c8ca;\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: #1b1e21;\n                background-color: #b9bbbe;\n            }\n            &.active {\n                color: $white;\n                background-color: #1b1e21;\n                border-color: #1b1e21;\n            }\n        }\n    }\n    .list-group-item {\n        border-color: $transparent-border;\n        color: $white-7;\n        &.active .icon {\n            color: inherit !important;\n        }\n        .icon {\n            color: $white !important;\n        }\n    }\n    .list-group-transparent {\n        .list-group-item {\n            &.active {\n                background: $transparent-theme;\n                color: $white;\n            }\n        }\n        &.file-manager .list-group-item {\n            color: $white;\n        }\n    }\n    .file-radius-attachments i {\n        color: $text-color;\n    }\n    .file-square-attachments a {\n        color: $text-color;\n    }\n    .file-image-1 {\n        border-color: $transparent-border;\n    }\n    .file-image-1 .file-name-1 {\n        color: $text-color;\n    }\n    .list-group-transparent.file-manager.file-manager-border .list-group-item {\n        border-color: $transparent-border;\n    }\n    /*------ Media object ------*/\n    .btn-close {\n        color: $white-7;\n        &:hover,\n        &:focus {\n            color: inherit;\n        }\n    }\n    .navbar-toggler {\n        background-color: transparent;\n        border-color: transparent;\n    }\n    @media (min-width: 992px) {\n        .responsive-navbar .navbar-collapse {\n            background: transparent;\n        }\n    }\n    a.icon:hover {\n        color: #dcdfed !important;\n    }\n    .navbar-light {\n        .navbar-brand {\n            color: $white;\n            &:hover,\n            &:focus {\n                color: $white;\n            }\n        }\n        .navbar-nav {\n            .nav-link {\n                color: rgba(0, 0, 0, 0.5);\n                &:hover,\n                &:focus {\n                    color: rgba(0, 0, 0, 0.7);\n                }\n                &.disabled {\n                    color: rgba(0, 0, 0, 0.3);\n                }\n            }\n            .show > .nav-link,\n            .active > .nav-link {\n                color: rgba(0, 0, 0, 0.9);\n            }\n            .nav-link {\n                &.show,\n                &.active {\n                    color: rgba(0, 0, 0, 0.9);\n                }\n            }\n        }\n        .navbar-toggler {\n            color: rgba(0, 0, 0, 0.5);\n            border-color: rgba(0, 0, 0, 0.1);\n        }\n        .navbar-text {\n            color: rgba(0, 0, 0, 0.5);\n            a {\n                color: rgba(0, 0, 0, 0.9);\n                &:hover,\n                &:focus {\n                    color: rgba(0, 0, 0, 0.9);\n                }\n            }\n        }\n    }\n    .navbar-dark {\n        .navbar-brand {\n            color: $white;\n            &:hover,\n            &:focus {\n                color: $white;\n            }\n        }\n        .navbar-nav {\n            .nav-link {\n                color: rgba(255, 255, 255, 0.5);\n                &:hover,\n                &:focus {\n                    color: rgba(255, 255, 255, 0.75);\n                }\n                &.disabled {\n                    color: rgba(255, 255, 255, 0.25);\n                }\n            }\n            .show > .nav-link,\n            .active > .nav-link {\n                color: $white;\n            }\n            .nav-link {\n                &.show,\n                &.active {\n                    color: $white;\n                }\n            }\n        }\n        .navbar-toggler {\n            color: rgba(255, 255, 255, 0.5);\n            border-color: rgba(255, 255, 255, 0.1);\n        }\n        .navbar-text {\n            color: rgba(255, 255, 255, 0.5);\n            a {\n                color: $white;\n                &:hover,\n                &:focus {\n                    color: $white;\n                }\n            }\n        }\n    }\n    @media (max-width: 991px) {\n        .navresponsive-toggler span {\n            // color: $white;\n            &:after {\n                background: rgba($primary-1, 0);\n            }\n        }\n        &.header-light {\n            #navbarSupportedContent-4 {\n                .nav-link.icon {\n                    color: #fff !important;\n                }\n            }\n        }\n        &.dark-header {\n            #navbarSupportedContent-4 {\n                .nav-link.icon {\n                    color: #fff !important;\n                }\n            }\n        }\n        &.dark-header {\n            .navresponsive-toggler span {\n                color: $white;\n            }\n        }\n    }\n    @media (max-width: 991px) {\n        .responsive-navbar .navbar-collapse {\n            background: $transparent-body;\n            box-shadow: 0 12px 11px -3px rgba(0, 0, 0, 0.5);\n            border-top-color: $transparent-border;\n        }\n        .responsive-navbar .navbar-collapse .icon.navsearch {\n            border-color: #e4e6f9;\n        }\n    }\n    /*--scrollspy ---*/\n    .scrollspy-example {\n        border-right-color: $transparent-border;\n        border-left-color: $transparent-border;\n        border-bottom-color: $transparent-border;\n    }\n    .scrollspy-example-2 {\n        border-color: $transparent-border;\n    }\n    #navbar-example3 .nav-link {\n        color: $white;\n        &.active {\n            color: $white;\n        }\n    }\n    .nav-link {\n        &:focus,\n        &:hover {\n            color: $primary-1;\n        }\n    }\n    /*-----Pricing tables-----*/\n    .pricing {\n        color: $white;\n    }\n    .pricing1 {\n        color: #707070;\n    }\n    .pricing {\n        .list-unstyled li {\n            border-bottom-color: rgba(255, 255, 255, 0.1);\n        }\n    }\n    .pricing1 {\n        .list-unstyled li {\n            border-bottom-color: $transparent-border;\n        }\n    }\n    .panel-heading-landing {\n        background: #f7f7f7 !important;\n        border: solid 2px #1643a3 !important;\n        border-bottom: none !important;\n    }\n    /*------ Pricing Styles ---------*/\n    .panel-heading {\n        border-bottom-color: rgba(255, 255, 255, 0.2);\n        background: $transparent-theme;\n    }\n    .panel.price {\n        box-shadow: 0 0.15rem 1.75rem 0 $transparent-theme;\n        background: $transparent-theme;\n        > .panel-heading {\n            color: $white;\n        }\n    }\n    .price {\n        .panel-footer {\n            background-color: transparent;\n        }\n        &.panel-color > .panel-body {\n            background-color: transparent;\n        }\n    }\n    .ribbone1-price .ribbon span {\n        color: $white;\n        background: #79a70a;\n        background: $primary-1;\n        box-shadow: 0 3px 10px -5px black;\n        &::before {\n            border-left: 3px solid $primary-1;\n            border-right: 3px solid transparent;\n            border-bottom: 3px solid transparent;\n            border-top: 3px solid $primary-1;\n        }\n        &::after {\n            border-left: 3px solid transparent;\n            border-right: 3px solid $primary-1;\n            border-bottom: 3px solid transparent;\n            border-top: 3px solid $primary-1;\n        }\n    }\n    .secondary .pricing-divider {\n        background: transparent;\n    }\n    .danger .pricing-divider {\n        background: transparent !important;\n    }\n    .primary .pricing-divider {\n        background: transparent !important;\n    }\n    .success .pricing-divider {\n        background: transparent;\n    }\n    .primary .princing-item .bg-white {\n        background-color: transparent !important;\n    }\n    .secondary .princing-item .bg-white {\n        background-color: transparent !important;\n    }\n    .danger .princing-item .bg-white {\n        background-color: transparent !important;\n    }\n    .success .princing-item .bg-white {\n        background-color: transparent !important;\n    }\n    /*-- rating--*/\n    .rating-stars {\n        input {\n            color: #495057;\n            background-color: $transparent-theme;\n            border-color: $transparent-border;\n        }\n    }\n    .tabs-menu ul li {\n        a {\n            color: $white-7;\n        }\n        .active {\n            color: $primary-1;\n        }\n    }\n    .tabs-menu1 ul li a {\n        color: $white;\n    }\n    .tab-menu-heading {\n        border-bottom-color: $transparent-border !important;\n    }\n    .tabs-menu2 ul li {\n        a {\n            color: #636262;\n        }\n        .fade {\n            color: #eeee;\n        }\n    }\n    .sidebar-right .tab-content i,\n    .tabs-menu2 ul li .active {\n        color: $white-7;\n    }\n    .search-tabs ul li a {\n        &.active {\n            border-bottom: 3px solid $primary-1;\n            background-color: transparent !important;\n        }\n        &:hover {\n            background-color: transparent !important;\n        }\n    }\n    .tabs-menu-border ul li .active {\n        border-color: $transparent-border;\n    }\n    .tabs-menu-boxed ul li {\n        a {\n            color: $white;\n            border-bottom-color: $transparent-border;\n        }\n        .active {\n            border-color: $transparent-border;\n            border-bottom-color: transparent;\n        }\n    }\n    /***** time-line*****/\n    .timeline__item:after {\n        background: $white !important;\n    }\n    .timeline__content {\n        background-color: $transparent-theme;\n    }\n    /*---- Time line -----*/\n    .timeline:before {\n        background-color: #e9ecef;\n    }\n    .timeline-item {\n        &:first-child:before,\n        &:last-child:before {\n            background: $transparent-theme;\n        }\n    }\n    .timeline-badge {\n        border-color: $white;\n        background: #adb5bd;\n    }\n    .timeline-time {\n        color: #9aa0ac;\n    }\n    .timeline__item--right .timeline__content:before {\n        border-right: 12px solid rgba(238, 232, 239, 0.9);\n    }\n    ul.timeline {\n        &:before {\n            background: #d4d9df;\n        }\n        > li:before {\n            border-color: #6c6c6f;\n        }\n    }\n    /*----Timeline---*/\n    .vtimeline::before {\n        background-color: $transparent-border;\n    }\n    .vtimeline .timeline-wrapper {\n        .timeline-panel {\n            background: $transparent-theme;\n            box-shadow: 0 5px 12px 0 #101329;\n            &:after {\n                border-top: 10px solid transparent;\n                border-left: 10px solid $transparent-border;\n                border-right: 0 solid $transparent-border;\n                border-bottom: 10px solid transparent;\n            }\n        }\n        .timeline-badge {\n            border: 2px solid $transparent-body;\n            i {\n                color: $white;\n            }\n        }\n        &.timeline-inverted .timeline-panel:after {\n            border-left-width: 0;\n            border-right-width: 10px;\n        }\n    }\n    .timeline-wrapper-primary {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: $primary-1;\n        }\n    }\n    .timeline-wrapper-secondary {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: $secondary;\n        }\n    }\n    .timeline-wrapper-success {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: $success;\n        }\n    }\n    .timeline-wrapper-green {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: $green;\n        }\n    }\n    .timeline-wrapper-warning {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: #fcd539;\n        }\n    }\n    .timeline-wrapper-danger {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: #f16d75;\n        }\n    }\n    .timeline-wrapper-light {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: $transparent-theme;\n        }\n    }\n    .timeline-wrapper-dark {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: #828db1;\n        }\n    }\n    @media (max-width: 767px) {\n        .vtimeline .timeline-wrapper .timeline-panel:after {\n            border-right: 14px solid $white-2 !important;\n            border-left: 0 solid $white-2 !important;\n        }\n    }\n    /* ######## LAYOUT-STYLES ######## */\n    .footer {\n        background: transparent;\n        border-top-color: rgba(255, 255, 255, 0.02);\n        .social ul li a {\n            border-color: $transparent-border;\n            background: $transparent-body;\n            color: $white;\n        }\n    }\n    .top-footer {\n        p {\n            color: $white-7;\n        }\n        a {\n            color: $white-7;\n            address {\n                color: $white-7;\n            }\n        }\n        img {\n            border-color: $transparent-border;\n            &:hover {\n                color: #8e9090;\n            }\n        }\n    }\n    .footer-payments a {\n        color: #a7a8c7;\n    }\n    .main-footer {\n        background-color: transparent;\n        border-top-color: $transparent-border;\n    }\n    .header {\n        background: $transparent-theme;\n        border-bottom-color: $transparent-border;\n    }\n    .header-brand,\n    .app-header .header-brand,\n    .header-brand:hover {\n        color: inherit;\n    }\n    .app-header.sticky.stickyClass {\n        background-color: $primary-1;\n        box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0) !important;\n    }\n    @media (max-width: 991px) {\n        .header.hor-header {\n            border-bottom-color: $transparent-border;\n            box-shadow: 0 8px 24px $transparent-theme;\n        }\n        .hor-header .header-brand-img.light-logo {\n            margin: 0 auto;\n            margin-top: 6px;\n        }\n    }\n    .header {\n        .form-inline {\n            .form-control {\n                border-color: rgba(225, 225, 225, 0.1);\n                background: rgba(225, 225, 225, 0.3);\n                color: $black !important;\n            }\n            .btn {\n                border: 2px solid transparent;\n                box-shadow: none;\n                background: transparent;\n                color: $white;\n            }\n        }\n    }\n    @media (max-width: 767.98px) and (min-width: 576px) {\n        .header .navsearch i {\n            color: $white;\n        }\n        .search-element .form-control {\n            background: $white !important;\n            color: $black;\n        }\n        .header {\n            .form-inline .form-control::-webkit-input-placeholder {\n                color: $white-7;\n            }\n            .navsearch i {\n                color: $white;\n            }\n            .form-inline .btn {\n                color: #46494a !important;\n            }\n        }\n    }\n    @media only screen and (max-width: 991px) {\n        .animated-arrow span {\n            &:before,\n            &:after {\n                background: $default-color;\n            }\n        }\n        .animated-arrow span {\n            &:before,\n            &:after {\n                background: $default-color;\n            }\n        }\n    }\n    @media only screen and (max-width: 991px) {\n        body {\n            background-color: $transparent-theme;\n        }\n    }\n    .hor-header .header-brand1 {\n        color: inherit;\n    }\n    .header-right-icons {\n        .nav-link.icon:hover {\n            background: none;\n        }\n        .profile-user:hover {\n            box-shadow: none;\n        }\n    }\n    .hor-header .header-right-icons .nav-link.icon {\n        color: $white;\n    }\n    .logo-horizontal .header-brand-img.desktop-logo {\n        display: block !important;\n    }\n    .logo-horizontal .header-brand-img.light-logo1 {\n        display: none;\n    }\n    .hor-header .header-brand-img.light-logo {\n        display: block;\n    }\n    /*Logo-center header */\n    @media (max-width: 992px) {\n        .header {\n            border-bottom-color: rgba(255, 255, 255, 0.2);\n        }\n        .header.hor-header {\n            background-color: $transparent-body;\n            box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.01) !important;\n        }\n    }\n    .header {\n        .dropdown-menu {\n            box-shadow: 0 10px 40px 0 rgba(104, 113, 123, 0.2);\n            border-color: $transparent-border;\n            .dropdown-item {\n                border-bottom-color: $transparent-border;\n            }\n        }\n        .dropdown-item,\n        .notifications-menu h5,\n        .message-menu h5 {\n            color: $white;\n        }\n        .notifications-menu span,\n        .message-menu span {\n            color: $white-7;\n        }\n        .dropdown-menu {\n            box-shadow: 0 10px 40px 0 rgba(104, 113, 123, 0.2);\n            border-color: $transparent-border;\n            .dropdown-item {\n                border-bottom-color: $transparent-border;\n            }\n        }\n        .profile-1 .dropdown-item .dropdown-icon {\n            color: $primary-1;\n            &::after {\n                background: rgba($primary-1, 0);\n            }\n        }\n    }\n    .responsive-navbar {\n        .notifications-menu h5,\n        .message-menu h5 {\n            color: $white;\n        }\n        .notifications-menu span,\n        .message-menu span {\n            color: $white-7;\n        }\n        .dropdown-menu {\n            box-shadow: 0 10px 40px 0 rgba(34, 34, 61, 0.8);\n            border-color: $transparent-border;\n            .dropdown-item {\n                border-bottom-color: $transparent-border;\n            }\n        }\n        .profile-1 .dropdown-item .dropdown-icon {\n            color: $white-7;\n            &::after {\n                background: rgba($primary-1, 0);\n            }\n        }\n    }\n    &.horizontal {\n        .side-menu > li > a {\n            color: $white-8;\n        }\n        .logo-horizontal .header-brand-img.light-logo1 {\n            display: none;\n        }\n    }\n    .ps__rail-y:hover > .ps__thumb-y,\n    .ps__rail-y:focus > .ps__thumb-y,\n    .ps__rail-y.ps--clicking .ps__thumb-y {\n        background-color: $transparent-theme;\n    }\n    /*==============================================================================\n                                Start Mobile CSS\n  ===============================================================================*/\n    /* ================== Mobile Menu Change Brake Point ================== */\n    @media only screen and (max-width: 991px) {\n        /* ================== Mobile Slide Down Links CSS ================== */\n        /* ================== Mobile Mega Menus CSS  ================== */\n        /* ================== Mobile Header CSS ================== */\n        .horizontal-header {\n            border-bottom-color: rgba(0, 0, 0, 0.1);\n            border-top-color: rgba(0, 0, 0, 0.1);\n        }\n        .callusbtn {\n            color: #a9a9a9;\n            &:hover .fa {\n                color: #a9a9a9;\n            }\n        }\n        /* Mobile Toggle Menu icon (X ICON) */\n        .animated-arrow span {\n            background: $white-7;\n            &:before,\n            &:after {\n                background: $white-7;\n            }\n        }\n        &.active .animated-arrow span {\n            background-color: transparent;\n        }\n        /* ================== Mobile Overlay/Drawer CSS ================== */\n        .horizontal-overlapbg {\n            background-color: rgba(0, 0, 0, 0.45);\n        }\n        /*End Media Query*/\n    }\n    /* Extra @Media Query*/\n    .horizontal-main.hor-menu {\n        background: $transparent-theme;\n        border-bottom-color: $transparent-border;\n    }\n    .icons-list-item {\n        border-color: $transparent-border;\n        i {\n            color: $white-5;\n        }\n    }\n    .browser {\n        background: no-repeat center/100% 100%;\n    }\n    .flag,\n    .payment {\n        box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);\n    }\n    .col-sm-3 a {\n        border-color: transparent;\n        &:hover {\n            border-color: #ff4647;\n            background: linear-gradient(rgba(56, 123, 131, 0.7), rgba(56, 123, 131, 0.7));\n        }\n    }\n    /* ######## LIB-STYLES ######## */\n    /*----- Date Picker ----*/\n    .ui-datepicker {\n        background-color: $transparent-body;\n        border-color: $transparent-border;\n        .ui-datepicker-header {\n            color: $white;\n            .ui-datepicker-next,\n            .ui-datepicker-prev {\n                text-indent: -99999px;\n                color: $white-7;\n            }\n            .ui-datepicker-next {\n                &:hover::before,\n                &:focus::before {\n                    color: $dark;\n                }\n            }\n            .ui-datepicker-prev {\n                &:hover::before,\n                &:focus::before {\n                    color: $dark;\n                }\n            }\n            .ui-datepicker-next-hover,\n            .ui-datepicker-prev-hover {\n                color: $white-7;\n            }\n        }\n        .ui-datepicker-calendar {\n            th {\n                color: $white-7;\n            }\n            td {\n                border-color: $transparent-border;\n                background-color: transparent;\n                span {\n                    background-color: $transparent-theme;\n                    color: $white;\n                }\n                a {\n                    background-color: $transparent-theme;\n                    color: $white-7;\n                    &:hover {\n                        background-color: $transparent-theme;\n                        color: $white;\n                    }\n                }\n            }\n            .ui-datepicker-today a {\n                background-color: $transparent-theme;\n                color: $white;\n            }\n        }\n        .ui-datepicker-title {\n            color: $white;\n        }\n    }\n    .jvectormap-tip {\n        background: $white;\n        color: white;\n    }\n    .jvectormap-zoomin,\n    .jvectormap-zoomout,\n    .jvectormap-goback {\n        background: $transparent-body;\n        color: white;\n    }\n    .jvectormap-legend {\n        background: black;\n        color: white;\n    }\n    .select2-container--default {\n        .select2-selection--single {\n            background-color: transparent;\n            border-color: $transparent-border !important;\n        }\n        .select2-selection--single .select2-selection__placeholder {\n            color: $white-5;\n        }\n        &.select2-container--focus .select2-selection--multiple {\n            background-color: $transparent-theme;\n            border-color: #9ca3b1;\n            box-shadow: none;\n        }\n        .select2-selection--multiple {\n            background-color: $transparent-theme;\n            border-color: $transparent-border !important;\n        }\n        .select2-search--dropdown .select2-search__field {\n            border-color: $transparent-border !important;\n            background: $transparent-theme;\n        }\n        .select2-selection--multiple {\n            .select2-selection__choice,\n            .select2-selection__choice__remove {\n                color: $white !important;\n            }\n        }\n        .select2-results > .select2-results__options {\n            box-shadow: 0px 16px 18px rgba(104, 113, 123, 0.2);\n        }\n        .select2-selection--single .select2-selection__rendered {\n            color: $white;\n        }\n    }\n    .select2-container--default.select2-container--disabled .select2-selection--single {\n        background: $transparent-theme;\n    }\n    .selectgroup-button {\n        border-color: $transparent-border;\n        color: $white-7;\n    }\n    .selectgroup-input {\n        &:checked + .selectgroup-button {\n            background: $transparent-theme;\n        }\n        &:focus + .selectgroup-button {\n            box-shadow: 0 0 0 2px rgba(98, 58, 162, 0.25);\n        }\n    }\n    /*-----selectize ------*/\n    .selectize-dropdown {\n        color: #495057;\n    }\n    .selectize-input {\n        color: #495057;\n        background: $transparent-theme;\n        input {\n            color: #495057;\n        }\n    }\n    .selectize-input {\n        border-color: $transparent-border;\n        &.full {\n            background-color: $transparent-theme;\n        }\n        &.focus {\n            border-color: #467fcf;\n            box-shadow: 0 0 0 2px rgba(98, 58, 162, 0.25);\n        }\n    }\n    .selectize-input {\n        &.dropdown-active::before {\n            background: #f0f0f0;\n        }\n    }\n    .selectize-dropdown {\n        border-color: $transparent-border;\n        background: $transparent-theme;\n        [data-selectable] .highlight {\n            background: rgba(125, 168, 208, 0.2);\n        }\n        .optgroup-header {\n            color: #495057;\n            background: $transparent-theme;\n        }\n        .active {\n            background-color: #f1f4f8;\n            color: #467fcf;\n            &.create {\n                color: #495057;\n            }\n        }\n        .create {\n            color: rgba(48, 48, 48, 0.5);\n        }\n    }\n    .selectize-dropdown .image img,\n    .selectize-input .image img {\n        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.4);\n    }\n    /* ######## SIDEMENU-STYLES ######## */\n    @media (min-width: 992px) {\n        .side-header {\n            background: transparent;\n        }\n    }\n    @media (max-width: 991px) {\n        &.app.sidebar-mini .app-sidebar {\n            background: $transparent-body;\n        }\n    }\n    @media print {\n        .app-content {\n            background-color: $transparent-theme;\n        }\n    }\n    .app-header {\n        border-bottom-color: $transparent-border;\n        background: $transparent-theme;\n    }\n    .app-header__logo {\n        color: $white;\n    }\n    .ps__thumb-y {\n        background-color: transparent;\n        width: 1px;\n    }\n    .app-sidebar__toggle {\n        color: $white;\n        &:after {\n            background: rgba($primary-1, 0);\n        }\n    }\n    .app-sidebar {\n        color: $white;\n        background: transparent;\n        -webkit-box-shadow: 0px 0px 0px 0px rgba(42, 38, 53, 0);\n        box-shadow: 0px 0px 0px 0px rgba(42, 38, 53, 0);\n        border-right-color: $transparent-border;\n        &::-webkit-scrollbar-thumb {\n            background: rgba(0, 0, 0, 0.2);\n        }\n    }\n    .app-sidebar__user {\n        color: #a8a8a8;\n        img {\n            box-shadow: 0 0 25px rgba(255, 255, 255, 0.1);\n            border: rgba(255, 255, 255, 0.2);\n            box-shadow: 0px 5px 5px 0px rgba(44, 44, 44, 0.2);\n        }\n    }\n    .app-sidebar__user-name,\n    .app-sidebar__user-designation {\n        color: #e5e9ec;\n    }\n    .side-menu__item {\n        color: $white-8;\n        &.active {\n            color: $white !important;\n            &:hover,\n            &:focus {\n                color: $primary-1;\n            }\n        }\n        &:hover,\n        &:focus {\n            color: $primary-1;\n        }\n        &:hover {\n            .side-menu__icon,\n            .side-menu__label {\n                color: $white !important;\n            }\n        }\n        &:focus {\n            .side-menu__icon,\n            .side-menu__label {\n                color: $white;\n            }\n        }\n    }\n    .slide-item {\n        &.active,\n        &:hover,\n        &:focus {\n            color: #b5c1d2;\n        }\n    }\n    .slide-menu a.active {\n        color: $primary-1;\n    }\n    .slide-item,\n    .sub-slide-item,\n    .sub-slide-item2 {\n        &.active,\n        &:hover,\n        &:focus {\n            color: $white !important;\n        }\n    }\n    .sub-side-menu__item,\n    .sub-side-menu__item2 {\n        color: $white-8;\n    }\n    .sub-slide-item,\n    .sub-slide-item2,\n    .sub-side-menu__label,\n    .sub-side-menu__label1,\n    .sub-side-menu__label2 {\n        &.active,\n        &:hover,\n        &:focus {\n            color: $white;\n        }\n    }\n    .slide-menu li .slide-item:before {\n        color: $white;\n    }\n    .side-menu .side-menu__icon {\n        color: $white-7 !important;\n    }\n    .slide-item {\n        color: $white;\n    }\n    .side-menu__item.active .side-menu__icon {\n        color: $white !important;\n    }\n    @media (min-width: 992px) {\n        .sidebar-mini.sidenav-toggled {\n            .side-menu .side-menu__icon {\n                background: none !important;\n                box-shadow: none;\n            }\n            .sidebar-mini.sidenav-toggled.user-notification::before {\n                background: transparent;\n            }\n            .app-sidebar__user {\n                border-bottom-color: rgba(225, 225, 225, 0.05);\n            }\n        }\n        &.horizontal{\n            .sticky.stickyClass{\n                .app-sidebar{\n                    box-shadow: 0 8px 24px rgb(0 0 0 / 20%);\n                    -webkit-box-shadow: 0 8px 24px rgb(0 0 0 / 20%);\n                }\n            }\n            .horizontal-main .slide .slide-menu,\n            .horizontal-main .slide .sub-slide-menu,\n            .horizontal-main .slide .sub-slide-menu2 {\n                background-color: $transparent-body;\n                border-color: $transparent-border;\n                box-shadow: 0px 16px 18px rgba(0, 0, 0, 0.3) !important;\n            }\n        }\n    }\n    .app-title {\n        background-color: $transparent-theme;\n        -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n    }\n    .app-breadcrumb {\n        background-color: transparent;\n    }\n    .user-info {\n        .text-dark {\n            color: #25252a !important;\n        }\n        .text-muted {\n            color: $white-7 !important;\n        }\n    }\n    .side-header {\n        border-bottom-color: $white-1;\n        border-right-color: $white-1;\n    }\n    .side-menu {\n        background: $transparent-theme;\n    }\n    &.sidebar-mini .side-menu {\n        height: 150vh;\n    }\n    &.horizontal {\n        .side-menu {\n            background: transparent;\n        }\n        .horizontal-main {\n            background: $transparent-theme;\n        }\n        .main-sidemenu .slide-left,\n        .main-sidemenu .slide-right {\n            border-color: $transparent-border;\n            svg {\n                fill: $text-color;\n            }\n        }\n    }\n    .side-menu .sub-category {\n        color: rgba(255, 255, 255, 0.3);\n    }\n    .side-menu .sub-side-menu__label,\n    .sub-slide-label,\n    .sub-side-menu__label2 {\n        color: $white-8;\n    }\n    .sub-slide-item,\n    .sub-slide-item2 {\n        color: $text-color;\n    }\n    /*-- Subslide ---*/\n    /*-- Subslide2 ---*/\n    /* ######## TEMP-STYLES ######## */\n    .richText {\n        border: $transparent-border solid 1px;\n        background-color: transparent !important;\n        .richText-toolbar {\n            border-bottom: rgba(156, 162, 161, 0) solid 1px;\n            ul li a {\n                border-right: rgba(156, 162, 161, 0.2) solid 1px;\n            }\n        }\n    }\n    .transparent-mode .cal1 .clndr .clndr-table tr .day.event:hover,\n    .cal1 .clndr .clndr-table tr .day.my-event:hover,\n    .transparent-mode .cal1 .clndr .clndr-table tr .day.today,\n    .cal1 .clndr .clndr-table tr .day.my-today {\n        color: $white;\n    }\n    .cal1 .clndr {\n        .clndr-table {\n            .header-days .header-day {\n                border-left-color: $transparent-border;\n                border-top-color: $transparent-border;\n                border-right-color: $transparent-border;\n                color: $text-color;\n            }\n            tr .day.event:hover,\n            .cal1 .clndr .clndr-table tr .day.my-event:hover {\n                color: $white;\n            }\n            tr:last-child .day,\n            .cal1 .clndr .clndr-table tr:last-child .my-day {\n                border-bottom-color: $transparent-border;\n            }\n            tr {\n                .empty,\n                .adjacent-month,\n                .my-empty,\n                .my-adjacent-month {\n                    border-left-color: $transparent-border;\n                    border-top-color: $transparent-border;\n                    color: $white-7;\n                }\n                .day {\n                    border-left-color: $transparent-border;\n                    border-top-color: $transparent-border;\n                    &.event,\n                    &.my-event {\n                        background: $transparent-theme !important;\n                    }\n                    &:last-child {\n                        border-right-color: $transparent-border;\n                    }\n                    &:hover {\n                        background: $transparent-body;\n                    }\n                }\n            }\n        }\n        .clndr-controls {\n            border-color: $transparent-border;\n            background-color: transparent;\n        }\n        .clndr-controls .clndr-control-button {\n            .clndr-previous-button,\n            .clndr-next-button {\n                color: $white;\n            }\n        }\n    }\n    .fc-unthemed {\n        .fc-content,\n        .fc-divider,\n        .fc-list-heading td,\n        .fc-list-view,\n        .fc-popover,\n        .fc-row,\n        tbody,\n        td,\n        th,\n        thead {\n            border-color: $transparent-border;\n        }\n    }\n    .fc-event,\n    .fc-event-dot {\n        color: $transparent-border !important;\n    }\n    .fc-unthemed {\n        .fc-divider,\n        .fc-list-heading td,\n        .fc-popover .fc-header {\n            background: $transparent-border;\n        }\n    }\n    .fc-toolbar {\n        .fc-state-active,\n        .ui-state-active {\n            background: #b4b4b4;\n        }\n    }\n    .fc-today-button fc-button fc-state-default fc-corner-left fc-corner-right fc-state-disabled:focus {\n        border: none !important;\n        box-shadow: none !important;\n    }\n    .fc-unthemed .fc-list-item:hover td {\n        background-color: #eeeeee;\n    }\n    .cal1 .clndr .clndr-table tr {\n        .empty:hover,\n        .adjacent-month:hover,\n        .my-empty:hover,\n        .my-adjacent-month:hover {\n            background: $transparent-theme;\n        }\n    }\n    .cal1 .clndr .clndr-table .header-days .header-day {\n        color: $white;\n    }\n    /*------ Charts styles ------*/\n    .instagram {\n        background: linear-gradient(to right bottom, #de497b 0%, #e1164f 100%);\n    }\n    .linkedin {\n        background-image: linear-gradient(to right bottom, #0d97de 0%, #13547a 100%);\n    }\n    .twitter {\n        background-image: linear-gradient(to right bottom, #00f2fe 0%, #1e63c3 100%);\n    }\n    .facebook {\n        background-image: linear-gradient(to right bottom, #3d6cbf 0%, #1e3c72 100%);\n    }\n    .map-header:before {\n        background: linear-gradient(to bottom, rgba(245, 247, 251, 0) 5%, $transparent-theme 95%);\n    }\n    /*----chart-drop-shadow----*/\n    .chart-dropshadow {\n        -webkit-filter: drop-shadow(-6px 5px 4px #2a2635);\n        filter: drop-shadow(-6px 5px 4px #2a2635);\n    }\n    .chart-dropshadow-primary {\n        -webkit-filter: drop-shadow((-6px) 12px 4px rgba(133, 67, 246, 0.2));\n        filter: drop-shadow((-6px) 12px 4px rgba(133, 67, 246, 0.2));\n    }\n    .chart-dropshadow-primary-1 {\n        -webkit-filter: drop-shadow((-6px) 12px 4px rgba(133, 67, 246, 0.2));\n        filter: drop-shadow((-6px) 12px 4px rgba(133, 67, 246, 0.2));\n    }\n    .chart-dropshadow-danger {\n        -webkit-filter: drop-shadow((-6px) 12px 4px rgba(244, 88, 91, 0.1));\n        filter: drop-shadow((-6px) 5px 4px rgba(244, 88, 91, 0.1));\n    }\n    .chart-dropshadow-warning {\n        -webkit-filter: drop-shadow((-6px) 5px 4px rgba(247, 183, 49, 0.1));\n        filter: drop-shadow((-6px) 5px 4px rgba(247, 183, 49, 0.1));\n    }\n    .BarChartShadow {\n        -webkit-filter: drop-shadow((-4px) 9px 4px rgba(0, 0, 0, 0.3));\n        filter: drop-shadow((-6px) 9px 4px rgba(0, 0, 0, 0.3));\n    }\n    /*----chart-drop-shadow----*/\n    .chart-dropshadow2 {\n        -webkit-filter: drop-shadow((-6px) 5px 4px rgba(0, 0, 0, 0.2));\n        filter: drop-shadow((-6px) 5px 4px rgba(0, 0, 0, 0.2));\n    }\n    .chart-dropshadow-secondary {\n        -webkit-filter: drop-shadow((-6px) 5px 4px rgba(130, 207, 242, 0.1));\n        filter: drop-shadow((-6px) 5px 4px rgba(130, 207, 242, 0.1));\n    }\n    .chart-dropshadow-success {\n        -webkit-filter: drop-shadow((-6px) 5px 4px rgba(19, 191, 166, 0.1));\n        filter: drop-shadow((-6px) 5px 4px rgba(19, 191, 166, 0.1));\n    }\n    .chart-dropshadow-info {\n        -webkit-filter: drop-shadow((-6px) 5px 4px rgba(7, 116, 248, 0.1));\n        filter: drop-shadow((-6px) 5px 4px rgba(7, 116, 248, 0.1));\n    }\n    .donutShadow {\n        -webkit-filter: drop-shadow((-5px) 4px 6px rgba(0, 0, 0, 0.5));\n        filter: drop-shadow((-1px) 0px 2px rgba(159, 120, 255, 0.5));\n    }\n    .donutShadow-yellow {\n        -webkit-filter: drop-shadow((-5px) 4px 6px rgba(0, 0, 0, 0.5));\n        filter: drop-shadow((-1px) 0px 2px rgba(251, 196, 52, 0.5));\n    }\n    .donutShadow-blue {\n        -webkit-filter: drop-shadow((-5px) 4px 6px rgba(0, 0, 0, 0.5));\n        filter: drop-shadow((-1px) 0px 2px rgba(36, 72, 135, 0.5));\n    }\n    /* ###### Chat  ###### */\n    .main-chat-contacts-wrapper {\n        border-bottom-color: $transparent-border;\n    }\n    .main-chat-list {\n        .media {\n            border-color: $transparent-border;\n            + .media {\n                border-top-color: $transparent-border;\n            }\n            &.new {\n                background-color: transparent;\n                .media-contact-name span:first-child {\n                    color: $white;\n                }\n                .media-body p {\n                    color: rgba(255, 255, 255, 0.5);\n                }\n            }\n            &:hover,\n            &:focus {\n                background-color: $transparent-theme;\n                border-top-color: $transparent-border;\n                border-bottom-color: $transparent-border;\n            }\n            &:hover:first-child,\n            &:focus:first-child {\n                border-top-color: transparent;\n            }\n            &.selected {\n                background-color: $transparent-theme;\n                border-top-color: $transparent-border;\n                border-bottom-color: $transparent-border;\n                &:first-child {\n                    border-top-color: transparent;\n                }\n                .media-contact-name span:first-child {\n                    color: $white;\n                }\n                .media-body p {\n                    color: $white-7;\n                }\n            }\n        }\n        .main-img-user span {\n            color: $white;\n            background-color: $secondary;\n            box-shadow: 0 0 0 2px $transparent-theme;\n        }\n        .media-body p {\n            color: $white-7;\n        }\n        .media-contact-name span {\n            &:first-child {\n                color: $white;\n            }\n            &:last-child {\n                color: $white-7;\n            }\n        }\n    }\n    .main-chat-header {\n        border-bottom-color: $transparent-border;\n        .nav-link {\n            color: $white-7;\n        }\n    }\n    .main-chat-msg-name small,\n    .main-chat-body .media-body > div:last-child {\n        color: $white-7;\n    }\n    .main-chat-time {\n        span {\n            background: transparent;\n        }\n        &::before,\n        &::after {\n            background-color: $transparent-border;\n        }\n    }\n    .main-chat-footer {\n        border-top-color: $transparent-border;\n        background-color: $transparent-theme;\n        .nav-link {\n            color: $white-7;\n        }\n        .form-control {\n            border-color: $transparent-border;\n            &:hover,\n            &:focus {\n                box-shadow: none;\n            }\n        }\n    }\n    .main-content-title {\n        color: #170c6b;\n    }\n    .main-msg-wrapper {\n        background-color: $transparent-body;\n    }\n    .main-chat-body .media.flex-row-reverse .main-msg-wrapper {\n        background-color: $transparent-theme;\n        color: $white;\n    }\n    /* ###### Chat  ###### */\n    .chat-profile {\n        color: $white-7;\n    }\n    .shared-files {\n        border-color: $transparent-border;\n    }\n    .main-chat-list .media {\n        &:hover,\n        &:focus {\n            background: $transparent-theme !important;\n        }\n    }\n    .authentication {\n        .card:hover {\n            box-shadow: 0 16px 26px 0 rgba(0, 0, 0, 0.4), 0 3px 6px 0 rgba(0, 0, 0, 0.4);\n        }\n        .form-control:focus {\n            box-shadow: none;\n        }\n        input::placeholder {\n            color: $white-7;\n        }\n    }\n    .wrap-login100 {\n        background: $transparent-theme;\n        box-shadow: 0 3px 9px 0 rgba(28, 28, 51, 0.15);\n    }\n    .login100-form-title {\n        color: $white;\n    }\n    .input100 {\n        color: #dedefd;\n        background: transparent;\n        border-color: $transparent-border;\n    }\n    .symbol-input100,\n    .wrap-input100 input::-webkit-input-placeholder {\n        color: $white-7;\n    }\n    .construction .btn.btn-icon {\n        background: rgba(255, 255, 255, 0.08);\n        color: $white;\n    }\n    /*----- Range slider -------*/\n    .range {\n        background-color: whitesmoke;\n        -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        input[type=\"range\"] {\n            background-color: transparent;\n            &::-webkit-slider-thumb,\n            &::-moz-slider-thumb {\n                color: white;\n                background-color: #999999;\n            }\n        }\n        output {\n            color: white;\n            background-color: #999999;\n        }\n        &.range-success {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $green;\n                }\n            }\n            output {\n                background-color: $green;\n            }\n            input[type=\"range\"] {\n                outline-color: $green;\n            }\n        }\n        &.range-info {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $azure;\n                }\n            }\n            output {\n                background-color: $azure;\n            }\n            input[type=\"range\"] {\n                outline-color: $azure;\n            }\n        }\n        &.range-warning {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #ecb403;\n                }\n            }\n            output {\n                background-color: #ecb403;\n            }\n            input[type=\"range\"] {\n                outline-color: #ecb403;\n            }\n        }\n        &.range-danger {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #c21a1a;\n                }\n            }\n            output {\n                background-color: #c21a1a;\n            }\n            input[type=\"range\"] {\n                outline-color: #c21a1a;\n            }\n        }\n        background-color: whitesmoke;\n        -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        input[type=\"range\"] {\n            background-color: transparent;\n            &::-webkit-slider-thumb,\n            &::-moz-slider-thumb {\n                color: white;\n                background-color: #999999;\n            }\n        }\n        output {\n            color: white;\n            background-color: #999999;\n        }\n        &.range-success {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $green;\n                }\n            }\n            output {\n                background-color: $green;\n            }\n            input[type=\"range\"] {\n                outline-color: $green;\n            }\n        }\n        &.range-info {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $azure;\n                }\n            }\n            output {\n                background-color: $azure;\n            }\n            input[type=\"range\"] {\n                outline-color: $azure;\n            }\n        }\n        &.range-warning {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #ecb403;\n                }\n            }\n            output {\n                background-color: #ecb403;\n            }\n            input[type=\"range\"] {\n                outline-color: #ecb403;\n            }\n        }\n        &.range-danger {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #c21a1a;\n                }\n            }\n            output {\n                background-color: #c21a1a;\n            }\n            input[type=\"range\"] {\n                outline-color: #c21a1a;\n            }\n        }\n        background-color: whitesmoke;\n        -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        input[type=\"range\"] {\n            background-color: transparent;\n            &::-webkit-slider-thumb,\n            &::-moz-slider-thumb {\n                color: white;\n                background-color: #999999;\n            }\n        }\n        output {\n            color: white;\n            background-color: #999999;\n        }\n        &.range-success {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $green;\n                }\n            }\n            output {\n                background-color: $green;\n            }\n            input[type=\"range\"] {\n                outline-color: $green;\n            }\n        }\n        &.range-info {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $azure;\n                }\n            }\n            output {\n                background-color: $azure;\n            }\n            input[type=\"range\"] {\n                outline-color: $azure;\n            }\n        }\n        &.range-warning {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #ecb403;\n                }\n            }\n            output {\n                background-color: #ecb403;\n            }\n            input[type=\"range\"] {\n                outline-color: #ecb403;\n            }\n        }\n        &.range-danger {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #c21a1a;\n                }\n            }\n            output {\n                background-color: #c21a1a;\n            }\n            input[type=\"range\"] {\n                outline-color: #c21a1a;\n            }\n        }\n        background-color: whitesmoke;\n        -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        input[type=\"range\"] {\n            background-color: transparent;\n            &::-webkit-slider-thumb,\n            &::-moz-slider-thumb {\n                color: white;\n                background-color: #999999;\n            }\n        }\n        output {\n            color: white;\n            background-color: #999999;\n        }\n        &.range-success {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $green;\n                }\n            }\n            output {\n                background-color: $green;\n            }\n            input[type=\"range\"] {\n                outline-color: $green;\n            }\n        }\n        &.range-info {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $azure;\n                }\n            }\n            output {\n                background-color: $azure;\n            }\n            input[type=\"range\"] {\n                outline-color: $azure;\n            }\n        }\n        &.range-warning {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #ecb403;\n                }\n            }\n            output {\n                background-color: #ecb403;\n            }\n            input[type=\"range\"] {\n                outline-color: #ecb403;\n            }\n        }\n        &.range-danger {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #c21a1a;\n                }\n            }\n            output {\n                background-color: #c21a1a;\n            }\n            input[type=\"range\"] {\n                outline-color: #c21a1a;\n            }\n        }\n        background-color: whitesmoke;\n        -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        input[type=\"range\"] {\n            background-color: transparent;\n            &::-webkit-slider-thumb,\n            &::-moz-slider-thumb {\n                color: white;\n                background-color: #999999;\n            }\n        }\n        output {\n            color: white;\n            background-color: #999999;\n        }\n        &.range-success {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $green;\n                }\n            }\n            output {\n                background-color: $green;\n            }\n            input[type=\"range\"] {\n                outline-color: $green;\n            }\n        }\n        &.range-info {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $azure;\n                }\n            }\n            output {\n                background-color: $azure;\n            }\n            input[type=\"range\"] {\n                outline-color: $azure;\n            }\n        }\n        &.range-warning {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #ecb403;\n                }\n            }\n            output {\n                background-color: #ecb403;\n            }\n            input[type=\"range\"] {\n                outline-color: #ecb403;\n            }\n        }\n        &.range-danger {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #c21a1a;\n                }\n            }\n            output {\n                background-color: #c21a1a;\n            }\n            input[type=\"range\"] {\n                outline-color: #c21a1a;\n            }\n        }\n    }\n    /*----- Range slider -------*/\n    /*----- Range slider -------*/\n    /*----- Range slider -------*/\n    /*----- Range slider -------*/\n    .ribbon1 {\n        color: $white;\n        &:after {\n            border-left: 20px solid transparent;\n            border-right: 24px solid transparent;\n            border-top: 13px solid #f8463f;\n        }\n        span {\n            background: #f8463f;\n            &:before {\n                background: #f8463f;\n            }\n            &:after {\n                background: #c02031;\n            }\n        }\n    }\n    .ribbon span {\n        color: $white;\n        background: #79a70a;\n        background: linear-gradient(#f8463f 0%, #f8463f 100%);\n        box-shadow: 0 3px 10px -5px black;\n        &::before {\n            border-left: 3px solid #f8463f;\n            border-right: 3px solid transparent;\n            border-bottom: 3px solid transparent;\n            border-top: 3px solid #f8463f;\n        }\n        &::after {\n            border-left: 3px solid transparent;\n            border-right: 3px solid #f8463f;\n            border-bottom: 3px solid transparent;\n            border-top: 3px solid #f8463f;\n        }\n    }\n    /*--- WIZARD ELEMENTS ---*/\n    .wizard {\n        border-color: $transparent-border;\n        background-color: $transparent-theme;\n        > {\n            .steps {\n                a {\n                    color: $primary-1;\n                    &:hover,\n                    &:active {\n                        color: $primary-1;\n                    }\n                    .number,\n                    &:hover .number,\n                    &:active .number {\n                        background-color: #ededf3;\n                    }\n                }\n                .disabled a {\n                    color: $white-7;\n                    &:hover,\n                    &:active {\n                        color: $white-7;\n                    }\n                }\n                .current a {\n                    color: $primary-1;\n                    &:hover,\n                    &:active {\n                        color: $primary-1;\n                    }\n                    .number,\n                    &:hover .number,\n                    &:active .number {\n                        background-color: $white-1;\n                        color: $white;\n                    }\n                }\n                .done a {\n                    color: $success;\n                    &:hover,\n                    &:active {\n                        color: $success;\n                    }\n                    .number,\n                    &:hover .number,\n                    &:active .number {\n                        background-color: $success;\n                        color: $white;\n                    }\n                }\n            }\n            .content {\n                border-top-color: $transparent-border;\n                border-bottom-color: $transparent-border;\n                > {\n                    .title {\n                        color: $default-color;\n                    }\n                    .body input.parsley-error {\n                        border-color: #ff5c77;\n                    }\n                }\n            }\n            .actions {\n                > ul > li:last-child a {\n                    background-color: $success;\n                }\n                a {\n                    background-color: $primary-1;\n                    color: $white;\n                    &:hover,\n                    &:active {\n                        background-color: $primary-1;\n                        color: $white;\n                    }\n                }\n                .disabled a {\n                    background-color: $transparent-body;\n                    color: $white-7;\n                    &:hover,\n                    &:active {\n                        background-color: $transparent-body;\n                        color: $white-7;\n                    }\n                }\n            }\n        }\n    }\n    @media (min-width: 576px) {\n        .wizard.vertical > .content {\n            border-left-color: $transparent-border;\n            border-right-color: $transparent-border;\n        }\n    }\n    @media (min-width: 576px) {\n        .wizard.vertical > .actions {\n            border-left-color: $transparent-border;\n            border-right-color: $transparent-border;\n        }\n    }\n    /****** EQUAL COLUMN WIDTH STEP INDICATOR *****/\n    /***** CUSTOM STYLES *****/\n    .wizard-style-1 > .steps > ul {\n        a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                color: #696e8d;\n                background-color: #f3f7fd;\n            }\n        }\n        .current a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                background-color: $primary-1;\n                color: $white;\n            }\n        }\n        .done a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                background-color: #643ab0;\n                color: $white;\n            }\n        }\n    }\n    .wizard-style-2 > .steps > ul {\n        a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                border: 2px solid #f3f7fd;\n                color: #696e8d;\n                background-color: $transparent-theme;\n            }\n        }\n        .current a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                border-color: $primary-1;\n                color: $primary-1;\n            }\n        }\n        .done a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                border-color: #8c3feb;\n                color: #8c3feb;\n            }\n        }\n    }\n    /*--- WIZARD ELEMENTS ---*/\n    .parsley-required {\n        color: #ff5c77;\n    }\n    .wizard-card .moving-tab {\n        background-color: $primary-1 !important;\n    }\n    .form-group label.control-label {\n        color: $primary-1;\n    }\n    .wizard-card.form-group .form-control {\n        background-image: linear-gradient(#c4c4c4, #c4c4c4, linear-gradient(#e1e1e2, #e1e1e2));\n    }\n    .wizard-container .wizard-navigation {\n        background: #ebeff8;\n    }\n    .wizard-card .choice {\n        &:hover .icon,\n        &.active .icon {\n            border-color: $primary-1 !important;\n            color: $primary-1 !important;\n        }\n    }\n    .widgets-cards .wrp.icon-circle i {\n        color: $white;\n    }\n    /* ######## UTILITIES-STYLES ######## */\n    .bg-secondary {\n        background: $secondary !important;\n    }\n    a.bg-secondary {\n        &:hover,\n        &:focus {\n            background-color: $secondary !important;\n        }\n    }\n    button.bg-secondary {\n        &:hover,\n        &:focus {\n            background-color: $secondary !important;\n        }\n    }\n    .bg-success {\n        background: $success !important;\n    }\n    a.bg-success {\n        &:hover,\n        &:focus {\n            background-color: #15bf42 !important;\n        }\n    }\n    button.bg-success {\n        &:hover,\n        &:focus {\n            background-color: #15bf42 !important;\n        }\n    }\n    .bg-info {\n        background: $info !important;\n    }\n    a.bg-info {\n        &:hover,\n        &:focus {\n            background-color: #1eb0e2 !important;\n        }\n    }\n    button.bg-info {\n        &:hover,\n        &:focus {\n            background-color: #1eb0e2 !important;\n        }\n    }\n    .bg-warning {\n        background: $warning !important;\n    }\n    a.bg-warning {\n        &:hover,\n        &:focus {\n            background-color: #e0a325 !important;\n        }\n    }\n    button.bg-warning {\n        &:hover,\n        &:focus {\n            background-color: #e0a325 !important;\n        }\n    }\n    .bg-danger {\n        background: $danger !important;\n    }\n    a.bg-danger {\n        &:hover,\n        &:focus {\n            background-color: #de223d !important;\n        }\n    }\n    button.bg-danger {\n        &:hover,\n        &:focus {\n            background-color: #de223d !important;\n        }\n    }\n    .bg-light {\n        background-color: $transparent-body !important;\n    }\n    a.bg-light {\n        &:hover,\n        &:focus {\n            background-color: $transparent-theme !important;\n        }\n    }\n    button.bg-light {\n        &:hover,\n        &:focus {\n            background-color: $transparent-theme !important;\n        }\n    }\n    .bg-dark {\n        background-color: $dark !important;\n    }\n    a.bg-dark {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    button.bg-dark {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    .bg-facebook {\n        background: #2b4170 !important;\n    }\n    /*--- gradient-backgrounds --*/\n    .bg-secondary-gradient {\n        background: linear-gradient(to bottom right, #82cff2 0%, #28b7f9 100%) !important;\n    }\n    a.bg-secondary-gradient {\n        &:hover,\n        &:focus {\n            background-color: $secondary !important;\n        }\n    }\n    button.bg-secondary-gradient {\n        &:hover,\n        &:focus {\n            background-color: $secondary !important;\n        }\n    }\n    .bg-success-gradient {\n        background: linear-gradient(to bottom right, #1ea38f 0%, #5cf9e2 100%) !important;\n    }\n    a.bg-success-gradient {\n        &:hover,\n        &:focus {\n            background-color: #448700 !important;\n        }\n    }\n    button.bg-success-gradient {\n        &:hover,\n        &:focus {\n            background-color: #448700 !important;\n        }\n    }\n    .bg-info-gradient {\n        background: linear-gradient(to bottom right, #1e63c3 0%, #00f2fe 100%) !important;\n    }\n    a.bg-info-gradient {\n        &:hover,\n        &:focus {\n            background-color: #1594ef !important;\n        }\n    }\n    button.bg-info-gradient {\n        &:hover,\n        &:focus {\n            background-color: #1594ef !important;\n        }\n    }\n    .bg-warning-gradient {\n        background: linear-gradient(to bottom right, #f66b4e 0%, #fbc434 100%) !important;\n    }\n    a.bg-warning-gradient {\n        &:hover,\n        &:focus {\n            background-color: $yellow !important;\n        }\n    }\n    button.bg-warning-gradient {\n        &:hover,\n        &:focus {\n            background-color: $yellow !important;\n        }\n    }\n    .bg-danger-gradient {\n        background-image: linear-gradient(to bottom right, #b51b35 0%, #fd4a68 100%) !important;\n    }\n    a.bg-danger-gradient {\n        &:hover,\n        &:focus {\n            background-color: #a11918 !important;\n        }\n    }\n    button.bg-danger-gradient {\n        &:hover,\n        &:focus {\n            background-color: #a11918 !important;\n        }\n    }\n    .bg-light-gradient {\n        background-color: #f8f9fa !important;\n    }\n    a.bg-light-gradient {\n        &:hover,\n        &:focus {\n            background-color: #dae0e5 !important;\n        }\n    }\n    button.bg-light-gradient {\n        &:hover,\n        &:focus {\n            background-color: #dae0e5 !important;\n        }\n    }\n    .bg-dark-gradient {\n        background-color: $dark !important;\n    }\n    a.bg-dark-gradient {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    button.bg-dark-gradient {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    .bg-facebook-gradient {\n        background: linear-gradient(to bottom right, #3b5998, #2b4170) !important;\n    }\n    .bg-white {\n        background-color: $transparent-theme !important;\n    }\n    .bg-transparent {\n        background-color: transparent !important;\n    }\n    .bg1 {\n        background: linear-gradient(to right bottom, #163b7c 0%, #548beb 100%);\n    }\n    .bg2 {\n        background: linear-gradient(to bottom right, #00f2fe 0%, #1e63c3 100%) !important;\n    }\n    .bg3 {\n        background: linear-gradient(to bottom right, #f53e31, #dd4b39);\n    }\n    /*------ Background colors -------*/\n    .bg-purple {\n        background: $purple !important;\n        color: $white !important;\n    }\n    a.bg-purple {\n        &:hover,\n        &:focus {\n            background-color: #8c31e4 !important;\n        }\n    }\n    button.bg-purple {\n        &:hover,\n        &:focus {\n            background-color: #8c31e4 !important;\n        }\n    }\n    .bg-blue-lightest {\n        background-color: #edf2fa !important;\n    }\n    a.bg-blue-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c5d5ef !important;\n        }\n    }\n    button.bg-blue-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c5d5ef !important;\n        }\n    }\n    .bg-blue-lighter {\n        background-color: #c8d9f1 !important;\n    }\n    a.bg-blue-lighter {\n        &:hover,\n        &:focus {\n            background-color: #9fbde7 !important;\n        }\n    }\n    button.bg-blue-lighter {\n        &:hover,\n        &:focus {\n            background-color: #9fbde7 !important;\n        }\n    }\n    .bg-blue-light {\n        background-color: #7ea5dd !important;\n    }\n    a.bg-blue-light {\n        &:hover,\n        &:focus {\n            background-color: #5689d2 !important;\n        }\n    }\n    button.bg-blue-light {\n        &:hover,\n        &:focus {\n            background-color: #5689d2 !important;\n        }\n    }\n    .bg-blue-dark {\n        background-color: #3866a6 !important;\n    }\n    a.bg-blue-dark {\n        &:hover,\n        &:focus {\n            background-color: #2b4f80 !important;\n        }\n    }\n    button.bg-blue-dark {\n        &:hover,\n        &:focus {\n            background-color: #2b4f80 !important;\n        }\n    }\n    .bg-blue-darker {\n        background-color: #1c3353 !important;\n    }\n    a.bg-blue-darker {\n        &:hover,\n        &:focus {\n            background-color: #0f1c2d !important;\n        }\n    }\n    button.bg-blue-darker {\n        &:hover,\n        &:focus {\n            background-color: #0f1c2d !important;\n        }\n    }\n    .bg-blue-darkest {\n        background-color: #0e1929 !important;\n    }\n    a.bg-blue-darkest {\n        &:hover,\n        &:focus {\n            background-color: #010203 !important;\n        }\n    }\n    button.bg-blue-darkest {\n        &:hover,\n        &:focus {\n            background-color: #010203 !important;\n        }\n    }\n    .bg-purssianblue {\n        background-color: #362f71;\n    }\n    a.bg-purssianblue-lightest {\n        &:hover,\n        &:focus {\n            background-color: #3f3688 !important;\n        }\n    }\n    button.bg-purssianblue-lightest {\n        &:hover,\n        &:focus {\n            background-color: #3f3688 !important;\n        }\n    }\n    .bg-indigo-lightest {\n        background-color: #f0f1fa !important;\n    }\n    a.bg-indigo-lightest {\n        &:hover,\n        &:focus {\n            background-color: #cacded !important;\n        }\n    }\n    button.bg-indigo-lightest {\n        &:hover,\n        &:focus {\n            background-color: #cacded !important;\n        }\n    }\n    .bg-indigo-lighter {\n        background-color: #d1d5f0 !important;\n    }\n    a.bg-indigo-lighter {\n        &:hover,\n        &:focus {\n            background-color: #abb2e3 !important;\n        }\n    }\n    button.bg-indigo-lighter {\n        &:hover,\n        &:focus {\n            background-color: #abb2e3 !important;\n        }\n    }\n    .bg-indigo-light {\n        background-color: #939edc !important;\n    }\n    a.bg-indigo-light {\n        &:hover,\n        &:focus {\n            background-color: #6c7bd0 !important;\n        }\n    }\n    button.bg-indigo-light {\n        &:hover,\n        &:focus {\n            background-color: #6c7bd0 !important;\n        }\n    }\n    .bg-indigo-dark {\n        background-color: #515da4 !important;\n    }\n    a.bg-indigo-dark {\n        &:hover,\n        &:focus {\n            background-color: #404a82 !important;\n        }\n    }\n    button.bg-indigo-dark {\n        &:hover,\n        &:focus {\n            background-color: #404a82 !important;\n        }\n    }\n    .bg-indigo-darker {\n        background-color: #282e52 !important;\n    }\n    a.bg-indigo-darker {\n        &:hover,\n        &:focus {\n            background-color: #171b30 !important;\n        }\n    }\n    button.bg-indigo-darker {\n        &:hover,\n        &:focus {\n            background-color: #171b30 !important;\n        }\n    }\n    .bg-indigo-darkest {\n        background-color: #141729 !important;\n    }\n    a.bg-indigo-darkest {\n        &:hover,\n        &:focus {\n            background-color: #030407 !important;\n        }\n    }\n    button.bg-indigo-darkest {\n        &:hover,\n        &:focus {\n            background-color: #030407 !important;\n        }\n    }\n    .bg-purple-lightest {\n        background-color: #f6effd !important;\n    }\n    a.bg-purple-lightest {\n        &:hover,\n        &:focus {\n            background-color: #ddc2f7 !important;\n        }\n    }\n    button.bg-purple-lightest {\n        &:hover,\n        &:focus {\n            background-color: #ddc2f7 !important;\n        }\n    }\n    .bg-purple-lighter {\n        background-color: #e4cff9 !important;\n    }\n    a.bg-purple-lighter {\n        &:hover,\n        &:focus {\n            background-color: #cba2f3 !important;\n        }\n    }\n    button.bg-purple-lighter {\n        &:hover,\n        &:focus {\n            background-color: #cba2f3 !important;\n        }\n    }\n    .bg-purple-light {\n        background-color: #c08ef0 !important;\n    }\n    a.bg-purple-light {\n        &:hover,\n        &:focus {\n            background-color: #a761ea !important;\n        }\n    }\n    button.bg-purple-light {\n        &:hover,\n        &:focus {\n            background-color: #a761ea !important;\n        }\n    }\n    .bg-purple-dark {\n        background-color: #844bbb !important;\n    }\n    a.bg-purple-dark {\n        &:hover,\n        &:focus {\n            background-color: #6a3a99 !important;\n        }\n    }\n    button.bg-purple-dark {\n        &:hover,\n        &:focus {\n            background-color: #6a3a99 !important;\n        }\n    }\n    .bg-purple-darker {\n        background-color: #42265e !important;\n    }\n    a.bg-purple-darker {\n        &:hover,\n        &:focus {\n            background-color: #29173a !important;\n        }\n    }\n    button.bg-purple-darker {\n        &:hover,\n        &:focus {\n            background-color: #29173a !important;\n        }\n    }\n    .bg-purple-darkest {\n        background-color: #21132f !important;\n    }\n    a.bg-purple-darkest {\n        &:hover,\n        &:focus {\n            background-color: #08040b !important;\n        }\n    }\n    button.bg-purple-darkest {\n        &:hover,\n        &:focus {\n            background-color: #08040b !important;\n        }\n    }\n    .bg-pink-lightest {\n        background-color: #fef0f5 !important;\n    }\n    a.bg-pink-lightest {\n        &:hover,\n        &:focus {\n            background-color: #fbc0d5 !important;\n        }\n    }\n    button.bg-pink-lightest {\n        &:hover,\n        &:focus {\n            background-color: #fbc0d5 !important;\n        }\n    }\n    .bg-pink-lighter {\n        background-color: #fcd3e1 !important;\n    }\n    a.bg-pink-lighter {\n        &:hover,\n        &:focus {\n            background-color: #f9a3c0 !important;\n        }\n    }\n    button.bg-pink-lighter {\n        &:hover,\n        &:focus {\n            background-color: #f9a3c0 !important;\n        }\n    }\n    .bg-pink-light {\n        background-color: #f999b9 !important;\n    }\n    a.bg-pink-light {\n        &:hover,\n        &:focus {\n            background-color: #f66998 !important;\n        }\n    }\n    button.bg-pink-light {\n        &:hover,\n        &:focus {\n            background-color: #f66998 !important;\n        }\n    }\n    .bg-pink-dark {\n        background-color: #c5577c !important;\n    }\n    a.bg-pink-dark {\n        &:hover,\n        &:focus {\n            background-color: #ad3c62 !important;\n        }\n    }\n    button.bg-pink-dark {\n        &:hover,\n        &:focus {\n            background-color: #ad3c62 !important;\n        }\n    }\n    .bg-pink-darker {\n        background-color: #622c3e !important;\n    }\n    a.bg-pink-darker {\n        &:hover,\n        &:focus {\n            background-color: #3f1c28 !important;\n        }\n    }\n    button.bg-pink-darker {\n        &:hover,\n        &:focus {\n            background-color: #3f1c28 !important;\n        }\n    }\n    .bg-pink-darkest {\n        background-color: #31161f !important;\n    }\n    a.bg-pink-darkest {\n        &:hover,\n        &:focus {\n            background-color: #0e0609 !important;\n        }\n    }\n    button.bg-pink-darkest {\n        &:hover,\n        &:focus {\n            background-color: #0e0609 !important;\n        }\n    }\n    .bg-red-lightest {\n        background-color: #fae9e9 !important;\n    }\n    a.bg-red-lightest {\n        &:hover,\n        &:focus {\n            background-color: #f1bfbf !important;\n        }\n    }\n    button.bg-red-lightest {\n        &:hover,\n        &:focus {\n            background-color: #f1bfbf !important;\n        }\n    }\n    .bg-red-lighter {\n        background-color: #f0bcbc !important;\n    }\n    a.bg-red-lighter {\n        &:hover,\n        &:focus {\n            background-color: #e79292 !important;\n        }\n    }\n    button.bg-red-lighter {\n        &:hover,\n        &:focus {\n            background-color: #e79292 !important;\n        }\n    }\n    .bg-red-light {\n        background-color: #dc6362 !important;\n    }\n    a.bg-red-light {\n        &:hover,\n        &:focus {\n            background-color: #d33a38 !important;\n        }\n    }\n    button.bg-red-light {\n        &:hover,\n        &:focus {\n            background-color: #d33a38 !important;\n        }\n    }\n    .bg-red-dark {\n        background-color: #a41a19 !important;\n    }\n    a.bg-red-dark {\n        &:hover,\n        &:focus {\n            background-color: #781312 !important;\n        }\n    }\n    button.bg-red-dark {\n        &:hover,\n        &:focus {\n            background-color: #781312 !important;\n        }\n    }\n    .bg-red-darker {\n        background-color: #520d0c !important;\n    }\n    a.bg-red-darker {\n        &:hover,\n        &:focus {\n            background-color: #260605 !important;\n        }\n    }\n    button.bg-red-darker {\n        &:hover,\n        &:focus {\n            background-color: #260605 !important;\n        }\n    }\n    .bg-red-darkest {\n        background-color: #290606 !important;\n    }\n    a.bg-red-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-red-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-orange-lightest {\n        background-color: $transparent-theme !important;\n    }\n    a.bg-orange-lightest {\n        &:hover,\n        &:focus {\n            background-color: peachpuff !important;\n        }\n    }\n    button.bg-orange-lightest {\n        &:hover,\n        &:focus {\n            background-color: peachpuff !important;\n        }\n    }\n    .bg-orange-lighter {\n        background-color: #fee0c7 !important;\n    }\n    a.bg-orange-lighter {\n        &:hover,\n        &:focus {\n            background-color: #fdc495 !important;\n        }\n    }\n    button.bg-orange-lighter {\n        &:hover,\n        &:focus {\n            background-color: #fdc495 !important;\n        }\n    }\n    .bg-orange-light {\n        background-color: #feb67c !important;\n    }\n    a.bg-orange-light {\n        &:hover,\n        &:focus {\n            background-color: #fe9a49 !important;\n        }\n    }\n    button.bg-orange-light {\n        &:hover,\n        &:focus {\n            background-color: #fe9a49 !important;\n        }\n    }\n    .bg-orange-dark {\n        background-color: #ca7836 !important;\n    }\n    a.bg-orange-dark {\n        &:hover,\n        &:focus {\n            background-color: #a2602b !important;\n        }\n    }\n    button.bg-orange-dark {\n        &:hover,\n        &:focus {\n            background-color: #a2602b !important;\n        }\n    }\n    .bg-orange-darker {\n        background-color: #653c1b !important;\n    }\n    a.bg-orange-darker {\n        &:hover,\n        &:focus {\n            background-color: #3d2410 !important;\n        }\n    }\n    button.bg-orange-darker {\n        &:hover,\n        &:focus {\n            background-color: #3d2410 !important;\n        }\n    }\n    .bg-orange-darkest {\n        background-color: #331e0e !important;\n    }\n    a.bg-orange-darkest {\n        &:hover,\n        &:focus {\n            background-color: #0b0603 !important;\n        }\n    }\n    button.bg-orange-darkest {\n        &:hover,\n        &:focus {\n            background-color: #0b0603 !important;\n        }\n    }\n    .bg-yellow-lightest {\n        background-color: #fef9e7 !important;\n    }\n    a.bg-yellow-lightest {\n        &:hover,\n        &:focus {\n            background-color: #fcedb6 !important;\n        }\n    }\n    button.bg-yellow-lightest {\n        &:hover,\n        &:focus {\n            background-color: #fcedb6 !important;\n        }\n    }\n    .bg-yellow-lighter {\n        background-color: #fbedb7 !important;\n    }\n    a.bg-yellow-lighter {\n        &:hover,\n        &:focus {\n            background-color: #f8e187 !important;\n        }\n    }\n    button.bg-yellow-lighter {\n        &:hover,\n        &:focus {\n            background-color: #f8e187 !important;\n        }\n    }\n    .bg-yellow-light {\n        background-color: #f5d657 !important;\n    }\n    a.bg-yellow-light {\n        &:hover,\n        &:focus {\n            background-color: #f2ca27 !important;\n        }\n    }\n    button.bg-yellow-light {\n        &:hover,\n        &:focus {\n            background-color: #f2ca27 !important;\n        }\n    }\n    .bg-yellow-dark {\n        background-color: #c19d0c !important;\n    }\n    a.bg-yellow-dark {\n        &:hover,\n        &:focus {\n            background-color: #917609 !important;\n        }\n    }\n    button.bg-yellow-dark {\n        &:hover,\n        &:focus {\n            background-color: #917609 !important;\n        }\n    }\n    .bg-yellow-darker {\n        background-color: #604e06 !important;\n    }\n    a.bg-yellow-darker {\n        &:hover,\n        &:focus {\n            background-color: #302703 !important;\n        }\n    }\n    button.bg-yellow-darker {\n        &:hover,\n        &:focus {\n            background-color: #302703 !important;\n        }\n    }\n    .bg-yellow-darkest {\n        background-color: #302703 !important;\n    }\n    a.bg-yellow-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-yellow-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-green-lightest {\n        background-color: #eff8e6 !important;\n    }\n    a.bg-green-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d6edbe !important;\n        }\n    }\n    button.bg-green-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d6edbe !important;\n        }\n    }\n    .bg-green-lighter {\n        background-color: #cfeab3 !important;\n    }\n    a.bg-green-lighter {\n        &:hover,\n        &:focus {\n            background-color: #b6df8b !important;\n        }\n    }\n    button.bg-green-lighter {\n        &:hover,\n        &:focus {\n            background-color: #b6df8b !important;\n        }\n    }\n    .bg-green-light {\n        background-color: #8ecf4d !important;\n    }\n    a.bg-green-light {\n        &:hover,\n        &:focus {\n            background-color: #75b831 !important;\n        }\n    }\n    button.bg-green-light {\n        &:hover,\n        &:focus {\n            background-color: #75b831 !important;\n        }\n    }\n    .bg-green-dark {\n        background-color: #4b9500 !important;\n    }\n    a.bg-green-dark {\n        &:hover,\n        &:focus {\n            background-color: #316200 !important;\n        }\n    }\n    button.bg-green-dark {\n        &:hover,\n        &:focus {\n            background-color: #316200 !important;\n        }\n    }\n    .bg-green-darker {\n        background-color: #264a00 !important;\n    }\n    a.bg-green-darker {\n        &:hover,\n        &:focus {\n            background-color: #0c1700 !important;\n        }\n    }\n    button.bg-green-darker {\n        &:hover,\n        &:focus {\n            background-color: #0c1700 !important;\n        }\n    }\n    .bg-green-darkest {\n        background-color: #132500 !important;\n    }\n    a.bg-green-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-green-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-teal-lightest {\n        background-color: #eafaf8 !important;\n    }\n    a.bg-teal-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c1f0ea !important;\n        }\n    }\n    button.bg-teal-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c1f0ea !important;\n        }\n    }\n    .bg-teal-lighter {\n        background-color: #bfefea !important;\n    }\n    a.bg-teal-lighter {\n        &:hover,\n        &:focus {\n            background-color: #96e5dd !important;\n        }\n    }\n    button.bg-teal-lighter {\n        &:hover,\n        &:focus {\n            background-color: #96e5dd !important;\n        }\n    }\n    .bg-teal-light {\n        background-color: #6bdbcf !important;\n    }\n    a.bg-teal-light {\n        &:hover,\n        &:focus {\n            background-color: #42d1c2 !important;\n        }\n    }\n    button.bg-teal-light {\n        &:hover,\n        &:focus {\n            background-color: #42d1c2 !important;\n        }\n    }\n    .bg-teal-dark {\n        background-color: #22a295 !important;\n    }\n    a.bg-teal-dark {\n        &:hover,\n        &:focus {\n            background-color: #19786e !important;\n        }\n    }\n    button.bg-teal-dark {\n        &:hover,\n        &:focus {\n            background-color: #19786e !important;\n        }\n    }\n    .bg-teal-darker {\n        background-color: #11514a !important;\n    }\n    a.bg-teal-darker {\n        &:hover,\n        &:focus {\n            background-color: #082723 !important;\n        }\n    }\n    button.bg-teal-darker {\n        &:hover,\n        &:focus {\n            background-color: #082723 !important;\n        }\n    }\n    .bg-teal-darkest {\n        background-color: #092925 !important;\n    }\n    a.bg-teal-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-teal-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-cyan-lightest {\n        background-color: #e8f6f8 !important;\n    }\n    a.bg-cyan-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c1e7ec !important;\n        }\n    }\n    button.bg-cyan-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c1e7ec !important;\n        }\n    }\n    .bg-cyan-lighter {\n        background-color: #b9e3ea !important;\n    }\n    a.bg-cyan-lighter {\n        &:hover,\n        &:focus {\n            background-color: #92d3de !important;\n        }\n    }\n    button.bg-cyan-lighter {\n        &:hover,\n        &:focus {\n            background-color: #92d3de !important;\n        }\n    }\n    .bg-cyan-light {\n        background-color: #5dbecd !important;\n    }\n    a.bg-cyan-light {\n        &:hover,\n        &:focus {\n            background-color: #3aabbd !important;\n        }\n    }\n    button.bg-cyan-light {\n        &:hover,\n        &:focus {\n            background-color: #3aabbd !important;\n        }\n    }\n    .bg-cyan-dark {\n        background-color: #128293 !important;\n    }\n    a.bg-cyan-dark {\n        &:hover,\n        &:focus {\n            background-color: #0c5a66 !important;\n        }\n    }\n    button.bg-cyan-dark {\n        &:hover,\n        &:focus {\n            background-color: #0c5a66 !important;\n        }\n    }\n    .bg-cyan-darker {\n        background-color: #09414a !important;\n    }\n    a.bg-cyan-darker {\n        &:hover,\n        &:focus {\n            background-color: #03191d !important;\n        }\n    }\n    button.bg-cyan-darker {\n        &:hover,\n        &:focus {\n            background-color: #03191d !important;\n        }\n    }\n    .bg-cyan-darkest {\n        background-color: #052025 !important;\n    }\n    a.bg-cyan-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-cyan-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-white-lightest {\n        background-color: white !important;\n    }\n    a.bg-white-lightest {\n        &:hover,\n        &:focus {\n            background-color: $transparent-theme !important;\n        }\n    }\n    button.bg-white-lightest {\n        &:hover,\n        &:focus {\n            background-color: $transparent-theme !important;\n        }\n    }\n    .bg-white-lighter {\n        background-color: white !important;\n    }\n    a.bg-white-lighter {\n        &:hover,\n        &:focus {\n            background-color: $transparent-theme !important;\n        }\n    }\n    button.bg-white-lighter {\n        &:hover,\n        &:focus {\n            background-color: $transparent-theme !important;\n        }\n    }\n    .bg-white-light {\n        background-color: white !important;\n    }\n    a.bg-white-light {\n        &:hover,\n        &:focus {\n            background-color: $transparent-theme !important;\n        }\n    }\n    button.bg-white-light {\n        &:hover,\n        &:focus {\n            background-color: $transparent-theme !important;\n        }\n    }\n    .bg-white-dark {\n        background-color: #cccccc !important;\n    }\n    a.bg-white-dark {\n        &:hover,\n        &:focus {\n            background-color: #b3b2b2 !important;\n        }\n    }\n    button.bg-white-dark {\n        &:hover,\n        &:focus {\n            background-color: #b3b2b2 !important;\n        }\n    }\n    .bg-white-darker {\n        background-color: #666666 !important;\n    }\n    a.bg-white-darker {\n        &:hover,\n        &:focus {\n            background-color: #4d4c4c !important;\n        }\n    }\n    button.bg-white-darker {\n        &:hover,\n        &:focus {\n            background-color: #4d4c4c !important;\n        }\n    }\n    .bg-white-darkest {\n        background-color: #333333 !important;\n    }\n    a.bg-white-darkest {\n        &:hover,\n        &:focus {\n            background-color: #1a1919 !important;\n        }\n    }\n    button.bg-white-darkest {\n        &:hover,\n        &:focus {\n            background-color: #1a1919 !important;\n        }\n    }\n    .bg-gray-lightest {\n        background-color: #f3f4f5 !important;\n    }\n    a.bg-gray-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d7dbde !important;\n        }\n    }\n    button.bg-gray-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d7dbde !important;\n        }\n    }\n    .bg-gray-lighter {\n        background-color: #dbdde0 !important;\n    }\n    a.bg-gray-lighter {\n        &:hover,\n        &:focus {\n            background-color: #c0c3c8 !important;\n        }\n    }\n    button.bg-gray-lighter {\n        &:hover,\n        &:focus {\n            background-color: #c0c3c8 !important;\n        }\n    }\n    .bg-gray-light {\n        background-color: #aab0b6 !important;\n    }\n    a.bg-gray-light {\n        &:hover,\n        &:focus {\n            background-color: #8f979e !important;\n        }\n    }\n    button.bg-gray-light {\n        &:hover,\n        &:focus {\n            background-color: #8f979e !important;\n        }\n    }\n    .bg-gray-dark {\n        background-color: #6b7278 !important;\n        background: $dark !important;\n    }\n    a.bg-gray-dark {\n        &:hover,\n        &:focus {\n            background-color: #53585d !important;\n        }\n    }\n    button.bg-gray-dark {\n        &:hover,\n        &:focus {\n            background-color: #53585d !important;\n        }\n    }\n    .bg-gray-darker {\n        background-color: #36393c !important;\n    }\n    a.bg-gray-darker {\n        &:hover,\n        &:focus {\n            background-color: #1e2021 !important;\n        }\n    }\n    button.bg-gray-darker {\n        &:hover,\n        &:focus {\n            background-color: #1e2021 !important;\n        }\n    }\n    .bg-gray-darkest {\n        background-color: #1b1c1e !important;\n    }\n    a.bg-gray-darkest {\n        &:hover,\n        &:focus {\n            background-color: #030303 !important;\n        }\n    }\n    button.bg-gray-darkest {\n        &:hover,\n        &:focus {\n            background-color: #030303 !important;\n        }\n    }\n    .bg-gray-dark-lightest {\n        background-color: #ebebec !important;\n    }\n    a.bg-gray-dark-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d1d1d3 !important;\n        }\n    }\n    button.bg-gray-dark-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d1d1d3 !important;\n        }\n    }\n    .bg-gray-dark-lighter {\n        background-color: #c2c4c6 !important;\n    }\n    a.bg-gray-dark-lighter {\n        &:hover,\n        &:focus {\n            background-color: #a8abad !important;\n        }\n    }\n    button.bg-gray-dark-lighter {\n        &:hover,\n        &:focus {\n            background-color: #a8abad !important;\n        }\n    }\n    .bg-gray-dark-light {\n        background-color: #717579 !important;\n    }\n    a.bg-gray-dark-light {\n        &:hover,\n        &:focus {\n            background-color: #585c5f !important;\n        }\n    }\n    button.bg-gray-dark-light {\n        &:hover,\n        &:focus {\n            background-color: #585c5f !important;\n        }\n    }\n    .bg-gray-dark-dark {\n        background-color: #2a2e33 !important;\n    }\n    a.bg-gray-dark-dark {\n        &:hover,\n        &:focus {\n            background-color: #131517 !important;\n        }\n    }\n    button.bg-gray-dark-dark {\n        &:hover,\n        &:focus {\n            background-color: #131517 !important;\n        }\n    }\n    .bg-gray-dark-darker {\n        background-color: #15171a !important;\n    }\n    a.bg-gray-dark-darker {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-gray-dark-darker {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-gray-dark-darkest {\n        background-color: #0a0c0d !important;\n    }\n    a.bg-gray-dark-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-gray-dark-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-azure-lightest {\n        background-color: #ecf7fe !important;\n    }\n    a.bg-azure-lightest {\n        &:hover,\n        &:focus {\n            background-color: #bce3fb !important;\n        }\n    }\n    button.bg-azure-lightest {\n        &:hover,\n        &:focus {\n            background-color: #bce3fb !important;\n        }\n    }\n    .bg-azure-lighter {\n        background-color: #c7e6fb !important;\n    }\n    a.bg-azure-lighter {\n        &:hover,\n        &:focus {\n            background-color: #97d1f8 !important;\n        }\n    }\n    button.bg-azure-lighter {\n        &:hover,\n        &:focus {\n            background-color: #97d1f8 !important;\n        }\n    }\n    .bg-azure-light {\n        background-color: #7dc4f6 !important;\n    }\n    a.bg-azure-light {\n        &:hover,\n        &:focus {\n            background-color: #4daef3 !important;\n        }\n    }\n    button.bg-azure-light {\n        &:hover,\n        &:focus {\n            background-color: #4daef3 !important;\n        }\n    }\n    .bg-azure-dark {\n        background-color: #3788c2 !important;\n    }\n    a.bg-azure-dark {\n        &:hover,\n        &:focus {\n            background-color: #2c6c9a !important;\n        }\n    }\n    button.bg-azure-dark {\n        &:hover,\n        &:focus {\n            background-color: #2c6c9a !important;\n        }\n    }\n    .bg-azure-darker {\n        background-color: #1c4461 !important;\n    }\n    a.bg-azure-darker {\n        &:hover,\n        &:focus {\n            background-color: #112839 !important;\n        }\n    }\n    button.bg-azure-darker {\n        &:hover,\n        &:focus {\n            background-color: #112839 !important;\n        }\n    }\n    .bg-azure-darkest {\n        background-color: #0e2230 !important;\n    }\n    a.bg-azure-darkest {\n        &:hover,\n        &:focus {\n            background-color: #020609 !important;\n        }\n    }\n    button.bg-azure-darkest {\n        &:hover,\n        &:focus {\n            background-color: #020609 !important;\n        }\n    }\n    .bg-lime-lightest {\n        background-color: #f2fbeb !important;\n    }\n    a.bg-lime-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d6f3c1 !important;\n        }\n    }\n    button.bg-lime-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d6f3c1 !important;\n        }\n    }\n    .bg-lime-lighter {\n        background-color: #d7f2c2 !important;\n    }\n    a.bg-lime-lighter {\n        &:hover,\n        &:focus {\n            background-color: #bbe998 !important;\n        }\n    }\n    button.bg-lime-lighter {\n        &:hover,\n        &:focus {\n            background-color: #bbe998 !important;\n        }\n    }\n    .bg-lime-light {\n        background-color: #a3e072 !important;\n    }\n    a.bg-lime-light {\n        &:hover,\n        &:focus {\n            background-color: #88d748 !important;\n        }\n    }\n    button.bg-lime-light {\n        &:hover,\n        &:focus {\n            background-color: #88d748 !important;\n        }\n    }\n    .bg-lime-dark {\n        background-color: #62a82a !important;\n    }\n    a.bg-lime-dark {\n        &:hover,\n        &:focus {\n            background-color: #4a7f20 !important;\n        }\n    }\n    button.bg-lime-dark {\n        &:hover,\n        &:focus {\n            background-color: #4a7f20 !important;\n        }\n    }\n    .bg-lime-darker {\n        background-color: #315415 !important;\n    }\n    a.bg-lime-darker {\n        &:hover,\n        &:focus {\n            background-color: #192b0b !important;\n        }\n    }\n    button.bg-lime-darker {\n        &:hover,\n        &:focus {\n            background-color: #192b0b !important;\n        }\n    }\n    .bg-lime-darkest {\n        background-color: #192a0b !important;\n    }\n    a.bg-lime-darkest {\n        &:hover,\n        &:focus {\n            background-color: #010200 !important;\n        }\n    }\n    button.bg-lime-darkest {\n        &:hover,\n        &:focus {\n            background-color: #010200 !important;\n        }\n    }\n    .bg-blue-1 {\n        background-color: #0061da;\n        color: $white !important;\n    }\n    .bg-blue {\n        background: $blue;\n        color: $white !important;\n    }\n    a.bg-blue {\n        &:hover,\n        &:focus {\n            background-color: #4032f1 !important;\n        }\n    }\n    button.bg-blue {\n        &:hover,\n        &:focus {\n            background-color: #4032f1 !important;\n        }\n    }\n    .bg-indigo {\n        background: $indigo;\n        color: $white !important;\n    }\n    a.bg-indigo {\n        &:hover,\n        &:focus {\n            background-color: #3f51c1 !important;\n        }\n    }\n    button.bg-indigo {\n        &:hover,\n        &:focus {\n            background-color: #3f51c1 !important;\n        }\n    }\n    .bg-purple-gradient {\n        background: linear-gradient(to bottom right, $purple 0%, #647dee 100%) !important;\n        color: $white !important;\n    }\n    a.bg-purple-gradient {\n        &:hover,\n        &:focus {\n            background-color: #8c31e4 !important;\n        }\n    }\n    button.bg-purple-gradient {\n        &:hover,\n        &:focus {\n            background-color: #8c31e4 !important;\n        }\n    }\n    .bg-pink {\n        background: $pink !important;\n        color: $white !important;\n    }\n    .bg-darkpink {\n        background-color: #14a485 !important;\n        color: $white !important;\n    }\n    a.bg-pink {\n        &:hover,\n        &:focus {\n            background-color: #f33d7a !important;\n        }\n    }\n    button.bg-pink {\n        &:hover,\n        &:focus {\n            background-color: #f33d7a !important;\n        }\n    }\n    .bg-red {\n        background: $red;\n        color: $white !important;\n    }\n    a.bg-red {\n        &:hover,\n        &:focus {\n            background-color: #a11918 !important;\n        }\n    }\n    button.bg-red {\n        &:hover,\n        &:focus {\n            background-color: #a11918 !important;\n        }\n    }\n    .bg-orange {\n        background: $orange;\n        color: $white !important;\n    }\n    a.bg-orange {\n        &:hover,\n        &:focus {\n            background-color: #fc7a12 !important;\n        }\n    }\n    button.bg-orange {\n        &:hover,\n        &:focus {\n            background-color: #fc7a12 !important;\n        }\n    }\n    .bg-yellow-1 {\n        background-color: $yellow;\n        color: $white !important;\n    }\n    .bg-yellow {\n        background: $yellow;\n        color: $white !important;\n    }\n    a.bg-yellow {\n        &:hover,\n        &:focus {\n            background-color: #c29d0b !important;\n        }\n    }\n    button.bg-yellow {\n        &:hover,\n        &:focus {\n            background-color: #c29d0b !important;\n        }\n    }\n    .bg-green-1 {\n        background-color: $green;\n        color: $white !important;\n    }\n    .bg-green {\n        background: $green;\n        color: $white !important;\n    }\n    a.bg-green {\n        &:hover,\n        &:focus {\n            background-color: #448700 !important;\n        }\n    }\n    button.bg-green {\n        &:hover,\n        &:focus {\n            background-color: #448700 !important;\n        }\n    }\n    .bg-teal {\n        background: $teal !important;\n    }\n    a.bg-teal {\n        &:hover,\n        &:focus {\n            background-color: #22a193 !important;\n        }\n    }\n    button.bg-teal {\n        &:hover,\n        &:focus {\n            background-color: #22a193 !important;\n        }\n    }\n    .bg-cyan {\n        background: $cyan !important;\n        color: $white !important;\n    }\n    a.bg-cyan {\n        &:hover,\n        &:focus {\n            background-color: #117a8b !important;\n        }\n    }\n    button.bg-cyan {\n        &:hover,\n        &:focus {\n            background-color: #117a8b !important;\n        }\n    }\n    a.bg-white {\n        &:hover,\n        &:focus {\n            background-color: $transparent-theme !important;\n        }\n    }\n    button.bg-white {\n        &:hover,\n        &:focus {\n            background-color: $transparent-theme !important;\n        }\n    }\n    .bg-gray {\n        background: $gray !important;\n    }\n    a.bg-gray {\n        &:hover,\n        &:focus {\n            background-color: #6c757d !important;\n        }\n    }\n    button.bg-gray {\n        &:hover,\n        &:focus {\n            background-color: #6c757d !important;\n        }\n    }\n    .bg-lightpink-red {\n        color: #ff7088 !important;\n    }\n    a.bg-gray-dark {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    button.bg-gray-dark {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    .bg-azure {\n        background: $azure;\n    }\n    a.bg-azure {\n        &:hover,\n        &:focus {\n            background-color: #1594ef !important;\n        }\n    }\n    button.bg-azure {\n        &:hover,\n        &:focus {\n            background-color: #1594ef !important;\n        }\n    }\n    .bg-purple-1 {\n        background: $purple;\n    }\n    .bg-lime {\n        background: $lime;\n    }\n    a.bg-lime {\n        &:hover,\n        &:focus {\n            background-color: #63ad27 !important;\n        }\n    }\n    button.bg-lime {\n        &:hover,\n        &:focus {\n            background-color: #63ad27 !important;\n        }\n    }\n    .bg-square {\n        color: $white;\n        background: #868e96;\n    }\n    .bg-primary-light {\n        background: $transparent-theme;\n    }\n    .bg-google-plus {\n        background-color: #dd4b39;\n    }\n    .bg-pinterest {\n        background: linear-gradient(to right bottom, #c51629 0%, #bd081c 100%);\n    }\n    .bg-light-gray {\n        background-color: $transparent-theme;\n    }\n    .bg-progress-white {\n        background-color: #eaeceb;\n    }\n    .bg-dribbble {\n        background: linear-gradient(to bottom right, #ea4c89, #c93764) !important;\n    }\n    .bg-google {\n        background: linear-gradient(to bottom right, #e64522, #c33219) !important;\n        color: $white;\n    }\n    /*--bg-transparents--*/\n    .bg-success-transparent {\n        background-color: rgba(0, 230, 130, 0.1) !important;\n    }\n    .bg-info-transparent {\n        background-color: #123d68 !important;\n    }\n    .bg-warning-transparent {\n        background-color: rgba(255, 162, 43, 0.1) !important;\n    }\n    .bg-danger-transparent {\n        background-color: rgba(255, 56, 43, 0.1) !important;\n    }\n    .bg-pink-transparent {\n        background-color: rgba(213, 109, 252, 0.1) !important;\n    }\n    .bg-purple-transparent {\n        background-color: rgba(96, 77, 216, 0.1) !important;\n    }\n    .bg-dark-transparent {\n        background-color: rgba(0, 0, 0, 0.15) !important;\n    }\n    .bg-white-transparent {\n        background-color: rgba(255, 255, 255, 0.2) !important;\n    }\n    .bg-secondary-transparent {\n        background-color: rgba(5, 195, 251, 0.1) !important;\n    }\n    .shadow-primary {\n        box-shadow: 0 7px 30px $primary-03 !important;\n    }\n    .shadow-secondary {\n        box-shadow: 0 7px 30px rgba(130, 207, 242, 0.1) !important;\n    }\n    .shadow-warning {\n        box-shadow: 0 7px 30px rgba(251, 176, 52, 0.1) !important;\n    }\n    .shadow-info {\n        box-shadow: 0 7px 30px rgba(40, 146, 235, 0.5) !important;\n    }\n    .shadow-success {\n        box-shadow: 0 7px 30px rgba(26, 122, 16, 0.1) !important;\n    }\n    .shadow-danger {\n        box-shadow: 0 7px 30px rgba(245, 167, 184, 0.1) !important;\n    }\n    .google-plus1 {\n        background: linear-gradient(to right bottom, #dd4b39 0%, #ef6a5a 100%);\n    }\n    .pinterest1 {\n        background: linear-gradient(to right bottom, #bd081c 0%, #eb4553 100%);\n    }\n    .bg-default {\n        background: #e9e9f1;\n        color: $default-color;\n    }\n    /*--Box-shadow--*/\n    .border {\n        border-color: $transparent-border !important;\n        border-color: $transparent-border;\n    }\n    .border-top {\n        border-top-color: $transparent-border !important;\n    }\n    .border-end {\n        border-right-color: $transparent-border !important;\n    }\n    .border-bottom {\n        border-bottom-color: $transparent-border !important;\n    }\n    .border-start {\n        border-left-color: $transparent-border !important;\n    }\n    .border-secondary {\n        border: 1px solid $secondary !important;\n    }\n    .border-success {\n        border-color: #1bbfa7 !important;\n    }\n    .border-info {\n        border-color: $azure !important;\n    }\n    .border-warning {\n        border-color: #ecb403 !important;\n    }\n    .border-danger {\n        border-color: #f82649 !important;\n    }\n    .border-pink {\n        border-color: $pink !important;\n    }\n    .border-orange {\n        border-color: $orange !important;\n    }\n    .border-light {\n        border-color: #f8f9fa !important;\n    }\n    .border-transparent {\n        border-color: #656a71 !important;\n    }\n    .border-dark {\n        border-color: $dark !important;\n    }\n    .border-white {\n        border-color: $white !important;\n    }\n    .border-end-1 {\n        border-right-color: #d5dce3;\n    }\n    /*------- Alignments & values-------*/\n    .text-white-transparent,\n    .text-white-transparent-1 {\n        color: $white !important;\n    }\n    a.text-primary {\n        &:hover,\n        &:focus {\n            color: $primary-1 !important;\n        }\n    }\n    .text-secondary {\n        color: $secondary !important;\n    }\n    a.text-secondary {\n        &:hover,\n        &:focus {\n            color: $secondary !important;\n        }\n    }\n    .text-success {\n        color: $success !important;\n    }\n    a.text-success {\n        &:hover,\n        &:focus {\n            color: #448700 !important;\n        }\n    }\n    .text-info {\n        color: #538ed7 !important;\n    }\n    a.text-info {\n        &:hover,\n        &:focus {\n            color: #1594ef !important;\n        }\n    }\n    .alert-info {\n        color: #538ed7 !important;\n    }\n    .alert-info .alert-link {\n        color: #79b6ff;\n    }\n    .text-warning {\n        color: $warning !important;\n    }\n    a.text-warning {\n        &:hover,\n        &:focus {\n            color: #c29d0b !important;\n        }\n    }\n    .text-danger {\n        color: $danger !important;\n    }\n    a.text-danger {\n        &:hover,\n        &:focus {\n            color: #d22827 !important;\n        }\n    }\n    .text-light {\n        color: #ebedef !important;\n    }\n    a.text-light {\n        &:hover,\n        &:focus {\n            color: #dae0e5 !important;\n        }\n    }\n    .text-dark {\n        color: $white !important;\n    }\n    a.text-dark {\n        &:hover,\n        &:focus {\n            color: $primary-1 !important;\n        }\n    }\n    .text-body {\n        color: #495057 !important;\n    }\n    .text-muted {\n        color: $white-7 !important;\n    }\n    .text-black-50 {\n        color: rgba(0, 0, 0, 0.5) !important;\n    }\n    .text-white-50 {\n        color: rgba(255, 255, 255, 0.5) !important;\n    }\n    .text-hide {\n        color: transparent;\n        background-color: transparent;\n    }\n    /*----- Typography ------*/\n    .heading-inverse {\n        background-color: #333;\n        color: $white;\n    }\n    .heading-success {\n        background-color: #1643a3;\n        color: $white;\n    }\n    .heading-info {\n        background-color: $azure;\n        color: $white;\n    }\n    .heading-warning {\n        background-color: #ecb403;\n        color: $white;\n    }\n    .heading-danger {\n        background-color: #c21a1a;\n        color: $white;\n    }\n    .text-inherit {\n        color: inherit !important;\n    }\n    .text-default {\n        color: $white !important;\n    }\n    .text-muted-dark {\n        color: #1c232f !important;\n    }\n    .text-fb-blue {\n        color: #234684 !important;\n    }\n    .text-blue {\n        color: #467fcf !important;\n    }\n    .text-indigo {\n        color: $indigo !important;\n    }\n    .text-purple {\n        color: #867efc !important;\n    }\n    .text-lightpink-red {\n        color: #ff7088 !important;\n    }\n    .text-lightgreen {\n        color: #26eda2 !important;\n    }\n    .text-pink {\n        color: #ec82ef !important;\n    }\n    .text-red {\n        color: #c21a1a !important;\n    }\n    .text-orange {\n        color: $orange !important;\n    }\n    .text-yellow {\n        color: #ecb403 !important;\n    }\n    .text-green {\n        color: $green !important;\n    }\n    .text-green-1 {\n        color: #0dff01 !important;\n    }\n    .text-teal {\n        color: #2bcbba !important;\n    }\n    .text-cyan {\n        color: #17a2b8 !important;\n    }\n    .text-white {\n        color: $white !important;\n    }\n    .text-gray {\n        color: #969696 !important;\n    }\n    .text-gray-dark {\n        color: $dark !important;\n    }\n    .text-azure {\n        color: $azure !important;\n    }\n    .text-lime {\n        color: $lime !important;\n    }\n    .text-transparent {\n        color: #332525;\n    }\n    .text-facebook {\n        color: #3b5998;\n    }\n    .text-google-plus {\n        color: #dd4b39;\n    }\n    .text-twitter {\n        color: #1da1f2;\n    }\n    .text-pinterest {\n        color: #bd081c;\n    }\n    .text-secondary-gradient {\n        background: linear-gradient(to bottom right, #82cff2 0%, #28b7f9 100%);\n        -webkit-text-fill-color: transparent;\n        background: linear-gradient(to right, #cd489c 0%, #ce4ba4 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    .text-info-gradient {\n        background: linear-gradient(to right bottom, #1e63c3 0%, #00f2fe 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    .text-success-gradient {\n        background: linear-gradient(to bottom right, #1ea38f 0%, #5cf9e2 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    .text-facebook-gradient {\n        background: linear-gradient(to right bottom, #1e3c72 0%, #3d6cbf 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    .text-twitter-gradient {\n        background: linear-gradient(to right bottom, #1e63c3 0%, #00f2fe 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    .text-google-plus-gradient {\n        background: linear-gradient(to right bottom, #dd4b39 0%, #ef6a5a 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    /*--text-shadow--*/\n    .text-success-shadow {\n        text-shadow: 0 5px 10px rgba(19, 191, 166, 0.3) !important;\n    }\n    .text-info-shadow {\n        text-shadow: 0 5px 10px rgba(7, 116, 248, 0.3) !important;\n    }\n    .text-warning-shadow {\n        text-shadow: 0 5px 10px rgba(255, 162, 43, 0.3) !important;\n    }\n    .text-danger-shadow {\n        text-shadow: 0 5px 10px rgba(255, 56, 43, 0.3) !important;\n    }\n    .text-pink-shadow {\n        text-shadow: 0 5px 10px rgba(213, 109, 252, 0.3) !important;\n    }\n    .text-purple-shadow {\n        text-shadow: 0 5px 10px rgba(96, 77, 216, 0.3) !important;\n    }\n    .text-dark-shadow {\n        text-shadow: 0 5px 10px rgba(0, 0, 0, 0.3) !important;\n    }\n    .text-white-shadow {\n        text-shadow: 0 5px 10px rgba(255, 255, 255, 0.3) !important;\n    }\n    .text-secondary-shadow {\n        text-shadow: 0 5px 10px rgba(130, 207, 242, 0.3);\n    }\n\n    .alert-success {\n        hr {\n            border-top-color: $success;\n        }\n    }\n\n    .alert-info {\n        hr {\n            border-top-color: $info;\n        }\n    }\n\n    .alert-warning {\n        hr {\n            border-top-color: $warning;\n        }\n    }\n\n    .alert-danger {\n        hr {\n            border-top-color: $danger;\n        }\n    }\n}\n\n.transparent-mode .sidebar {\n    background: $primary-1;\n    border-color: $transparent-border;\n    box-shadow: 0px 8px 14.72px 1.28px rgba(42, 38, 53, 0.5);\n}\n\n.transparent-mode {\n    .light-layout {\n        display: none;\n    }\n    .dark-layout {\n        display: block;\n    }\n}\n\n.transparent-mode .slide.is-expanded a {\n    color: $white-8;\n}\n\n.transparent-mode .select2-container--default .select2-results__option[aria-selected=\"true\"] {\n    background-color: #30304d;\n}\n\n.transparent-mode .select2-container--default .select2-results > .select2-results__options {\n    box-shadow: 0px 8px 14.72px 1.28px rgba(34, 34, 61, 0.8);\n}\n\n.transparent-mode .select2-dropdown {\n    background-color: $transparent-body;\n    border-color: rgba(255, 255, 255, 0.1);\n}\n\n.transparent-mode .side-header .header-brand-img.desktop-logo {\n    display: block !important;\n}\n\n.transparent-mode .side-header .header-brand-img.light-logo1 {\n    display: none !important;\n}\n\n.transparent-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open\n    .app-sidebar\n    .side-header\n    .header-brand-img.light-logo1 {\n    display: none !important;\n}\n\n.transparent-mode .header-brand .header-brand-img.logo-3 {\n    display: none;\n}\n\n.transparent-mode .header-brand .header-brand-img.logo {\n    display: block;\n}\n\n.transparent-mode.sidebar-mini .app-header .light-logo1 {\n    display: none !important;\n}\n\n.transparent-mode.sidenav-toggled .header-brand-img.light-logo {\n    display: none !important;\n}\n\n.transparent-mode.hover-submenu.sidenav-toggled.sidenav-toggled-open .header-brand-img.light-logo {\n    display: none !important;\n}\n\n.transparent-mode.sidebar-mini.sidenav-toggled .app-sidebar .side-header .header-brand-img.desktop-logo {\n    display: none !important;\n}\n\n.transparent-mode.sidebar-mini.sidenav-toggled.sidenav-toggled-open\n    .app-sidebar\n    .side-header\n    .header-brand-img.desktop-logo {\n    display: block !important;\n}\n\n.transparent-mode.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open\n    .app-sidebar\n    .side-header\n    .header-brand-img.desktop-logo {\n    display: none !important;\n}\n\n.transparent-mode.hover-submenu.sidebar-mini.sidenav-toggled.sidenav-toggled-open\n    .app-sidebar\n    .side-header\n    .header-brand-img.desktop-logo {\n    display: none !important;\n}\n\n.transparent-mode.sidebar-mini.sidenav-toggled .app-sidebar .side-header .header-brand-img.toggle-logo {\n    display: block;\n}\n\n.transparent-mode.sidebar-mini.sidenav-toggled.sidenav-toggled-open\n    .app-sidebar\n    .side-header\n    .header-brand-img.toggle-logo {\n    display: none;\n}\n\n.transparent-mode.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open\n    .app-sidebar\n    .side-header\n    .header-brand-img.toggle-logo {\n    display: block;\n}\n\n.transparent-mode.hover-submenu.sidebar-mini.sidenav-toggled.sidenav-toggled-open\n    .side-header\n    .header-brand-img.light-logo {\n    display: none !important;\n}\n\n.transparent-mode.hover-submenu.sidebar-mini.sidenav-toggled.sidenav-toggled-open\n    .app-sidebar\n    .side-header\n    .header-brand-img.toggle-logo {\n    display: block;\n}\n\n.transparent-mode.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open\n    .side-header\n    .header-brand-img.light-logo {\n    display: none !important;\n}\n\n@media (max-width: 991px) {\n    .transparent-mode {\n        .app-header.header .header-brand-img.desktop-logo {\n            display: block;\n        }\n    }\n}\n\n//FULL CALENDAR\n.transparent-mode {\n    #external-events {\n        border-color: $transparent-border;\n    }\n    .fc-theme-standard td,\n    .fc-theme-standard th {\n        border-color: $transparent-border;\n    }\n    .fc-theme-standard .fc-scrollgrid {\n        border: 0px solid $transparent-border;\n        border-top: 1px solid $transparent-border;\n        border-left: 1px solid $transparent-border;\n    }\n    .fc .fc-daygrid-day-number {\n        color: $white;\n    }\n    .fc .fc-daygrid-day.fc-day-today {\n        background-color: $transparent-theme;\n    }\n    .fc-theme-standard {\n        .fc-list {\n            border-color: $transparent-border;\n        }\n        .fc-list-day-cushion {\n            background: $transparent-body;\n        }\n    }\n    .fc .fc-list-event:hover td {\n        background: $transparent-body;\n    }\n    .fc-list-event.fc-event {\n        color: $white !important;\n    }\n    .fc-direction-ltr .fc-list-day-text,\n    .fc-direction-rtl .fc-list-day-side-text {\n        color: $white;\n    }\n    .fc-direction-ltr .fc-list-day-side-text,\n    .fc-direction-rtl .fc-list-day-text {\n        color: $white;\n    }\n}\n\n//SWEET ALERT\n.transparent-mode {\n    .sweet-alert {\n        background: $transparent-body;\n    }\n    .sweet-alert {\n        h2 {\n            color: $white;\n        }\n        P {\n            color: $white-7;\n        }\n    }\n    .alert-default {\n        background-color: $transparent-body;\n    }\n}\n\n//RANGE SLIDER\n.transparent-mode {\n    .irs-line-mid,\n    .irs-line-right,\n    .irs-line-left {\n        background-color: $transparent-body;\n    }\n    .irs-from,\n    .irs-to,\n    .irs-single {\n        background: $transparent-body;\n    }\n    .irs-from,\n    .irs-to,\n    .irs-single {\n        color: $white;\n    }\n    .irs-min,\n    .irs-max {\n        color: $white;\n        background: $transparent-body;\n    }\n    .irs-grid-text {\n        color: $white-7;\n    }\n    .irs-modern .irs-slider,\n    .irs-outline .irs-slider {\n        background-color: $transparent-body;\n    }\n    .irs-bar {\n        background: $primary-1;\n    }\n    .irs-slider:before {\n        background-color: $primary-1;\n    }\n    .irs-outline .irs-line {\n        border-color: $primary-1;\n    }\n}\n\n.transparent-mode {\n    .tree {\n        li {\n            color: $white;\n            a {\n                color: $white;\n            }\n            &.branch li {\n                background: transparent;\n            }\n        }\n        ul {\n            &:before {\n                border-left-color: $white-4;\n            }\n            li:before {\n                border-top-color: $white-4;\n            }\n        }\n    }\n    .dTree a {\n        color: $white;\n    }\n}\n\n//TABS\n.transparent-mode {\n    .tab_wrapper {\n        .content_wrapper {\n            border-color: $transparent-border;\n            .accordian_header {\n                border-bottom-color: $transparent-border;\n                border-top-color: $transparent-border;\n            }\n            .accordian_header .arrow {\n                background: transparent;\n                border-top-color: $white-3;\n                border-left-color: $white-3;\n            }\n            .accordian_header.active {\n                border-color: $transparent-border;\n            }\n        }\n        &.right_side {\n            .content_wrapper {\n                border-color: $transparent-border;\n            }\n            > ul li {\n                &.active {\n                    border-color: $transparent-border;\n                }\n                &.active::before {\n                    background: $transparent-border;\n                }\n                border-left-color: $transparent-border;\n                &:after {\n                    background: rgba(255, 255, 255, 0.1);\n                }\n            }\n        }\n        > ul {\n            border-bottom-color: $transparent-border !important;\n        }\n        > ul li {\n            border-color: $transparent-border;\n            &.active:after {\n                background: transparent;\n            }\n        }\n    }\n}\n\n//FILE UPLOAD\n.transparent-mode {\n    .dropify-wrapper {\n        color: $white;\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n        .dropify-preview {\n            background-color: $transparent-theme;\n        }\n    }\n    .dropify-wrapper .dropify-message span.file-icon {\n        color: $white-7;\n    }\n    .dropify-wrapper:hover {\n        background-image: linear-gradient(\n            -45deg,\n            $transparent-body 25%,\n            transparent 25%,\n            transparent 50%,\n            $transparent-body 50%,\n            $transparent-body 75%,\n            transparent 75%,\n            transparent\n        );\n    }\n    .ff_fileupload_wrap .ff_fileupload_dropzone {\n        &:focus,\n        &:active {\n            background-color: $transparent-theme;\n            border-color: $transparent-border;\n        }\n    }\n    .ff_fileupload_wrap .ff_fileupload_dropzone {\n        border-color: $transparent-border;\n        background-color: $transparent-theme;\n        &:hover {\n            background-color: $transparent-theme;\n            border-color: $transparent-border;\n        }\n    }\n    .sp-replacer {\n        border-color: $transparent-border;\n        background: $transparent-body;\n        color: $white;\n        &:hover,\n        &.sp-active {\n            border-color: $transparent-border;\n            color: $white;\n        }\n    }\n    .sp-container {\n        background: $transparent-body;\n        box-shadow: 0 10px 40px 0 rgba(34, 34, 61, 0.8);\n        border-color: $transparent-border;\n    }\n    .sp-picker-container {\n        border-left: 0;\n    }\n    .ui-timepicker-wrapper {\n        background: $transparent-body;\n        border-color: rgba(255, 255, 255, 0.07);\n        box-shadow: 0 16px 18px 0 #0e0f2e;\n    }\n    .ui-timepicker-list li {\n        color: rgba(255, 255, 255, 0.8);\n    }\n    .datepicker {\n        .datepicker-switch,\n        td,\n        th {\n            color: $white !important;\n        }\n    }\n    .datepicker .datepicker-switch:hover,\n    .datepicker .prev:hover,\n    .datepicker .next:hover,\n    .datepicker tfoot tr th:hover {\n        color: $white-7 !important;\n    }\n    .datepicker-dropdown.datepicker-orient-top {\n        &:after,\n        &:before {\n            border-top-color: $transparent-body;\n        }\n    }\n    .datepicker table tr td span.old,\n    .datepicker table tr td span.new {\n        color: $white-7;\n    }\n    .SumoSelect {\n        > .CaptionCont {\n            border-color: $transparent-border;\n            background-color: transparent;\n            color: $white-7;\n        }\n        &.disabled > .CaptionCont {\n            border-color: $transparent-border;\n            background: $transparent-theme;\n            border-radius: 0;\n        }\n    }\n    .SumoSelect {\n        &.open > .optWrapper {\n            background: $transparent-body;\n        }\n        > .optWrapper {\n            border-color: $transparent-border;\n            > .options li.opt {\n                border-bottom-color: $transparent-border;\n                &:hover {\n                    background-color: #262641;\n                }\n            }\n        }\n    }\n    .SumoSelect {\n        &.open .search-txt {\n            background: $transparent-theme;\n            color: $white-7;\n        }\n        .select-all {\n            background-color: $transparent-theme;\n            border-bottom-color: $transparent-border;\n        }\n        > .optWrapper {\n            > .MultiControls {\n                border-top: 1px solid rgba(255, 255, 255, 0.12);\n                background-color: $transparent-theme;\n            }\n            &.multiple > .MultiControls > p:hover {\n                background-color: #393958;\n            }\n        }\n    }\n    datepicker-dropdown {\n        &.datepicker-orient-top {\n            &:after,\n            &:before {\n                border-top: 7px solid #3e3e50;\n            }\n        }\n        &:after,\n        &:before {\n            border-bottom-color: #2e2e4a;\n        }\n    }\n    .datepicker table tr td span {\n        &:hover,\n        &.focused {\n            background: $transparent-theme !important;\n            color: #dedefd !important;\n        }\n    }\n    .datepicker .prev,\n    .datepicker .next {\n        background: $transparent-theme;\n    }\n    .datepicker .prev:hover,\n    .datepicker .next:hover {\n        background: $transparent-theme !important;\n        color: $white-7 !important;\n    }\n    .datepicker-dropdown:after {\n        border-bottom-color: $transparent-border;\n    }\n    .datepicker-dropdown::before {\n        border-bottom-color: $transparent-border;\n    }\n    .ms-choice {\n        color: $white-7;\n        background-color: transparent;\n        border-color: $transparent-border;\n        &.disabled {\n            background-color: $transparent-theme;\n            border-color: $transparent-border;\n        }\n    }\n    .ms-drop {\n        &.bottom {\n            box-shadow: 0px 8px 14.72px 1.28px rgba(34, 34, 61, 0.8);\n        }\n        color: rgba(255, 255, 255, 0.7);\n        background-color: $transparent-body;\n        border-color: $transparent-border;\n    }\n    .select2-dropdown {\n        background-color: $transparent-body;\n        border-color: $transparent-border;\n    }\n    .select2-container--default {\n        .select2-results__option[aria-selected=\"true\"] {\n            background-color: $transparent-theme;\n        }\n        &.select2-container--disabled .select2-selection--single {\n            background-color: $transparent-theme;\n        }\n    }\n    .ms-search input {\n        background: $transparent-theme;\n        border-color: $transparent-border;\n    }\n    .transfer-double {\n        background-color: transparent;\n    }\n    .transfer-double-content-tabs {\n        .tab-item-name.tab-active {\n            background: $transparent-theme;\n        }\n        border-bottom-color: $transparent-border;\n    }\n    .transfer-double-content-left,\n    .transfer-double-content-right {\n        border-color: $transparent-border;\n    }\n    .transfer-double-list-footer {\n        border-top-color: $transparent-border;\n    }\n    .transfer-double-list-search-input {\n        border-color: $transparent-border;\n        background-color: $transparent-theme;\n    }\n    .transfer-double-list-main::-webkit-scrollbar-track {\n        background-color: $transparent-theme;\n    }\n    .checkbox-group label:before {\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n    }\n    .transfer-double-content-param {\n        border-bottom-color: $transparent-border;\n    }\n    .transfer-double-selected-list-search-input {\n        border-color: $transparent-border;\n        background-color: $transparent-theme;\n    }\n    .btn-select-arrow {\n        color: $white;\n        background: $transparent-theme;\n        border-color: $transparent-border;\n    }\n    .multi-wrapper {\n        .search-input {\n            border-bottom-color: $transparent-border;\n            background: transparent;\n        }\n        border-color: $transparent-border;\n        .item-1 {\n            color: $white;\n            background: $transparent-theme;\n        }\n        .selected-wrapper,\n        .item-group-1 .group-label {\n            background: $transparent-theme;\n        }\n    }\n    .multi-wrapper .non-selected-wrapper {\n        background: $transparent-theme;\n        border-right-color: $transparent-border;\n    }\n    .iti input {\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n        color: $white;\n        &[type=\"tel\"],\n        &[type=\"text\"] {\n            border-color: $transparent-border;\n            background-color: $transparent-theme;\n            border-right-color: $transparent-border;\n            color: $white;\n        }\n    }\n    .iti__selected-flag {\n        border-right-color: $transparent-border;\n    }\n    .iti--allow-dropdown .iti__flag-container:hover .iti__selected-flag {\n        background-color: $transparent-theme;\n        border-top-color: $transparent-border;\n    }\n    .iti__country-list {\n        border-color: $transparent-border;\n        background-color: $transparent-body;\n        border-right-color: $transparent-border;\n    }\n    .iti__country.iti__highlight {\n        background-color: $transparent-theme;\n    }\n    .iti__divider {\n        border-bottom-color: $transparent-border;\n    }\n}\n\n//SUMMERNOTE\n.transparent-mode {\n    .note-editor.note-frame.panel.panel-default .panel-heading {\n        background-color: transparent;\n        border-bottom-color: $transparent-border;\n    }\n    .note-editor.note-frame.panel.panel-default.fullscreen .panel-heading.note-toolbar {\n        background-color: $transparent-body;\n    }\n    .note-btn.btn-default {\n        background-color: transparent;\n        border-color: $transparent-border;\n        color: $white;\n    }\n    .panel-default.fullscreen .note-editable {\n        background-color: $transparent-body;\n    }\n    .note-editor.note-airframe,\n    .note-editor.note-frame {\n        border-color: $transparent-border;\n    }\n    .note-editor .btn-default:not(:disabled):not(.disabled):active {\n        background-color: $transparent-theme;\n        border-color: $transparent-border;\n        color: $white;\n    }\n    .form-control-file::-webkit-file-upload-button {\n        background-color: $transparent-body;\n        color: $white;\n        border-color: $transparent-border;\n    }\n    .note-editor.note-airframe .note-editing-area .note-codable,\n    .note-editor.note-frame .note-editing-area .note-codable {\n        background-color: $transparent-body;\n        color: $white-7;\n    }\n}\n\n//RICHTEXT\n.transparent-mode {\n    .richText {\n        .richText-editor {\n            background-color: transparent;\n            border-left-color: rgba(255, 255, 255, 0);\n        }\n        .richText-toolbar ul {\n            border-bottom-color: $transparent-border;\n        }\n        .richText-toolbar ul li a {\n            color: $white;\n            &:hover {\n                background-color: $transparent-theme;\n            }\n            .richText-dropdown-outer .richText-dropdown {\n                background-color: $transparent-body;\n                border-color: $transparent-border;\n                .richText-dropdown-close {\n                    background: $transparent-body;\n                    color: $white-7;\n                }\n            }\n        }\n        .richText-form {\n            input {\n                &[type=\"text\"],\n                &[type=\"file\"],\n                &[type=\"number\"] {\n                    border-color: $transparent-border;\n                    background: $transparent-theme;\n                    color: $white;\n                }\n            }\n            select {\n                border-color: $transparent-border;\n                background: $transparent-theme;\n                color: $white;\n            }\n        }\n        .richText-toolbar ul li a .richText-dropdown-outer ul.richText-dropdown li a {\n            &:hover {\n                background: $transparent-theme;\n            }\n            border-bottom-color: $transparent-border;\n        }\n        .richText-undo,\n        .richText-redo {\n            border-right-color: $transparent-border;\n        }\n    }\n}\n\n//QUILL EDITOR\n.transparent-mode {\n    .ql-toolbar.ql-snow {\n        .ql-picker-label {\n            border-color: $transparent-border;\n            background: $transparent-body;\n            color: $white;\n        }\n        border-color: $transparent-border;\n    }\n    .ql-container.ql-snow {\n        border-color: $transparent-border;\n    }\n    .ql-snow {\n        &.ql-toolbar button,\n        .ql-toolbar button {\n            border-color: $transparent-border;\n            background: $transparent-body;\n            color: $white;\n        }\n        &.ql-toolbar button:last-child,\n        .ql-toolbar button:last-child {\n            border-right-color: $transparent-border;\n        }\n    }\n    .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {\n        background-color: $transparent-body;\n    }\n    .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {\n        border-color: $transparent-border;\n    }\n    .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {\n        border-color: $transparent-border;\n    }\n    .ql-snow .ql-formats {\n        color: $white;\n    }\n    .ql-snow .ql-tooltip {\n        background-color: $transparent-body;\n        border-color: $transparent-border;\n        box-shadow: 0px 0px 5px $transparent-theme;\n        color: $white;\n        margin-left: 100px;\n    }\n    .ql-snow .ql-stroke {\n        stroke: $white-5;\n    }\n    .ql-snow.ql-toolbar button:hover,\n    .ql-snow .ql-toolbar button:hover,\n    .ql-snow.ql-toolbar button:focus,\n    .ql-snow .ql-toolbar button:focus,\n    .ql-snow.ql-toolbar button.ql-active,\n    .ql-snow .ql-toolbar button.ql-active,\n    .ql-snow.ql-toolbar .ql-picker-label:hover,\n    .ql-snow .ql-toolbar .ql-picker-label:hover,\n    .ql-snow.ql-toolbar .ql-picker-label.ql-active,\n    .ql-snow .ql-toolbar .ql-picker-label.ql-active,\n    .ql-snow.ql-toolbar .ql-picker-item:hover,\n    .ql-snow .ql-toolbar .ql-picker-item:hover,\n    .ql-snow.ql-toolbar .ql-picker-item.ql-selected,\n    .ql-snow .ql-toolbar .ql-picker-item.ql-selected {\n        color: $white-7;\n    }\n    .ql-tooltip.ql-editing {\n        input {\n            &[type=\"text\"],\n            &[type=\"file\"],\n            &[type=\"number\"] {\n                border-color: $transparent-border;\n                background: $transparent-theme;\n                color: $white;\n            }\n        }\n    }\n    .ql-bubble .ql-tooltip {\n        background-color: $transparent-theme;\n    }\n}\n\n//FORM WIZARD\n.transparent-mode {\n    .sw-theme-dots {\n        > ul.step-anchor {\n            background: transparent;\n            border-color: $transparent-border;\n            &:before {\n                background-color: $transparent-body;\n            }\n            > li > a:before {\n                background: $transparent-body;\n            }\n            > li.active > a::after {\n                background: $white-7;\n            }\n            > li.done > a {\n                color: $white;\n            }\n        }\n        .step-content {\n            background-color: transparent;\n        }\n        .sw-toolbar {\n            background: transparent;\n        }\n    }\n    .wizard {\n        border-color: $transparent-border;\n        background-color: transparent;\n        border-radius: 3px;\n        > {\n            .content {\n                border-top-color: $transparent-border;\n                border-bottom-color: $transparent-border;\n                > .title {\n                    color: #dedefd;\n                }\n            }\n            .actions .disabled a {\n                background-color: $white-1;\n                color: rgba(255, 255, 255, 0.62);\n            }\n            .steps {\n                .current a {\n                    .number,\n                    &:active .number,\n                    &:hover .number {\n                        background-color: $primary-1;\n                    }\n                }\n                a {\n                    .number,\n                    &:active .number,\n                    &:hover .number {\n                        background-color: $white-1;\n                    }\n                }\n            }\n        }\n        &.vertical > {\n            .content,\n            .actions {\n                border-left-color: $transparent-border;\n            }\n        }\n        > .steps {\n            .done a {\n                .number,\n                &:active .number,\n                &:hover .number {\n                    background-color: #0dcd94;\n                }\n            }\n            a {\n                .number,\n                &:active .number,\n                &:hover .number {\n                    background-color: $white-1;\n                }\n            }\n        }\n    }\n}\n\n//OWL CAROUSEL\n.transparent-mode {\n    .owl-nav button {\n        background: $transparent-body !important;\n        border-color: $transparent-border !important;\n    }\n}\n\n//CHARTS\n.transparent-mode {\n    .flot-text {\n        color: $white-7 !important;\n    }\n    tspan {\n        fill: $white-7 !important;\n    }\n    .nvd3 {\n        text {\n            fill: $white-7;\n        }\n        .nv-axis line {\n            stroke: rgba(119, 119, 142, 0.2);\n        }\n        .nv-discretebar .nv-groups text,\n        .nv-multibarHorizontal .nv-groups text {\n            fill: $white;\n        }\n    }\n    .countdown li {\n        background: $transparent-body;\n        border: 5px solid $transparent-border;\n        color: $white;\n    }\n}\n\n.transparent-mode .app-sidebar.sidemenu-scroll .side-header {\n    background: $primary-1 !important;\n}\n\n.transparent-mode .bg-recentorder {\n    background: linear-gradient(to right, var(--transparent-body) 0%, tranparent 100%), url(\"../images/media/bg2.jpg\");\n}\n\n.transparent-mode .bg-recentorder:before {\n    background: none;\n}\n\n.rtl.transparent-mode {\n    .app-sidebar {\n        border-left-color: rgba(255, 255, 255, 0.1);\n    }\n    .side-header {\n        border-left-color: rgba(255, 255, 255, 0.1);\n    }\n    .vtimeline .timeline-wrapper .timeline-panel:after {\n        border-right: 10px solid rgba(255, 255, 255, 0.1);\n        border-left: 0 solid rgba(255, 255, 255, 0.1);\n    }\n    .vtimeline .timeline-wrapper.timeline-inverted .timeline-panel:after {\n        border-right: 0 !important;\n        border-left: 10px solid rgba(255, 255, 255, 0.1) !important;\n        right: -10px !important;\n        left: auto;\n    }\n    .notification .notification-body:before {\n        border-left-color: rgba(0, 0, 0, 0.2) !important;\n        border-right-color: transparent !important;\n    }\n    .border-end {\n        border-left-color: rgba(255, 255, 255, 0.1) !important;\n        border-right: transparent !important;\n    }\n    .iti__selected-flag {\n        border-left-color: rgba(255, 255, 255, 0.2);\n        border-right-color: inherit;\n    }\n    .card-group{\n        .card.border-end{\n            border-left: 1px solid $transparent-border !important;\n            border-right: inherit !important;\n        }\n    }\n}\n\n.rtl.transparent-mode .main-content-body-chat {\n    .border-start-0 {\n        border-left-color: $transparent-border !important;\n    }\n}\n.transparent-mode .buttons-columnVisibility.dropdown-item:hover,\n.transparent-mode .buttons-columnVisibility.dropdown-item:focus,\n.transparent-mode .buttons-columnVisibility.dropdown-item.active,\n.transparent-mode .buttons-columnVisibility.dropdown-item:active {\n    color: $white;\n}\n.transparent-mode .dataTables_wrapper .selected {\n    background: $transparent-body;\n}\n.transparent-mode .dropdown-item:hover,\n.transparent-mode .dropdown-item:focus,\n.transparent-mode .dropdown-item.active,\n.transparent-mode .dropdown-item:active {\n    color: $white;\n}\n.transparent-mode .list-group-item a {\n    color: rgba(255, 255, 255, 0.7);\n}\n.transparent-mode .container-login100 {\n    .text-primary {\n        color: rgba(255, 255, 255, 0.6) !important;\n    }\n    .tab-content i,\n    .tabs-menu2 ul li .active {\n        color: $white;\n    }\n    .social-login i {\n        color: $white;\n    }\n    .validate-form.login100-form {\n        a {\n            color: rgb(255, 255, 255) !important;\n        }\n    }\n}\n@media (max-width: 991px) {\n    .transparent-mode.sidebar-gone.sidenav-toggled.sidebar-mini .side-menu {\n        margin-top: 0px !important;\n    }\n    .transparent-mode .navresponsive-toggler span {\n        color: $white !important;\n    }\n}\n.transparent-mode.light-menu .hor-header.header {\n    border-bottom-color: rgba(233, 237, 244, 0.2);\n}\n@media (max-width: 991px) {\n    .transparent-mode.header-light .navresponsive-toggler span {\n        color: $primary-1 !important;\n    }\n}\n.transparent-mode .onoffswitch2-label {\n    border: 1px solid $transparent-border;\n    background-color: $transparent-body;\n}\n.transparent-mode .onoffswitch2-label:before {\n    border: 1px solid $transparent-border;\n    background-color: $transparent-theme;\n}\n.transparent-mode .theme-layout.nav-link-bg.layout-setting {\n    display: none;\n}\n\n@media (max-width: 991px) {\n    .app.sidebar-mini.transparent-mode.header-light {\n        .side-header .header-brand-img.light-logo1 {\n            display: none !important;\n        }\n    }\n    .app.sidebar-mini.transparent-mode.header-light {\n        .side-header .header-brand-img.desktop-logo {\n            display: block !important;\n        }\n    }\n    .app.sidebar-mini.transparent-mode.light-menu.header-light {\n        .side-header .header-brand-img.light-logo1 {\n            display: block !important;\n        }\n    }\n    .app.sidebar-mini.transparent-mode.light-menu.header-light {\n        .side-header .header-brand-img.desktop-logo {\n            display: none !important;\n        }\n    }\n    .app.sidebar-mini.sidebar-gone.transparent-mode.light-menu.header-light.sidenav-toggled {\n        .side-header .header-brand-img.light-logo1 {\n            display: none !important;\n        }\n    }\n}\n\n.transparent-mode {\n    .fc-theme-standard .fc-popover {\n        border: 1px solid rgba(255, 255, 255, 0.1);\n        background: $transparent-body;\n    }\n    .fc-theme-standard .fc-popover-header {\n        background: $transparent-theme;\n    }\n}\n.transparent-mode .card-aside.color-card-radius {\n    .card-header {\n        border-bottom-color: transparent;\n    }\n}\n.transparent-mode\n    .ff_fileupload_wrap\n    table.ff_fileupload_uploads\n    td.ff_fileupload_summary\n    .ff_fileupload_filename\n    input {\n    background: transparent;\n    color: $white;\n}\n.transparent-mode\n    .ff_fileupload_wrap\n    table.ff_fileupload_uploads\n    td.ff_fileupload_preview\n    .ff_fileupload_preview_image {\n    background-color: $transparent-theme;\n}\n.transparent-mode .ff_fileupload_wrap table.ff_fileupload_uploads td.ff_fileupload_actions button {\n    border-color: $transparent-border;\n    background-color: $transparent-theme;\n}\n.transparent-mode {\n    .dropify-wrapper .dropify-preview .dropify-render img {\n        background-color: transparent;\n    }\n    &.rtl .table.border-dashed thead th:last-child {\n        border-left-color: transparent;\n    }\n    &.rtl .table thead th:last-child {\n        border-left-color: $transparent-border;\n    }\n    .fc .fc-list-event:hover td {\n        color: $white;\n    }\n    #navbar-example2 {\n        .nav-link {\n            color: $white;\n        }\n    }\n    &.icontext-menu .side-menu__item:hover .side-menu__icon,\n    &.icontext-menu .side-menu__item:hover .side-menu__label,\n    &.icontext-menu .side-menu__item:focus .side-menu__icon,\n    &.icontext-menu .side-menu__item:focus .side-menu__label {\n        color: $white !important;\n    }\n    &.hover-submenu .slide-menu a.active {\n        color: $white;\n        .sub-side-menu__label {\n            color: $white;\n        }\n    }\n    &.hover-submenu1 .slide-menu a.active {\n        color: $white;\n        .sub-side-menu__label {\n            color: $white;\n        }\n    }\n    &.horizontal .slide-menu a.active {\n        color: $white;\n        .sub-side-menu__label {\n            color: $white;\n        }\n    }\n}\n.transparent-mode {\n    &.hover-submenu,\n    &.hover-submenu1 {\n        &.sidenav-toggled {\n            .side-menu {\n                height: 100vh;\n            }\n        }\n        .side-menu {\n            height: 150vh;\n        }\n    }\n    .qty {\n        &:focus {\n            background: transparent;\n        }\n    }\n}\n.transparent-mode .card-transparent {\n    &.bg-primary-transparent,\n    &.bg-secondary-transparent,\n    &.bg-info-transparent,\n    &.bg-success-transparent,\n    &.bg-warning-transparent,\n    &.bg-danger-transparent {\n        background-color: $black-2 !important;\n    }\n}\n.transparent-mode {\n    .btn-primary-light {\n        color: $white;\n        background: $primary-06 !important;\n        border-color: $primary-06 !important;\n    }\n    .multi-wrapper .search-input {\n        &::placeholder {\n            color: $white-8;\n        }\n    }\n    .handle-counter{\n        input{\n            background-color: transparent;\n            border: 1px solid $transparent-border;\n            color: $text-color;\n        }\n    }\n    .card-group{\n        .card.border-end{\n            border-right: 1px solid $transparent-border !important;\n        }\n    }\n}\n@media screen and(max-width:991px) {\n    .transparent-mode.horizontal .horizontal-main .slide .slide-menu,\n    .transparent-mode.horizontal .horizontal-main .slide .sub-slide-menu,\n    .transparent-mode.horizontal .horizontal-main .slide .sub-slide-menu2 {\n        box-shadow: none !important;\n    }\n}\n"]}