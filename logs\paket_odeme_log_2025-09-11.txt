=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-11 10:47:31
İşlem No: ORDER******************
Ödeme ID: 4100
Kullanıcı ID: 3833
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4950,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => ebc77dc1-9fcc-459c-95d7-a171ecdf6a87
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=ebc77dc1-9fcc-459c-95d7-a171ecdf6a87" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER******************
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-11 11:19:24
İşlem No: ORDER202509111119233950
Ödeme ID: 4101
Kullanıcı ID: 1800
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4950,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 22cad65d-4907-42f3-9d97-3f8783ad30bb
            [UCD_HTML] => <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="tr" xmlns="http://www.w3.org/1999/xhtml">
<head>
<script type="text/javascript" src="/ruxitagentjs_ICA27NVfqrux_10263230921131557.js" data-dtconfig="rid=RID_-**********|rpid=-**********|domain=denizbank.com|reportUrl=/rb_d0ac9e6b-b11a-4747-9900-8db5d8d1e8c3|app=600f804e2f46f262|cuc=w2s3jly9|owasp=1|mel=100000|featureHash=ICA27NVfqrux|dpvc=1|lastModification=*************|tp=500,50,0,1|rdnt=1|uxrgce=1|srbbv=2|agentUri=/ruxitagentjs_ICA27NVfqrux_10263230921131557.js"></script></head>
<body>
<form id="form1" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="POST">
<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=22cad65d-4907-42f3-9d97-3f8783ad30bb" />
<script type="text/javascript">
 document.getElementById("form1").submit(); 
</script>
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509111119233950
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
