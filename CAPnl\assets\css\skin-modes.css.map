{"version": 3, "sources": ["../scss/_variables.scss", "skin-modes.scss"], "names": [], "mappings": "AAKA;AAqCA;AASA;AAeA;AAaA;AAOA;AAQA;AC5FA;AAEA;AAGI;EACI,OD0BD;;ACzBC;EACI;EACA;;AAGR;EACI;;;AAKJ;EACI;EACA;;AAEJ;AAAA;EAEI,YDnBG;ECoBH;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AACA;EACI;;AACA;EAEI;;AAIZ;EACI;;AAGA;EACI;EACA;EACA,ODlBL;;ACsBC;EACI;EACA;;AAEJ;EACI;EACA;;AAGR;EACI;;AACA;EACI,ODlCL;;ACsCC;EACI;;AAGR;EACI;;;AAIR;EAGY;IACI;;EAEJ;IACI;;;AAOhB;EAEQ;AAAA;IAEI;;EAEJ;IACI;;EAEJ;IACI;;EACA;IACI,ODzET;;;AC+EP;EAGY;IACI;;EAEJ;IACI;;EAKA;IACI;;EAEJ;IACI;IACA;;;AAOpB;EAGY;IAEI;;EAEJ;IACI;;;AAOhB;EAEQ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;;AAMR;AAAA;EAEI,YDrKG;;ACuKP;EACI;EACA,OD9ID;;;ACkJP;AAGI;AAAA;EAEI;EACA;;AAEJ;EACI;;AAEJ;EACI;;AACA;EACI,YD3LD;;AC4LC;EAEI,YD9LL;;ACkMP;EACI;;AAEJ;AAAA;EAEI;;AAEJ;EACI,OD/KD;;ACiLH;EACI;;AAEJ;EACI;EACA,ODtLD;ECuLC;;AAGA;EACI,OD3LL;EC4LK;;AAEJ;EACI,OD/LL;ECgMK;;AAIJ;EACI;;AAEJ;EACI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI,ODlND;;ACoNH;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI,YDvKM;;;AC2Kd;EAGY;IACI;;EAGJ;IACI;;EAKA;IACI;;EAEJ;IACI;IACA;;EAIZ;IACI;;EAEJ;IACI;;;AAKZ;EAGY;IACI;;EAEJ;IACI;;;AAOhB;EAGY;IAEI;;EAEJ;IACI;;;AAOhB;EACI;;;AAGJ;AAGI;AAAA;EAEI,YDvSD;ECwSC;;AAEJ;EACI;;AAGA;EACI;;AACA;EAEI;;AAIZ;EACI;;AAEJ;EACI;EACA;EACA,OD3TD;;AC6TH;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI,ODjRM;;ACmRV;EACI,ODtWG;;ACyWH;EACI;;;AAKZ;EACI;IACI;;;AAIR;EAEQ;IACI;;EAGA;IACI;;EAEJ;IACI;;EAGR;IACI;;EAEJ;IACI;;EAEJ;IACI;;;AAKZ;EAGY;IACI;;EAGJ;IAGI;;;AAMhB;EAGY;IAEI;;EAEJ;IACI;;;AAOhB;AAGI;EACI;;AAEJ;AAAA;EAEI;EACA;;AAEJ;EACI,OD9ZD;;AC+ZC;EACI;EACA;;AAGR;EACI;;AACA;EACI;;AACA;EAEI;;AAIZ;EACI;;AAEJ;EACI,ODlbD;;ACobH;EACI;;AAEJ;EACI;;AAGA;EACI;EACA;EACA,OD9bL;;ACgcC;EACI;EACA;;AAGR;EACI;;AACA;EACI,ODxcL;;AC2cH;EACI,OD5cD;;AC8cH;EACI;;AAEJ;EACI;;;AAIR;EAEQ;AAAA;IAEI;;EAEJ;IACI;;EAEJ;IACI;;EACA;IACI,ODleT;;ECqeC;IACI;;EAEJ;IACI;;;AAKZ;EAGY;IACI;;EAEJ;IACI;;EAIJ;IACI;;EAEJ;IACI;;;AAMhB;EAGY;IACI;;EAGJ;IAGI;;;AAMhB;EAGY;IACI;;EAEJ;IACI;;EAGR;IACI;;EAII;IACI;;EAEJ;IACI;IACA;IACA;;EAKZ;IACI;;EAEJ;IACI;;;AAKZ;EAGY;IAEI;;EAEJ;IACI;;;AAQZ;AAAA;EAEI;;AAEJ;EACI;EACA,OD5kBD;;;ACglBP;AAEA;AAGI;EACI,YDtlBD;;ACulBC;EACI;;AAEJ;EACI;;AAGA;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGR;EACI;;AACA;AAAA;EAEI;;AAIZ;AAAA;EAEI,ODlnBD;;ACqnBC;EAEI;;AAGR;EACI;;AAEJ;EAII;;AAEJ;EAII;;AAEJ;EAII;;AAEJ;EAII;;AAEJ;EACI;;;AAKJ;EACI;;AACA;EACI;;AAGA;EACI;;AAEJ;EACI;;AAKR;EAEI;;;AAKZ;EAEQ;IACI;IACA;;EAGA;IACI;IACA;;EAGJ;IACI;;EAKA;IACI;;EAMJ;IACI;IACA;;EAGJ;IACI;;EAIZ;IACI;;EAEJ;IACI;;;EAIJ;IACI;IACA;IACA;;EAEJ;IACI;;EAEJ;AAAA;IAEI;;EAGA;IACI;IACA;;EAGJ;IACI;;EAKA;IACI;;EAMJ;IACI;IACA;;EAGJ;IACI;;;AAOpB;EAII;;;AAGJ;EAII;;;AAGJ;EAII;;;AAGJ;EAII;;;AAGJ;AAGI;EACI;;AAEJ;EACI,YDt0BG;ECu0BH;;AACA;EACI,OD9yBL;EC+yBK;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI;EACA;;AAEJ;AAAA;EAEI;EACA;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;AAAA;EAGI;EACA;;AAEJ;EACI;;AACA;EACI,ODh1BT;;ACk1BK;EACI;EACA;;AAGR;EACI;;AAGR;EAGI,kBDz3BG;EC03BH,cDtyBK;;ACwyBT;EACI;;AAEJ;AAAA;AAAA;EAGI;;AAEJ;AAAA;EAEI,OD32BD;;;AC+2BP;EAEQ;IACI;IACA;;EAGA;IACI;IACA;;EAEJ;IACI;;EAKA;IACI;;EAEJ;IACI;;EAEJ;IAGI;;EAMJ;IACI;IACA;;EAEJ;IACI;;EAEJ;IAEI;;EAMJ;IACI;;EAEJ;IACI;;EAMJ;IACI;;EAEJ;IACI;;EAMJ;IACI;;EAEJ;IACI;;EAMJ;IACI;;EAEJ;IACI;;EAKR;IACI;IACA,ODv8BT;;ECy8BK;IAEI;;EAEJ;IAEI;;EAGA;IAEI;;EAIZ;IACI,ODz9BL;;;AC+9BH;EACI,YD3/BG;;AC4/BH;EACI,ODl+BL;ECm+BK;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI,OD7+BD;EC8+BC;;AAEJ;EACI,ODj/BD;ECk/BC;;AAGA;EAEI;EACA;;AAIJ;AAAA;EAEI;EACA;;AAGR;EACI;EACA;;;AAIR;EACI;IACI;IACA;;;AAIR;AAGI;EACI;EACA;;AACA;EACI,ODthCL;ECuhCK;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;AAAA;EAEI;EACA;;AAEJ;AAAA;EAEI;EACA;;AAEJ;AAAA;AAAA;EAGI;EACA;;AAEJ;AAAA;AAAA;AAAA;EAII;;AAEJ;EACI;;AACA;EACI,ODtkCT;;ACwkCK;EACI;EACA;;AAIZ;EACI,YDxhCM;;AC0hCV;EACI,ODllCD;;AColCH;EACI;;AAEJ;EAGI,kBDniCM;ECoiCN,cDliCK;;;ACsiCb;EAEQ;IACI;IACA;;EAGA;IACI;IACA;;EAEJ;IACI;;EAKA;IACI;;EAEJ;IACI;;EAMJ;IACI;IACA;;EAEJ;IACI;;EAMJ;IACI;;EAMJ;IACI;;EAMJ;IACI;;EAEJ;IACI;;EAMJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAMJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;;AAOpB;AAGI;EACI;;AACA;EACI,OD9rCL;EC+rCK;;AAEJ;EACI,YD7tCD;EC8tCC;EACA;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;AAAA;AAAA;AAAA;EAII;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;AAAA;EAEI;EACA;;AAEJ;AAAA;EAEI;EACA;;AAEJ;AAAA;AAAA;EAGI;EACA;;AAEJ;EACI;;AACA;EACI,OD3vCT;;AC6vCK;EACI;;AAGR;EACI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAIR;EAEQ;IACI;IACA;;EAGA;IACI;IACA;;EAEJ;IACI;;EAKA;IACI;;EAEJ;IACI;;EAGJ;IAGI;;EAMJ;IACI;IACA;;EAGJ;IACI;;EAEJ;IACI;;EAIZ;IAGI;IACA,cDzwCC;;;AC+wCT;EACI;;AACA;EACI,OD30CL;EC40CK;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI,ODt1CD;ECu1CC;;AAEJ;EACI,OD11CD;EC21CC;;AAGA;EAEI;EACA;;AAIJ;AAAA;EAEI;EACA;;AAGR;EACI;EACA;;;AAIR;EACI;IACI;IACA;;;AAIR;AAEA;EACI;;;AAIA;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAIR;AAEA;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;IACI;;;EAEJ;IACI;;;EAEJ;IACI;;;EAEJ;IACI;;;EAEJ;IACI;;;EAEJ;IACI;;;EAEJ;IACI;;;EAEJ;IACI;;;EAEJ;IACI;;;AAIR;EACI;IACI;IACA;IACA;IACA;;;AAIR;EACI;;;AAGJ;EACI;;;AAGJ;EACI,YDvgDO;;;AC0gDX;EACI;IACI;;;EAEJ;IACI;;;EAEJ;IACI;;;EAEJ;IACI,OD1/CD;;;AC8/CP;EACI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAIA;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAKJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAKJ;EACI;;;AAKJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAIR;EACI;;;AAGJ;EACI;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAIA;EACI;;AAEJ;EACI;;;AAKJ;EACI;;;AAKJ;EACI;;;AAIR;EACI;EACA;;;AAGJ;EACI;;;AAIA;EACI;;AAEJ;EACI;;;AAKJ;EACI;;;AAKJ;EACI;;;AAIR;EACI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAIA;EACI;;AAEJ;EACI;;;AAKJ;EACI;;;AAKJ;EACI;;;AAIR;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EAII;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EAII;;;AAGJ;EACI;;;AAGJ;EAII;;;AAGJ;EACI;;;AAGJ;EAII;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EAGY;IACI;;EAEJ;IACI;;;AAMhB;EAGY;IACI;;EAEJ;IACI;;;AAMhB;EACI;;;AAGJ;EACI,OD5yDW;;;AC+yDf;EACI,ODhzDW;;;ACmzDf;AAAA;AAAA;EAGI;;;AAGJ;AAAA;AAAA;EAGI,kBD5xDG;;;AC+xDP;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAGJ;EACI,OD10DW;;;AC60Df;EACI;;;AAGJ;EACI;;;AAGJ;AAAA;AAAA;AAAA;AAAA;EAKI,OD11DW;;;AC81DX;EACI,YDxwDM;;AC0wDV;EACI,qBDzwDK;;AC0wDL;EACI,OD5wDA;;;ACkxDR;EACI,YDpxDM;;;ACwxDV;EACI;;AAEJ;EACI;;;AAIR;AAEA;EAGY;IACI;;;EAKZ;AAAA;AAAA;IAGI;;;EAGJ;IACI;;;EAEJ;IACI;;EACA;IACI;IACA;;EAEJ;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EAEJ;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EAEJ;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EAEJ;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;EAGR;IACI;;;EAGA;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EAEJ;IACI;IACA;;EAEJ;IACI;;EAEJ;IACI;IACA;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;AAAA;AAAA;IAGI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;IACA;IACA;;;EAGR;IACI;;;EAEJ;IACI;IACA;IACA;;;AAIR;AAGI;AAAA;EAEI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAIR;EAEQ;IACI;;EAEJ;AAAA;IAEI;;;AAKZ;AAEA;EAEQ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGA;IACI;IACA;IACA;;EAEJ;IACI;IACA;IACA;IACA;;EAIJ;IACI;;EAEJ;IACI;;EAGA;IACI;;EAEJ;IACI;;EAEJ;IACI;IACA;IACA;;EAEJ;IACI;IACA;IACA;;EAEJ;IACI;;;AAYZ;EACI;;AAEJ;EACI,YD1kEL;;AC4kEC;EACI,OD7mEG;;AC+mEP;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;AAAA;AAAA;AAAA;AAAA;EAKI;;AAEJ;EACI,ODpoEG;;ACsoEP;EACI;;AAEJ;EACI,OD1oEG;;AC4oEP;EACI;;AAEJ;EACI,YDhnEL;;ACmnEK;EACI,YDpnET;;ACsnEK;EACI;;AACA;EACI,ODzpEL;;AC8pEH;EACI;;AAEJ;EACI;;AAIJ;EACI;;AAEJ;EACI;;AAIJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAOJ;EACI;;AAEJ;EACI;;AAOJ;EACI;;AAEJ;EACI;;AAGR;EACI,OD/sEG;;ACitEP;AAAA;EAEI,kBDnrEL;;ACqrEC;AAAA;AAAA;EAGI,ODxtEG;;AC0tEP;EAEI,kBD5rEL;;AC8rEC;EACI;;AAEJ;AAAA;AAAA;EAGI,ODpuEG;;ACsuEP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASI,OD1uED;;AC+uEH;EACI;;AAKJ;EACI,YDpqEE;;ACsqEN;EACI,YDvqEE;ECwqEF;;AAEJ;EACI,YD3qEE;EC4qEF;;AAKJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI,YD3rEE;EC4rEF;;AAEJ;EACI,YD/rEE;ECgsEF;;;AAIZ;EAEQ;IACI;;EAEJ;IACI;;EAEJ;IACI,qBD3sEC;;EC6sEL;IACI,OD/sEA;;ECitEJ;IACI,OD1wEL;;EC4wEC;IACI;;EAGA;IAMI,ODtxET;;EC2xEK;IACI;;EAEJ;IACI,YD/xET;;ECiyEK;IACI,ODl0ED;;ECo0EH;IACI,ODh0EL;;ECk0EC;IACI;IACA;;EAEJ;AAAA;AAAA;AAAA;AAAA;IAKI;;EAEJ;IACI,ODn1ED;;ECq1EH;IACI;;EAEJ;IACI,ODz1ED;;EC21EH;IACI;;EAEJ;IACI,YD/zET;;ECk0ES;IACI,YDn0Eb;;ECq0ES;IACI;;EACA;IACI,ODx2ET;;EC62EC;IACI;;EAEJ;IACI;;EAIJ;IACI;;EAEJ;IACI;;EAIJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAOJ;IACI;;EAEJ;IACI;;EAOJ;IACI;;EAEJ;IACI;;EAGR;IACI,OD95ED;;ECg6EH;AAAA;IAEI,kBDl4ET;;ECo4EK;AAAA;AAAA;IAGI,ODv6ED;;ECy6EH;IAGI,kBD54ET;;EC84EK;IACI;;EAEJ;AAAA;AAAA;IAGI,ODp7ED;;ECs7EH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IASI,OD17EL;;EC+7EC;IACI;;EAKJ;IACI,YDp3EF;;ECs3EF;IACI,YDv3EF;ICw3EE;;EAEJ;IACI,YD33EF;IC43EE;;EAKJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI,YD34EF;IC44EE;;EAEJ;IACI,YD/4EF;ICg5EE;;EAIJ;IACI;;EAIJ;IACI;;EAIJ;IACI;;;AAMhB;EACI;IACI;IACA;IACA;IACA;IACA;;;EAEJ;IACI;;;AAIR;EACI;;;AAIA;EACI;;AAEJ;EACI;;;AAMA;EACI;;AAIJ;EACI;;AAIJ;EACI;;AAIJ;EACI;;AAGR;EACI;;;AAKJ;EACI;EACA;;;AAKJ;EACI;IACI;;;;AAMR;EACI,YDj+EU;;;ACq+Ed;EACI,YDt+EU;;;AC0+ElB;EACI;IACI;;;EAEJ;IACI;;;EAEJ;IACI;;;EAEJ;IACI,kBDrjFD;;;ECujFH;IACI,ODnlFG;;;ECqlFP;IACI,ODtlFG;;;ECwlFP;IACI,kBDzlFG;;;EC8lFK;IACI;;;AASZ;EACI;;AAEJ;EACI,YDjlFL;;ACmlFC;EACI,YDplFL;;ACulFK;EACI;;AAEJ;EACI;;AAGR;EACI,OD1nFD;;AC4nFH;EACI,cDjoFH;;ACmoFD;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGA;EACI;;AAIZ;EACI;;;AAKJ;AAAA;AAAA;EAGI;;;AAGR;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAGJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAIA;EACI,kBD3mFG;EC4mFH;;AAEJ;EACI;EACA;;AAEJ;EACI,kBDlnFM;;AConFV;EACI,kBDrnFM;;ACunFV;EACI,kBDxnFM;;;AC4nFV;EACI,kBDrnFW;ECsnFX;;AAEJ;EACI,kBDxnFU;ECynFV;;AAEJ;EACI,kBD5nFU;;AC8nFd;EACI,kBD/nFU;;ACioFd;EACI,kBDloFU;;;ACuoFV;EACI;;;AAMR;EACI;;;AAKJ;EACI;;;AAOR;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;AAAA;EAEI;;;AAEJ;EACI;;;AAGA;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;;AAGR;EACI,ODxxFE;;;AC0xFN;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;AAAA;EAEI;;;AAEJ;AAAA;EAEI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAmBJ;EAEI;;;AAEJ;EAEI;;;AAGJ;EACI;EACA;;;AAEJ;EACI;;;AAEJ;EACI,OD12FO;;;AC42FX;EACI,OD72FO;;;AC+2FX;EACI;;;AAEJ;EACI;EACA;;;AAGJ;EACI,OD71FG;;;AC+1FP;EACI,OD33FO;;;AC63FX;EACI,ODn2FG;;;ACs2FP;EACI;AAAA;IAEI;IACA;IACA,YDt4FG;;;ECw4FP;AAAA;IAEI;;;EAEJ;AAAA;IAEI;IACA;IACA,YD9zFM;;;ECg0FV;AAAA;IAEI;;;EAEJ;AAAA;IAEI;IACA;IACA,YD15FG;;;EC45FP;AAAA;IAEI;;;EAEJ;AAAA;IAEI,YDv4FD;ICw4FC;;;EAEJ;AAAA;IAEI,MD56FO;;;EC+6FP;AAAA;IAEI,YD11FE;IC21FF;;EAEJ;AAAA;IAEI,MDt5FL;;ECw5FC;IAEI,YDr7FD;;ECu7FH;IAEI,YDz7FD;;EC27FH;IAEI,YDl6FL;ICm6FK;;EAEJ;IAEI,MDv8FG;;;EC28FP;AAAA;IAEI,YD92FO;IC+2FP;;EAEJ;AAAA;IAEI,MDl7FL;;ECo7FC;IAEI,YDt7FL;ICu7FK;;EAEJ;IAEI,MD39FG;;;ACg+Ff;EACI;IACI;;;EAEJ;IACI;;;EAEJ;IACI;;;AAIJ;EACI,qBD5+FC;;;AC++FT;EACI;AAAA;IAEI;;;EAEJ;IACI;;;EAEJ;IACI;;;AAIJ;EACI;;AAEJ;EACI;;;AAIJ;EACI;;;AAIJ;EACI;;;AASR;EAEQ;IACI;;;AAKR;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAIJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAKJ;EACI;;;AAIJ;EACI;;;AAKJ;EACI;;;AAKJ;EACI;;;AAIJ;EACI;;;AAIJ;EACI;;;AAIJ;EACI;;;AAIJ;EACI;;;AAIJ;EACI;;;AAIJ;EACI;;;AAIJ;EACI;;;AAIJ;EACI;;;AAIJ;EACI;;;AAIJ;EACI;;;AAKJ;EACI;;;AAKJ;EACI;;AAEJ;EACI;;;AAIJ;EACI;;AAEJ;EACI;;;AAKJ;EACI;;AAEJ;EACI;;;AAIJ;EACI;;AAEJ;EACI;;;AAIJ;EACI;;AAEJ;EACI;;;AAIJ;EACI;;AAEJ;EACI;;;AAIJ;EACI;;AAEJ;EACI;;;AAIJ;EACI;;AAEJ;EACI;;;AAGR;EAIgB;IACI;;EAEJ;IACI;;;EAQJ;IACI;;EAEJ;IACI;;;EAKhB;IAEI;;;EAEJ;IAEI;;;EAGJ;IAEI;;;EAEJ;IAEI;;;EAEJ;IAEI;;;EAEJ;IAEI;;;EAEJ;IAEI;;;EAEJ;IAEI;;;EAKI;IACI;;EAEJ;IACI;;;EAQJ;IACI;;EAEJ;IACI;;;EAMJ;IACI;;EAEJ;IACI;;;EAQJ;IACI;;EAEJ;IACI;;;EAMJ;IACI;;EAEJ;IACI;;;EAQJ;IACI;;EAEJ;IACI;;;EAOJ;IACI;;EAEJ;IACI;;;EAQJ;IACI;;EAEJ;IACI;;;EAOA;IACI;;EAEJ;IACI;;;EAOR;IACI;;EAEJ;IACI;;;EAQJ;IACI;;EAEJ;IACI;;;EAMJ;IACI;;EAEJ;IACI;;;EAQJ;IACI;;EAEJ;IACI;;;EAMJ;IACI;;EAEJ;IACI;;;EAQJ;IACI;;EAEJ;IACI;;;EAOJ;IACI;;EAEJ;IACI;;;EAQJ;IACI;;EAEJ;IACI;;;AAMhB;EACI;IACI;;;EAEJ;IACI;;;EAEJ;AAAA;IAMI;;;EAEJ;AAAA;IAMI;;;EAEJ;IAGI;;;EAEJ;IAGI;;;EAEJ;IAGI;;;EAEJ;IAGI;;;AAGR;EAEQ;IACI;;;AAIZ;EAEI;;;AAEJ;EAEI;;;AAEJ;EAEI;;;AAEJ;EAEI;;;AAEJ;EACI;;;AAGJ;EACI;IACI;;;EAEJ;IACI;;;AAGR;EAEQ;IACI;;;AASJ;AAAA;AAAA;AAAA;EACI;;AAEJ;AAAA;AAAA;AAAA;EACI;;;AASJ;AAAA;AAAA;AAAA;EACI;;AAEJ;AAAA;AAAA;AAAA;EACI;;;AAOR;AAAA;AAAA;EACI;;;AASI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;;AAEJ;AAAA;AAAA;AAAA;EACI;;;AAWJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;;AAEJ;AAAA;AAAA;AAAA;EACI;;;AAWJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;;AAEJ;AAAA;AAAA;AAAA;EACI;;;AAWJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;;AAEJ;AAAA;AAAA;AAAA;EACI;;;AAKhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAeI;;;AAGJ;EAEQ;IACI,kBDnnHL", "file": "skin-modes.css", "sourcesContent": ["\r\n$background: #f0f0f5;\r\n$default-color:#282f53;\r\n$border: #e9edf4;\r\n\r\n/*Color variables*/\r\n\r\n$primary-1:var(--primary-bg-color);\r\n$primary-01:var(--primary01);\r\n$primary-02:var(--primary02);\r\n$primary-03:var(--primary03);\r\n$primary-06:var(--primary06);\r\n$primary-09:var(--primary09);\r\n$primary-005:var(--primary005);\r\n$primary-hover:var(--primary-bg-hover);\r\n$primary-border:var(--primary-bg-border);\r\n$primary-transparent:var(--primary-transparentcolor);\r\n$darkprimary-transparent:var(--darkprimary-transparentcolor);\r\n$transparentprimary-transparent:var(--transparentprimary-transparentcolor);\r\n$secondary:#05c3fb;\r\n$pink:#fc5296;\r\n$teal:#1caf9f;\r\n$purple:#8927ec;\r\n$success:#09ad95;\r\n$warning:#f7b731;\r\n$danger:#e82646;\r\n$info:#1170e4;\r\n$orange:#fc7303;\r\n$red:#e73827;\r\n$lime:#7bd235;\r\n$dark:#343a40;\r\n$indigo:#6574cd;\r\n$cyan:#007ea7;\r\n$azure:#45aaf2;\r\n$white:#fff;\r\n$black:#000;\r\n$light:#f2f2f9;\r\n$gray:#5a6970;\r\n$green:#4ecc48;\r\n$blue:#3223f1;\r\n$yellow:#FBB034;\r\n\r\n/*Gradient variables*/\r\n\r\n$primary-gradient-1:linear-gradient(to bottom right, $primary-1 0%, #8e77fa 100%);\r\n$secondary-gradient:linear-gradient(to bottom right, #82cff2 0%, #28b7f9 100%);\r\n$warning-gradient:linear-gradient(to bottom right, #f66b4e 0%, #fbc434 100%);\r\n$info-gradient:linear-gradient(to bottom right, #1e63c3 0%, #00f2fe 100%);\r\n$danger-gradient:linear-gradient(to bottom right, #b51b35 0%, #fd4a68 100%);\r\n$success-gradient:linear-gradient(to bottom right, #1ea38f 0%, #5cf9e2 100%);\r\n\r\n/*white variables*/\r\n\r\n$white-1:rgba(255, 255, 255, 0.1);\r\n$white-2:rgba(255, 255, 255, 0.2);\r\n$white-3:rgba(255, 255, 255, 0.3);\r\n$white-4:rgba(255, 255, 255, 0.4);\r\n$white-5:rgba(255, 255, 255, 0.5);\r\n$white-6:rgba(255, 255, 255, 0.6);\r\n$white-7:rgba(255, 255, 255, 0.7);\r\n$white-8:rgba(255, 255, 255, 0.8);\r\n$white-9:rgba(255, 255, 255, 0.9);\r\n$white-05:rgba(255, 255, 255, 0.05);\r\n$white-08:rgba(255, 255, 255, 0.08);\r\n$white-75:rgba(255, 255, 255, 0.075);\r\n\r\n/*black variables*/\r\n\r\n$black-1:rgba(0, 0, 0, 0.1);\r\n$black-2:rgba(0, 0, 0, 0.2);\r\n$black-3:rgba(0, 0, 0, 0.3);\r\n$black-4:rgba(0, 0, 0, 0.4);\r\n$black-5:rgba(0, 0, 0, 0.5);\r\n$black-6:rgba(0, 0, 0, 0.6);\r\n$black-7:rgba(0, 0, 0, 0.7);\r\n$black-8:rgba(0, 0, 0, 0.8);\r\n$black-9:rgba(0, 0, 0, 0.9);\r\n$black-05:rgba(0, 0, 0, 0.05);\r\n\r\n/*shadow variables*/\r\n\r\n$shadow:0 5px 15px 5px rgba(80, 102, 224, 0.08);\r\n$dark-theme:#1e2448;\r\n$dark-theme2:#16192f;\r\n$dark-theme3:#181d3e;\r\n\r\n/*Dark Theme Variables*/\r\n\r\n$dark-body:#1a1a3c;\r\n$dark-theme-1:#2a2a4a;\r\n$text-color:#dedefd;\r\n$border-dark:rgba(255, 255, 255, 0.1);\r\n$dark-card-shadow:0 3px 9px 0 rgba(28, 28, 51, 0.15);\r\n\r\n/*Transparent variables*/\r\n\r\n$transparent-primary:$primary-1;\r\n$transparent-theme:rgba(0, 0, 0, 0.2);\r\n$transparent-body:var(--transparent-body);\r\n$transparent-border:rgba(255, 255, 255, 0.2);", "@import \"../scss/variables\";\r\n\r\n/*---------- Header-styles ----------*/\r\n\r\n/*Header-Color*/\r\n\r\n.color-header {\r\n    .main-header-center .form-control {\r\n        color: $white;\r\n        &::placeholder {\r\n            color: $white !important;\r\n            opacity: 0.3;\r\n        }\r\n    }\r\n    .hor-header .header-brand1 {\r\n        margin-top: 4px;\r\n    }\r\n}\r\n\r\n.color-header {\r\n    .main-header-center .form-control::placeholder {\r\n        color: $white !important;\r\n        opacity: 0.3;\r\n    }\r\n    .app-header,\r\n    .hor-header {\r\n        background: $primary-1;\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n    }\r\n    .app-sidebar__toggle:hover {\r\n        color: #fff;\r\n    }\r\n    .demo-icon.nav-link i {\r\n        color: #e7e7f7 !important;\r\n    }\r\n    .animated-arrow {\r\n        background: none;\r\n        span {\r\n            background: #e7e7f7;\r\n            &:before,\r\n            &:after {\r\n                background: #e7e7f7;\r\n            }\r\n        }\r\n    }\r\n    &.active .animated-arrow span {\r\n        background-color: transparent;\r\n    }\r\n    .header {\r\n        .form-inline .form-control {\r\n            border: 1px solid $white-05 !important;\r\n            background: rgba(0, 0, 0, 0.08);\r\n            color: $white;\r\n        }\r\n    }\r\n    .header .form-inline {\r\n        .btn i {\r\n            color: $white !important;\r\n            opacity: 0.5;\r\n        }\r\n        .form-control::placeholder {\r\n            color: $white !important;\r\n            opacity: 0.3;\r\n        }\r\n    }\r\n    .app-sidebar__toggle {\r\n        color: #fff;\r\n        a {\r\n            color: $white;\r\n        }\r\n    }\r\n    .hor-header {\r\n        .header-brand-img.light-logo {\r\n            display: block;\r\n        }\r\n    }\r\n    .logo-horizontal .header-brand-img.light-logo1 {\r\n        display: none;\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .color-header {\r\n        .hor-header.header .header-brand .header-brand-img {\r\n            &.desktop-lgo {\r\n                display: none;\r\n            }\r\n            &.dark-logo {\r\n                display: block;\r\n                // margin-top: 5px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (min-width: 991px) {\r\n    .color-header {\r\n        .header .nav-link.icon i,\r\n        .header-right .nav-link.icon:hover i {\r\n            color: $white !important;\r\n        }\r\n        .nav-link.icon {\r\n            color: $white !important;\r\n        }\r\n        .nav-link i {\r\n            color: #e7e7f7 !important;\r\n            &:hover {\r\n                color: $white;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .color-header {\r\n        .hor-header.header .header-brand-img {\r\n            &.mobile-logo {\r\n                display: none !important;\r\n            }\r\n            &.darkmobile-logo {\r\n                display: block;\r\n            }\r\n        }\r\n        .hor-header {\r\n            .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none;\r\n                }\r\n                &.light-logo {\r\n                    margin: 0 auto;\r\n                    margin-top: 6px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 1024px) and (min-width: 992px) {\r\n    .color-header {\r\n        .hor-header.header .header-brand-img {\r\n            &.desktop-lgo,\r\n            &.mobile-logo {\r\n                display: none !important;\r\n            }\r\n            &.dark-logo {\r\n                display: block !important;\r\n                // margin-top: 5px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .color-header {\r\n        .app-header .header-brand1 .header-brand-img.light-logo1 {\r\n            display: none !important;\r\n        }\r\n        .app-header .header-brand1 .header-brand-img.desktop-logo {\r\n            display: block !important;\r\n        }\r\n        .app-header.header .header-brand-img.desktop-logo {\r\n            display: block;\r\n        }\r\n        .logo-horizontal .header-brand-img.light-logo1 {\r\n            display: none !important;\r\n        }\r\n    }\r\n}\r\n\r\n.dark-mode.color-header {\r\n    .app-header,\r\n    .hor-header {\r\n        background: $primary-1;\r\n    }\r\n    .header .form-inline .form-control {\r\n        border: 1px solid $white-05 !important;\r\n        color: $white;\r\n    }\r\n}\r\n\r\n/*Header-dark*/\r\n\r\n.dark-header {\r\n    .app-header,\r\n    .hor-header {\r\n        background: $dark-theme-1 !important;\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n    }\r\n    .hor-header .header-brand1 {\r\n        margin-top: 4px;\r\n    }\r\n    .animated-arrow {\r\n        background: none;\r\n        span {\r\n            background: $primary-1;\r\n            &:before,\r\n            &:after {\r\n                background: $primary-1;\r\n            }\r\n        }\r\n    }\r\n    &.active .animated-arrow span {\r\n        background-color: transparent;\r\n    }\r\n    .header.hor-header .nav-link.icon,\r\n    .header-right-icons .nav-link.icon:hover {\r\n        color: #e7e7f7 !important;\r\n    }\r\n    .app-header.header .nav-link:hover {\r\n        color: $white;\r\n    }\r\n    .app-sidebar__toggle:hover {\r\n        color: #fff;\r\n    }\r\n    .header .main-header-center .form-control {\r\n        border: 1px solid $white-1 !important;\r\n        color: $white;\r\n        background: #353554;\r\n    }\r\n    .header .form-inline {\r\n        .btn i {\r\n            color: $white;\r\n            opacity: 0.5;\r\n        }\r\n        .form-control::placeholder {\r\n            color: $white;\r\n            opacity: 0.3;\r\n        }\r\n    }\r\n    .header.hor-header .header-brand-img {\r\n        &.desktop-logo {\r\n            display: block;\r\n        }\r\n        &.light-logo {\r\n            display: block;\r\n        }\r\n    }\r\n    &.horizontal .logo-horizontal .header-brand-img.light-logo1 {\r\n        display: none;\r\n    }\r\n    .app-header .header-right-icons .nav-link.icon {\r\n        color: #d5d7df !important;\r\n    }\r\n    .app-header .header-right-icons .nav-link.icon:hover {\r\n        color: $white;\r\n    }\r\n    .app-sidebar__toggle {\r\n        color: #d5d7df !important;\r\n    }\r\n    .app-sidebar__toggle:after {\r\n        background: none;\r\n    }\r\n    .nav-link.icon i::after {\r\n        background: none;\r\n    }\r\n    &.transparent-mode .app-header.sticky-pin {\r\n        background: $dark-theme-1;\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .dark-header {\r\n        .app-header .header-brand1 .header-brand-img {\r\n            &.light-logo1 {\r\n                display: none !important;\r\n                // margin-top: 5px;\r\n            }\r\n            &.desktop-logo {\r\n                display: block;\r\n            }\r\n        }\r\n        .hor-header {\r\n            .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none;\r\n                }\r\n                &.light-logo {\r\n                    margin: 0 auto;\r\n                    margin-top: 6px;\r\n                }\r\n            }\r\n        }\r\n        .app-header.header .header-brand-img.desktop-logo {\r\n            display: block;\r\n        }\r\n        .logo-horizontal .header-brand-img.light-logo1 {\r\n            display: none !important;\r\n        }\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .dark-header {\r\n        .hor-header.header .header-brand .header-brand-img {\r\n            &.desktop-lgo {\r\n                display: none;\r\n            }\r\n            &.dark-logo {\r\n                display: block;\r\n                // margin-top: 5px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 1024px) and (min-width: 992px) {\r\n    .dark-header {\r\n        .hor-header.header .header-brand-img {\r\n            &.desktop-lgo,\r\n            &.mobile-logo {\r\n                display: none !important;\r\n            }\r\n            &.dark-logo {\r\n                display: block !important;\r\n                // margin-top: 5px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.light-mode.dark-header .hor-header .mobile-header.hor-mobile-header {\r\n    background: #30304d !important;\r\n}\r\n\r\n/*Header-light*/\r\n\r\n.dark-mode.header-light {\r\n    .app-header,\r\n    .hor-header {\r\n        background: $white;\r\n        border-bottom: 1px solid rgb(236, 240, 250);\r\n    }\r\n    .app-sidebar {\r\n        box-shadow: none;\r\n    }\r\n    .animated-arrow {\r\n        span {\r\n            background: #5c678f;\r\n            &:before,\r\n            &:after {\r\n                background: #5c678f;\r\n            }\r\n        }\r\n    }\r\n    &.active .animated-arrow span {\r\n        background-color: transparent;\r\n    }\r\n    .header .form-inline .form-control {\r\n        border: 1px solid transparent !important;\r\n        background: #f1f5ff;\r\n        color: $black;\r\n    }\r\n    .hor-header .header-brand-img.desktop-logo-1 {\r\n        display: none;\r\n    }\r\n    .drop-profile a {\r\n        color: #171a29;\r\n    }\r\n    .hor-header .header-brand-img.desktop-logo {\r\n        display: block;\r\n    }\r\n    .header .nav-link.icon i {\r\n        color: $dark-theme-1;\r\n    }\r\n    .header .nav-link.icon i {\r\n        color: $primary-1;\r\n    }\r\n    .app-sidebar__toggle {\r\n        a {\r\n            color: #555b95;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 575.98px) {\r\n    .dark-mode.header-light .header .form-inline .form-control {\r\n        background: #191d43;\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .dark-mode.header-light {\r\n        .hor-header .header-brand-img.desktop-logo {\r\n            display: none;\r\n        }\r\n        .app-header .header-brand1 .header-brand-img {\r\n            &.desktop-logo {\r\n                display: none;\r\n            }\r\n            &.light-logo1 {\r\n                display: block !important;\r\n            }\r\n        }\r\n        .mobile-header {\r\n            background-color: #ffffff;\r\n        }\r\n        .mobile-header .header-brand-img.desktop-logo {\r\n            display: none !important;\r\n        }\r\n        .mobile-header .header-brand-img.desktop-logo.mobile-light {\r\n            display: block !important;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) and (min-width: 568px) {\r\n    .app.dark-mode.header-light {\r\n        .app-header .header-brand .header-brand-img {\r\n            &.desktop-lgo {\r\n                display: block;\r\n                // margin-top: 5px;\r\n            }\r\n            &.dark-logo,\r\n            &.mobile-logo,\r\n            &.darkmobile-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 1024px) and (min-width: 992px) {\r\n    .dark-mode.header-light {\r\n        .hor-header.header .header-brand-img {\r\n            &.dark-logo,\r\n            &.mobile-logo {\r\n                display: none !important;\r\n            }\r\n            &.desktop-lgo {\r\n                display: block !important;\r\n                // margin-top: 5px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/*Header-Gradient-header*/\r\n\r\n.gradient-header {\r\n    .hor-header .header-brand1 {\r\n        margin-top: 4px;\r\n    }\r\n    .app-header,\r\n    .hor-header {\r\n        background: linear-gradient(to right, $primary-1 0%, #8e77fa 100%) !important;\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n    }\r\n    .main-header-center .form-control {\r\n        color: $white;\r\n        &::placeholder {\r\n            color: $white !important;\r\n            opacity: 0.3;\r\n        }\r\n    }\r\n    .animated-arrow {\r\n        background: none;\r\n        span {\r\n            background: #e7e7f7;\r\n            &:before,\r\n            &:after {\r\n                background: #e7e7f7;\r\n            }\r\n        }\r\n    }\r\n    &.active .animated-arrow span {\r\n        background-color: transparent;\r\n    }\r\n    .app-header.header .nav-link:hover {\r\n        color: $white;\r\n    }\r\n    .app-sidebar__toggle:hover {\r\n        color: #fff;\r\n    }\r\n    .demo-icon.nav-link.icon i {\r\n        color: #e7e7f7 !important;\r\n    }\r\n    .header {\r\n        .main-header-center .form-control {\r\n            border: 1px solid $white-1 !important;\r\n            background: rgba(0, 0, 0, 0.08) !important;\r\n            color: $white;\r\n        }\r\n        .main-header-center .form-control:placeholder {\r\n            color: $white !important;\r\n            opacity: 0.5;\r\n        }\r\n    }\r\n    .app-sidebar__toggle {\r\n        color: #fff;\r\n        a {\r\n            color: $white;\r\n        }\r\n    }\r\n    .app-header .header-right-icons .nav-link.icon {\r\n        color: $white;\r\n    }\r\n    .app-header .header-right-icons .nav-link.icon:hover {\r\n        color: $white !important;\r\n    }\r\n    .hor-header .header-right-icons .nav-link.icon {\r\n        color: $white !important;\r\n    }\r\n}\r\n\r\n@media (min-width: 991px) {\r\n    .gradient-header {\r\n        .header .nav-link.icon i,\r\n        .header-right .nav-link.icon:hover i {\r\n            color: $white !important;\r\n        }\r\n        .nav-link.icon i {\r\n            color: $white !important;\r\n        }\r\n        .nav-link i {\r\n            color: #e7e7f7 !important;\r\n            &:hover {\r\n                color: $white;\r\n            }\r\n        }\r\n        &.horizontal .logo-horizontal .header-brand-img.light-logo1 {\r\n            display: none;\r\n        }\r\n        &.horizontal .logo-horizontal .header-brand-img.desktop-logo {\r\n            display: block;\r\n        }\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .gradient-header {\r\n        .hor-header.header .header-brand1 .header-brand-img {\r\n            &.desktop-logo {\r\n                display: none !important;\r\n            }\r\n            &.light-logo {\r\n                display: block;\r\n            }\r\n        }\r\n        .hor-header.header .header-brand2 .header-brand-img {\r\n            &.desktop-logo {\r\n                display: none !important;\r\n            }\r\n            &.light-logo {\r\n                display: block;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) and (min-width: 575.98px) {\r\n    .gradient-header {\r\n        .app-header .header-brand .header-brand-img {\r\n            &.dark-logo {\r\n                display: block;\r\n                // margin-top: 5px;\r\n            }\r\n            &.desktop-lgo,\r\n            &.mobile-logo,\r\n            &.darkmobile-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .gradient-header {\r\n        .app-header .header-brand1 .header-brand-img {\r\n            &.desktop-logo {\r\n                display: block;\r\n            }\r\n            &.light-logo1 {\r\n                display: none !important;\r\n            }\r\n        }\r\n        .navresponsive-toggler span {\r\n            color: $white !important;\r\n        }\r\n        .hor-header {\r\n            .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: block !important;\r\n                }\r\n                &.light-logo {\r\n                    margin: 0 auto;\r\n                    margin-top: 6px;\r\n                    display: block;\r\n                }\r\n            }\r\n        }\r\n\r\n        .app-header.header .header-brand-img.desktop-logo {\r\n            display: block;\r\n        }\r\n        .logo-horizontal .header-brand-img.light-logo1 {\r\n            display: none !important;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 1024px) and (min-width: 992px) {\r\n    .gradient-header {\r\n        .hor-header.header .header-brand-img {\r\n            &.desktop-lgo,\r\n            &.mobile-logo {\r\n                display: none !important;\r\n            }\r\n            &.dark-logo {\r\n                display: block !important;\r\n                // margin-top: 5px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.dark-mode.gradient-header {\r\n    .app-header,\r\n    .hor-header {\r\n        background: linear-gradient(to right, $primary-1 0%, #8e77fa 100%) !important;\r\n    }\r\n    .header .form-inline .form-control {\r\n        border: 1px solid $white-05 !important;\r\n        color: $white;\r\n    }\r\n}\r\n\r\n/*---------- Leftmenu-styles ----------*/\r\n\r\n/*Light-menu*/\r\n\r\n.light-menu {\r\n    .app-sidebar {\r\n        background: $white;\r\n        .app-sidebar__user {\r\n            border-bottom: 1px solid $border;\r\n        }\r\n        .side-menu .sidemenu_icon {\r\n            color: $white !important;\r\n        }\r\n        .app-sidebar__user {\r\n            .user-pic .avatar-xxl {\r\n                border: 3px solid #ebeff8;\r\n            }\r\n            .user-pic::before {\r\n                border: 2px solid #fff;\r\n            }\r\n            .user-info h5 {\r\n                color: #263871;\r\n            }\r\n        }\r\n        .Annoucement_card {\r\n            background: #ebeff8;\r\n            .title,\r\n            .text-white {\r\n                color: #263871 !important;\r\n            }\r\n        }\r\n    }\r\n    .slide a.active .sidemenu_icon,\r\n    .side-menu__item:hover .sidemenu_icon {\r\n        color: $white;\r\n    }\r\n    .slide-menu li {\r\n        &.active > a,\r\n        &:hover > a {\r\n            color: $primary-1 !important;\r\n        }\r\n    }\r\n    .side-item.side-item-category {\r\n        opacity: 0.7 !important;\r\n    }\r\n    &.dark-mode.sidebar-mini.hover-submenu.sidenav-toggled.sidenav-toggled-open\r\n        .app-sidebar\r\n        .side-header\r\n        .header-brand-img.light-logo {\r\n        display: block !important;\r\n    }\r\n    &.dark-mode.sidebar-mini.hover-submenu.sidenav-toggled.sidenav-toggled-open\r\n        .app-sidebar\r\n        .side-header\r\n        .header-brand-img.toggle-logo {\r\n        display: none !important;\r\n    }\r\n    &.dark-mode.sidebar-mini.hover-submenu1.sidenav-toggled.sidenav-toggled-open\r\n        .app-sidebar\r\n        .side-header\r\n        .header-brand-img.light-logo {\r\n        display: block !important;\r\n    }\r\n    &.dark-mode.sidebar-mini.hover-submenu1.sidenav-toggled.sidenav-toggled-open\r\n        .app-sidebar\r\n        .side-header\r\n        .header-brand-img.toggle-logo {\r\n        display: none !important;\r\n    }\r\n    &.dark-mode.hover-submenu1.sidebar-mini.sidenav-toggled .side-menu-label1 {\r\n        border-bottom-color: rgb(225, 229, 233);\r\n    }\r\n}\r\n\r\n.dark-mode.light-menu {\r\n    .app-sidebar {\r\n        background: $white !important;\r\n        .app-sidebar__user {\r\n            border-bottom: 1px solid $border;\r\n        }\r\n        .app-sidebar__user {\r\n            .user-pic .avatar-xxl {\r\n                border: 3px solid #ebeff8;\r\n            }\r\n            .user-pic::before {\r\n                border: 2px solid #fff;\r\n            }\r\n        }\r\n    }\r\n    .slide-menu li {\r\n        &.active > a,\r\n        &:hover > a {\r\n            color: $primary-1 !important;\r\n        }\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .light-menu {\r\n        .app-sidebar__logo {\r\n            border-bottom: 1px solid $border;\r\n            border-right: 1px solid $border;\r\n        }\r\n        .app-sidebar .app-sidebar__logo .header-brand-img {\r\n            &.desktop-lgo {\r\n                display: block;\r\n                margin: 0 auto;\r\n                // margin-top: 5px;\r\n            }\r\n            &.dark-logo {\r\n                display: none;\r\n            }\r\n        }\r\n        &.sidenav-toggled {\r\n            .app-sidebar .app-sidebar__logo .header-brand-img {\r\n                &.desktop-lgo {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n        &.sidenav-toggled.sidenav-toggled1 {\r\n            .app-sidebar .app-sidebar__logo .header-brand-img {\r\n                &.desktop-lgo {\r\n                    display: block;\r\n                    margin: 0 auto;\r\n                    // margin-top: 5px;\r\n                }\r\n                &.dark-logo {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n        &.light-menu.dark-mode.hover-submenu.sidebar-mini.sidenav-toggled .slide-menu {\r\n            background: #f1f1f1;\r\n        }\r\n        &.light-menu.dark-mode.hover-submenu1.sidebar-mini.sidenav-toggled .slide-menu {\r\n            background: #f1f1f1;\r\n        }\r\n    }\r\n    .dark-mode.light-menu {\r\n        .side-header {\r\n            border-bottom: 1px solid $border;\r\n            border-right: 1px solid $border;\r\n            top: 0px;\r\n        }\r\n        .side-menu h3 {\r\n            color: #000;\r\n        }\r\n        .side-menu__item.active:hover,\r\n        .side-menu__item.active:focus {\r\n            color: #ffffff;\r\n        }\r\n        .app-sidebar .side-header .header-brand-img {\r\n            &.light-logo1 {\r\n                display: block !important;\r\n                margin: 0 auto;\r\n                // margin-top: 5px;\r\n            }\r\n            &.desktop-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n        &.sidenav-toggled {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none !important;\r\n                }\r\n            }\r\n        }\r\n        &.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.light-logo1 {\r\n                    display: block !important;\r\n                    margin: 0 auto;\r\n                    // margin-top: 5px;\r\n                }\r\n                &desktop-logo {\r\n                    display: none !important;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.app.sidebar-mini.dark-mode.light-menu.sidenav-toggled.sidenav-toggled-open\r\n    .app-sidebar\r\n    .side-header\r\n    .header-brand-img.desktop-logo {\r\n    display: none !important;\r\n}\r\n\r\n.app.sidebar-mini.dark-mode.light-menu.sidenav-toggled.sidenav-toggled-open\r\n    .app-sidebar\r\n    .side-header\r\n    .header-brand-img.light-logo1 {\r\n    display: block !important;\r\n}\r\n\r\n.app.sidebar-mini.dark-mode.hover-submenu1.light-menu.sidenav-toggled.sidenav-toggled-open\r\n    .app-sidebar\r\n    .side-header\r\n    .header-brand-img.light-logo1 {\r\n    display: none !important;\r\n}\r\n\r\n.app.sidebar-mini.dark-mode.light-menu.hover-submenu.sidenav-toggled.sidenav-toggled-open\r\n    .app-sidebar\r\n    .side-header\r\n    .header-brand-img.light-logo1 {\r\n    display: none !important;\r\n}\r\n\r\n/*Color-menu*/\r\n\r\n.color-menu {\r\n    .app-sidebar {\r\n        border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n    }\r\n    .app-sidebar .side-header {\r\n        background: $primary-1;\r\n        border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n        .side-item.side-item-category {\r\n            color: $white;\r\n            opacity: 0.3;\r\n        }\r\n        .side-menu .side-menu__icon,\r\n        .side-menu .side-menu__item {\r\n            color: #e7eefb;\r\n        }\r\n        .slide a.active .sidemenu_icon,\r\n        .side-menu__item:hover .sidemenu_icon {\r\n            color: $white !important;\r\n            opacity: inherit;\r\n        }\r\n        .side-menu .slide a.active,\r\n        .side-menu .slide a:hover {\r\n            color: $white !important;\r\n            opacity: inherit;\r\n        }\r\n        .slide .side-menu__item.active,\r\n        .slide .side-menu__item:hover {\r\n            background: rgba(0, 0, 0, 0.1);\r\n        }\r\n        .side-menu__item.active:hover,\r\n        .side-menu__item.active:focus {\r\n            background: linear-gradient(to right, $primary-1 0%, #8e77fa 100%) !important;\r\n        }\r\n        .side-menu__item:hover .side-menu__label,\r\n        .side-menu__item:hover .side-menu__icon,\r\n        .side-menu__item:hover .angle {\r\n            color: $white !important;\r\n            opacity: inherit;\r\n        }\r\n        .app-sidebar__user {\r\n            border-bottom: 1px solid $white-1;\r\n            .user-info h5 {\r\n                color: $white;\r\n            }\r\n            .app-sidebar__user-name.text-muted {\r\n                color: $white !important;\r\n                opacity: 0.7;\r\n            }\r\n        }\r\n        .app-sidebar__user .user-pic .avatar-xxl {\r\n            border: 3px solid $white;\r\n        }\r\n    }\r\n    &.horizontal .horizontal-main .slide .slide-menu,\r\n    &.horizontal .horizontal-main .slide .sub-slide-menu,\r\n    &.horizontal .horizontal-main .slide .sub-slide-menu2 {\r\n        background-color: $primary-1;\r\n        border-color: $border-dark;\r\n    }\r\n    &.horizontal .side-menu .slide .side-menu__item.active {\r\n        color: $white !important;\r\n    }\r\n    .slide-item.active,\r\n    .slide-item:hover,\r\n    .slide-item:focus {\r\n        color: $white !important;\r\n    }\r\n    .is-expanded .sub-slide-item.active,\r\n    .sub-side-menu__item.active.is-expanded {\r\n        color: $white;\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .color-menu {\r\n        .side-header {\r\n            border-bottom: 1px solid $white-1;\r\n            border-right: 1px solid $white-1;\r\n        }\r\n        .app-sidebar .side-header .header-brand-img {\r\n            &.desktop-logo {\r\n                display: block;\r\n                margin: 0 auto;\r\n            }\r\n            &.light-logo1 {\r\n                display: none;\r\n            }\r\n        }\r\n        &.sidenav-toggled {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none;\r\n                }\r\n                &.toggle-logo {\r\n                    display: block !important;\r\n                }\r\n                &.light-logo,\r\n                &.light-logo1,\r\n                &.mobile-light {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n        &.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: block !important;\r\n                    margin: 0 auto;\r\n                }\r\n                &.toggle-logo {\r\n                    display: none !important;\r\n                }\r\n                &.light-logo,\r\n                &.light-logo1 {\r\n                    display: none !important;\r\n                }\r\n            }\r\n        }\r\n        &.hover-submenu.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none !important;\r\n                }\r\n                &.toggle-logo {\r\n                    display: block !important;\r\n                }\r\n            }\r\n        }\r\n        &.hover-submenu.dark-mode.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none !important;\r\n                }\r\n                &.toggle-logo {\r\n                    display: block !important;\r\n                }\r\n            }\r\n        }\r\n        &.hover-submenu1.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none !important;\r\n                }\r\n                &.toggle-logo {\r\n                    display: block !important;\r\n                }\r\n            }\r\n        }\r\n        &.hover-submenu1.dark-mode.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none !important;\r\n                }\r\n                &.toggle-logo {\r\n                    display: block !important;\r\n                }\r\n            }\r\n        }\r\n        &.sidenav-toggled {\r\n            .slide-menu {\r\n                background: $primary-1 !important;\r\n                color: $white;\r\n            }\r\n            .sub-side-menu__item,\r\n            & .sub-side-menu__item2 {\r\n                color: #e7eefb;\r\n            }\r\n            .sub-slide-item,\r\n            & .sub-slide-item2 {\r\n                color: #e7eefb;\r\n            }\r\n            .slide-item {\r\n                &:hover,\r\n                &:focus {\r\n                    color: $white !important;\r\n                }\r\n            }\r\n        }\r\n        .sub-side-menu__item {\r\n            color: $white;\r\n        }\r\n    }\r\n}\r\n\r\n.dark-mode.color-menu {\r\n    .app-sidebar {\r\n        background: $primary-1;\r\n        .side-menu .slide a {\r\n            color: $white;\r\n            opacity: 0.7;\r\n        }\r\n    }\r\n    .app-sidebar .app-sidebar__user .user-pic .avatar-xxl {\r\n        border: 3px solid $white;\r\n    }\r\n    .app-sidebar .app-sidebar__user .user-pic::before {\r\n        border: 2px solid $white;\r\n    }\r\n    .side-item.side-item-category {\r\n        color: $white;\r\n        opacity: 0.5;\r\n    }\r\n    .app-sidebar ul li a {\r\n        color: $white;\r\n        opacity: 0.8;\r\n    }\r\n    .slide-menu li {\r\n        &.active > a,\r\n        &:hover > a {\r\n            color: $white !important;\r\n            opacity: inherit;\r\n        }\r\n    }\r\n    .app-sidebar {\r\n        .slide-menu .slide-item:hover,\r\n        .side-menu__item.active .side-menu__icon {\r\n            color: $white !important;\r\n            opacity: inherit;\r\n        }\r\n    }\r\n    .slide.is-expanded .side-menu__item {\r\n        color: $white !important;\r\n        opacity: inherit;\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .dark-mode.color-menu .app-sidebar__logo {\r\n        border-bottom: 1px solid $white-1;\r\n        border-right: 1px solid $white-1;\r\n    }\r\n}\r\n\r\n/*dark-menu*/\r\n\r\n.dark-menu {\r\n    .app-sidebar {\r\n        background: $dark-theme-1 !important;\r\n        border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n        .side-item.side-item-category {\r\n            color: $white;\r\n            opacity: 0.3;\r\n        }\r\n        .side-header {\r\n            background: $dark-theme-1 !important;\r\n            border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n        }\r\n        .side-menu h3 {\r\n            color: rgba(255, 255, 255, 0.3);\r\n        }\r\n        .side-menu .side-menu__icon {\r\n            color: $primary-1 !important;\r\n            opacity: 0.7;\r\n        }\r\n        .side-menu a {\r\n            color: $text-color !important;\r\n        }\r\n        .side-menu__item {\r\n            color: $primary-1 !important;\r\n        }\r\n        .slide-item {\r\n            color: $text-color !important;\r\n        }\r\n        .slide a.active .side-menu__icon,\r\n        .side-menu__item:hover .side-menu__icon {\r\n            color: $primary-1 !important;\r\n            opacity: inherit;\r\n        }\r\n        .side-menu .slide a.active,\r\n        .side-menu .slide a:hover {\r\n            color: $primary-1 !important;\r\n            opacity: inherit;\r\n        }\r\n        .side-menu__item:hover .side-menu__label,\r\n        .side-menu__item:hover .side-menu__icon,\r\n        .side-menu__item:hover .angle {\r\n            color: $primary-1 !important;\r\n            opacity: inherit;\r\n        }\r\n        .side-menu__item:hover .side-menu__icon,\r\n        .side-menu__item:hover .side-menu__label,\r\n        .side-menu__item:focus .side-menu__icon,\r\n        .side-menu__item:focus .side-menu__label {\r\n            color: $primary-1 !important;\r\n        }\r\n        .app-sidebar__user {\r\n            border-bottom: 1px solid $white-1;\r\n            .user-info h5 {\r\n                color: $white;\r\n            }\r\n            .app-sidebar__user-name.text-muted {\r\n                color: $white !important;\r\n                opacity: 0.5;\r\n            }\r\n        }\r\n    }\r\n    &.sidebar-mini.sidenav-toggled .slide-menu {\r\n        background: $dark-theme-1;\r\n    }\r\n    .slide-menu li .slide-item:before {\r\n        color: $white;\r\n    }\r\n    .side-menu-label1 {\r\n        border-bottom-color: $border-dark !important;\r\n    }\r\n    &.horizontal .horizontal-main .slide .slide-menu,\r\n    &.horizontal .horizontal-main .slide .sub-slide-menu,\r\n    &.horizontal .horizontal-main .slide .sub-slide-menu2 {\r\n        background-color: $dark-theme-1;\r\n        border-color: $border-dark;\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .dark-menu {\r\n        .side-header {\r\n            border-bottom: 1px solid $white-1;\r\n            border-right: 1px solid $white-1;\r\n        }\r\n        .app-sidebar .side-header .header-brand-img {\r\n            &.desktop-logo {\r\n                display: block;\r\n                margin: 0 auto;\r\n            }\r\n            &.light-logo1 {\r\n                display: none;\r\n            }\r\n        }\r\n        &.sidenav-toggled {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none;\r\n                }\r\n                &.light-logo1 {\r\n                    display: block;\r\n                }\r\n            }\r\n        }\r\n        &.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: block;\r\n                    margin: 0 auto;\r\n                }\r\n                &.light-logo1 {\r\n                    display: none !important;\r\n                }\r\n            }\r\n        }\r\n        &.hover-submenu.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n        &.hover-submenu1.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n        &.dark-mode.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: block !important;\r\n                }\r\n                &.toggle-logo {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n        &.dark-mode.hover-submenu.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none !important;\r\n                }\r\n                &.light-logo {\r\n                    display: none !important;\r\n                }\r\n                &.toggle-logo {\r\n                    display: block !important;\r\n                }\r\n            }\r\n        }\r\n        &.dark-mode.hover-submenu1.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none !important;\r\n                }\r\n                &.light-logo {\r\n                    display: none !important;\r\n                }\r\n                &.toggle-logo {\r\n                    display: block !important;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/*Graident-Color*/\r\n\r\n.gradient-menu {\r\n    .app-sidebar {\r\n        background: linear-gradient(to bottom, $primary-1 0%, #8e77fa 100%) !important;\r\n        .side-item.side-item-category {\r\n            color: $white;\r\n            opacity: 0.3;\r\n        }\r\n        .side-header {\r\n            background: $primary-1;\r\n            border-bottom: 1px solid $white-1;\r\n            border-right: 1px solid $white-1;\r\n        }\r\n        .side-menu__item.active::after {\r\n            background: #fff;\r\n        }\r\n        .side-menu__item:hover::after {\r\n            background: #fff;\r\n        }\r\n        .side-menu .slide a {\r\n            color: #e7eefb;\r\n        }\r\n        .side-menu h3 {\r\n            color: rgba(255, 255, 255, 0.3);\r\n        }\r\n        .side-menu .side-menu__icon {\r\n            color: #e7eefb !important;\r\n            opacity: 0.7;\r\n        }\r\n        .slide-menu li .slide-item:before {\r\n            color: #c7c9eb;\r\n        }\r\n        .side-menu__item:hover .side-menu__icon,\r\n        .side-menu__item:hover .side-menu__label,\r\n        .side-menu__item:focus .side-menu__icon,\r\n        .side-menu__item:focus .side-menu__label {\r\n            color: #ffffff !important;\r\n        }\r\n        .side-menu .slide a {\r\n            color: #e7eefb;\r\n        }\r\n        .side-menu__item {\r\n            color: #e7eefb;\r\n        }\r\n        .slide-item {\r\n            color: #e7eefb;\r\n        }\r\n        .slide a.active .side-menu__icon,\r\n        .side-menu__item:hover .side-menu__icon {\r\n            color: $white !important;\r\n            opacity: inherit;\r\n        }\r\n        .side-menu .slide a.active,\r\n        .side-menu .slide a:hover {\r\n            color: $white !important;\r\n            opacity: inherit;\r\n        }\r\n        .side-menu__item:hover .side-menu__label,\r\n        .side-menu__item:hover .side-menu__icon,\r\n        .side-menu__item:hover .angle {\r\n            color: $white !important;\r\n            opacity: inherit;\r\n        }\r\n        .app-sidebar__user {\r\n            border-bottom: 1px solid $white-1;\r\n            .user-info h5 {\r\n                color: $white;\r\n            }\r\n            .app-sidebar__user-name.text-muted {\r\n                color: $white !important;\r\n            }\r\n        }\r\n        .app-sidebar__user .user-pic .avatar-xxl {\r\n            border: 3px solid $white;\r\n        }\r\n    }\r\n    &.hover-submenu1 .side-menu-label1 {\r\n        border-bottom-color: $border-dark !important;\r\n    }\r\n    &.hover-submenu.sidebar-mini.sidenav-toggled .slide-menu {\r\n        background: linear-gradient(to bottom, $primary-1 0%, #8e77fa 100%) !important;\r\n    }\r\n    &.hover-submenu1.sidebar-mini.sidenav-toggled .slide-menu {\r\n        background: linear-gradient(to bottom, $primary-1 0%, #8e77fa 100%) !important;\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .gradient-menu {\r\n        .app-sidebar {\r\n            border-bottom: 1px solid $white-1;\r\n            border-right: 1px solid $white-1;\r\n        }\r\n        .app-sidebar .side-header .header-brand-img {\r\n            &.desktop-logo {\r\n                display: block;\r\n                margin: 0 auto;\r\n            }\r\n            &.light-logo1 {\r\n                display: none;\r\n            }\r\n        }\r\n        &.sidenav-toggled {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: none;\r\n                }\r\n                &.toggle-logo {\r\n                    display: block !important;\r\n                    // margin-top: 5px;\r\n                }\r\n                &.light-logo,\r\n                &.light-logo1,\r\n                &.mobile-light {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n        &.sidenav-toggled.sidenav-toggled-open {\r\n            .app-sidebar .side-header .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: block;\r\n                    margin: 0 auto;\r\n                    // margin-top: 5px;\r\n                }\r\n                &.toggle-logo {\r\n                    display: none !important;\r\n                }\r\n                &.light-logo1 {\r\n                    display: none !important;\r\n                }\r\n            }\r\n        }\r\n        &.horizontal .horizontal-main .slide .slide-menu,\r\n        &.horizontal .horizontal-main .slide .sub-slide-menu,\r\n        &.horizontal .horizontal-main .slide .sub-slide-menu2 {\r\n            background: linear-gradient(to bottom, $primary-1 0%, #8e77fa 100%);\r\n            border-color: $border-dark;\r\n        }\r\n    }\r\n}\r\n\r\n.dark-mode.gradient-menu {\r\n    .app-sidebar {\r\n        background: linear-gradient(to bottom, $primary-1 0%, #8e77fa 100%) !important;\r\n        .side-menu .slide a {\r\n            color: $white;\r\n            opacity: 0.7;\r\n        }\r\n    }\r\n    .app-sidebar .app-sidebar__user .user-pic .avatar-xxl {\r\n        border: 3px solid $white;\r\n    }\r\n    .app-sidebar .app-sidebar__user .user-pic::before {\r\n        border: 2px solid $white;\r\n    }\r\n    .side-item.side-item-category {\r\n        color: $white;\r\n        opacity: 0.5;\r\n    }\r\n    .app-sidebar ul li a {\r\n        color: $white;\r\n        opacity: 0.8;\r\n    }\r\n    .slide-menu li {\r\n        &.active > a,\r\n        &:hover > a {\r\n            color: $white !important;\r\n            opacity: inherit;\r\n        }\r\n    }\r\n    .app-sidebar {\r\n        .slide-menu .slide-item:hover,\r\n        .side-menu__item.active .side-menu__icon {\r\n            color: $white !important;\r\n            opacity: inherit;\r\n        }\r\n    }\r\n    .slide.is-expanded .side-menu__item {\r\n        color: $white !important;\r\n        opacity: inherit;\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .dark-mode.gradient-menu .app-sidebar__logo {\r\n        border-bottom: 1px solid $white-1;\r\n        border-right: 1px solid $white-1;\r\n    }\r\n}\r\n\r\n/*---------- Horizontalmenu-styles ----------*/\r\n\r\n.dark-mode.gradient-header .mobile-header {\r\n    border-bottom: 1px solid rgb(100, 76, 252) !important;\r\n}\r\n\r\n.dark-mode {\r\n    &.color-header.gradient-menu .app-sidebar {\r\n        box-shadow: none;\r\n    }\r\n    &.gradient-header.gradient-header .app-sidebar {\r\n        box-shadow: none;\r\n    }\r\n    &.color-menu.color-header .app-sidebar {\r\n        box-shadow: none;\r\n    }\r\n    &.light-menu.color-header .app-sidebar {\r\n        box-shadow: none;\r\n    }\r\n}\r\n\r\n/*dark-hormenu*/\r\n\r\n.gradient-header.color-menu .app-sidebar {\r\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n    box-shadow: none;\r\n}\r\n\r\n.dark-header.color-menu .app-sidebar {\r\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n    box-shadow: none;\r\n}\r\n\r\n.color-header.color-menu .app-sidebar {\r\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n    box-shadow: none;\r\n}\r\n\r\n.gradient-header.gradient-menu .app-sidebar {\r\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n    box-shadow: none;\r\n}\r\n\r\n.dark-header.gradient-menu .app-sidebar {\r\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n    box-shadow: none;\r\n}\r\n\r\n.color-header.gradient-menu .app-sidebar {\r\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n    box-shadow: none;\r\n}\r\n\r\n.gradient-header.dark-menu .app-sidebar {\r\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n    box-shadow: none;\r\n}\r\n\r\n.dark-header.dark-menu .app-sidebar {\r\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n    box-shadow: none;\r\n}\r\n\r\n.color-header.dark-menu .app-sidebar {\r\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n    box-shadow: none;\r\n}\r\n\r\n@media (min-width: 991px) {\r\n    .dark-hormenu.gradient-header .header.hor-header {\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.07);\r\n    }\r\n    .dark-hormenu.dark-header .header.hor-header {\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.07);\r\n    }\r\n    .dark-hormenu.color-header .header.hor-header {\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.07);\r\n    }\r\n    .color-hormenu.gradient-header .header.hor-header {\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.07);\r\n    }\r\n    .color-hormenu.dark-header .header.hor-header {\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.07);\r\n    }\r\n    .color-hormenu.color-header .header.hor-header {\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.07);\r\n    }\r\n    .gradient-hormenu.gradient-header .header.hor-header {\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.07);\r\n    }\r\n    .gradient-hormenu.dark-header .header.hor-header {\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.07);\r\n    }\r\n    .gradient-hormenu.color-header .header.hor-header {\r\n        border-bottom: 1px solid transparent;\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .dark-mode .mobile-header {\r\n        background-color: #30304d;\r\n        border: 1px solid transparent;\r\n        -webkit-box-shadow: none;\r\n        box-shadow: none;\r\n    }\r\n}\r\n\r\n.color-header .header-brand-img.desktop-logo {\r\n    display: none;\r\n}\r\n\r\n.color-header.horizontal .header-brand-img.desktop-logo {\r\n    display: block;\r\n}\r\n\r\n.color-header .mobile-header.hor-mobile-header {\r\n    background: $primary-1;\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .color-header .mobile-header .header-brand-img.desktop-logo {\r\n        display: block;\r\n    }\r\n    .color-header .mobile-header .header-brand-img.mobile-light {\r\n        display: none !important;\r\n    }\r\n    .dark-header .app-header .header-right-icons .responsive-navbar .nav-link.icon {\r\n        color: $default-color !important;\r\n    }\r\n    .dark-header .navresponsive-toggler span {\r\n        color: $white;\r\n    }\r\n}\r\n\r\n.color-header .main-header-center .form-control {\r\n    background: rgba(0, 0, 0, 0.1);\r\n    border: 1px solid rgba(255, 255, 255, 0.08);\r\n}\r\n\r\n.color-header .app-sidebar .side-header .header-brand-img.light-logo {\r\n    display: none;\r\n}\r\n\r\n.color-header.sidenav-toggled .app-sidebar .side-header .header-brand-img.light-logo {\r\n    display: block;\r\n}\r\n\r\n.color-header.gradient-menu.sidenav-toggled .app-sidebar .side-header .header-brand-img.light-logo {\r\n    display: none;\r\n}\r\n\r\n.gradient-menu.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img {\r\n    &.desktop-logo {\r\n        display: block !important;\r\n    }\r\n    &.light-logo {\r\n        display: none;\r\n    }\r\n    &.light-logo1 {\r\n        display: block;\r\n    }\r\n}\r\n\r\n.gradient-menu.hover-submenu.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img {\r\n    &.desktop-logo {\r\n        display: none !important;\r\n    }\r\n    &.light-logo {\r\n        display: none !important;\r\n    }\r\n    &.toggle-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n\r\n.gradient-menu.sidenav-toggled .app-sidebar .side-header .header-brand-img {\r\n    &.desktop-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.gradient-menu.hover-submenu1.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img {\r\n    &.desktop-logo {\r\n        display: none !important;\r\n    }\r\n    &.light-logo {\r\n        display: none !important;\r\n    }\r\n    &.toggle-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n\r\n.header-light .hor-header .header-brand-img.light-logo {\r\n    display: none;\r\n}\r\n\r\n.header-light .main-header-center .form-control {\r\n    color: $text-color !important;\r\n    background-color: #ffffff;\r\n    border: 1px solid #ecf0fa !important;\r\n}\r\n\r\n.header-light .header .dropdown .nav-link.icon:hover {\r\n    background: none !important;\r\n}\r\n\r\n.header-light.light-menu .side-header {\r\n    border-bottom: 1px solid $border;\r\n    background: #fff;\r\n}\r\n\r\n.header-light.light-menu .app-sidebar {\r\n    background: #fff;\r\n}\r\n\r\n.header-light.light-menu .app-sidebar .side-header .header-brand-img {\r\n    &.light-logo1 {\r\n        display: block !important;\r\n    }\r\n    &.desktop-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.header-light.light-menu.sidenav-toggled .app-sidebar .side-header .header-brand-img {\r\n    &.light-logo1 {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.header-light.light-menu.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img {\r\n    &.light-logo1 {\r\n        display: block !important;\r\n    }\r\n}\r\n\r\n.color-header.light-menu .side-header {\r\n    border-bottom: 1px solid $border;\r\n    background: #fff;\r\n}\r\n\r\n.color-header.light-menu .app-sidebar {\r\n    background: #fff;\r\n}\r\n\r\n.color-header.light-menu .app-sidebar .side-header .header-brand-img {\r\n    &.light-logo1 {\r\n        display: block !important;\r\n    }\r\n    &.desktop-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.color-header.light-menu.sidenav-toggled .app-sidebar .side-header .header-brand-img {\r\n    &.light-logo1 {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.color-header.light-menu.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img {\r\n    &.light-logo1 {\r\n        display: block !important;\r\n    }\r\n}\r\n\r\n.color-header .navresponsive-toggler span {\r\n    color: $white !important;\r\n}\r\n\r\n.gradient-header.light-menu .side-header {\r\n    border-bottom: 1px solid $border;\r\n    background: #fff;\r\n}\r\n\r\n.gradient-header.light-menu .app-sidebar {\r\n    background: #fff;\r\n}\r\n\r\n.gradient-header.light-menu .app-sidebar .side-header .header-brand-img {\r\n    &.light-logo1 {\r\n        display: block !important;\r\n    }\r\n    &.desktop-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.gradient-header.light-menu.sidenav-toggled .app-sidebar .side-header .header-brand-img {\r\n    &.light-logo1 {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.gradient-header.light-menu.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img {\r\n    &.light-logo1 {\r\n        display: block !important;\r\n    }\r\n}\r\n\r\n.dark-mode .apexcharts-radialbar-track.apexcharts-track path {\r\n    stroke: #21203a;\r\n}\r\n\r\n.dark-mode .apex-charts text {\r\n    fill: #fff;\r\n}\r\n\r\n.light-mode.color-header.color-menu .app-sidebar .side-header .header-brand-img.light-logo1 {\r\n    display: none !important;\r\n}\r\n\r\n.light-mode.color-header.color-menu.sidenav-toggled.sidenav-toggled-open\r\n    .app-sidebar\r\n    .side-header\r\n    .header-brand-img.light-logo1 {\r\n    display: none !important;\r\n}\r\n\r\n.light-mode.color-header.color-menu.sidenav-toggled .app-sidebar .side-header .header-brand-img.light-logo {\r\n    display: none;\r\n}\r\n\r\n.light-mode.color-menu .app-sidebar .side-header .header-brand-img.light-logo1 {\r\n    display: none !important;\r\n}\r\n\r\n.app.sidebar-mini.color-menu .app-sidebar .side-header .header-brand-img.light-logo1 {\r\n    display: none !important;\r\n}\r\n\r\n.app.sidebar-mini.color-menu.sidenav-toggled.sidenav-toggled-open\r\n    .app-sidebar\r\n    .side-header\r\n    .header-brand-img.light-logo1 {\r\n    display: none !important;\r\n}\r\n\r\n.app.sidebar-mini.dark-menu .app-sidebar .side-header .header-brand-img.light-logo1 {\r\n    display: none !important;\r\n}\r\n\r\n.app.sidebar-mini.dark-menu.sidenav-toggled.sidenav-toggled-open\r\n    .app-sidebar\r\n    .side-header\r\n    .header-brand-img.light-logo1 {\r\n    display: none !important;\r\n}\r\n\r\n.app.sidebar-mini.gradient-menu .app-sidebar .side-header .header-brand-img.light-logo1 {\r\n    display: none !important;\r\n}\r\n\r\n.app.sidebar-mini.gradient-menu.sidenav-toggled.sidenav-toggled-open\r\n    .app-sidebar\r\n    .side-header\r\n    .header-brand-img.light-logo1 {\r\n    display: none !important;\r\n}\r\n\r\n.app.sidebar-mini.dark-mode.light-menu .side-header {\r\n    background: #ffffff;\r\n}\r\n\r\n.dark-header .mobile-header.hor-mobile-header {\r\n    background: #30304d;\r\n}\r\n\r\n.gradient-header .mobile-header.hor-mobile-header {\r\n    background: linear-gradient(to right, $primary-1 0%, #8e77fa 100%) !important;\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .dark-header .mobile-header {\r\n        .header-brand-img {\r\n            &.desktop-logo {\r\n                display: block;\r\n            }\r\n            &.mobile-light {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .gradient-header .mobile-header {\r\n        .header-brand-img {\r\n            &.desktop-logo {\r\n                display: block;\r\n            }\r\n            &.mobile-light {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.dark-mode.header-light .main-header-center .form-control {\r\n    border: 1px solid #ecf0fa !important;\r\n}\r\n\r\n.dark-mode.light-menu .side-menu__item {\r\n    color: $default-color;\r\n}\r\n\r\n.dark-mode.horizontal.light-menu .side-menu > li > a {\r\n    color: $default-color;\r\n}\r\n\r\n.dark-mode.horizontal.light-menu .side-menu .sub-side-menu__label,\r\n.dark-mode.horizontal.light-menu .sub-slide-label,\r\n.dark-mode.horizontal.light-menu .sub-side-menu__label2 {\r\n    color: $default-color !important;\r\n}\r\n\r\n.dark-mode.horizontal.light-menu .horizontal-main .slide .slide-menu,\r\n.dark-mode.horizontal.light-menu .horizontal-main .slide .sub-slide-menu,\r\n.dark-mode.horizontal.light-menu .horizontal-main .slide .sub-slide-menu2 {\r\n    background-color: $white;\r\n}\r\n\r\n.dark-mode.light-menu .side-menu__icon {\r\n    color: $primary-1 !important;\r\n}\r\n.dark-mode.light-menu.sidenav-toggled .side-menu__icon {\r\n    color: $default-color !important;\r\n}\r\n.dark-mode.light-menu.sidenav-toggled-open .side-menu__icon {\r\n    color: $primary-1 !important;\r\n}\r\n\r\n.dark-mode.light-menu .slide.is-expanded a {\r\n    color: $default-color;\r\n}\r\n\r\n.dark-mode.light-menu.hover-submenu .slide-item {\r\n    color: $default-color !important;\r\n}\r\n\r\n.dark-mode.light-menu.hover-submenu1 .slide-item {\r\n    color: $default-color !important;\r\n}\r\n\r\n.dark-mode.light-menu .side-menu .sub-category,\r\n.dark-mode.light-menu .sub-side-menu__item,\r\n.dark-mode.light-menu .sub-slide-item,\r\n.dark-mode.light-menu .sub-side-menu__item2,\r\n.dark-mode.light-menu .sub-slide-item2 {\r\n    color: $default-color;\r\n}\r\n\r\n.dark-mode.hover-submenu1.sidebar-mini.sidenav-toggled {\r\n    .slide-menu {\r\n        background: $dark-theme-1;\r\n    }\r\n    .side-menu-label1 {\r\n        border-bottom-color: $border-dark;\r\n        a {\r\n            color: $text-color;\r\n        }\r\n    }\r\n}\r\n\r\n.dark-mode.hover-submenu.sidebar-mini.sidenav-toggled {\r\n    .slide-menu {\r\n        background: $dark-theme-1;\r\n    }\r\n}\r\n.dark-mode.header-light {\r\n    &.sidebar-mini .app-header .light-logo1 {\r\n        display: block !important;\r\n    }\r\n    &.sidebar-mini .app-header.header .header-brand-img.desktop-logo {\r\n        display: none;\r\n    }\r\n}\r\n\r\n/*--Boxed--*/\r\n\r\n@media (min-width: 1400px) {\r\n    .horizontal.layout-boxed {\r\n        .stickyClass {\r\n            .app-sidebar {\r\n                max-width: 1400px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .layout-boxed.horizontal .hor-header .container,\r\n    .layout-boxed.horizontal .horizontal-main .container,\r\n    .layout-boxed.horizontal .main-content.hor-content .container {\r\n        max-width: 95% !important;\r\n    }\r\n\r\n    body.layout-boxed {\r\n        background: #cfd1e2;\r\n    }\r\n    body.transparent-mode.layout-boxed {\r\n        background: var(--transparent-body);\r\n        .page {\r\n            background: var(--transparent-body);\r\n            box-shadow: rgba(0, 0, 0, 0.3) 0px 5px 0px 0px, rgba(0, 0, 0, 0.3) 0px 5px 25px 0px;\r\n        }\r\n        &.bg-img1 .page {\r\n            background: url(../images/media/bg-img1.jpg);\r\n            background-blend-mode: overlay;\r\n            background-size: cover;\r\n            background-position: center;\r\n            background-repeat: no-repeat;\r\n            background-attachment: fixed;\r\n            content: \"\";\r\n            left: 0;\r\n            right: 0;\r\n            top: 0;\r\n        }\r\n        &.bg-img2 .page {\r\n            background: url(../images/media/bg-img2.jpg);\r\n            background-blend-mode: overlay;\r\n            background-size: cover;\r\n            background-position: center;\r\n            background-repeat: no-repeat;\r\n            background-attachment: fixed;\r\n            content: \"\";\r\n            left: 0;\r\n            right: 0;\r\n            top: 0;\r\n        }\r\n        &.bg-img3 .page {\r\n            background: url(../images/media/bg-img3.jpg);\r\n            background-blend-mode: overlay;\r\n            background-size: cover;\r\n            background-position: center;\r\n            background-repeat: no-repeat;\r\n            background-attachment: fixed;\r\n            content: \"\";\r\n            left: 0;\r\n            right: 0;\r\n            top: 0;\r\n        }\r\n        &.bg-img4 .page {\r\n            background: url(../images/media/bg-img4.jpg);\r\n            background-blend-mode: overlay;\r\n            background-size: cover;\r\n            background-position: center;\r\n            background-repeat: no-repeat;\r\n            background-attachment: fixed;\r\n            content: \"\";\r\n            left: 0;\r\n            right: 0;\r\n            top: 0;\r\n        }\r\n    }\r\n    body.layout-boxed.dark-mode {\r\n        background: #414165;\r\n    }\r\n    .layout-boxed {\r\n        .page {\r\n            width: 1400px;\r\n            margin: 0 auto;\r\n            background: #f2f3f9;\r\n            position: relative;\r\n            display: -ms-flexbox;\r\n            display: -webkit-box;\r\n            display: flex;\r\n            -ms-flex-direction: column;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-box-direction: normal;\r\n            flex-direction: column;\r\n            -ms-flex-pack: center;\r\n            -webkit-box-pack: center;\r\n            -webkit-box-shadow: rgba(104, 113, 123, 0.3) 0px 5px 0px 0px, rgba(104, 113, 123, 0.3) 0px 5px 25px 0px;\r\n            box-shadow: rgba(104, 113, 123, 0.3) 0px 5px 0px 0px, rgba(104, 113, 123, 0.3) 0px 5px 25px 0px;\r\n        }\r\n        .app-header.header {\r\n            width: 1400px !important;\r\n            margin: 0 auto;\r\n        }\r\n        .app-sidebar {\r\n            left: auto !important;\r\n        }\r\n        .side-header {\r\n            left: auto !important;\r\n            right: auto !important;\r\n        }\r\n        h3.number-font {\r\n            font-size: 1.8rem;\r\n        }\r\n        .sticky-pin .horizontal-main.hor-menu {\r\n            width: 1400px !important;\r\n        }\r\n        .hor-content .container,\r\n        .hor-header .container,\r\n        .horizontal-main.hor-menu .container {\r\n            max-width: 95%;\r\n        }\r\n        .main-sidemenu .slide-right {\r\n            right: 6px;\r\n        }\r\n        .main-sidemenu .slide-left {\r\n            left: 8px;\r\n        }\r\n        .browser-stats img {\r\n            padding: 5px;\r\n            width: 30px;\r\n            height: 30px;\r\n        }\r\n    }\r\n    .layout-boxed .media.media-xs.overflow-visible {\r\n        display: inline-block;\r\n    }\r\n    .dark-mode.layout-boxed .page {\r\n        background: #22223d;\r\n        -webkit-box-shadow: rgba(0, 0, 0, 0.3) 0px 5px 0px 0px, rgba(0, 0, 0, 0.3) 0px 5px 25px 0px;\r\n        box-shadow: rgba(0, 0, 0, 0.3) 0px 5px 0px 0px, rgba(0, 0, 0, 0.3) 0px 5px 25px 0px;\r\n    }\r\n}\r\n\r\n/*--- Scrollabel-header ----*/\r\n\r\n.scrollable-layout {\r\n    .app-header,\r\n    .mobile-header {\r\n        position: absolute !important;\r\n    }\r\n    .app-sidebar {\r\n        position: absolute;\r\n    }\r\n    .side-header {\r\n        position: absolute;\r\n    }\r\n    .page {\r\n        position: relative;\r\n    }\r\n    .sticky.sticky-pin {\r\n        position: absolute;\r\n    }\r\n    .navbar {\r\n        position: inherit;\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .scrollable-layout {\r\n        .responsive-navbar.navbar .navbar-collapse {\r\n            position: absolute;\r\n        }\r\n        .app-header,\r\n        .hor-header {\r\n            position: absolute;\r\n        }\r\n    }\r\n}\r\n\r\n/*--- Only Demo Purpose scss Icon-Text----*/\r\n\r\n@media (min-width: 992px) {\r\n    .icontext-menu {\r\n        &.sidebar-mini.sidenav-toggled .app-sidebar {\r\n            width: 120px;\r\n        }\r\n        &.app.sidebar-mini.sidenav-toggled .side-header {\r\n            width: 120px !important;\r\n        }\r\n        &.sidebar-mini.sidenav-toggled .app-content {\r\n            margin-left: 120px;\r\n        }\r\n        &.app.sidebar-mini.sidenav-toggled {\r\n            .side-menu__item {\r\n                padding: 12px 5px;\r\n                text-align: center;\r\n                display: block;\r\n            }\r\n            .side-menu__label {\r\n                display: block !important;\r\n                font-size: 12px;\r\n                padding-top: 0px;\r\n                margin-top: 0;\r\n            }\r\n        }\r\n        &.sidebar-mini.sidenav-toggled {\r\n            .app-header.header {\r\n                padding-left: 130px;\r\n            }\r\n            .side-menu .side-menu__icon:after {\r\n                top: 6px;\r\n            }\r\n            &.sidenav-toggled-open {\r\n                .app-sidebar {\r\n                    width: 270px;\r\n                }\r\n                .side-header {\r\n                    width: 270px !important;\r\n                }\r\n                .side-menu__item {\r\n                    display: flex;\r\n                    padding: 10px 20px;\r\n                    text-align: inherit;\r\n                }\r\n                .side-menu__label {\r\n                    display: block !important;\r\n                    margin-top: 3px;\r\n                    font-size: 15px;\r\n                }\r\n                .side-menu .side-menu__icon:after {\r\n                    top: 12px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n//Transparent mode\r\n\r\n.transparent-mode {\r\n    //Transparent light-menu\r\n    &.light-menu {\r\n        .app-sidebar {\r\n            background: $white !important;\r\n        }\r\n        .side-menu {\r\n            background: $white;\r\n        }\r\n        .side-menu__item {\r\n            color: $default-color;\r\n        }\r\n        .side-menu .side-menu__icon {\r\n            color: $primary-1 !important;\r\n        }\r\n        &.sidenav-toggled .side-menu .side-menu__icon {\r\n            color: $default-color !important;\r\n        }\r\n        &.sidenav-toggled-open .side-menu .side-menu__icon {\r\n            color: $primary-1 !important;\r\n        }\r\n        .side-header {\r\n            border-bottom: 1px solid $border;\r\n            background: $white !important;\r\n        }\r\n        .side-menu .sub-category,\r\n        .sub-side-menu__item,\r\n        .sub-slide-item,\r\n        .sub-side-menu__item2,\r\n        .sub-slide-item2 {\r\n            color: #74829c;\r\n        }\r\n        .slide.is-expanded a {\r\n            color: $default-color;\r\n        }\r\n        .slide-menu li .slide-item:before {\r\n            color: #68798b;\r\n        }\r\n        .slide-menu li .slide-item {\r\n            color: $default-color;\r\n        }\r\n        .app-sidebar.sidemenu-scroll .side-header {\r\n            background: $white !important;\r\n        }\r\n        &.hover-submenu.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n            background: $white;\r\n        }\r\n        &.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open {\r\n            .slide-menu {\r\n                background: $white;\r\n            }\r\n            .side-menu-label1 {\r\n                border-bottom: 1px solid $border;\r\n                a {\r\n                    color: $default-color;\r\n                }\r\n            }\r\n        }\r\n        .side-header .header-brand-img {\r\n            &.light-logo1 {\r\n                display: block !important;\r\n            }\r\n            &.desktop-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n        &.sidenav-toggled .side-header .header-brand-img {\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n        &.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img {\r\n            &.light-logo1 {\r\n                display: block !important;\r\n            }\r\n            &.desktop-logo {\r\n                display: none !important;\r\n            }\r\n            &.light-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n        &.app.sidebar-mini.hover-submenu.sidenav-toggled.sidenav-toggled-open\r\n            .app-sidebar\r\n            .side-header\r\n            .header-brand-img {\r\n            &.light-logo1 {\r\n                display: none !important;\r\n            }\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n        &.app.sidebar-mini.hover-submenu1.sidenav-toggled.sidenav-toggled-open\r\n            .app-sidebar\r\n            .side-header\r\n            .header-brand-img {\r\n            &.light-logo1 {\r\n                display: none !important;\r\n            }\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n        &.horizontal .side-menu > li > a {\r\n            color: $default-color;\r\n        }\r\n        &.horizontal .horizontal-main .slide .slide-menu,\r\n        .horizontal .horizontal-main .slide .sub-slide-menu {\r\n            background-color: $white;\r\n        }\r\n        &.horizontal .side-menu .sub-side-menu__label,\r\n        .transparent-mode .sub-slide-label,\r\n        .transparent-mode .sub-side-menu__label2 {\r\n            color: $default-color;\r\n        }\r\n        &.horizontal .horizontal-main .slide .slide-menu,\r\n        &.horizontal .horizontal-main .slide .sub-slide-menu {\r\n            background-color: $white;\r\n        }\r\n        .side-menu__item.active {\r\n            color: $primary-1 !important;\r\n        }\r\n        .side-menu .sub-side-menu__label,\r\n        .sub-slide-label,\r\n        .sub-side-menu__label2 {\r\n            color: $default-color;\r\n        }\r\n        .sub-side-menu__label.active,\r\n        .sub-side-menu__label:hover,\r\n        .sub-side-menu__label:focus,\r\n        .sub-side-menu__label1.active,\r\n        .sub-side-menu__label1:hover,\r\n        .sub-side-menu__label1:focus,\r\n        .sub-side-menu__label2.active,\r\n        .sub-side-menu__label2:hover,\r\n        .sub-side-menu__label2:focus {\r\n            color: $primary-1;\r\n        }\r\n    }\r\n    //Transparent color-menu\r\n    &.color-menu {\r\n        .side-menu {\r\n            background: var(--primary-bg-color);\r\n        }\r\n    }\r\n    //Transparent dark-menu\r\n    &.dark-menu {\r\n        .side-menu {\r\n            background: $dark-theme-1;\r\n        }\r\n        &.hover-submenu.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n            background: $dark-theme-1;\r\n            left: 107px;\r\n        }\r\n        &.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n            background: $dark-theme-1;\r\n            left: 67px;\r\n        }\r\n    }\r\n    //Transparent gradient-menu\r\n    &.gradient-menu {\r\n        .side-menu {\r\n            background: linear-gradient(to bottom, var(--primary-bg-color) 0%, #8e77fa 100%) !important;\r\n        }\r\n        .app-sidebar {\r\n            background: linear-gradient(to bottom, var(--primary-bg-color) 0%, #8e77fa 100%) !important;\r\n        }\r\n        &.sidenav-toggled.sidenav-toggled-open .app-sidebar {\r\n            background: linear-gradient(to bottom, var(--primary-bg-color) 0%, #8e77fa 100%) !important;\r\n        }\r\n        &.hover-submenu.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n            background: $dark-theme-1;\r\n            left: 107px;\r\n        }\r\n        &.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n            background: $dark-theme-1;\r\n            left: 67px;\r\n        }\r\n    }\r\n}\r\n@media (min-width: 992px) {\r\n    .transparent-mode {\r\n        &.hover-submenu.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n            background: var(--transparent-body);\r\n        }\r\n        &.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n            background: var(--transparent-body);\r\n        }\r\n        &.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open .side-menu-label1 {\r\n            border-bottom-color: $border-dark;\r\n        }\r\n        &.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open .side-menu-label1 a {\r\n            color: $text-color;\r\n        }\r\n        .slide-menu li .slide-item:before {\r\n            color: $white;\r\n        }\r\n        &.sidenav-toggled.sidenav-toggled-open .app-sidebar {\r\n            background: var(--primary-bg-color) !important;\r\n        }\r\n        &.sidebar-mini .side-menu__item {\r\n            &.active .side-menu__icon,\r\n            &:hover .side-menu__icon,\r\n            &:focus .side-menu__icon,\r\n            &.active,\r\n            &:hover,\r\n            &:focus {\r\n                color: $white;\r\n            }\r\n        }\r\n        //Transparent light-menu\r\n        &.light-menu {\r\n            .app-sidebar {\r\n                background: $white !important;\r\n            }\r\n            .side-menu {\r\n                background: $white;\r\n            }\r\n            .side-menu__item {\r\n                color: $default-color;\r\n            }\r\n            .side-menu .side-menu__icon {\r\n                color: $primary-1;\r\n            }\r\n            .side-header {\r\n                border-bottom: 1px solid $border;\r\n                background: $white !important;\r\n            }\r\n            .side-menu .sub-category,\r\n            .sub-side-menu__item,\r\n            .sub-slide-item,\r\n            .sub-side-menu__item2,\r\n            .sub-slide-item2 {\r\n                color: #74829c;\r\n            }\r\n            .slide.is-expanded a {\r\n                color: $default-color;\r\n            }\r\n            .slide-menu li .slide-item:before {\r\n                color: #68798b;\r\n            }\r\n            .slide-menu li .slide-item {\r\n                color: $default-color;\r\n            }\r\n            .app-sidebar.sidemenu-scroll .side-header {\r\n                background: $white !important;\r\n            }\r\n            &.hover-submenu.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n                background: $white;\r\n            }\r\n            &.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open {\r\n                .slide-menu {\r\n                    background: $white;\r\n                }\r\n                .side-menu-label1 {\r\n                    border-bottom: 1px solid $border;\r\n                    a {\r\n                        color: $default-color;\r\n                    }\r\n                }\r\n            }\r\n            .side-header .header-brand-img {\r\n                &.light-logo1 {\r\n                    display: block !important;\r\n                }\r\n                &.desktop-logo {\r\n                    display: none !important;\r\n                }\r\n            }\r\n            &.sidenav-toggled .side-header .header-brand-img {\r\n                &.light-logo {\r\n                    display: block !important;\r\n                }\r\n                &.toggle-logo {\r\n                    display: none !important;\r\n                }\r\n            }\r\n            &.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img {\r\n                &.light-logo1 {\r\n                    display: block !important;\r\n                }\r\n                &.desktop-logo {\r\n                    display: none !important;\r\n                }\r\n                &.light-logo {\r\n                    display: none !important;\r\n                }\r\n            }\r\n            &.app.sidebar-mini.hover-submenu.sidenav-toggled.sidenav-toggled-open\r\n                .app-sidebar\r\n                .side-header\r\n                .header-brand-img {\r\n                &.light-logo1 {\r\n                    display: none !important;\r\n                }\r\n                &.light-logo {\r\n                    display: block !important;\r\n                }\r\n            }\r\n            &.app.sidebar-mini.hover-submenu1.sidenav-toggled.sidenav-toggled-open\r\n                .app-sidebar\r\n                .side-header\r\n                .header-brand-img {\r\n                &.light-logo1 {\r\n                    display: none !important;\r\n                }\r\n                &.light-logo {\r\n                    display: block !important;\r\n                }\r\n            }\r\n            &.horizontal .side-menu > li > a {\r\n                color: $default-color;\r\n            }\r\n            &.horizontal .horizontal-main .slide .slide-menu,\r\n            .horizontal .horizontal-main .slide .sub-slide-menu {\r\n                background-color: $white;\r\n            }\r\n            &.horizontal .side-menu .sub-side-menu__label,\r\n            .transparent-mode .sub-slide-label,\r\n            .transparent-mode .sub-side-menu__label2 {\r\n                color: $default-color;\r\n            }\r\n            &.horizontal .horizontal-main .slide .slide-menu,\r\n            &.horizontal .horizontal-main .slide .sub-slide-menu,\r\n            &.horizontal .horizontal-main .slide .sub-slide-menu2 {\r\n                background-color: $white;\r\n            }\r\n            .side-menu__item.active {\r\n                color: $primary-1 !important;\r\n            }\r\n            .side-menu .sub-side-menu__label,\r\n            .sub-slide-label,\r\n            .sub-side-menu__label2 {\r\n                color: $default-color;\r\n            }\r\n            .sub-side-menu__label.active,\r\n            .sub-side-menu__label:hover,\r\n            .sub-side-menu__label:focus,\r\n            .sub-side-menu__label1.active,\r\n            .sub-side-menu__label1:hover,\r\n            .sub-side-menu__label1:focus,\r\n            .sub-side-menu__label2.active,\r\n            .sub-side-menu__label2:hover,\r\n            .sub-side-menu__label2:focus {\r\n                color: $primary-1;\r\n            }\r\n        }\r\n        //Transparent color-menu\r\n        &.color-menu {\r\n            .side-menu {\r\n                background: var(--primary-bg-color);\r\n            }\r\n        }\r\n        //Transparent dark-menu\r\n        &.dark-menu {\r\n            .side-menu {\r\n                background: $dark-theme-1;\r\n            }\r\n            &.hover-submenu.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n                background: $dark-theme-1;\r\n                left: 107px;\r\n            }\r\n            &.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n                background: $dark-theme-1;\r\n                left: 67px;\r\n            }\r\n        }\r\n        //Transparent gradient-menu\r\n        &.gradient-menu {\r\n            .side-menu {\r\n                background: linear-gradient(to bottom, var(--primary-bg-color) 0%, #8e77fa 100%) !important;\r\n            }\r\n            .app-sidebar {\r\n                background: linear-gradient(to bottom, var(--primary-bg-color) 0%, #8e77fa 100%) !important;\r\n            }\r\n            &.sidenav-toggled.sidenav-toggled-open .app-sidebar {\r\n                background: linear-gradient(to bottom, var(--primary-bg-color) 0%, #8e77fa 100%) !important;\r\n            }\r\n            &.hover-submenu.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n                background: $dark-theme-1;\r\n                left: 107px;\r\n            }\r\n            &.hover-submenu1.sidebar-mini.sidenav-toggled.sidenav-toggled-open .slide-menu {\r\n                background: $dark-theme-1;\r\n                left: 67px;\r\n            }\r\n        }\r\n        &.header-light.color-menu {\r\n            .side-header .header-brand-img.desktop-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n        &.header-light.dark-menu {\r\n            .side-header .header-brand-img.desktop-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n        &.header-light.gradient-menu {\r\n            .side-header .header-brand-img.desktop-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (min-width: 991px) {\r\n    .centerlogo-header .header-brand2 .header-brand-img {\r\n        position: absolute;\r\n        left: 0;\r\n        right: 0;\r\n        margin: 0 auto;\r\n        top: 18px;\r\n    }\r\n    .centerlogo-header .header-brand1 {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.hor-header.centerlogo-header {\r\n    display: none;\r\n}\r\n\r\n.centerlogo-horizontal {\r\n    .hor-header.centerlogo-header {\r\n        display: block !important;\r\n    }\r\n    .header.hor-header {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.transparent-mode {\r\n    &.bg-img1 {\r\n        .login-img {\r\n            background-image: none;\r\n        }\r\n    }\r\n    &.bg-img2 {\r\n        .login-img {\r\n            background-image: none;\r\n        }\r\n    }\r\n    &.bg-img3 {\r\n        .login-img {\r\n            background-image: none;\r\n        }\r\n    }\r\n    &.bg-img4 {\r\n        .login-img {\r\n            background-image: none;\r\n        }\r\n    }\r\n    .login-img {\r\n        background-image: none;\r\n    }\r\n}\r\n\r\n.layout-boxed .login-img {\r\n    .page {\r\n        background: rgba(0, 0, 0, 0.1);\r\n        box-shadow: rgba(0, 0, 0, 0.3) 0px 5px 0px 0px, rgba(0, 0, 0, 0.5) 0px 5px 25px 0px;\r\n    }\r\n}\r\n\r\n.rtl {\r\n    @media (min-width: 1400px) {\r\n        &.layout-boxed .app-sidebar {\r\n            right: auto !important;\r\n        }\r\n    }\r\n}\r\n\r\n.transparent-mode.hover-submenu .sticky.stickyClass {\r\n    .side-header {\r\n        background: $transparent-body;\r\n    }\r\n}\r\n.transparent-mode.hover-submenu1 .sticky.stickyClass {\r\n    .side-header {\r\n        background: $transparent-body;\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .transparent-mode.header-light .logo-horizontal .header-brand-img.desktop-logo {\r\n        display: none !important;\r\n    }\r\n    .transparent-mode.header-light.sidebar-mini .app-header .light-logo1 {\r\n        display: block !important;\r\n    }\r\n    .gradient-header .responsive-navbar .navbar-collapse {\r\n        background: linear-gradient(to right, $primary-1 0%, #8e77fa 100%);\r\n    }\r\n    .dark-mode.header-light .responsive-navbar .navbar-collapse {\r\n        background-color: $white;\r\n    }\r\n    .dark-mode.header-light .app-sidebar__toggle {\r\n        color: $primary-1;\r\n    }\r\n    .dark-mode.header-light .navresponsive-toggler span {\r\n        color: $primary-1;\r\n    }\r\n    .dark-mode.color-header .responsive-navbar .navbar-collapse {\r\n        background-color: $primary-1;\r\n    }\r\n    .dark-header.horizontal {\r\n        .responsive-navbar {\r\n            .navbar-collapse {\r\n                .nav-link {\r\n                    color: $default-color !important;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.transparent-mode {\r\n    &.header-light {\r\n        .app-header {\r\n            background: $white !important;\r\n        }\r\n        .app-header.sticky-pin {\r\n            background: $white;\r\n        }\r\n        .hor-header {\r\n            background: $white;\r\n        }\r\n        .side-header .header-brand-img {\r\n            &.light-logo1 {\r\n                display: block !important;\r\n            }\r\n            &.desktop-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n        .app-sidebar__toggle {\r\n            color: $primary-1;\r\n        }\r\n        .main-header-center .form-control {\r\n            border-color: $border;\r\n        }\r\n        .app-header .header-right-icons .nav-link.icon {\r\n            color: $primary-1 !important;\r\n        }\r\n        .app-header .header-right-icons .nav-link.icon:hover {\r\n            color: $primary-1 !important;\r\n        }\r\n        &.sidenav-toggled .header-brand-img.light-logo {\r\n            display: block !important;\r\n        }\r\n        .hor-header .header-right-icons .nav-link.icon {\r\n            color: $primary-1 !important;\r\n        }\r\n        .main-header-center .form-control {\r\n            color: #4d5875 !important;\r\n        }\r\n        &.horizontal.header-light .logo-horizontal .header-brand-img.desktop-logo {\r\n            display: none !important;\r\n        }\r\n        &.horizontal.header-light .logo-horizontal .header-brand-img.light-logo1 {\r\n            display: block;\r\n        }\r\n        .main-header-center .form-control {\r\n            &::placeholder {\r\n                color: $default-color !important;\r\n            }\r\n        }\r\n    }\r\n    &.color-menu.sidenav-toggled .app-sidebar .side-header .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.dark-mode.light-menu {\r\n    .side-menu .sub-side-menu__label,\r\n    .sub-slide-label,\r\n    .sub-side-menu__label2 {\r\n        color: $default-color !important;\r\n    }\r\n}\r\n.dark-mode.color-menu.hover-submenu.sidebar-mini.sidenav-toggled .slide-menu {\r\n    background: transparent;\r\n}\r\n.dark-mode.gradient-menu.hover-submenu.sidebar-mini.sidenav-toggled .slide-menu {\r\n    background: transparent;\r\n}\r\n.dark-mode.light-menu.hover-submenu.sidebar-mini.sidenav-toggled .slide-menu {\r\n    background: transparent;\r\n}\r\n\r\n.dark-mode.color-menu.hover-submenu1.sidebar-mini.sidenav-toggled .slide-menu {\r\n    background: transparent;\r\n}\r\n.dark-mode.gradient-menu.hover-submenu1.sidebar-mini.sidenav-toggled .slide-menu {\r\n    background: transparent;\r\n}\r\n.dark-mode.light-menu.hover-submenu1.sidebar-mini.sidenav-toggled .slide-menu {\r\n    background: transparent;\r\n}\r\n\r\n.dark-mode .main-container {\r\n    .onoffswitch2-label {\r\n        background-color: $dark-body;\r\n        border: 1px solid $border-dark;\r\n    }\r\n    .onoffswitch2-label:before {\r\n        background-color: #2a2a4a;\r\n        border: 1px solid $border-dark;\r\n    }\r\n    .input-color-picker {\r\n        background-color: $dark-theme-1;\r\n    }\r\n    .input-dark-color-picker {\r\n        background-color: $dark-theme-1;\r\n    }\r\n    .input-transparent-color-picker {\r\n        background-color: $dark-theme-1;\r\n    }\r\n}\r\n.transparent-mode .main-container {\r\n    .onoffswitch2-label {\r\n        background-color: $transparent-theme;\r\n        border: 1px solid $border-dark;\r\n    }\r\n    .onoffswitch2-label:before {\r\n        background-color: $transparent-body;\r\n        border: 1px solid $border-dark;\r\n    }\r\n    .input-color-picker {\r\n        background-color: $transparent-body;\r\n    }\r\n    .input-dark-color-picker {\r\n        background-color: $transparent-body;\r\n    }\r\n    .input-transparent-color-picker {\r\n        background-color: $transparent-body;\r\n    }\r\n}\r\n.color-header.color-menu {\r\n    &.sidenav-toggled {\r\n        .header-brand-img.light-logo {\r\n            display: none !important;\r\n        }\r\n    }\r\n}\r\n\r\n.header-light.light-menu.sidenav-toggled .app-sidebar .side-header .header-brand-img {\r\n    &.light-logo1 {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.header-light.light-menu.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img {\r\n    &.light-logo1 {\r\n        display: block !important;\r\n    }\r\n}\r\n\r\n// .transparent-mode.closed-leftmenu.sidebar-mini .side-menu{\r\n//     margin-top: -4px !important;\r\n// }\r\n.transparent-mode.dark-menu.sidenav-toggled .app-sidebar .side-header .header-brand-img.light-logo {\r\n    display: none !important;\r\n}\r\n.header-light.sidenav-toggled.transparent-mode .header-brand-img.toggle-logo {\r\n    display: none !important;\r\n}\r\n.header-light.sidenav-toggled.sidenav-toggled-open.transparent-mode .header-brand-img.toggle-logo {\r\n    display: none !important;\r\n}\r\n.transparent-mode.gradient-menu.header-light.sidenav-toggled .header-brand-img.light-logo {\r\n    display: none !important;\r\n}\r\n.header-light.sidenav-toggled.transparent-mode.sidenav-toggled-open .header-brand-img.light-logo {\r\n    display: none !important;\r\n}\r\n.header-light.transparent-mode.sidenav-toggled.dark-menu .header-brand-img.toggle-logo {\r\n    display: block !important;\r\n}\r\n.header-light.transparent-mode.sidenav-toggled.sidenav-toggled-open.dark-menu .header-brand-img.toggle-logo {\r\n    display: none !important;\r\n}\r\n.header-light.transparent-mode .side-header {\r\n    border-right-color: rgb(233, 237, 244);\r\n}\r\n.transparent-mode.dark-menu.sidenav-toggled.sidenav-toggled-open .app-sidebar {\r\n    background: $dark !important;\r\n}\r\n.transparent-mode.light-menu.sidenav-toggled.sidenav-toggled-open .app-sidebar {\r\n    background: $white !important;\r\n}\r\n.color-menu .sub-slide-item,\r\n.color-menu .sub-slide-item2 {\r\n    color: #e7eefb;\r\n}\r\n.dark-mode.header-light .app-sidebar__toggle {\r\n    color: var(--primary-bg-color) !important;\r\n}\r\n.switch-toggle {\r\n    .bg-img1 #bgimage1 {\r\n        width: 50px;\r\n        height: 50px;\r\n    }\r\n    .bg-img2 #bgimage2 {\r\n        width: 50px;\r\n        height: 50px;\r\n    }\r\n    .bg-img3 #bgimage3 {\r\n        width: 50px;\r\n        height: 50px;\r\n    }\r\n    .bg-img4 #bgimage4 {\r\n        width: 50px;\r\n        height: 50px;\r\n    }\r\n}\r\n.dark-mode.light-menu .app-sidebar .side-menu .slide a {\r\n    color: $dark;\r\n}\r\n.switch_section p {\r\n    margin-bottom: 0px;\r\n}\r\n.dark-mode.light-menu.header-light.scrollable-layout .app-sidebar {\r\n    border-right: 1px solid #e6e6e6;\r\n}\r\n.transparent-mode.header-light.scrollable-layout .app-sidebar {\r\n    border-right: 1px solid rgba(185, 185, 185, 0.22);\r\n}\r\n.dark-mode.color-menu .side-menu .sub-side-menu__label,\r\n.dark-mode.color-menu .side-menu .sub-side-menu__label2 {\r\n    color: $white !important;\r\n}\r\n.dark-mode.gradient-menu .side-menu .sub-side-menu__label,\r\n.dark-mode.gradient-menu .side-menu .sub-side-menu__label2 {\r\n    color: $white !important;\r\n}\r\n.dark-mode.header-light .country span:hover {\r\n    color: $primary-1 !important;\r\n}\r\n.dark-mode.header-light .app-header .header-right-icons .nav-link.icon:hover {\r\n    color: $primary-1 !important;\r\n}\r\n// @media (max-width: 992px) and (min-width: 768px){\r\n//     .horizontal {\r\n//         &.dark-mode.light-menu  .header.hor-header {\r\n//             box-shadow: 0 8px 24px #adadb3;\r\n//         }\r\n//         &.dark-mode.color-menu .header.hor-header {\r\n//             box-shadow: 0 8px 24px #5d51e7;\r\n//         }\r\n//         &.dark-mode.gradient-menu .header.hor-header {\r\n//             box-shadow: 0 8px 24px #5d51e7;\r\n//         }\r\n//         &.dark-mode.dark-menu .header.hor-header {\r\n//             box-shadow: 0 8px 24px #3d3c46;\r\n//         }\r\n\r\n//     }\r\n// }\r\n.hover-submenu1.header-light.transparent-mode.sidenav-toggled.sidenav-toggled-open.dark-menu\r\n    .header-brand-img.toggle-logo {\r\n    display: block !important;\r\n}\r\n.hover-submenu.header-light.transparent-mode.sidenav-toggled.sidenav-toggled-open.dark-menu\r\n    .header-brand-img.toggle-logo {\r\n    display: block !important;\r\n}\r\n\r\n.dark-mode.color-menu .app-sidebar .side-menu .slide a.active {\r\n    opacity: 1;\r\n    color: $white !important;\r\n}\r\n.dark-mode.light-menu .side-menu .slide a.active .sub-side-menu__label {\r\n    color: $primary-1 !important;\r\n}\r\n.dark-mode .side-menu .slide a.active .sub-side-menu__label {\r\n    color: $primary-1;\r\n}\r\n.transparent-mode.light-menu .side-menu .slide a.active .sub-side-menu__label {\r\n    color: $primary-1;\r\n}\r\n.transparent-mode.light-menu .app-sidebar .side-menu .slide a.active {\r\n    color: $primary-1 !important;\r\n}\r\n.dark-mode.gradient-menu .app-sidebar .side-menu .slide a.active {\r\n    opacity: 1;\r\n    color: $white !important;\r\n}\r\n\r\n.transparent-mode.color-menu .side-menu .slide a.active .sub-side-menu__label {\r\n    color: $white;\r\n}\r\n.transparent-mode.dark-menu .side-menu .slide a.active .sub-side-menu__label {\r\n    color: $primary-1;\r\n}\r\n.transparent-mode.gradient-menu .side-menu .slide a.active .sub-side-menu__label {\r\n    color: $white;\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .color-menu .main-sidemenu .slide-left,\r\n    .color-menu .main-sidemenu .slide-right {\r\n        color: #fff;\r\n        border: 1px solid $white-3;\r\n        background: $primary-1;\r\n    }\r\n    .color-menu .main-sidemenu .slide-left svg,\r\n    .color-menu .main-sidemenu .slide-right svg {\r\n        fill: #fff;\r\n    }\r\n    .dark-menu .main-sidemenu .slide-left,\r\n    .dark-menu .main-sidemenu .slide-right {\r\n        color: #fff;\r\n        border: 1px solid $white-3;\r\n        background: $dark-theme-1;\r\n    }\r\n    .dark-menu .main-sidemenu .slide-left svg,\r\n    .dark-menu .main-sidemenu .slide-right svg {\r\n        fill: #fff;\r\n    }\r\n    .gradient-menu .main-sidemenu .slide-left,\r\n    .gradient-menu .main-sidemenu .slide-right {\r\n        color: #fff;\r\n        border: 1px solid $white-3;\r\n        background: $primary-1;\r\n    }\r\n    .gradient-menu .main-sidemenu .slide-left svg,\r\n    .gradient-menu .main-sidemenu .slide-right svg {\r\n        fill: #fff;\r\n    }\r\n    .main-sidemenu .slide-left,\r\n    .main-sidemenu .slide-right {\r\n        background: $white;\r\n        border: 1px solid $border;\r\n    }\r\n    .main-sidemenu .slide-left svg,\r\n    .main-sidemenu .slide-right svg {\r\n        fill: $default-color;\r\n    }\r\n    .dark-mode {\r\n        .main-sidemenu .slide-left,\r\n        .main-sidemenu .slide-right {\r\n            background: $dark-theme-1;\r\n            border: 1px solid $border-dark;\r\n        }\r\n        .main-sidemenu .slide-left svg,\r\n        .main-sidemenu .slide-right svg {\r\n            fill: $white;\r\n        }\r\n        &.color-menu .main-sidemenu .slide-left,\r\n        &.color-menu .main-sidemenu .slide-right {\r\n            background: $primary-1;\r\n        }\r\n        &.gradient-menu .main-sidemenu .slide-left,\r\n        &.gradient-menu .main-sidemenu .slide-right {\r\n            background: $primary-1;\r\n        }\r\n        &.light-menu .main-sidemenu .slide-left,\r\n        &.light-menu .main-sidemenu .slide-right {\r\n            background: $white;\r\n            border: 1px solid $border;\r\n        }\r\n        &.light-menu .main-sidemenu .slide-left svg,\r\n        &.light-menu .main-sidemenu .slide-right svg {\r\n            fill: $default-color;\r\n        }\r\n    }\r\n    .transparent-mode {\r\n        .main-sidemenu .slide-left,\r\n        .main-sidemenu .slide-right {\r\n            background: $transparent-theme;\r\n            border: 1px solid $border-dark;\r\n        }\r\n        .main-sidemenu .slide-left svg,\r\n        .main-sidemenu .slide-right svg {\r\n            fill: $white;\r\n        }\r\n        &.light-menu .main-sidemenu .slide-left,\r\n        &.light-menu .main-sidemenu .slide-right {\r\n            background: $white;\r\n            border: 1px solid $border;\r\n        }\r\n        &.light-menu .main-sidemenu .slide-left svg,\r\n        &.light-menu .main-sidemenu .slide-right svg {\r\n            fill: $default-color;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .dark-mode.color-header .header.hor-header {\r\n        box-shadow: 0 8px 24px transparent;\r\n    }\r\n    .dark-mode.dark-header .header.hor-header {\r\n        box-shadow: 0 8px 24px transparent;\r\n    }\r\n    .dark-mode.gradient-header .header.hor-header {\r\n        box-shadow: 0 8px 24px transparent;\r\n    }\r\n}\r\n.horizontal.transparent-mode.header-light.light-menu {\r\n    .hor-header.header {\r\n        border-bottom-color: $border;\r\n    }\r\n}\r\n@media (max-width: 320px) {\r\n    .sidebar-mini .responsive-navbar .navbar-collapse,\r\n    .responsive-navbar .navbar-collapse {\r\n        margin-top: 72px !important;\r\n    }\r\n    .app .app-sidebar {\r\n        top: 72px;\r\n    }\r\n    .transparent-mode.app.light-menu .app-sidebar {\r\n        top: 72px !important;\r\n    }\r\n}\r\n.transparent-mode.header-light .app-sidebar.sidemenu-scroll .side-header {\r\n    .header-brand-img.light-logo1 {\r\n        display: none !important;\r\n    }\r\n    .header-brand-img.desktop-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.header-light.sidenav-toggled.hover-submenu1.sidenav-toggled-open {\r\n    .side-header .header-brand-img.light-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.header-light.sidenav-toggled.hover-submenu.sidenav-toggled-open {\r\n    .side-header .header-brand-img.light-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n// .app.sidebar-mini.sidebar-gone.transparent-mode.header-light.sidenav-toggled {\r\n//     .side-header .header-brand-img.desktop-logo {\r\n//         display: block !important;\r\n//     }\r\n// }\r\n\r\n@media (max-width: 992px) {\r\n    .horizontal.dark-mode.sidenav-toggled {\r\n        .header.hor-header {\r\n            box-shadow: none;\r\n        }\r\n    }\r\n}\r\n.light-mode {\r\n    &.bg-img1 .form_holder {\r\n        background: $white !important;\r\n    }\r\n    &.bg-img2 .form_holder {\r\n        background: $white !important;\r\n    }\r\n    &.bg-img3 .form_holder {\r\n        background: $white !important;\r\n    }\r\n    &.bg-img4 .form_holder {\r\n        background: $white !important;\r\n    }\r\n}\r\n.dark-mode {\r\n    &.bg-img1 .form_holder {\r\n        background: $dark-theme-1 !important;\r\n    }\r\n    &.bg-img2 .form_holder {\r\n        background: $dark-theme-1 !important;\r\n    }\r\n    &.bg-img3 .form_holder {\r\n        background: $dark-theme-1 !important;\r\n    }\r\n    &.bg-img4 .form_holder {\r\n        background: $dark-theme-1 !important;\r\n    }\r\n}\r\n\r\n.app.sidebar-mini.transparent-mode.sidenav-toggled.header-light {\r\n    .app-sidebar.sidemenu-scroll .side-header .header-brand-img.desktop-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.sidenav-toggled.sidenav-toggled-open.header-light {\r\n    .app-sidebar.sidemenu-scroll .side-header .header-brand-img.desktop-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n\r\n.app.sidebar-mini.transparent-mode.header-light.light-menu.sidenav-toggled.sidenav-toggled-open {\r\n    .app-sidebar.sidemenu-scroll .side-header .header-brand-img.desktop-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.app.sidebar-mini.transparent-mode.light-menu.header-light {\r\n    .app-sidebar.sidemenu-scroll .side-header .header-brand-img.light-logo1 {\r\n        display: block !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.light-menu.header-light {\r\n    .app-sidebar.sidemenu-scroll .side-header .header-brand-img.desktop-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.header-light.light-menu.sidenav-toggled {\r\n    .app-sidebar.sidemenu-scroll .side-header .header-brand-img.light-logo1 {\r\n        display: none !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.header-light.light-menu.sidenav-toggled.sidenav-toggled-open {\r\n    .app-sidebar.sidemenu-scroll .side-header .header-brand-img.light-logo1 {\r\n        display: block !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.hover-submenu.sidenav-toggled.header-light.color-menu.sidenav-toggled-open {\r\n    .side-header .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.hover-submenu.sidenav-toggled.header-light.dark-menu.sidenav-toggled-open {\r\n    .side-header .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.hover-submenu1.sidenav-toggled.header-light.gradient-menu.sidenav-toggled-open {\r\n    .side-header .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.hover-submenu1.sidenav-toggled.header-light.color-menu.sidenav-toggled-open {\r\n    .side-header .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.hover-submenu1.sidenav-toggled.header-light.dark-menu.sidenav-toggled-open {\r\n    .side-header .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.hover-submenu1.sidenav-toggled.header-light.gradient-menu.sidenav-toggled-open {\r\n    .side-header .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.light-menu.header-light.hover-submenu1.sidenav-toggled.sidenav-toggled-open {\r\n    .app-sidebar.sidemenu-scroll .side-header .header-brand-img.light-logo1 {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.app.sidebar-mini.header-light.sidenav-toggled.hover-submenu.transparent-mode.sidenav-toggled-open {\r\n    .app-sidebar.sidemenu-scroll .side-header .header-brand-img.desktop-logo {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.app.sidebar-mini.transparent-mode.bg-img4.hover-submenu.sidenav-toggled.header-light.sidenav-toggled-open {\r\n    .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n    .header-brand-img.toggle-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.bg-img4.hover-submenu1.sidenav-toggled.header-light.sidenav-toggled-open {\r\n    .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n    .header-brand-img.toggle-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n\r\n.app.sidebar-mini.transparent-mode.bg-img1.hover-submenu.sidenav-toggled.header-light.sidenav-toggled-open {\r\n    .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n    .header-brand-img.toggle-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.bg-img1.hover-submenu1.sidenav-toggled.header-light.sidenav-toggled-open {\r\n    .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n    .header-brand-img.toggle-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.bg-img2.hover-submenu.sidenav-toggled.header-light.sidenav-toggled-open {\r\n    .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n    .header-brand-img.toggle-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.bg-img2.hover-submenu1.sidenav-toggled.header-light.sidenav-toggled-open {\r\n    .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n    .header-brand-img.toggle-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.bg-img3.hover-submenu.sidenav-toggled.header-light.sidenav-toggled-open {\r\n    .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n    .header-brand-img.toggle-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.bg-img3.hover-submenu1.sidenav-toggled.header-light.sidenav-toggled-open {\r\n    .header-brand-img.light-logo {\r\n        display: none !important;\r\n    }\r\n    .header-brand-img.toggle-logo {\r\n        display: block !important;\r\n    }\r\n}\r\n@media screen and (min-width: 992px) {\r\n    .header-light.transparent-mode.sidenav-toggled .sidemenu-scroll {\r\n        .side-header {\r\n            .header-brand-img {\r\n                &.toggle-logo {\r\n                    display: block !important;\r\n                }\r\n                &.light-logo {\r\n                    display: none !important;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .header-light.transparent-mode.sidenav-toggled.sidenav-toggled-open .sidemenu-scroll {\r\n        .side-header {\r\n            .header-brand-img {\r\n                &.desktop-logo {\r\n                    display: block !important;\r\n                }\r\n                &.toggle-logo {\r\n                    display: none !important;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img2.hover-submenu.sidenav-toggled.header-light.light-menu.sidenav-toggled-open\r\n        .header-brand-img.toggle-logo {\r\n        display: none !important;\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img2.hover-submenu1.sidenav-toggled.header-light.light-menu.sidenav-toggled-open\r\n        .header-brand-img.toggle-logo {\r\n        display: none !important;\r\n    }\r\n\r\n    .app.sidebar-mini.transparent-mode.bg-img1.hover-submenu.sidenav-toggled.header-light.light-menu.sidenav-toggled-open\r\n        .header-brand-img.toggle-logo {\r\n        display: none !important;\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img1.hover-submenu1.sidenav-toggled.header-light.light-menu.sidenav-toggled-open\r\n        .header-brand-img.toggle-logo {\r\n        display: none !important;\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img3.hover-submenu.sidenav-toggled.header-light.light-menu.sidenav-toggled-open\r\n        .header-brand-img.toggle-logo {\r\n        display: none !important;\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img3.hover-submenu1.sidenav-toggled.header-light.light-menu.sidenav-toggled-open\r\n        .header-brand-img.toggle-logo {\r\n        display: none !important;\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img4.hover-submenu.sidenav-toggled.header-light.light-menu.sidenav-toggled-open\r\n        .header-brand-img.toggle-logo {\r\n        display: none !important;\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img4.hover-submenu1.sidenav-toggled.header-light.light-menu.sidenav-toggled-open\r\n        .header-brand-img.toggle-logo {\r\n        display: none !important;\r\n    }\r\n\r\n    .app.sidebar-mini.transparent-mode.bg-img4.hover-submenu1.sidenav-toggled.header-light .stickyClass .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: none !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img4.hover-submenu1.sidenav-toggled.header-light.light-menu\r\n        .stickyClass\r\n        .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img3.hover-submenu1.sidenav-toggled.header-light .stickyClass .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: none !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img3.hover-submenu1.sidenav-toggled.header-light.light-menu\r\n        .stickyClass\r\n        .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img2.hover-submenu1.sidenav-toggled.header-light .stickyClass .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: none !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img2.hover-submenu1.sidenav-toggled.header-light.light-menu\r\n        .stickyClass\r\n        .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .app.sidebar-mini.transparent-mode.bg-img1.hover-submenu1.sidenav-toggled.header-light .stickyClass .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: none !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img1.hover-submenu1.sidenav-toggled.header-light.light-menu\r\n        .stickyClass\r\n        .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n    .header-light.light-menu.transparent-mode.sidenav-toggled .sidemenu-scroll {\r\n        .side-header {\r\n            .header-brand-img {\r\n                &.toggle-logo {\r\n                    display: none !important;\r\n                }\r\n                &.light-logo {\r\n                    display: block !important;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img4.hover-submenu.sidenav-toggled.header-light .stickyClass .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: none !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img4.hover-submenu.sidenav-toggled.header-light.light-menu\r\n        .stickyClass\r\n        .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img3.hover-submenu.sidenav-toggled.header-light .stickyClass .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: none !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img3.hover-submenu.sidenav-toggled.header-light.light-menu\r\n        .stickyClass\r\n        .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img2.hover-submenu.sidenav-toggled.header-light .stickyClass .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: none !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img2.hover-submenu.sidenav-toggled.header-light.light-menu\r\n        .stickyClass\r\n        .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .app.sidebar-mini.transparent-mode.bg-img1.hover-submenu.sidenav-toggled.header-light .stickyClass .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: none !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n    .app.sidebar-mini.transparent-mode.bg-img1.hover-submenu.sidenav-toggled.header-light.light-menu\r\n        .stickyClass\r\n        .side-header {\r\n        .header-brand-img {\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: none !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .hover-submenu.dark-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open.light-menu .side-menu__icon {\r\n        color: #282f53 !important;\r\n    }\r\n    .hover-submenu1.dark-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open.light-menu .side-menu__icon {\r\n        color: #282f53 !important;\r\n    }\r\n    .hover-submenu.dark-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open.light-menu\r\n        .side-menu__item:hover\r\n        .side-menu__icon,\r\n    .hover-submenu.dark-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open.light-menu\r\n        .side-menu__item:focus\r\n        .side-menu__icon {\r\n        color: $primary-1 !important;\r\n    }\r\n    .hover-submenu1.dark-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open.light-menu\r\n        .side-menu__item:hover\r\n        .side-menu__icon,\r\n    .hover-submenu1.dark-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open.light-menu\r\n        .side-menu__item:focus\r\n        .side-menu__icon {\r\n        color: $primary-1 !important;\r\n    }\r\n    .hover-submenu.transparent-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open.light-menu\r\n        .side-menu__item.active\r\n        .side-menu__icon {\r\n        color: $primary-1 !important;\r\n    }\r\n    .hover-submenu1.transparent-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open.light-menu\r\n        .side-menu__item.active\r\n        .side-menu__icon {\r\n        color: $primary-1 !important;\r\n    }\r\n    .hover-submenu.dark-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open.light-menu\r\n        .side-menu__item.active\r\n        .side-menu__icon {\r\n        color: $primary-1 !important;\r\n    }\r\n    .hover-submenu1.dark-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open.light-menu\r\n        .side-menu__item.active\r\n        .side-menu__icon {\r\n        color: $primary-1 !important;\r\n    }\r\n}\r\n@media screen and (max-width: 992px) {\r\n    .app.sidebar-mini.sidebar-gone.sidenav-toggled.light-menu {\r\n        .side-menu__icon {\r\n            color: $primary-1 !important;\r\n        }\r\n    }\r\n}\r\n.app.sidebar-mini.transparent-mode.hover-submenu.sidenav-toggled.header-light.gradient-menu.sidenav-toggled-open\r\n    .header-brand-img.light-logo {\r\n    display: none !important;\r\n}\r\n.app.sidebar-mini.transparent-mode.hover-submenu1.sidenav-toggled.header-light.gradient-menu.sidenav-toggled-open\r\n    .header-brand-img.light-logo {\r\n    display: none !important;\r\n}\r\n.app.sidebar-mini.transparent-mode.hover-submenu.sidenav-toggled.header-light.gradient-menu.sidenav-toggled-open\r\n    .header-brand-img.toggle-logo {\r\n    display: block !important;\r\n}\r\n.app.sidebar-mini.transparent-mode.hover-submenu1.sidenav-toggled.header-light.gradient-menu.sidenav-toggled-open\r\n    .header-brand-img.toggle-logo {\r\n    display: block !important;\r\n}\r\n.color-menu .side-menu .side-menu__item.active {\r\n    color: $white !important;\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    .rtl.layout-boxed.hover-submenu.sidebar-mini.sidenav-toggled .app-sidebar {\r\n        right: -1px !important;\r\n    }\r\n    .rtl.layout-boxed.hover-submenu1.sidebar-mini.sidenav-toggled .app-sidebar {\r\n        right: -1px !important;\r\n    }\r\n}\r\n@media (max-width: 991.98px) {\r\n    .header-light.light-menu.transparent-mode.sidebar-gone.sidenav-toggled {\r\n        .app-header.header {\r\n            border-bottom: 1px solid #e9edf4;\r\n        }\r\n    }\r\n}\r\n.rtl.transparent-mode.header-light.layout-boxed.hover-submenu,\r\n.rtl.transparent-mode.header-light.layout-boxed.hover-submenu1,\r\n.ltr.transparent-mode.header-light.layout-boxed.hover-submenu,\r\n.ltr.transparent-mode.header-light.layout-boxed.hover-submenu1 {\r\n    .sticky.is-expanded.stickyClass {\r\n        .header-brand1 .header-brand-img.light-logo1 {\r\n            display: none !important;\r\n        }\r\n        .header-brand1 .header-brand-img.desktop-logo {\r\n            display: block !important;\r\n        }\r\n    }\r\n}\r\n.rtl.transparent-mode.header-light.light-menu.layout-boxed.hover-submenu,\r\n.rtl.transparent-mode.header-light.light-menu.layout-boxed.hover-submenu1,\r\n.ltr.transparent-mode.header-light.light-menu.layout-boxed.hover-submenu,\r\n.ltr.transparent-mode.header-light.light-menu.layout-boxed.hover-submenu1 {\r\n    .sticky.is-expanded.stickyClass {\r\n        .header-brand1 .header-brand-img.light-logo1 {\r\n            display: block !important;\r\n        }\r\n        .header-brand1 .header-brand-img.desktop-logo {\r\n            display: none !important;\r\n        }\r\n    }\r\n}\r\n.rtl.transparent-mode.layout-boxed.icontext-menu,\r\n.rtl.transparent-mode.layout-boxed.hover-submenu1,\r\n.rtl.transparent-mode.layout-boxed.hover-submenu {\r\n    .app-sidebar {\r\n        border-right: transparent !important;\r\n    }\r\n}\r\n.rtl.transparent-mode.header-light.layout-boxed.hover-submenu.sidenav-toggled,\r\n.rtl.transparent-mode.header-light.layout-boxed.hover-submenu1.sidenav-toggled,\r\n.ltr.transparent-mode.header-light.layout-boxed.hover-submenu.sidenav-toggled,\r\n.ltr.transparent-mode.header-light.layout-boxed.hover-submenu1.sidenav-toggled {\r\n    .sticky.is-expanded.stickyClass .app-sidebar .header-brand1 {\r\n        .header-brand-img {\r\n            &.desktop-logo,\r\n            &.light-logo,\r\n            &.light-logo1 {\r\n                display: none !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n.rtl.transparent-mode.header-light.light-menu.layout-boxed.hover-submenu.sidenav-toggled,\r\n.rtl.transparent-mode.header-light.light-menu.layout-boxed.hover-submenu1.sidenav-toggled,\r\n.ltr.transparent-mode.header-light.light-menu.layout-boxed.hover-submenu.sidenav-toggled,\r\n.ltr.transparent-mode.header-light.light-menu.layout-boxed.hover-submenu1.sidenav-toggled {\r\n    .sticky.is-expanded.stickyClass .app-sidebar .header-brand1 {\r\n        .header-brand-img {\r\n            &.desktop-logo,\r\n            &.toggle-logo,\r\n            &.light-logo1 {\r\n                display: none !important;\r\n            }\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n.rtl.transparent-mode.header-light.layout-boxed.sidenav-toggled.hover-submenu.sidenav-toggled-open,\r\n.rtl.transparent-mode.header-light.layout-boxed.sidenav-toggled.hover-submenu1.sidenav-toggled-open,\r\n.ltr.transparent-mode.header-light.layout-boxed.sidenav-toggled.hover-submenu.sidenav-toggled-open,\r\n.ltr.transparent-mode.header-light.layout-boxed.sidenav-toggled.hover-submenu1.sidenav-toggled-open {\r\n    .app-sidebar .header-brand1 {\r\n        .header-brand-img {\r\n            &.desktop-logo,\r\n            &.light-logo,\r\n            &.light-logo1 {\r\n                display: none !important;\r\n            }\r\n            &.toggle-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n.rtl.transparent-mode.header-light.light-menu.layout-boxed.sidenav-toggled.hover-submenu.sidenav-toggled-open,\r\n.rtl.transparent-mode.header-light.light-menu.layout-boxed.sidenav-toggled.hover-submenu1.sidenav-toggled-open,\r\n.ltr.transparent-mode.header-light.light-menu.layout-boxed.sidenav-toggled.hover-submenu.sidenav-toggled-open,\r\n.ltr.transparent-mode.header-light.light-menu.layout-boxed.sidenav-toggled.hover-submenu1.sidenav-toggled-open {\r\n    .app-sidebar .header-brand1 {\r\n        .header-brand-img {\r\n            &.desktop-logo,\r\n            &.toggle-logo,\r\n            &.light-logo1 {\r\n                display: none !important;\r\n            }\r\n            &.light-logo {\r\n                display: block !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n.dark-mode.color-menu .sub-slide-item.active,\r\n.dark-mode.color-menu .sub-slide-item:hover,\r\n.dark-mode.color-menu .sub-slide-item:focus,\r\n.dark-mode.color-menu .sub-slide-item2.active,\r\n.dark-mode.color-menu .sub-slide-item2:hover,\r\n.dark-mode.color-menu .sub-slide-item2:focus,\r\n.dark-mode.color-menu .sub-side-menu__label.active,\r\n.dark-mode.color-menu .sub-side-menu__label:hover,\r\n.dark-mode.color-menu .sub-side-menu__label:focus,\r\n.dark-mode.color-menu .sub-side-menu__label1.active,\r\n.dark-mode.color-menu .sub-side-menu__label1:hover,\r\n.dark-mode.color-menu .sub-side-menu__label1:focus,\r\n.dark-mode.color-menu .sub-side-menu__label2.active,\r\n.dark-mode.color-menu .sub-side-menu__label2:hover,\r\n.dark-mode.color-menu .sub-side-menu__label2:focus {\r\n    color: $white !important;\r\n}\r\n\r\n@media(max-width:992px){\r\n    .transparent-mode.horizontal.light-menu{\r\n        .horizontal-main .slide .slide-menu,  .horizontal-main .slide .sub-slide-menu, .horizontal-main .slide .sub-slide-menu2 {\r\n            background-color:$white;\r\n        }\r\n    } \r\n}\r\n"]}