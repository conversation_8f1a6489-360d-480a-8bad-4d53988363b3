<?php
require 'header.php';
require 'solmenu.php';

// Seviyeleri getir (sayfalar tablosu, sayfatip = 17)
$seviyelerSorgu = $db->prepare("SELECT id, baslik_tr FROM sayfalar WHERE sayfatip = 17 AND menu_durum = 1 ORDER BY menu_sira");
$seviyelerSorgu->execute();
$seviyeler = $seviyelerSorgu->fetchAll(PDO::FETCH_ASSOC);

// Mevcut sınavları getir
$sinavlarSorgu = $db->prepare("
    SELECT 
        ss.*,
        s.baslik_tr as seviye_adi,
        y.isim as olusturan_adi
    FROM seviye_sinavlari ss
    LEFT JOIN sayfalar s ON ss.seviye_id = s.id
    LEFT JOIN yonetim y ON ss.olusturan_id = y.kullanici_id
    ORDER BY ss.id ASC
");
$sinavlarSorgu->execute();
$sinavlar = $sinavlarSorgu->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="main-content app-content mt-0">
    <div class="side-app">
        <div class="main-container container-fluid">
            <div class="page-header">
                <div>
                    <h1 class="page-title">Seviye Sınavları</h1>
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Ana Sayfa</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Seviye Sınavları</li>
                    </ol>
                </div>
            </div>

            <!-- Sınav Listesi -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header border-bottom-0 bg-transparent bg-mavi d-flex justify-content-between align-items-center">
                            <div class="card-title text-white">Seviye Sınavları Listesi</div>
                                                         <div class="btn-group">
                                <button class="btn btn-white" onclick="yeniSinavEkle()">
                                    <i class="ri-add-line me-1"></i>Yeni Sınav Ekle
                                </button>
                                <button class="btn btn-success" onclick="sertifikaOlusturModal()">
                                    <i class="ri-award-line me-1"></i>Sertifika Oluştur
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover border-0 mb-0">
                                    <thead class="bg-light">
                                        <tr>
                                            <th class="border-top-0">Sınav Adı</th>
                                            <th class="border-top-0">Seviye</th>
                                            <th class="border-top-0">Soru Sayıları</th>
                                            <th class="border-top-0">Süre</th>
                                            <th class="border-top-0">Durum</th>
                                            <th class="border-top-0 text-end">İşlemler</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (count($sinavlar) > 0): ?>
                                            <?php foreach ($sinavlar as $sinav): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="me-3">
                                                                <i class="ri-file-text-line fs-4 text-primary"></i>
                                                            </div>
                                                            <div>
                                                                <h6 class="mb-0"><?= htmlspecialchars($sinav['sinav_adi']) ?></h6>
                                                                <small class="text-muted">ID: <?= $sinav['id'] ?></small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info"><?= htmlspecialchars($sinav['seviye_adi']) ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex flex-column">
                                                            <small>
                                                                <i class="ri-checkbox-multiple-line me-1"></i>
                                                                Çoktan Seçmeli: <strong><?= $sinav['coktan_secmeli_soru_sayisi'] ?></strong>
                                                            </small>
                                                            <small>
                                                                <i class="ri-file-text-line me-1"></i>
                                                                Cümle: <strong><?= $sinav['cumle_soru_sayisi'] ?></strong>
                                                            </small>
                                                            <small class="text-primary fw-bold">
                                                                Toplam: <strong><?= $sinav['toplam_soru'] ?></strong>
                                                            </small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary"><?= $sinav['sure_dakika'] ?> dakika</span>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $durumClass = '';
                                                        $durumText = '';
                                                        switch ($sinav['durum']) {
                                                            case 'aktif':
                                                                $durumClass = 'bg-success';
                                                                $durumText = 'Aktif';
                                                                break;
                                                            case 'pasif':
                                                                $durumClass = 'bg-danger';
                                                                $durumText = 'Pasif';
                                                                break;
                                                            case 'taslak':
                                                                $durumClass = 'bg-warning';
                                                                $durumText = 'Taslak';
                                                                break;
                                                        }
                                                        ?>
                                                        <span class="badge <?= $durumClass ?>"><?= $durumText ?></span>
                                                    </td>
                                                    <td class="text-end">
                                                        <div class="btn-group" role="group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                                    onclick="sinavDuzenle(<?= $sinav['id'] ?>)">
                                                                <i class="ri-edit-line me-1"></i>Düzenle
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                                    onclick="sinavAtama(<?= $sinav['id'] ?>)">
                                                                <i class="ri-share-line me-1"></i>Atama
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                                    onclick="sinavSonuclari(<?= $sinav['id'] ?>)">
                                                                <i class="ri-bar-chart-line me-1"></i>Sonuçlar
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                    onclick="sinavSil(<?= $sinav['id'] ?>)">
                                                                <i class="ri-delete-bin-line me-1"></i>Sil
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="8" class="text-center py-5">
                                                    <div class="text-muted">
                                                        <i class="ri-file-text-line fs-1 mb-3 d-block"></i>
                                                        <h5>Henüz sınav eklenmemiş</h5>
                                                        <p>Yeni sınav eklemek için "Yeni Sınav Ekle" butonunu kullanın.</p>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sınav Ekleme/Düzenleme Modal -->
<div class="modal fade" id="sinavEkleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Yeni Sınav Ekle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="sinavForm">
                <div class="modal-body">
                    <input type="hidden" name="sinav_id" id="sinav_id">
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Sınav Adı <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="sinav_adi" id="sinav_adi" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Süre (Dakika) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="sure_dakika" id="sure_dakika" 
                                       min="15" max="180" value="60" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Soru Seviyesi <span class="text-danger">*</span></label>
                                <select class="form-select" name="seviye_id" id="seviye_id" required>
                                    <option value="">Soru Seviyesi Seçiniz</option>
                                    <?php foreach ($seviyeler as $seviye): ?>
                                        <option value="<?= $seviye['id'] ?>"><?= htmlspecialchars($seviye['baslik_tr']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Seviye <span class="text-danger">*</span></label>
                                <select class="form-select" name="seviye_adi" id="seviye_adi" required>
                                    <option value="">Seviye Seçiniz</option>
                                    <option value="A1">A1</option>
                                    <option value="A2">A2</option>
                                    <option value="B1">B1</option>
                                    <option value="B2_Lower">B2 Lower Beginning</option>
                                    <option value="B2_Midpoint">B2 Midpoint</option>
                                    <option value="B2_Upper">B2 Upper</option>
                                    <option value="C1">C1</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Durum <span class="text-danger">*</span></label>
                                <select class="form-select" name="durum" id="durum" required>
                                    <option value="taslak">Taslak</option>
                                    <option value="aktif">Aktif</option>
                                    <option value="pasif">Pasif</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Soru Sayıları</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Çoktan Seçmeli Soru Sayısı</label>
                                        <input type="number" class="form-control" name="coktan_secmeli_soru_sayisi" 
                                               id="coktan_secmeli_soru_sayisi" min="0" value="0">
                                        <div class="form-text">Bu seviyede mevcut çoktan seçmeli soru sayısı: <span id="mevcut_coktan_secmeli">0</span></div>
                                        <div class="form-text text-danger" id="coktan_uyari" style="display:none;">
                                            <i class="ri-error-warning-line"></i> Mevcut soru sayısından fazla değer girilemez!
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Cümle Soru Sayısı</label>
                                        <input type="number" class="form-control" name="cumle_soru_sayisi" 
                                               id="cumle_soru_sayisi" min="0" value="0">
                                        <div class="form-text">Bu seviyede mevcut cümle soru sayısı: <span id="mevcut_cumle">0</span></div>
                                        <div class="form-text text-danger" id="cumle_uyari" style="display:none;">
                                            <i class="ri-error-warning-line"></i> Mevcut soru sayısından fazla değer girilemez!
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <strong>Toplam Soru Sayısı: <span id="toplam_soru">0</span></strong>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Kaydet</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Sınav Atama Modal -->
<div class="modal fade" id="sinavAtamaModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sınav Atama</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="atamaForm">
                <div class="modal-body">
                    <input type="hidden" name="sinav_id" id="atama_sinav_id">
                    
                                         <div class="mb-3">
                         <label class="form-label">Dönem Seçin <span class="text-danger">*</span></label>
                         <select class="form-select" name="donem_id" id="donem_id" required>
                             <option value="">Dönem Seçin</option>
                         </select>
                         <div class="form-text">
                             <i class="ri-information-line me-1"></i>
                             Her sınav için her dönemde yalnızca 1 kez atama yapılabilir.
                         </div>
                     </div>

                    <div class="mb-3">
                        <label class="form-label">Grup (Session) Seçin <span class="text-danger">*</span></label>
                        <select class="form-select" name="grup_id" id="grup_id" required disabled>
                            <option value="">Önce dönem seçin</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Ders Seçin <span class="text-danger">*</span></label>
                        <select class="form-select" name="ders_id" id="ders_id" required disabled>
                            <option value="">Önce grup seçin</option>
                        </select>
                    </div>

                    <div class="alert alert-info">
                        <i class="ri-information-line me-2"></i>
                        Bu atama ile seçilen dönem, grup ve dersteki öğrenciler bu sınavı görebilecek.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Atamayı Kaydet</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Sınav Atamaları Modal -->
<div class="modal fade" id="atamalarModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sınav Atamaları</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="atamalarTable">
                        <thead class="bg-light">
                            <tr>
                                <th>Dönem</th>
                                <th>Grup</th>
                                <th>Ders Bilgileri</th>
                                <th>Durum</th>
                                <th>Atama Tarihi</th>
                                <th class="text-end">İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Atamalar buraya yüklenecek -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>

<!-- Atama Düzenleme Modal -->
<div class="modal fade" id="atamaDuzenleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Atama Düzenle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="atamaDuzenleForm">
                    <input type="hidden" id="atama_id" name="atama_id">
                    <input type="hidden" id="edit_sinav_id" name="sinav_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Dönem</label>
                                <select class="form-select" id="edit_donem_id" name="donem_id" required disabled>
                                    <option value="">Dönem Seçin</option>
                                </select>
                                <small class="form-text text-muted">Dönem değiştirilemez, sadece session ve ders seçilebilir</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Grup</label>
                                <select class="form-select" id="edit_grup_id" name="grup_id" required>
                                    <option value="">Önce dönem seçin</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Ders</label>
                                <select class="form-select" id="edit_ders_id" name="ders_id" required>
                                    <option value="">Önce grup seçin</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Durum</label>
                                <select class="form-select" id="edit_durum" name="durum" required>
                                    <option value="aktif">Aktif</option>
                                    <option value="pasif">Pasif</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-primary" onclick="atamaDuzenleKaydet()">Kaydet</button>
            </div>
        </div>
    </div>
</div>

<script>
// Sayfa yüklendiğinde
$(document).ready(function() {
    // Soru sayılarını hesapla
    hesaplaToplamSoru();
    
    // Seviye değiştiğinde mevcut soru sayılarını getir
    $('#seviye_id').change(function() {
        getMevcutSoruSayilari();
    });
    
    // Soru sayıları değiştiğinde toplamı hesapla ve kontrol et
    $('#coktan_secmeli_soru_sayisi, #cumle_soru_sayisi').on('input', function() {
        hesaplaToplamSoru();
        kontrolSoruSayilari();
    });
    
    // Sınav formu submit
    $('#sinavForm').submit(function(e) {
        e.preventDefault();
        sinavKaydet();
    });
    
    // Atama formu submit
    $('#atamaForm').submit(function(e) {
        e.preventDefault();
        atamaKaydet();
    });
    
    // Dönem değiştiğinde grupları getir
    $('#donem_id').change(function() {
        getGruplar();
    });
    
    // Grup değiştiğinde dersleri getir
    $('#grup_id').change(function() {
        getDersler();
    });
    
    // Düzenleme modal'ı için event'ler
    // Dönem değişikliği artık engellendiği için event listener kaldırıldı
    
    $('#edit_grup_id').change(function() {
        getEditDersler();
    });
});

// Toplam soru sayısını hesapla
function hesaplaToplamSoru() {
    const coktan = parseInt($('#coktan_secmeli_soru_sayisi').val()) || 0;
    const cumle = parseInt($('#cumle_soru_sayisi').val()) || 0;
    const toplam = coktan + cumle;
    $('#toplam_soru').text(toplam);
}

// Mevcut soru sayılarını getir
function getMevcutSoruSayilari() {
    const seviyeId = $('#seviye_id').val();
    if (!seviyeId) {
        $('#mevcut_coktan_secmeli').text('0');
        $('#mevcut_cumle').text('0');
        return;
    }
    
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: {
            islem: 'get_soru_sayilari',
            seviye_id: seviyeId,
            csrf_token: csrf_token
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#mevcut_coktan_secmeli').text(response.data.coktan_secmeli || 0);
                $('#mevcut_cumle').text(response.data.cumle || 0);
                // Soru sayılarını güncelledikten sonra kontrol et
                kontrolSoruSayilari();
            }
        }
    });
}

// Soru sayılarını kontrol et
function kontrolSoruSayilari() {
    const girilenCoktan = parseInt($('#coktan_secmeli_soru_sayisi').val()) || 0;
    const girilenCumle = parseInt($('#cumle_soru_sayisi').val()) || 0;
    const mevcutCoktan = parseInt($('#mevcut_coktan_secmeli').text()) || 0;
    const mevcutCumle = parseInt($('#mevcut_cumle').text()) || 0;
    
    // Çoktan seçmeli soru kontrolü
    if (girilenCoktan > mevcutCoktan && mevcutCoktan > 0) {
        $('#coktan_secmeli_soru_sayisi').addClass('is-invalid');
        $('#coktan_uyari').show();
    } else {
        $('#coktan_secmeli_soru_sayisi').removeClass('is-invalid');
        $('#coktan_uyari').hide();
    }
    
    // Cümle sorusu kontrolü
    if (girilenCumle > mevcutCumle && mevcutCumle > 0) {
        $('#cumle_soru_sayisi').addClass('is-invalid');
        $('#cumle_uyari').show();
    } else {
        $('#cumle_soru_sayisi').removeClass('is-invalid');
        $('#cumle_uyari').hide();
    }
}

// Sınav kaydet
function sinavKaydet() {
    // Soru sayısı kontrolü
    const seviyeId = $('#seviye_id').val();
    const girilenCoktan = parseInt($('#coktan_secmeli_soru_sayisi').val()) || 0;
    const girilenCumle = parseInt($('#cumle_soru_sayisi').val()) || 0;
    const mevcutCoktan = parseInt($('#mevcut_coktan_secmeli').text()) || 0;
    const mevcutCumle = parseInt($('#mevcut_cumle').text()) || 0;
    
    // Eğer seviye seçilmişse soru sayısı kontrolü yap
    if (seviyeId) {
        if (girilenCoktan > mevcutCoktan) {
            Swal.fire({
                title: 'Uyarı!',
                text: `Bu seviyede sadece ${mevcutCoktan} adet çoktan seçmeli soru bulunmaktadır. Lütfen soru sayısını azaltın.`,
                icon: 'warning',
                confirmButtonText: 'Tamam'
            });
            return;
        }
        
        if (girilenCumle > mevcutCumle) {
            Swal.fire({
                title: 'Uyarı!',
                text: `Bu seviyede sadece ${mevcutCumle} adet cümle sorusu bulunmaktadır. Lütfen soru sayısını azaltın.`,
                icon: 'warning',
                confirmButtonText: 'Tamam'
            });
            return;
        }
    }
    
    const formData = new FormData($('#sinavForm')[0]);
    formData.append('csrf_token', csrf_token);
    formData.append('islem', 'sinav_kaydet');
    
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Başarılı!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'Tamam'
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire({
                    title: 'Hata!',
                    text: response.message,
                    icon: 'error',
                    confirmButtonText: 'Tamam'
                });
            }
        }
    });
}

// Yeni sınav ekle
function yeniSinavEkle() {
    // Modal'ı temizle ve yeni sınav ekleme moduna geç
    $('#sinavForm')[0].reset();
    $('#sinav_id').val('');
    $('#modalTitle').text('Yeni Sınav Ekle');
    $('#mevcut_coktan_secmeli').text('0');
    $('#mevcut_cumle').text('0');
    $('#toplam_soru').text('0');
    
    // Uyarı mesajlarını ve hata sınıflarını temizle
    $('#coktan_secmeli_soru_sayisi, #cumle_soru_sayisi').removeClass('is-invalid');
    $('#coktan_uyari, #cumle_uyari').hide();
    
    // Modal'ı aç
    $('#sinavEkleModal').modal('show');
}

// Sınav düzenle
function sinavDuzenle(sinavId) {
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: {
            islem: 'get_sinav',
            sinav_id: sinavId,
            csrf_token: csrf_token
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                const sinav = response.data;
                
                $('#modalTitle').text('Sınav Düzenle');
                $('#sinav_id').val(sinav.id);
                $('#sinav_adi').val(sinav.sinav_adi);
                $('#seviye_id').val(sinav.seviye_id);
                $('#seviye_adi').val(sinav.seviye_adi);
                $('#sure_dakika').val(sinav.sure_dakika);
                $('#durum').val(sinav.durum);
                $('#coktan_secmeli_soru_sayisi').val(sinav.coktan_secmeli_soru_sayisi);
                $('#cumle_soru_sayisi').val(sinav.cumle_soru_sayisi);
                
                hesaplaToplamSoru();
                getMevcutSoruSayilari();
                
                $('#sinavEkleModal').modal('show');
            }
        }
    });
}

// Sınav sil
function sinavSil(sinavId) {
    Swal.fire({
        title: 'Emin misiniz?',
        text: 'Bu sınavı silmek istediğinizden emin misiniz?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Evet, Sil',
        cancelButtonText: 'İptal'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: 'islemler/seviye_sinav_islemleri.php',
                method: 'POST',
                data: {
                    islem: 'sinav_sil',
                    sinav_id: sinavId,
                    csrf_token: csrf_token
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Başarılı!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'Tamam'
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            title: 'Hata!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'Tamam'
                        });
                    }
                }
            });
        }
    });
}

// Sınav atama
function sinavAtama(sinavId) {
    $('#atama_sinav_id').val(sinavId);
    getDonemler();
    $('#sinavAtamaModal').modal('show');
}

// Dönemleri getir
function getDonemler() {
    const sinavId = $('#atama_sinav_id').val();
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: {
            islem: 'get_donemler',
            sinav_id: sinavId,
            csrf_token: csrf_token
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                let options = '<option value="">Dönem Seçin</option>';
                if (response.data.length > 0) {
                    response.data.forEach(function(donem) {
                        options += `<option value="${donem.id}">${donem.donem_adi}</option>`;
                    });
                } else {
                    options = '<option value="">Bu sınav için atanabilecek dönem kalmadı</option>';
                }
                $('#donem_id').html(options);
            }
        }
    });
}

// Grupları getir
function getGruplar() {
    const donemId = $('#donem_id').val();
    if (!donemId) {
        $('#grup_id').html('<option value="">Önce dönem seçin</option>').prop('disabled', true);
        return;
    }
    
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: {
            islem: 'get_gruplar',
            donem_id: donemId,
            csrf_token: csrf_token
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                let options = '<option value="">Grup Seçin</option>';
                response.data.forEach(function(grup) {
                    options += `<option value="${grup.id}">${grup.grup_adi}</option>`;
                });
                $('#grup_id').html(options).prop('disabled', false);
            }
        }
    });
}

// Dersleri getir
function getDersler() {
    const grupId = $('#grup_id').val();
    if (!grupId) {
        $('#ders_id').html('<option value="">Önce grup seçin</option>').prop('disabled', true);
        return;
    }
    
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: {
            islem: 'get_dersler',
            grup_id: grupId,
            csrf_token: csrf_token
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                let options = '<option value="">Ders Seçin</option>';
                response.data.forEach(function(ders) {
                    const dersNo = ders.ders_no || '';
                    const dersAdi = ders.ders_adi || '';
                    const dersTarihi = ders.ders_tarihi ? new Date(ders.ders_tarihi).toLocaleDateString('tr-TR') : '';
                    
                    let displayText = '';
                    if (dersNo) displayText += `${dersNo} - `;
                    displayText += dersAdi;
                    if (dersTarihi) displayText += ` - ${dersTarihi}`;
                    
                    options += `<option value="${ders.id}">${displayText}</option>`;
                });
                $('#ders_id').html(options).prop('disabled', false);
            }
        }
    });
}

// Atama kaydet
function atamaKaydet() {
    const formData = new FormData($('#atamaForm')[0]);
    formData.append('csrf_token', csrf_token);
    formData.append('islem', 'atama_kaydet');
    
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Başarılı!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'Tamam'
                }).then(() => {
                    // Modal'ı kapat
                    $('#sinavAtamaModal').modal('hide');
                    // Dönem listesini güncelle
                    getDonemler();
                    // Formu temizle
                    $('#atamaForm')[0].reset();
                    $('#grup_id').html('<option value="">Önce dönem seçin</option>').prop('disabled', true);
                    $('#ders_id').html('<option value="">Önce grup seçin</option>').prop('disabled', true);
                });
            } else {
                Swal.fire({
                    title: 'Hata!',
                    text: response.message,
                    icon: 'error',
                    confirmButtonText: 'Tamam'
                });
            }
        }
    });
}

// Sınav sonuçları
function sinavSonuclari(sinavId) {
    // Atamaları getir ve modal aç
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: {
            islem: 'get_sinav_atamalari',
            sinav_id: sinavId,
            csrf_token: csrf_token
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                let atamalarHtml = '';
                if (response.data.length > 0) {
                    response.data.forEach(function(atama) {
                        atamalarHtml += `
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="ri-calendar-line fs-4 text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">${atama.donem_adi}</h6>
                                            <small class="text-muted">Dönem</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">${atama.grup_adi}</span>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <small><strong>Ders No:</strong> ${atama.ders_no || 'N/A'}</small>
                                        <small><strong>Ders Adı:</strong> ${atama.ders_adi}</small>
                                        <small><strong>Tarih:</strong> ${atama.ders_tarihi || 'N/A'}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge ${atama.durum === 'aktif' ? 'bg-success' : 'bg-danger'}">${atama.durum === 'aktif' ? 'Aktif' : 'Pasif'}</span>
                                </td>
                                <td>
                                    <small class="text-muted">${atama.atama_tarihi}</small>
                                </td>
                                <td class="text-end">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-primary" 
                                                onclick="sinavSonuclariDetay(${atama.sinav_id}, ${atama.donem_id})">
                                            <i class="ri-bar-chart-line me-1"></i>Sonuçlar
                                        </button>
                                        <button type="button" class="btn btn-sm btn-warning" 
                                                onclick="atamaDuzenle(${atama.id}, ${atama.sinav_id}, ${atama.donem_id}, ${atama.grup_id}, ${atama.ders_id}, '${atama.durum}')">
                                            <i class="ri-edit-line me-1"></i>Düzenle
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                onclick="atamaSil(${atama.id})">
                                            <i class="ri-delete-bin-line me-1"></i>Sil
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });
                } else {
                    atamalarHtml = `
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="ri-inbox-line fs-1 mb-3 d-block"></i>
                                    <h6>Henüz atama yapılmamış</h6>
                                    <p>Bu sınav henüz herhangi bir derse atanmamış.</p>
                                </div>
                            </td>
                        </tr>
                    `;
                }
                
                $('#atamalarTable tbody').html(atamalarHtml);
                $('#atamalarModal').modal('show');
            } else {
                Swal.fire({
                    title: 'Hata!',
                    text: response.message,
                    icon: 'error',
                    confirmButtonText: 'Tamam'
                });
            }
        }
    });
}

// Sınav sonuçları detay sayfasına yönlendir
function sinavSonuclariDetay(sinavId, donemId) {
    window.open(`seviye-sinav-sonuclari.php?sinav_id=${sinavId}&donem_id=${donemId}`, '_blank');
}

// Atama düzenle
                function atamaDuzenle(atamaId, sinavId, donemId, grupId, dersId, durum) {
                    // Modal'ı aç
                    $('#atamaDuzenleModal').modal('show');
                    
                    // Form alanlarını doldur
                    $('#atama_id').val(atamaId);
                    $('#edit_sinav_id').val(sinavId);
                    $('#edit_durum').val(durum);
    // Sadece mevcut dönemi option olarak ekle ve seçili yap
    $('#edit_donem_id').html(`<option value="${donemId}" selected>Yükleniyor...</option>`).prop('disabled', true);
    
    // Dönem adını AJAX ile getir ve göster
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: {
            islem: 'get_donem_adi', // New backend operation
            donem_id: donemId,
            csrf_token: csrf_token
        },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.donem_adi) {
                $('#edit_donem_id').html(`<option value="${donemId}" selected>${response.donem_adi}</option>`);
            } else {
                $('#edit_donem_id').html(`<option value="${donemId}" selected>Dönem ${donemId}</option>`);
            }
        },
        error: function() {
            $('#edit_donem_id').html(`<option value="${donemId}" selected>Dönem ${donemId}</option>`);
        }
    });

    // Grupları getir ve seçili grup/ders değerlerini ayarla
    getEditGruplar(donemId, grupId, dersId);
    
    // Modal açıldığında dönem seçiminin disabled olduğundan emin ol
    $('#atamaDuzenleModal').on('shown.bs.modal', function() {
        $('#edit_donem_id').prop('disabled', true);
    });
}



// Düzenleme modal'ı için grupları getir
function getEditGruplar(donemId, selectedGrupId = null, selectedDersId = null) {
    if (!donemId) {
        $('#edit_grup_id').html('<option value="">Önce dönem seçin</option>').prop('disabled', true);
        return;
    }
    
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: {
            islem: 'get_gruplar',
            donem_id: donemId,
            csrf_token: csrf_token
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                let options = '<option value="">Grup Seçin</option>';
                response.data.forEach(function(grup) {
                    const selected = selectedGrupId && grup.id == selectedGrupId ? 'selected' : '';
                    options += `<option value="${grup.id}" ${selected}>${grup.grup_adi}</option>`;
                });
                $('#edit_grup_id').html(options).prop('disabled', false);
                
                // Eğer grup seçili ise, dersleri de getir
                if (selectedGrupId) {
                    getEditDersler(selectedGrupId, selectedDersId);
                }
            }
        }
    });
}

// Düzenleme modal'ı için dersleri getir
function getEditDersler(selectedGrupId = null, selectedDersId = null) {
    const grupId = selectedGrupId || $('#edit_grup_id').val();
    if (!grupId) {
        $('#edit_ders_id').html('<option value="">Önce grup seçin</option>').prop('disabled', true);
        return;
    }
    
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: {
            islem: 'get_dersler',
            grup_id: grupId,
            csrf_token: csrf_token
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                let options = '<option value="">Ders Seçin</option>';
                response.data.forEach(function(ders) {
                    const dersTarihi = ders.ders_tarihi ? new Date(ders.ders_tarihi).toLocaleDateString('tr-TR') : 'Tarih Yok';
                    const selected = selectedDersId && ders.id == selectedDersId ? 'selected' : '';
                    options += `<option value="${ders.id}" ${selected}>${ders.ders_no} - ${ders.ders_adi} - ${dersTarihi}</option>`;
                });
                $('#edit_ders_id').html(options).prop('disabled', false);
            }
        }
    });
}

// Sertifika oluşturma modal'ını aç
function sertifikaOlusturModal() {
    // Modal'ı aç
    $('#sertifikaOlusturModal').modal('show');
}

// Sertifika oluştur
function sertifikaOlustur() {
    const formData = {
        islem: 'manuel_sertifika_olustur',
        ad_soyad: $('#sertifika_ad_soyad').val(),
        seviye: $('#sertifika_seviye').val(),
        sertifika_tarihi: $('#sertifika_tarihi').val(),
        csrf_token: csrf_token
    };
    
    // Form doğrulama
    if (!formData.ad_soyad || !formData.seviye || !formData.sertifika_tarihi) {
        Swal.fire({
            title: 'Hata!',
            text: 'Lütfen tüm alanları doldurun.',
            icon: 'error',
            confirmButtonText: 'Tamam'
        });
        return;
    }
    
    // Yükleniyor göstergesi
    Swal.fire({
        title: 'Lütfen Bekleyiniz',
        text: 'Sertifika oluşturuluyor...',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });
    
    // AJAX isteği
    $.ajax({
        url: 'islemler/manuel_sertifika_olustur.php',
        method: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            Swal.close();
            
            if (response.success) {
                Swal.fire({
                    title: 'Başarılı!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'Tamam'
                }).then(() => {
                    // Modal'ı kapat
                    $('#sertifikaOlusturModal').modal('hide');
                    
                    // Sertifika dosyasını indir
                    if (response.sertifika_dosya_yolu) {
                        window.open(response.sertifika_dosya_yolu, '_blank');
                    }
                });
            } else {
                Swal.fire({
                    title: 'Hata!',
                    text: response.message,
                    icon: 'error',
                    confirmButtonText: 'Tamam'
                });
            }
        },
        error: function() {
            Swal.close();
            Swal.fire({
                title: 'Hata!',
                text: 'Sunucu ile bağlantı kurulamadı.',
                icon: 'error',
                confirmButtonText: 'Tamam'
            });
        }
    });
}

// Atama düzenleme kaydet
function atamaDuzenleKaydet() {
    const formData = {
        islem: 'atama_duzenle',
        atama_id: $('#atama_id').val(),
        sinav_id: $('#edit_sinav_id').val(),
        donem_id: $('#edit_donem_id').val(), // Bu değer disabled olduğu için mevcut dönem kalır
        grup_id: $('#edit_grup_id').val(),
        ders_id: $('#edit_ders_id').val(),
        durum: $('#edit_durum').val(),
        csrf_token: csrf_token
    };
    
    $.ajax({
        url: 'islemler/seviye_sinav_islemleri.php',
        method: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Başarılı!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'Tamam'
                }).then(() => {
                    $('#atamaDuzenleModal').modal('hide');
                    // Atamalar listesini yenile
                    sinavSonuclari($('#edit_sinav_id').val());
                });
            } else {
                Swal.fire({
                    title: 'Hata!',
                    text: response.message,
                    icon: 'error',
                    confirmButtonText: 'Tamam'
                });
            }
        },
        error: function() {
            Swal.fire({
                title: 'Hata!',
                text: 'Bir hata oluştu.',
                icon: 'error',
                confirmButtonText: 'Tamam'
            });
        }
    });
}

// Atama sil
function atamaSil(atamaId) {
    Swal.fire({
        title: 'Emin misiniz?',
        text: 'Bu atamayı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Evet, Sil!',
        cancelButtonText: 'İptal'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: 'islemler/seviye_sinav_islemleri.php',
                method: 'POST',
                data: {
                    islem: 'atama_sil',
                    atama_id: atamaId,
                    csrf_token: csrf_token
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Başarılı!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'Tamam'
                        }).then(() => {
                            // Atamalar listesini yenile
                            $('#atamalarModal').modal('hide');
                        });
                    } else {
                        Swal.fire({
                            title: 'Hata!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'Tamam'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Hata!',
                        text: 'Bir hata oluştu.',
                        icon: 'error',
                        confirmButtonText: 'Tamam'
                    });
                }
            });
        }
    });
}

// Modal kapandığında formu temizle
$('#sinavEkleModal').on('hidden.bs.modal', function() {
    $('#sinavForm')[0].reset();
    $('#sinav_id').val('');
    $('#modalTitle').text('Yeni Sınav Ekle');
    $('#mevcut_coktan_secmeli').text('0');
    $('#mevcut_cumle').text('0');
    $('#toplam_soru').text('0');
    
    // Uyarı mesajlarını ve hata sınıflarını temizle
    $('#coktan_secmeli_soru_sayisi, #cumle_soru_sayisi').removeClass('is-invalid');
    $('#coktan_uyari, #cumle_uyari').hide();
    
    // Seviye adı seçimini sıfırla
    $('#seviye_adi').val('');
});

// Atama düzenleme modal'ı kapandığında formu temizle
$('#atamaDuzenleModal').on('hidden.bs.modal', function() {
    $('#atamaDuzenleForm')[0].reset();
    $('#atama_id').val('');
    $('#edit_sinav_id').val('');
    $('#edit_donem_id').html('<option value="">Dönem Seçin</option>').prop('disabled', false);
    $('#edit_grup_id').html('<option value="">Önce dönem seçin</option>').prop('disabled', true);
    $('#edit_ders_id').html('<option value="">Önce grup seçin</option>').prop('disabled', true);
});

// Sertifika oluşturma modal'ı kapandığında formu temizle
$('#sertifikaOlusturModal').on('hidden.bs.modal', function() {
    $('#sertifikaForm')[0].reset();
});
</script>

<!-- Sertifika Oluşturma Modal -->
<div class="modal fade" id="sertifikaOlusturModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Manuel Sertifika Oluştur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="sertifikaForm" onsubmit="event.preventDefault(); sertifikaOlustur();">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Ad Soyad <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="ad_soyad" id="sertifika_ad_soyad" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Seviye <span class="text-danger">*</span></label>
                                <select class="form-select" name="seviye" id="sertifika_seviye" required>
                                    <option value="">Seviye Seçiniz</option>
                                    <option value="A1">A1</option>
                                    <option value="A2">A2</option>
                                    <option value="B1">B1</option>
                                    <option value="B2_Lower">B2 Lower Beginning</option>
                                    <option value="B2_Midpoint">B2 Midpoint</option>
                                    <option value="B2_Upper">B2 Upper</option>
                                    <option value="C1">C1</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Sertifika Tarihi <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" name="sertifika_tarihi" id="sertifika_tarihi" 
                                       value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="ri-information-line me-2"></i>
                        Bu form ile manuel olarak sertifika oluşturabilirsiniz. Oluşturulan sertifika, belirtilen seviye için geçerli olacaktır.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Sertifika Oluştur</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require 'footer.php'; ?>
