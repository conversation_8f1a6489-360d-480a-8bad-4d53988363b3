/* Akt<PERSON> */
.active-exam-alert {
    background: white;
    border: 3px solid #ff6b35;
    border-radius: 20px;
    box-shadow: rgba(255, 107, 53, 0.55) 3px;
    animation: activeAlertPulse 3s ease-in-out infinite;
    position: relative;
    overflow: hidden;
    padding: 25px;
}



@keyframes activeAlertPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.03);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }
}

@keyframes neonBorder {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes borderGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.5), 0 0 40px rgba(40, 167, 69, 0.3), 0 0 60px rgba(40, 167, 69, 0.1);
    }
    50% {
        box-shadow: 0 0 30px rgba(40, 167, 69, 0.8), 0 0 60px rgba(40, 167, 69, 0.5), 0 0 90px rgba(40, 167, 69, 0.2);
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

.alert-icon {
    font-size: 4rem;
    animation: bounce 2s ease-in-out infinite;
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3));
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    40% {
        transform: translateY(-15px) rotate(5deg);
    }
    60% {
        transform: translateY(-8px) rotate(-3deg);
    }
}

.active-exam-alert h4 {
    color: #28a745 !important;
    text-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
    margin-bottom: 5px;
}

.active-exam-alert p {
    color: #333 !important;
    font-weight: 600;
    margin-bottom: 15px;
}

/* Sınav Detayları */
.exam-details {
    background: rgba(40, 167, 69, 0.05);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 10px;
    background: rgba(167, 97, 40, 0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 1px solid rgba(167, 123, 40, 0.2);
}

.detail-item:hover {
    background: rgba(255, 0, 0, 0.9);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);
}

.detail-icon {
    font-size: 1.5rem;
    margin-bottom: 5px;
    filter: drop-shadow(0 2px 4px rgba(40, 167, 69, 0.3));
}

.detail-label {
    color: #666;
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 3px;
}

.detail-value {
    color: #28a745;
    font-size: 1.1rem;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(40, 167, 69, 0.2);
}



/* Responsive Tasarım */
@media (max-width: 959px) {
    .active-exam-alert {
        padding: 20px;
    }
    
    .alert-icon {
        font-size: 3rem;
    }
    
    .exam-details {
        padding: 15px;
    }
    
    .detail-item {
        padding: 8px;
    }
    
    .detail-icon {
        font-size: 1.2rem;
    }
    
    .detail-label {
        font-size: 0.8rem;
    }
    
    .detail-value {
        font-size: 1rem;
    }
}

@media (max-width: 639px) {
    .active-exam-alert {
        padding: 15px;
    }
    
    .alert-icon {
        font-size: 2.5rem;
    }
    
    .exam-details {
        padding: 10px;
    }
    
    .detail-item {
        padding: 6px;
        margin-bottom: 10px;
    }
    
    .active-exam-alert .exam-btn {
        width: 100%;
        padding: 10px 0px;
        font-size: 1rem;
    }
}

/* Sınav Kartları - Odevlerim.php Assignment Card Stilinde */
.exam-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 22px;
    transition: all 0.3s ease;
    position: relative;
    overflow: visible;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.exam-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Sınav Durumu Badge'leri */
.exam-status-badge {
    position: absolute;
    top: -12px;
    right: 20px;
    z-index: 10;
}

.exam-status-text {
    color: white;
    padding: 10px 22px;
    border-radius: 25px;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.exam-status-text.active {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    animation: pulse 2s ease-in-out infinite;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
}

.exam-status-text.success {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
}

.exam-status-text.failed {
    background: linear-gradient(135deg, #ff4757, #ff3742);
}

.exam-status-text.completed {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
}

.exam-status-text {
    background: linear-gradient(135deg, #6c757d, #495057);
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Sınav İkonları */
.exam-icon {
    font-size: 4rem;
    margin-right: 20px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Sınav Başlığı */
.exam-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
}

.exam-subtitle {
    color: #7f8c8d;
    font-style: italic;
    margin-bottom: 10px;
}

/* Sınav Meta Bilgileri */
.exam-meta {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.exam-meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(0, 0, 0, 0.05);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
}

.exam-meta-icon {
    font-size: 1rem;
}

.exam-meta-text {
    font-weight: 500;
    color: #34495e;
}

/* Sınav Butonları */
.exam-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 15px;
    padding: 10px 20px;
    min-height: 30px;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}


@keyframes buttonPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

.exam-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
    text-decoration: none;
}

.exam-btn.success {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.exam-btn.success:hover {
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.exam-btn.failed {
    background: linear-gradient(135deg, #dc3545, #c82333);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.exam-btn.failed:hover {
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.exam-btn.pink {
    background: linear-gradient(135deg, #e91e63, #ad1457);
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
}

.exam-btn.pink:hover {
    box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
}

.exam-btn.disabled {
    background: linear-gradient(135deg, #6c757d, #495057);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    cursor: not-allowed;
    opacity: 0.7;
}

.exam-btn.disabled:hover {
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    transform: none;
}

/* Aktif Sınav Animasyonu */
.exam-card.active-exam {
    border: 3px solid #ff6b35;
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
    position: relative;
    background: rgba(255, 255, 255, 0.95);
}

.exam-card.active-exam::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: white;
    background-size: 400% 400%;
    border-radius: 19px;
    z-index: -1;
    animation: borderGlow 3s ease-in-out infinite;
    border: 3px solid #ff6b35;
}

.exam-card.active-exam.pulse {
    animation: activePulse 2s infinite;
}

@keyframes activePulse {
    0% {
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 107, 53, 0.6);
    }
    100% {
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
    }
}

@keyframes borderGlow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Başarılı Sınav */
.exam-card.success-exam {
    border: 2px solid #28a745;
    box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
}

/* Başarısız Sınav */
.exam-card.failed-exam {
    border: 2px solid #dc3545;
    box-shadow: 0 0 20px rgba(220, 53, 69, 0.3);
}

/* Modal Stilleri */
.uk-modal-dialog {
    border-radius: 16px;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    text-align: center;
}

.modal-body {
    padding: 30px;
}

/* Responsive Tasarım */
@media (max-width: 959px) {
    .exam-title {
        font-size: 1.25rem;
    }
    
    .exam-meta {
        gap: 10px;
    }
    
    .exam-meta-item {
        font-size: 0.8rem;
        padding: 5px 10px;
    }
    
    .exam-btn {
        padding: 12px 24px;
        font-size: 14px;
    }
}

@media (max-width: 639px) {
    .exam-title {
        font-size: 1.125rem;
    }
    
    .exam-meta {
        flex-direction: column;
        gap: 8px;
    }
    
    .exam-btn {
        width: 100%;
        justify-content: center;
        padding: 15px 20px;
    }
    
    .exam-icon {
        font-size: 3rem;
        margin-right: 15px;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.3);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Boş Durum */
.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
}

.empty-state-text {
    color: #7f8c8d;
    font-size: 1rem;
}
