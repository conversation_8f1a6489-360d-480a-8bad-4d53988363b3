<?php
session_start();
require_once 'db/baglanti.php';

// Kullanıcı giriş kontrolü
if (!isset($_SESSION['uye_id'])) {
    http_response_code(401);
    echo 'Oturum açmanız gerekiyor';
    exit;
}

$uye_id = $_SESSION['uye_id'];

// Yeni yaklaşım: sinav_id ve token ile
$sinav_id = $_GET['sinav_id'] ?? '';
$token = $_GET['token'] ?? '';
$dosya_yolu = $_GET['dosya'] ?? '';

if (!empty($sinav_id) && !empty($token)) {
    // CSRF token kontrolü
    if (!isset($_SESSION['csrf_token']) || $token !== $_SESSION['csrf_token']) {
        http_response_code(403);
        echo 'Geçersiz güvenlik token\'ı';
        exit;
    }

    // Sertifika dosya yolunu al
    $stmt = $db->prepare("
        SELECT sss.sertifika_dosya_yolu
        FROM seviye_sinav_sonuclari sss
        WHERE sss.uye_id = ? AND sss.sinav_id = ? AND sss.puan >= 70 AND sss.sertifika_olusturuldu = 1
        ORDER BY sss.id DESC LIMIT 1
    ");
    $stmt->execute([$uye_id, $sinav_id]);
    $sertifika = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$sertifika) {
        http_response_code(403);
        echo 'Bu sınav için sertifika bulunamadı';
        exit;
    }

    $dosya_yolu = $sertifika['sertifika_dosya_yolu'];

} elseif (empty($dosya_yolu)) {
    http_response_code(400);
    echo 'Dosya yolu veya sınav bilgisi belirtilmedi';
    exit;
}

// Güvenlik kontrolü - kullanıcının kendi sertifikası mı?
$stmt = $db->prepare("
    SELECT sss.sertifika_dosya_yolu 
    FROM seviye_sinav_sonuclari sss
    WHERE sss.uye_id = ? AND sss.sertifika_dosya_yolu = ? AND sss.sertifika_olusturuldu = 1
");
$stmt->execute([$uye_id, $dosya_yolu]);
$sertifika = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$sertifika) {
    http_response_code(403);
    echo 'Bu sertifikaya erişim izniniz yok';
    exit;
}

// Dosya var mı kontrol et
$absolute_dosya_yolu = __DIR__ . '/' . $dosya_yolu;
if (!file_exists($absolute_dosya_yolu)) {
    http_response_code(404);
    echo 'Sertifika dosyası bulunamadı: ' . $absolute_dosya_yolu;
    exit;
}

// Preview modu kontrolü
$preview = isset($_GET['preview']) && $_GET['preview'] == '1';

if ($preview) {
    // Preview modu - iframe içinde göstermek için
    header('Content-Type: application/pdf');
    header('Content-Disposition: inline; filename="' . basename($dosya_yolu) . '"');
    header('Content-Length: ' . filesize($absolute_dosya_yolu));
    header('Cache-Control: public, max-age=3600');
} else {
    // İndirme modu
    $dosya_adi = basename($dosya_yolu);
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="' . $dosya_adi . '"');
    header('Content-Length: ' . filesize($absolute_dosya_yolu));
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
}

readfile($absolute_dosya_yolu);
exit;
?>
