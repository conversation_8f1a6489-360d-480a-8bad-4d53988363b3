{"version": 3, "sources": ["../scss/_variables.scss", "dark-style.scss"], "names": [], "mappings": "AAKA;AAqCA;AASA;AAeA;AAaA;AAOA;AAQA;AC7FA;EACI,ODwFQ;ECvFR,kBDqFO;;;AClFX;AAAA;EAEI,YDgFO;;;AC7EX;AAwEI;AAiBA;AAuBA;AAOA;AAeA;AAyIA;AAWA;AAiCA;AAeA;AAMA;AA+BA;AAkBA;AAoCA;AAoCA;AAqCA;AA+CA;AAiHA;AAyCA;AA6KA;AAOA;AAkEA;AAOA;AA4MA;AAgIA;AAoJA;AAmKA;AA2EA;AAOA;AA2FA;AA0NA;AAgBA;AA8PA;AA+MA;AAmMA;AA6HA;AAqBA;AAiBA;AAkDA;AAoEA;AAOA;AA4BA;AAgFA;AAwIA;AA4DA;AACA;AAAA;AAAA;AAGA;AAgCA;AAgBA;AAeA;AACA;AAkHA;AAmDA;AA2JA;AACA;AACA;AAmHA;AAgBA;AAyBA;AA6BA;AAuGA;AA4CA;AAsWA;AACA;AACA;AACA;AAoCA;AAqGA;AACA;AAsDA;AA0BA;AA6GA;AA4HA;AA2nDA;AAwDA;AAqDA;AA0FA;AAgIA;;AA7mNA;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;AAAA;EAEI;;AAEJ;EACI,qBD0DK;;ACxDT;EACI,ODFD;ECGC,kBDPF;;ACSF;EACI,ODkDI;;AChDR;EACI;AAAA;IAEI;;EAEJ;IACI,cDbL;;ECeC;AAAA;AAAA;AAAA;IAII;;EAEJ;AAAA;AAAA;AAAA;IAII;;;AAIJ;EAEI,YDuBE;;ACpBV;EACI,YDmBM;EClBN;EACA,ODpDF;;ACsDF;EACI,OD7CF;EC8CE,kBDaM;ECZN;;AAEJ;EACI;EACA,cDUK;;ACPT;EACI,cDMK;;ACLL;EACI,ODGA;;ACDJ;EACI,YDDE;;ACGN;EACI,YDJE;;ACMN;EACI;EACA,qBDNC;;ACUT;EACI,cDXK;ECYL,YDdM;;ACgBV;EACI,ODhBI;;ACiBJ;EACI,cDjBC;;ACmBL;EACI,ODxGD;ECyGC;;AAIJ;EACI;;AAEJ;EACI;;AAIR;EACI;;AAEJ;EACI;;AAGJ;EACI,YD3CM;EC4CN;;AAEJ;EACI;;AAEJ;EACI;EACA,cDjDK;;ACmDT;EACI,cDpDK;;ACwDL;EACI;;AAEJ;EACI;;AAGR;EACI,kBDnEG;;ACsEH;EACI,ODxJD;;AC0JH;EACI;;AAEJ;EACI;;AAGR;EACI,ODvKO;ECwKP;EACA;;AACA;EACI,OD3KG;EC4KH;EACA;;AAEJ;EAEI;;AAEJ;EAEI,ODrLG;ECsLH;EACA;;AAGA;EAEI,OD5LD;EC6LC;EACA;;AAIZ;EACI,OD3GI;EC4GJ;EACA,cD5GK;;AC6GL;EACI,OD/GA;ECgHA;EACA,cDhHC;;ACkHL;EAEI;;AAEJ;EAEI;EACA;EACA;;AAGA;EAEI;EACA,kBDlIF;ECmIE,cDjIH;;ACqIT;EACI,ODvII;ECwIJ;EACA,cDxIK;;ACyIL;EACI,ODnOG;ECoOH;;AAGR;EACI,ODhJI;ECiJJ,kBDlJM;ECmJN,cDjJK;;ACkJL;EACI,ODpJA;ECqJA,kBDtJE;ECuJF,cDrJC;;ACuJL;EAEI;;AAEJ;EAEI;EACA,kBDhKE;ECiKF,cDxNL;;AC2NK;EAEI;EACA;EACA,cDtKH;;AC0KT;EACI;EACA;EACA,cD7KK;;ACgLL;EAEI,kBDtQD;ECuQC,cDvQD;;AC0QP;EACI,cD3QG;;AC4QH;EAGI,kBD/QD;ECgRC,cDhRD;;ACoRP;EACI,kBDnMM;ECoMN;EACA,YDlMU;;ACoMd;EACI;EACA,kBDvMK;ECwML;;AAGJ;EACI,cD5MK;;AC8MT;EACI,kBD/MK;;ACiNT;EACI,qBDlNK;;ACqNL;EACI;;AAGR;EACI;;AAEJ;EACI,kBD7NK;;AC+NT;EACI,kBDhOK;;ACkOT;EACI;;AACA;EACI;;AACA;EACI;;AAKZ;EACI;;AAGA;EACI;;AACA;EACI,OD5ST;;AC+SC;EACI;;AAIR;EACI;EACA,kBD7PK;EC8PL;;AAGJ;EACI,cDlQK;ECmQL;;AAEJ;EACI,kBDtQK;;ACyQL;EACI;;AAGR;EACI;;AAEJ;EACI,kBDjRK;;ACmRT;EACI,kBDpRK;;ACsRT;EACI;;AACA;EACI;;AACA;EACI;;AAKZ;EACI;;AAGA;EACI;;AACA;EACI,ODhWT;;ACmWC;EACI;;AAGR;EACI,qBD/SK;;ACkTT;AAAA;EAEI,OD7WD;;ACgXC;EAEI,ODlXL;;ACsXC;EAEI,ODxXL;;ACiYC;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI,kBDrYL;;ACwYH;EACI,ODzYD;;AC2YH;EACI;;AAGJ;AAAA;EAEI,ODjZD;;ACoZC;EAEI,ODtZL;;AC0ZC;EAEI,OD5ZL;;ACqaC;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI,kBDzaL;;AC4aH;EACI,OD7aD;;AC+aH;EACI;;AAGJ;EACI;EACA,kBD9XM;EC+XN,cD7XK;;AC+XT;EACI,kBDhYK;;ACkYT;EACI,ODpYI;;ACsYR;EACI;;AAGA;EAII;;AAEJ;EAEI,OD1cL;;AC6cH;EACI,cDrZK;ECsZL;;AAEJ;EACI;;AAEJ;EACI,OD7ZI;;ACgaR;AAAA;EAEI;EACA;EACA;EACA;;AAEJ;EACI,kBDzaM;EC0aN;EACA;;AACA;EACI,OD5aA;;AC6aA;EAEI;EACA,kBDjbF;;ACobN;EACI,kBDnbC;;ACqbL;EACI;EACA;EACA;;AAGR;EACI,qBD5bK;;AC6bL;EACI;;AAGR;AAAA;EAEI,YDrcM;ECscN,kBDpcK;ECqcL,mBDrcK;;ACucT;EACI;;AAEJ;EACI,kBD7cM;;ACgdV;EACI,ODhdI;ECidJ;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA,kBD1dE;EC2dF,cDzdC;;AC2dL;EACI;;AAEJ;EAII;;AAEJ;EAEI;;AAEJ;EACI,OD1eA;EC2eA;;AAEJ;EACI,kBD/eE;;ACkfV;EACI;;AAEJ;EACI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;AAAA;AAAA;EAII;EACA;;AAEJ;AAAA;AAAA;AAAA;EAII;;AAEJ;AAAA;AAAA;AAAA;EAII;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI;;AAEJ;EACI,YD5hBM;EC6hBN;EACA,ODrlBD;;ACslBC;EACI,ODvlBL;;ACylBC;EAEI;;AAGR;EACI;;AAEJ;EACI,cD7nBG;;AC+nBP;EACI;EACA,cD7iBK;;AC+iBT;EACI,ODzmBD;EC0mBC,kBDroBG;ECsoBH,cDljBK;;ACojBT;EACI,cDrjBK;;ACujBT;EACI,ODjnBD;ECknBC;;AAEJ;EACI,cD5jBK;;AC+jBT;EACI,kBDlkBM;ECmkBN,cDjkBK;ECkkBL;;AAEJ;EACI,kBD7nBD;;AC+nBH;EACI,qBDxkBK;;AC0kBT;EACI,kBD3kBK;;AC8kBL;EACI;EACA;;AAEJ;EACI,OD5oBL;;AC8oBC;EACI;EACA,qBD7nBH;;AC8nBG;EACI,ODlpBT;;ACqpBC;EACI;;AACA;EACI,ODxpBT;;AC2pBC;EACI;EACA,kBDpmBC;;ACwmBT;EACI,qBDzmBK;;AC4mBL;AAAA;EAEI,ODvqBL;;AC0qBH;EACI;;AAEJ;EACI,ODtnBI;;ACwnBR;EACI,YD3nBG;;AC8nBH;EAEI,ODtrBL;;ACyrBH;EACI,ODloBI;;ACmoBJ;EACI,ODvtBD;;ACytBH;EACI;;AAGR;EACI,qBD1oBK;;AC4oBD;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EAEI,OD9sBb;;ACktBC;EACI;;AACA;EACI;;AAEJ;EACI;EACA;;AAIZ;EACI,ODzvBG;;AC0vBH;EACI;;AACA;EACI;;AAIZ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI,ODlvBD;;ACmvBC;EACI;EACA;;AACA;EAEI,ODzvBT;;AC2vBK;EACI;EACA;EACA;;AAGR;EACI,YD3sBE;EC4sBF,cD1sBC;EC2sBD;EACA;;AACA;EACI;;AACA;EACI;EACA;EACA;;AAKhB;EACI,kBD1tBM;EC2tBN,cDztBK;EC0tBL;;AACA;EACI,kBD9tBE;;ACkuBN;EACI,OD1xBL;;AC4xBC;EACI;EACA;EACA,cDtuBC;;ACyuBT;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI,kBDvvBM;ECwvBN,kBDtvBK;ECuvBL,mBDvvBK;ECwvBL,oBDxvBK;;AC0vBT;AAAA;EAEI,cD5vBK;;AC8vBT;EACI;EACA,cDhwBK;;ACiwBL;EACI;;AAGR;EACI,cD/zBD;;ACg0BC;EACI;;AAGR;EACI,YD9wBM;;ACgxBV;EACI;;AAEJ;EACI;;AAIA;EAEI,ODj1BL;;ACq1BH;EACI;EACA;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI,qBDtyBK;;ACwyBT;EACI;EACA;;AAEJ;EACI,kBD/yBM;ECgzBN;EACA;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI,kBDzzBM;;AC2zBV;AAAA;EAEI;;AAEJ;AAAA;EAEI,oBDj0BM;;ACm0BV;AAAA;EAEI;;AAEJ;AAAA;EAEI,qBDz0BM;;AC20BV;AAAA;EAEI,qBD70BM;;AC+0BV;AAAA;EAEI;;AAEJ;AAAA;EAEI,mBDr1BM;;ACu1BV;EACI;EACA,kBDz1BM;EC01BN;;AAEJ;EACI;;AAGJ;EACI,kBDl2BG;;ACo2BP;EACI,OD35BD;;AC85BH;EACI,ODv2BI;;ACw2BJ;EACI,qBDx2BC;;AC22BT;EACI,qBD52BK;;AC82BT;AAAA;EAEI;;AAEJ;AAAA;AAAA;EAGI;;AAGA;AAAA;EAEI,kBD53BE;;AC+3BV;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI,cDn4BK;;ACq4BT;EACI;;AAGA;AAAA;EAEI,kBD94BD;EC+4BC,OD74BA;;ACg5BR;EACI,kBDj5BI;;ACm5BA;AAAA;EAEI,kBDv5BL;;AC25BP;EACI;;AAEI;AAAA;EAEI;;AAIZ;EACI,kBDl6BK;;ACo6BT;EACI;;AAEI;AAAA;EAEI;;AAIZ;EACI;;AAEI;AAAA;EAEI;;AAIZ;AAAA;EAEI,ODz7BI;EC07BJ,kBD17BI;EC27BJ;;AAEJ;AAAA;EAEI;EACA,kBDj8BM;ECk8BN,cDh8BK;;ACk8BT;EACI,ODr8BM;ECs8BN,kBDv8BG;;ACw8BH;AAAA;AAAA;EAGI;;AAKA;EACI,kBDh9BF;;ACk9BF;EACI;;AAGR;EACI,kBDt9BA;;ACy9BR;EACI,cDz9BK;;AC09BL;EACI;;AACA;EACI,qBD79BH;;ACg+BG;EAEI;;AAGR;EACI,YDx+BF;;AC4+BV;AAAA;EAEI,OD7+BI;;ACg/BJ;AAAA;EAEI,kBDj/BC;;ACq/BL;AAAA;AAAA;AAAA;EAII;;AAIJ;AAAA;AAAA;AAAA;EAII;;AAIJ;EACI,YDzgCD;EC0gCC,ODxgCA;;AC0gCJ;EACI,kBD5gCE;;AC+gCV;EACI;EACA;;AACA;EACI;;AAEJ;EACI,OD7kCL;EC8kCK;;AACA;EACI,YDzhCF;;AC6hCV;AAAA;AAAA;EAGI;;AAEJ;EACI,ODliCI;;ACsiCA;EACI,qBDtiCH;;ACyiCL;EACI,cD1iCC;;AC6iCT;EACI,OD/iCI;;ACkjCR;EACI,ODnjCI;ECojCJ,kBDtjCG;;ACwjCP;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA,OD3nCD;;AC6nCH;EACI;EACA,OD/nCD;;ACioCH;EACI;EACA,ODnoCD;;ACqoCH;EACI;EACA,ODvoCD;;ACyoCH;EACI;EACA,OD3oCD;;AC6oCH;EACI;EACA,OD/oCD;;ACipCH;EACI;EACA,ODnpCD;;ACqpCH;EACI;EACA,ODvpCD;;ACypCH;EACI;EACA,OD3pCD;;AC6pCH;EACI;EACA,OD/pCD;;ACiqCH;EACI,kBD3mCM;EC4mCN,ODnqCD;;ACqqCH;EACI;EACA,ODvqCD;;ACyqCH;EACI;EACA,OD3qCD;;AC6qCH;EACI;EACA,OD/qCD;;ACirCH;EACI;EACA,ODnrCD;;ACqrCH;EACI;EACA,ODvrCD;ECwrCC;EACA,ODzrCD;;AC2rCH;EACI;EACA,OD7rCD;;AC+rCH;EACI;EACA,ODjsCD;;ACmsCH;EACI,kBDrsCD;ECssCC,ODrsCD;;ACusCH;EACI;EACA,ODzsCD;;AC2sCH;EACI;EACA,OD7sCD;;AC+sCH;EACI;EACA,ODjtCD;;ACmtCH;EACI;EACA,OD7pCI;;AC+pCR;EACI,kBDjqCM;;ACmqCV;EACI;EACA,ODpuCF;ECquCE,cDruCF;;ACsuCE;EACI,cDvuCN;;AC0uCF;EACI,cD1qCK;;AC4qCT;EACI,cD7qCK;EC8qCL,kBDhrCM;;ACmrCV;EACI,kBDprCM;ECqrCN,cDnrCK;;ACqrCT;AAAA;EAEI,kBDzrCM;;AC2rCV;AAAA;EAEI,oBD7rCM;;AC+rCV;AAAA;EAEI,qBDjsCM;;ACmsCV;AAAA;EAEI,mBDrsCM;;ACusCV;EACI,kBDxsCM;;AC0sCV;EACI,kBD5sCG;EC6sCH,cD1sCK;;AC6sCL;EACI;EACA,ODxwCL;;AC0wCC;EAEI,kBDvyCD;;ACyyCH;EAEI,qBD3yCD;;AC6yCH;EAEI,mBD/yCD;;ACizCH;EAEI,oBDnzCD;;ACszCP;EACI,kBDruCM;ECsuCN;;AAEJ;AAAA;EAEI,kBD1uCM;;AC4uCV;AAAA;EAEI,kBD9uCM;;ACgvCV;AAAA;EAEI,oBDlvCM;;ACovCV;AAAA;EAEI,oBDtvCM;;ACwvCV;AAAA;EAEI,qBD1vCM;;AC4vCV;AAAA;EAEI,qBD9vCM;;ACgwCV;AAAA;EAEI,qBDhwCK;;ACkwCT;AAAA;EAEI;EACA,mBDvwCM;;ACywCV;AAAA;EAEI;EACA,mBD5wCM;;AC8wCV;EACI,OD9wCI;EC+wCJ,kBDhxCM;ECixCN,cD/wCK;;ACixCT;EACI;;AAGA;EACI,OD/0CL;ECg1CK;;AAEJ;EAEI;;AAIJ;EACI,ODz1CL;EC01CK;;AAEJ;EAEI;;AAGR;AAAA;EAEI;;AAEJ;EACI;;AACA;EACI;EACA;EACA,OD12CL;;AC42CC;EAEI;;AAEJ;EAEI;;AAEJ;EAEI;;AAEJ;EAEI;;AAKJ;EACI,ODh4CL;;ACk4CC;EACI,ODn4CL;ECo4CK;;AAEJ;EACI;;AACA;EACI,kBDh1CH;;ACo1CT;EACI,kBDr1CK;;ACu1CT;EACI;;AAEJ;EACI;EACA,cD51CK;EC61CL,YD/1CM;;ACg2CN;EACI,cD/1CC;ECg2CD;;AACA;EACI;EACA,kBDr2CF;;ACw2CN;EACI;EACA,kBD12CE;;AC62CV;EACI;EACA,kBD/2CM;ECg3CN,cD92CK;;AC+2CL;EACI,ODz6CL;EC06CK,mBDj3CC;;ACq3CL;EACI,YDx3CE;ECy3CF;;AACA;EACI;;AAEJ;EACI;;AAGR;EACI,kBDh4CC;ECi4CD;;AAEJ;EACI,YDt4CE;;ACu4CF;EACI;;AAEJ;EACI;;AAGR;EACI,kBD74CC;EC84CD;;AAEJ;EACI,YDn5CE;ECo5CF;;AACA;EACI;;AAEJ;EACI;;AAGR;EACI,kBD35CC;EC45CD;;AAGR;EACI,cDh6CK;ECi6CL;;AAII;EAGI;EACA;;AAGR;EACI;;AAEJ;EACI;EACA,kBDj7CC;;ACo7CT;EACI;;AAEJ;EACI;;AAEJ;EACI,YD77CM;EC87CN,cD57CK;;AC67CL;EACI,YDv/CL;ECw/CK;;AAGR;EACI;;AAEJ;AAAA;EAEI,kBDv8CK;;AC28CL;AAAA;EACI,kBD58CC;;AC88CL;AAAA;EACI;;AAGR;EACI;;AAEJ;EACI,YD1iDG;;AC4iDP;AAAA;EAEI,YD59CM;EC69CN,cD39CK;;AC49CL;AAAA;EACI,YDthDL;ECuhDK;;AAGR;EACI,YDp+CM;;ACs+CV;EACI,OD9hDD;;ACiiDH;EACI,YD3+CM;EC4+CN,mBD1+CK;EC2+CL;;AAEJ;EACI;;AAEJ;EACI;IACI,qBDl/CC;;;ACq/CT;EACI;AAAA;IAEI,cDx/CC;;;AC2/CT;EACI;IACI,ODtjDL;;;ACyjDH;EACI;IACI,OD3jDL;;;AC8jDH;EACI;IACI;;EAEJ;IACI;;EAEJ;IACI;;;AAGR;EACI,OD1kDD;EC2kDC;;AAEJ;EACI,cDrhDK;;ACuhDT;EACI,YD1hDM;;AC4hDV;EACI;EACA,ODrlDD;ECslDC;;AAEJ;EACI,cDhiDK;ECiiDL;;AAEJ;EACI,OD7lDD;;AC8lDC;EACI;;AAGR;EACI;;AAEJ;EACI,ODtmDD;;ACwmDH;EACI;;AAGJ;EACI,YDtjDM;;ACujDN;EACI,YDxjDE;;AC4jDV;EACI;;AAEJ;EACI;;AAEJ;EACI,OD1nDD;;AC2nDC;EACI;;AAIJ;EACI;;AAGR;EAEQ;IAEI;;;AAIZ;EACI;IACI,YDvlDE;;ECylDN;IACI,ODhpDL;;ECopDS;IACI;;EAEJ;IACI;;EAGR;IACI,OD7pDT;;;ACiqDH;EACI,ODlqDD;;ACoqDH;EACI,YD9mDM;EC+mDN,cD7mDK;;AC+mDT;EACI;AAAA;IAEI,YDpnDE;;;ACynDF;EACI;EACA;;AAEJ;EACI;EACA;;AAIJ;EAEI;;AAIZ;EACI;;AAEJ;EACI,cDjrDC;;ACmrDL;AAAA;EAEI,ODnuDG;;ACquDP;EACI,cDlpDK;;ACqpDT;EACI;IACI,YDzpDE;IC0pDF,cDxpDC;;EC0pDL;IACI,YDptDL;ICqtDK;;;AAGR;EACI;IACI;IACA;;;AAGR;EACI;;AACA;EACI;EACA;;AAGR;EACI;;AACA;EACI;EACA;;AAIJ;EACI;;AAEJ;EACI;;AAGR;EACI,ODrvDD;;ACuvDH;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI,YDjtDG;;ACmtDP;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGA;EACI,YDrzDD;ECszDC,OD3xDL;;AC8xDH;EACI,OD/zDO;;ACk0DP;EACI,OD9zDD;;ACg0DH;EAKI,ODzzDD;;ACqzDC;EACI;EACA,ODxyDT;;AC4yDC;EAKI,OD1zDJ;;ACszDI;EACI;EACA,OD/yDT;;ACmzDC;EACI,OD/zDH;;ACg0DG;EACI;EACA,ODvzDT;;AC0zDC;EACI,ODn0DN;;ACo0DM;EACI;EACA,OD9zDT;;ACk0DH;EACI,OD3wDI;;AC4wDJ;EACI,mBD5wDC;;AC+wDT;EACI;;AAGA;EACI;;AACA;EACI;;AAGR;EACI;;AAIJ;AAAA;EAEI,cDhyDC;ECiyDD,ODlyDA;;ACoyDJ;EACI;;AAGR;EACI,cDxyDK;;AC0yDT;EAII,kBDhzDM;ECizDN,cD/yDK;;AC2yDL;EACI,cD5yDC;;ACizDT;EACI,cDlzDK;;ACozDT;EAKI,cDzzDK;EC0zDL,OD3zDI;;ACszDJ;EACI,YD14DD;EC24DC,ODh3DL;;ACq3DH;AAAA;AAAA;EAGI,ODh0DI;;ACk0DR;EACI,YDp0DM;ECq0DN,cDn0DK;;ACq0DT;EACI;;AAEJ;EACI;;AAEJ;EACI,ODr4DD;;ACu4DH;EACI;;AACA;EACI;;AAGA;EACI;;AAEJ;EAGI,ODn5DT;;ACu5DK;EACI;;AAEJ;EAGI,OD75DT;;ACi6DH;EACI,QD32DM;;AC62DV;EACI;;AAGJ;EACI,YDl3DM;;ACo3DV;EACI,oBDn3DK;;ACq3DT;EACI,mBDt3DK;;ACw3DT;EACI,kBDz3DK;;AC23DT;EACI,qBD53DK;;AC+3DT;EACI,YDl4DM;ECm4DN,cDj4DK;ECk4DL;;AACA;EACI,YDt4DE;ECu4DF,ODt4DA;ECu4DA,qBDt4DC;;AC04DL;EACI;;AAGR;EACI;;AAEJ;EACI,kBDp5DM;ECq5DN;;AAEJ;EACI,OD/8DD;;ACg9DC;EACI;;AAEJ;EACI;;AAGR;EACI;;AAEJ;EACI;EACA,cDn6DK;ECo6DL;;AAEJ;EACI,cDv6DK;ECw6DL,YD16DM;;AC46DV;EAMI;;AALA;EAGI;;AAIR;EACI;IACI;;;AAGR;EACI;;AAEJ;EACI,cD37DK;;AC67DD;EACI;;AAEJ;EACI,ODrhEL;;ACyhEP;EACI;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;EACA;;AAEJ;EACI,YDp9DM;ECq9DN,cDn9DK;;ACs9DL;EAEI,ODjhEL;;ACohEH;EACI,ODrhED;;ACuhEH;EAII,cDl+DK;;AC+9DL;EACI,cDh+DC;;ACo+DT;EAKI,cDz+DK;;ACq+DL;EACI,kBDx+DE;ECy+DF,OD3jED;;ACgkEH;EAEI,kBD9+DC;;ACg/DL;EACI,YDn/DE;;ACs/DV;EACI,OD9iED;;ACgjEK;EACI;EACA;EACA;;AAEJ;EACI;;AAIZ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI,cDlmEG;ECmmEH,YDjhEM;ECkhEN,ODpmEG;;ACsmEP;EACI,cD1lEF;EC2lEE,YDthEM;ECuhEN,OD5lEF;;AC8lEF;EACI,cDhmEG;ECimEH,YD3hEM;EC4hEN,ODlmEG;;AComEP;EACI,cDjnEG;;ACmnEP;EACI,kBDliEM;;ACqiEN;EACI,MD7lEL;;AC+lEC;EACI,MDxiEA;;AC4iEJ;EACI,YD9nEA;;ACioEA;AAAA;EAEI;;AAGR;EACI,YDvjEE;ECwjEF,OD/mEL;ECgnEK;;AAEJ;EACI;EACA,YD7jEE;;AC8jEF;EACI;EACA,oBDhkEF;;ACokEV;EACI;;AAEJ;EACI,cDtkEK;ECukEL;EACA,ODzkEI;EC0kEJ;;AACA;EACI;EACA;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI,cDzlEK;EC0lEL;;AAEJ;EACI,ODtpED;;ACypEC;EACI;;AAEJ;EACI;;AAGR;EACI,ODjqED;;ACmqEH;EACI,ODpqED;;ACqqEC;EAGI,ODxqEL;;AC2qEH;EACI,OD5qED;;AC8qEH;EACI;;AAEJ;EACI;EACA;;AAKI;EACI;;AACA;EACI;;AAIZ;EACI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI,cDjpEK;ECkpEL;EACA,YDrpEM;;ACspEN;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;AAAA;EAEI;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;AAAA;AAAA;AAAA;AAAA;EAKI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI,ODjzEL;;ACmzEC;EACI;;AAEJ;AAAA;AAAA;AAAA;EAII;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;AAAA;AAAA;EAGI;;AAEJ;EACI;;AAGA;EACI;;AACA;EACI;;AAGR;EACI;;AAGR;AAAA;AAAA;EAGI;;AAGR;EACI;EACA,ODl3ED;;ACo3EH;EACI,YDh4EC;ECi4ED,ODt3ED;;ACw3EH;EACI;EACA,OD13ED;;AC43EH;EACI,YDv4EC;ECw4ED,OD93ED;;ACg4EH;EACI,YDz4EF;EC04EE,ODl4ED;;ACq4EH;EACI;EACA,OD/0EI;ECg1EJ,cD/0EK;;ACi1ET;EACI;;AACA;EAGI;EACA,kBDz1EE;;AC41EV;AAAA;AAAA;AAAA;AAAA;EAKI,kBDj2EM;ECk2EN,cDh2EK;ECi2EL;;AAGA;EAEI;EACA;;AAGR;EACI;EACA;;AAEI;EAEI;EACA;;AAEJ;EACI,OD76ET;EC86ES;EACA;;AAIZ;EACI;EACA;;AAEI;EAEI;EACA;;AAEJ;EACI,OD77ET;EC87ES;EACA;;AAIZ;EACI;EACA;;AAEI;EAEI,ODp9EP;ECq9EO;;AAEJ;EACI,OD78ET;EC88ES,kBDz9EP;EC09EO,cD19EP;;AC89EL;EACI;EACA;;AAEI;EAEI,ODj+EV;ECk+EU;;AAEJ;EACI,OD79ET;EC89ES;EACA;;AAIZ;EACI;EACA;;AAEI;EAEI,ODn/EP;ECo/EO;;AAEJ;EACI,OD7+ET;EC8+ES,kBDx/EP;ECy/EO,cDz/EP;;AC6/EL;EACI;EACA;;AAEI;EAEI,ODlgFR;ECmgFQ;;AAEJ;EACI,OD7/ET;EC8/ES,kBDvgFR;ECwgFQ,cDxgFR;;AC4gFJ;EACI;EACA;;AAEI;EAEI;EACA;;AAEJ;EACI,OD7gFT;EC8gFS;EACA;;AAIZ;EACI;EACA;;AAEI;EAEI;EACA;;AAEJ;EACI,OD7hFT;EC8hFS;EACA;;AAIZ;EACI,cD3+EK;EC4+EL;;AACA;EACI;;AAEJ;EACI;;AAKA;EACI;EACA,ODz/EJ;;AC4/EJ;EACI,OD7/EA;;ACggFR;EACI,cDhgFK;;ACkgFT;EACI,cDngFK;;ACqgFT;EACI,ODvgFI;;ACygFR;EACI,OD1gFI;;AC4gFR;EACI,OD7gFI;;ACghFR;EACI;;AACA;EAEI;;AAGR;EACI;EACA;;AAEJ;EACI;IACI;;;AAIJ;EACI,ODliFA;;ACmiFA;EAEI,ODriFJ;;ACyiFA;EACI;;AACA;EAEI;;AAEJ;EACI;;AAGR;AAAA;EAEI;;AAGA;EAEI;;AAIZ;EACI;EACA;;AAEJ;EACI;;AACA;EACI;;AACA;EAEI;;AAMZ;EACI,ODvoFL;;ACwoFK;EAEI,OD1oFT;;AC8oFK;EACI;;AACA;EAEI;;AAEJ;EACI;;AAGR;AAAA;EAEI,OD1pFT;;AC6pFS;EAEI,OD/pFb;;ACmqFC;EACI;EACA,cDlpFH;;ACopFD;EACI;;AACA;EACI,OD1qFT;;AC2qFS;EAEI,OD7qFb;;ACkrFH;EACI;IACI;;EACA;IACI;;EAGR;IACI;;;AAGR;EACI;IACI,YDxoFE;ICyoFF;IACA,kBDxoFC;;;AC4oFT;EACI,oBD7oFK;EC8oFL,mBD9oFK;EC+oFL,qBD/oFK;;ACipFT;EACI,cDlpFK;;ACopFT;EACI,ODtpFI;;ACupFJ;EACI,ODhtFL;;ACotFC;EAEI,ODjvFD;;ACqvFP;EACI,OD3tFD;;AC6tFH;EACI;;AAGA;EACI,qBD/sFH;;ACmtFD;EACI,qBD9qFC;;ACkrFT;EACI;EACA,YDtrFM;;ACwrFV;EACI;EACA,YD1rFM;;AC2rFN;EACI,ODnvFL;;ACuvFC;EACI,kBDjsFE;;ACmsFN;EACI,kBDpsFE;;ACusFV;EACI,OD/vFD;ECgwFC;EACA,YD5xFG;EC6xFH;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AAGR;EACI,YD1tFM;;AC4tFV;EACI;;AAEJ;EACI;;AAEJ;EACI,YDnuFM;;ACuuFN;EACI;EACA,kBDzuFE;EC0uFF,cDxuFC;;AC4uFL;EACI,OD9uFA;;ACgvFJ;EACI,ODp0FD;;ACu0FP;EACI,ODrvFI;;ACuvFR;EACI;;AAGA;EACI;;AAEJ;EACI;;AAIJ;EACI;EACA;;AAEJ;EACI;;AAGR;EACI,cD3wFK;;AC8wFL;EACI,ODhxFA;ECixFA,qBDhxFC;;ACkxFL;EACI,cDnxFC;ECoxFD;;AAKA;EACI,qBD1xFH;EC2xFG,kBD3xFH;;AC6xFD;EACI;EACA,kBDn0FP;ECo0FO,mBDp0FP;;ACs0FG;EACI,cDnyFH;;ACwyFT;EACI;;AAEJ;EACI,kBD9yFM;;ACizFV;EACI;;AAGA;EAEI,YDvzFE;;AC0zFV;EACI,cDl3FD;ECm3FC;;AAEJ;EACI;;AAEJ;EACI;;AAGA;EACI;;AAEJ;EACI;;AAIR;EACI,kBD50FK;;AC+0FL;EACI,YDl1FE;ECm1FF;;AACA;EACI;EACA,mBDp1FH;ECq1FG,oBDr1FH;ECs1FG;;AAGR;EACI,cD71FD;;AC81FC;EACI,ODr5FT;;ACw5FC;EACI;EACA;;AAIJ;AAAA;EAEI,YD37FD;;AC+7FH;AAAA;EAEI,YDr7FD;;ACy7FH;AAAA;EAEI,YDv7FH;;AC27FD;AAAA;EAEI,YD96FL;;ACk7FC;AAAA;EAEI;;AAIJ;AAAA;EAEI;;AAIJ;AAAA;EAEI,YD74FE;;ACi5FN;AAAA;EAEI;;AAGR;EACI;IACI;IACA;;;AAIR;EACI;EACA,kBD75FK;;AC85FL;EACI,cD/5FC;ECg6FD,YDn6FD;ECo6FC,ODl6FA;;ACs6FJ;EACI;;AAEJ;EACI;;AACA;EACI;;AAGR;EACI,cD/6FC;;ACg7FD;EACI;;AAIZ;EACI;;AAEJ;EACI,kBD37FM;EC47FN,kBD17FK;;AC47FT;EACI,YD/7FM;ECg8FN,qBD97FK;;ACg8FT;AAAA;AAAA;EAGI;;AAEJ;EACI;IACI,qBDv8FC;ICw8FD;;EAEJ;IACI;IACA;;;AAKA;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA,ODnhGT;;ACuhGH;EACI;IACI,ODzhGL;;EC2hGC;IACI;IACA,OD5hGL;;EC+hGK;IACI;;EAEJ;IACI,ODpiGT;;ECsiGK;IACI;;;AAIZ;EAEQ;IAEI,YD/kGD;;ECmlGH;IAEI,YDrlGD;;;ACylGX;EACI;IACI,kBDpgGE;;;ACugGV;EACI;;AAGA;EACI;;AAEJ;EACI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGJ;EACI;IACI;;;AAIJ;EACI;EACA,cD3iGC;;AC4iGD;EACI,qBD7iGH;;ACgjGL;AAAA;AAAA;EAGI,ODpjGA;;ACsjGJ;AAAA;EAEI;;AAEJ;EACI;EACA,cD3jGC;;AC4jGD;EACI,qBD7jGH;;ACgkGL;EACI,ODrpGD;;ACspGC;EACI;;AAKR;AAAA;EAEI,OD3kGA;;AC6kGJ;AAAA;EAEI;;AAEJ;EACI;EACA,cDllGC;;ACmlGD;EACI,qBDplGH;;ACulGL;EACI,OD5qGD;;AC6qGC;EACI;;AASZ;EAnrGJ;AAorGQ;AACA;AACA;AAWA;AAWA;AAIA;;EAzBA;IACI;IACA;;EAEJ;IACI;;EACA;IACI;;EAIR;IACI;;EACA;IAEI;;EAGR;IACI;;EAGJ;IACI;;;AAKR;EACI,YDtoGM;ECuoGN;EACA,cDtoGK;;ACwoGT;EACI,YD3oGM;EC4oGN,qBD1oGK;;AC4oGT;EACI,cD7oGK;;AC8oGL;EACI;;AAIR;EACI;;AAEJ;AAAA;EAEI;;AAEJ;EACI;;AACA;EACI;EACA;;AAKR;EACI,kBDtqGM;ECuqGN,cDrqGK;;ACsqGL;EACI,ODxqGA;;ACyqGA;AAAA;EAEI;EACA;;AAGA;EAEI,OD7uGd;;ACivGU;EAEI,ODnvGd;;ACsvGM;AAAA;EAEI;;AAIJ;EACI;;AAEJ;EACI,cDnsGH;ECosGG,kBDpsGH;;ACqsGG;EACI,kBDxsGN;ECysGM,ODxsGR;;AC0sGI;EACI,kBD5sGN;EC6sGM;;AACA;EACI,kBD/sGV;ECgtGU,OD/sGZ;;ACmtGA;EACI,kBDrtGF;ECstGE,ODrtGJ;;ACytGR;EACI,YD1tGI;EC2tGJ;;AAEJ;AAAA;AAAA;EAGI;EACA;;AAEJ;EACI;EACA;;AAGA;EACI,kBD1uGE;EC2uGF;;AAEJ;EACI,kBD9uGE;EC+uGF;EACA;;AAEJ;EACI,kBDnvGE;ECovGF;;AAEJ;EACI;EACA,YDxvGE;;AC2vGF;AAAA;EAEI;;AAGR;EACI;;AAEJ;EACI,ODnwGA;;ACswGR;EACI,YDxwGM;;AC0wGV;EACI,cDzwGK;EC0wGL;;AAGA;EACI,YDhxGE;;ACkxGN;EACI;;AAIR;EACI;;AAEJ;EACI;EACA,YD5xGM;;AC6xGN;EACI;;AAGR;EACI,cDhyGK;;ACiyGL;EACI,kBDpyGE;;ACsyGN;EACI;EACA;;AAIJ;EACI;;AAGR;EACI,cD/yGK;ECgzGL,YDlzGM;;ACmzGN;EACI;;AAEJ;EACI;EACA,YDxzGE;;AC0zGN;EACI;EACA;;AACA;EACI;;AAGR;EACI;;AAGR;AAAA;EAEI;;AAGJ;EACI;IACI,YD50GE;;;AC+0GV;EACI;IACI,kBDj1GE;;;ACo1GV;EACI,qBDn1GK;ECo1GL,YDt1GM;;ACw1GV;EACI,ODh5GD;;ACk5GH;EACI;;AACA;EACI;;AAGR;EACI,ODj2GI;ECk2GJ;EACA;EACA;EACA,oBDp2GK;;ACq2GL;EACI;;AAGR;EACI;;AACA;EACI;EACA;EACA;;AAGR;AAAA;EAEI;;AAEJ;EACI,ODv3GI;;ACw3GJ;EACI,OD58GD;;AC68GC;EAEI,OD/8GL;;ACk9GH;EAEI,ODp9GD;;ACu9GC;AAAA;EAEI;;AAIJ;AAAA;EAEI;;AAKR;EAGI;;AAGR;EACI,OD3+GG;;AC8+GH;EAGI;;AAQJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI,ODn7GI;;ACq7GR;EAEQ;IACI;IACA;;EAEJ;IACI;;EAEJ;IACI;;;AAIZ;EACI,kBDr8GM;ECs8GN;EACA;;AAEJ;EACI;;AAGA;EACI;;AAEJ;EACI;;AAGR;EACI,qBDn9GK;ECo9GL,oBDp9GK;;ACs9GT;EACI;;AAEJ;AAAA;AAAA;EAGI,OD79GI;;AC+9GR;AAAA;EAEI,ODj+GI;;ACs+GR;EACI;EACA;;AACA;EACI;;AACA;EACI;;AAIZ;AAAA;AAAA;AAAA;EAII,ODp/GI;;ACw/GA;EACI,mBDx/GH;ECy/GG,kBDz/GH;EC0/GG,oBD1/GH;EC2/GG,OD5/GJ;;AC8/GA;AAAA;EAEI,ODhgHJ;;ACkgHA;AAAA;EAEI,qBDngHH;;ACsgHG;AAAA;AAAA;AAAA;EAII,mBD1gHP;EC2gHO,kBD3gHP;EC4gHO;;AAEJ;EACI,mBD/gHP;ECghHO,kBDhhHP;;ACihHO;EAEI;;AAEJ;EACI,oBDthHX;;ACwhHO;EACI;;AAKhB;EACI,cD/hHC;ECgiHD;;AAGA;AAAA;EAEI,OD9lHT;;ACmmHC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUI,cDpjHC;;ACujHT;AAAA;EAEI;;AAGA;AAAA;AAAA;EAGI,YD/jHC;;ACmkHL;AAAA;EAEI;;AAGR;EACI;EACA;;AAEJ;EACI;;AAGA;AAAA;AAAA;AAAA;EAII;;AAIR;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAGJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAGJ;EACI,qBD/pHK;;ACkqHL;EACI,cDnqHC;;ACoqHD;EACI,kBDrqHH;;ACuqHD;EACI,kBD1qHF;;AC2qHE;EACI,OD3qHR;;AC6qHI;EACI;;AAGR;EAEI,kBDprHF;ECqrHE,kBDnrHH;ECorHG,qBDprHH;;ACsrHD;EAEI;;AAEJ;EACI;EACA,kBD5rHH;EC6rHG,qBD7rHH;;AC8rHG;EACI;;AAEJ;EACI,ODnsHR;;ACqsHI;EACI;;AAIZ;EACI,ODnwHL;ECowHK,kBDnxHD;ECoxHC;;AAEJ;EACI;;AAGA;EACI,ODptHJ;;ACstHA;EACI;;AAIZ;EACI,qBD3tHK;;AC4tHL;EACI;;AAGR;AAAA;EAEI;;AAGA;EACI,YDxuHE;;AC0uHN;EACI,kBDzuHC;;AC4uHT;EACI,kBD7uHK;EC8uHL,kBDhvHM;;ACivHN;EACI;;AAEJ;EACI,cDnvHC;;ACovHD;EAEI;;AAIZ;EACI;;AAEJ;EACI,kBDjwHG;;ACmwHP;EACI;EACA,ODnwHI;;ACswHR;EACI;;AAEJ;EACI,cDzwHK;;AC4wHL;EAEI;;AAIJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGR;EACI,YD/xHM;ECgyHN;;AAEJ;EACI,ODlyHI;;ACoyHR;EACI;EACA,YDvyHM;ECwyHN,cDtyHK;;ACwyHT;AAAA;EAEI;;AAEJ;EACI;EACA,ODv2HD;;AC02HH;EACI;EACA;EACA;EAqEA;EACA;EACA;EAqEA;EACA;EACA;EAqEA;EACA;EACA;EAqEA;EACA;EACA;;AA3RA;EACI;;AACA;EAEI;EACA;;AAGR;EACI;EACA;;AAII;EAEI,kBD13Hb;;AC63HK;EACI,kBD93HT;;ACg4HK;EACI,eDj4HT;;ACs4HS;EAEI,kBD74Hb;;ACg5HK;EACI,kBDj5HT;;ACm5HK;EACI,eDp5HT;;ACy5HS;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAKA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAMR;EACI;;AACA;EAEI;EACA;;AAGR;EACI;EACA;;AAII;EAEI,kBDj8Hb;;ACo8HK;EACI,kBDr8HT;;ACu8HK;EACI,eDx8HT;;AC68HS;EAEI,kBDp9Hb;;ACu9HK;EACI,kBDx9HT;;AC09HK;EACI,eD39HT;;ACg+HS;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAKA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAMR;EACI;;AACA;EAEI;EACA;;AAGR;EACI;EACA;;AAII;EAEI,kBDxgIb;;AC2gIK;EACI,kBD5gIT;;AC8gIK;EACI,eD/gIT;;ACohIS;EAEI,kBD3hIb;;AC8hIK;EACI,kBD/hIT;;ACiiIK;EACI,eDliIT;;ACuiIS;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAKA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAMR;EACI;;AACA;EAEI;EACA;;AAGR;EACI;EACA;;AAII;EAEI,kBD/kIb;;ACklIK;EACI,kBDnlIT;;ACqlIK;EACI,eDtlIT;;AC2lIS;EAEI,kBDlmIb;;ACqmIK;EACI,kBDtmIT;;ACwmIK;EACI,eDzmIT;;AC8mIS;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAKA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAMR;EACI;;AACA;EAEI;EACA;;AAGR;EACI;EACA;;AAII;EAEI,kBDtpIb;;ACypIK;EACI,kBD1pIT;;AC4pIK;EACI,eD7pIT;;ACkqIS;EAEI,kBDzqIb;;AC4qIK;EACI,kBD7qIT;;AC+qIK;EACI,eDhrIT;;ACqrIS;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAKA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAQZ;EACI,ODptID;;ACqtIC;EACI;EACA;EACA;;AAEJ;EACI;;AACA;EACI;;AAEJ;EACI;;AAIZ;EACI,ODruID;ECsuIC;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AAIR;EACI,cD/rIK;ECgsIL,kBDlsIM;;ACqsIE;EACI,ODxxIT;;ACyxIS;EAEI,OD3xIb;;AC6xIS;EAGI;;AAGR;EACI;;AACA;EAEI;;AAGR;EACI,OD3yIT;;AC4yIS;EAEI,OD9yIb;;ACgzIS;EAGI,kBDnzIb;ECozIa,ODzxIjB;;AC4xIS;EACI,ODxyIX;;ACyyIW;EAEI,OD3yIf;;AC6yIW;EAGI,kBDhzIf;ECizIe,ODtyIjB;;AC0yIK;EACI,kBDlvIH;ECmvIG,qBDnvIH;;ACqvIO;EACI,OD/0IT;;ACi1IK;EACI;;AAKR;EACI,kBDn0IX;;ACq0IO;EACI,kBDt1IT;ECu1IS,OD5zIb;;AC6zIa;EAEI,kBD11Ib;EC21Ia,ODh0IjB;;ACm0IS;EACI;EACA,ODr2IL;;ACs2IK;EAEI;EACA,ODz2IT;;AC+2IX;EACI;IACI,mBDxxIC;ICyxID,oBDzxIC;;;AC4xIT;EACI;IACI,mBD9xIC;IC+xID,oBD/xIC;;;ACsyID;EAGI;EACA;;AAIJ;EAGI,kBDr4IL;ECs4IK,OD32IT;;AC+2IK;EAGI;EACA,ODn3IT;;ACy3IK;EAGI;EACA;EACA,kBDv0IF;;AC20IF;EAGI,cDh6IL;ECi6IK,ODj6IL;;ACq6IC;EAGI;EACA;;AAKZ;EACI;;AAEJ;EACI;;AAEJ;EACI,ODr7IG;;ACu7IP;EACI;;AAEJ;EACI;;AAGA;EAEI;EACA;;AAGR;EACI,OD16ID;;AC66IH;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGJ;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGJ;EACI;EACA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;EACA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;EACA;;AAEJ;EACI,YD5/LF;EC6/LE;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,YDphMA;ECqhMA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;EACA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;EACA;;AAEJ;EACI;EACA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,YD3kMH;EC4kMG;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,YD5lMA;EC6lMA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,kBD/lMA;ECgmMA;;AAEJ;EACI,YDnmMA;EComMA;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,kBDrnMD;ECsnMC;;AAEJ;EACI,YDznMD;EC0nMC;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;EACA;;AAGA;EAEI;;AAIJ;EAEI;;AAIJ;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAIJ;EAEI;;AAGR;EACI,YDvtMD;;AC0tMC;EAEI;;AAIJ;EAEI;;AAGR;EACI,YDjvMA;;ACmvMJ;EACI,YD7uMF;;ACgvME;EAEI;;AAIJ;EAEI;;AAGR;EACI,ODvvMD;ECwvMC;;AAEJ;EACI,YDpsMM;;ACssMV;EACI;;AAEJ;EACI;;AAEJ;EACI,kBD7sMM;;AC+sMV;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA,OD9wMD;;ACixMH;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA,ODt2MO;;ACy2MX;EACI;EACA,cDlxMK;;ACoxMT;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAGJ;AAAA;EAEI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAGA;EAEI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAGJ;EACI;EACA,OD19MD;;AC49MH;EACI;EACA,OD99MD;;ACg+MH;EACI,kBDl+MD;ECm+MC,ODl+MD;;ACo+MH;EACI;EACA,ODt+MD;;ACw+MH;EACI;EACA,OD1+MD;;AC4+MH;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAGJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAIA;EACI,kBDjoNH;;ACsoND;EACI,kBDpoNN;;ACyoNE;EACI,kBD5oNH;;ACipND;EACI,kBDjpNJ;;ACspNA;AAAA;EAEI,cDtlNC;;ACulND;AAAA;EACI,MDzlNJ;;AC4lNJ;EACI;;AAEJ;EACI,ODhmNA;;;ACqmNZ;EACI,YDvmNU;ECwmNV,cDtmNS;ECumNT;;;AAIA;EACI;;AAEJ;EACI;;;AAIR;EACI;;;AAGJ;EACI;;;AAGJ;EACI,kBD9nNU;EC+nNV,cDnqNK;;;ACsqNT;EACI;;;AAGJ;EACI;;;AAGJ;EAII;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAIA;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAQJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAIR;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EAEQ;IACI;;;AAOR;EACI,cDjuNK;;ACmuNT;AAAA;EAEI,cDruNK;;ACuuNT;EACI;EACA;EACA;;AAEJ;EACI,OD9uNI;;ACgvNR;EACI;;AAGA;EACI,cDpvNC;;ACsvNL;EACI;;AAGR;EACI;;AAEJ;EACI;;AAEJ;AAAA;EAEI,ODnwNI;;ACqwNR;AAAA;EAEI,ODvwNI;;;AC6wNR;EACI,YD/wNM;;ACkxNN;EACI,ODlxNA;;ACoxNJ;EACI;;AAGR;EACI,kBD3xNG;;;ACiyNP;AAAA;AAAA;EAGI,kBDpyNG;;ACsyNP;AAAA;AAAA;EAGI,YDzyNG;;AC2yNP;AAAA;AAAA;EAGI,OD5yNI;;AC8yNR;AAAA;EAEI,ODhzNI;ECizNJ,YDnzNG;;ACqzNP;EACI;;AAEJ;AAAA;EAEI,kBD1zNG;;;ACg0NH;EACI,OD/zNA;;ACg0NA;EACI,ODj0NJ;;ACm0NA;EACI,YDr0NF;;ACy0NF;EACI,mBDx0NH;;AC00ND;EACI,kBD30NH;;AC+0NT;EACI,ODj1NI;;;ACw1NJ;EACI,cDx1NC;;AC21ND;EACI,cD51NH;;AC81ND;EAOI,mBDr2NH;;AC+1NG;EACI,cDh2NP;;ACk2NG;EACI,YDn2NP;;ACs2NG;EACI,YD74NX;;ACi5ND;EACI;;AAEJ;EACI,cD/2NC;;ACg3ND;EACI;;;AAQZ;EACI,OD33NI;EC43NJ,kBD73NM;EC83NN,cD53NK;;AC63NL;EACI,kBDh4NE;;ACm4NV;EACI;;AAEJ;EACI;;AAYA;EAEI,kBDr5NE;ECs5NF,cDp5NC;;ACu5NT;EACI,cDx5NK;ECy5NL,kBD35NM;;AC45NN;EACI,kBD75NE;EC85NF,cD55NC;;AC+5NT;EACI,cDh6NK;ECi6NL,YDn6NM;ECo6NN,OD39ND;;AC49NC;EAEI,cDr6NC;ECs6ND,OD/9NL;;ACk+NH;EACI,YD56NM;EC66NN;EACA,cD56NK;;AC86NT;EACI;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;;AAGA;AAAA;AAAA;EAGI;;AAIJ;EAEI;;AAIJ;EACI,cDx8NC;ECy8ND,kBD38NE;EC48NF;;AAEJ;EACI,cD78NC;EC88ND;EACA;;AAIJ;EACI,YDt9NE;;ACw9NN;EACI,cDv9NC;;ACw9ND;EACI,qBDz9NH;;AC09NG;EACI;;AAMZ;EACI,YDp+NE;ECq+NF;;AAEJ;EACI,kBDx+NE;ECy+NF,qBDv+NC;;AC0+ND;EACI;EACA,kBD9+NF;;ACg/NF;EACI;;AAMJ;EAEI;;AAGR;EAEI;;AAIJ;EAEI;EACA;;AAGR;AAAA;EAEI;;AAEJ;EACI,qBD3gOK;;AC6gOT;EACI,qBD9gOK;;ACghOT;EACI;EACA,kBDphOM;ECqhON,cDnhOK;;ACohOL;EACI;EACA,cDthOC;;ACyhOT;EAII;EACA,kBDhiOM;ECiiON,cD/hOK;;AC0hOL;EACI;;AAMR;EACI,kBDpiOM;ECqiON,cDniOK;;ACsiOL;EACI;;AAEJ;EACI;;AAGR;EACI,YDhjOM;ECijON,cD/iOK;;ACijOT;EACI,kBDpjOM;;ACsjOV;EAII,qBDxjOK;;ACqjOL;EACI;;AAIR;AAAA;EAEI,cD5jOK;;AC8jOT;EACI,kBD/jOK;;ACikOT;EACI,cDlkOK;ECmkOL,kBDrkOM;;ACukOV;EACI,kBDxkOM;;AC0kOV;EACI,kBD3kOM;EC4kON,cD1kOK;;AC4kOT;EACI,qBD7kOK;;AC+kOT;EACI,cDhlOK;ECilOL,kBDnlOM;;ACqlOV;EACI,ODrlOI;ECslOJ,YDvlOM;ECwlON,cDtlOK;;ACwlOT;EAKI,cD7lOK;;ACylOL;EACI,qBD1lOC;EC2lOD,YD7lOE;;ACgmON;EACI,ODhmOA;ECimOA,YDlmOE;;AComON;AAAA;EAEI,YDvmOD;;AC0mOP;EACI,YD1mOM;EC2mON,oBDzmOK;;AC2mOT;EACI,kBD9mOM;EC+mON,cD7mOK;EC8mOL,OD/mOI;;ACgnOJ;EAEI,cDjnOC;ECknOD,kBDpnOE;ECqnOF,oBDnnOC;EConOD,ODrnOA;;ACwnOR;EACI,oBDxnOK;ECynOL,mBDznOK;;AC2nOT;EACI,kBD9nOM;EC+nON,kBD7nOK;;AC+nOT;EACI,cDhoOK;ECioOL,kBDnoOM;ECooON,oBDloOK;;ACooOT;EACI,kBDvoOM;;ACyoOV;EACI,qBDxoOK;;;AC8oOT;EACI,kBDjpOM;ECkpON,qBDhpOK;;ACkpOT;EACI,kBDrpOM;ECspON,cDppOK;ECqpOL,ODtpOI;;ACwpOR;AAAA;EAEI,cDzpOK;;AC2pOT;EACI,kBD9pOM;EC+pON,cD7pOK;EC8pOL,OD/pOI;;ACiqOR;EACI,kBDpqOG;ECqqOH,ODnqOI;ECoqOJ,cDnqOK;;ACqqOT;AAAA;EAEI,kBD1qOG;EC2qOH,ODxsOC;;;AC+sOD;EACI,kBDlrOE;ECmrOF;;AAEJ;EACI,qBDprOC;;ACsrOL;EACI,ODxrOA;;ACyrOA;EACI,kBD3rOF;;AC6rOF;EACI,kBD9rOF;EC+rOE,cD7rOH;;AC8rOG;EACI,YDjsON;ECksOM;;AAMJ;EAGI,cDzsOP;EC0sOO;EACA,OD5sOR;;AC+sOA;EACI,cD/sOH;ECgtOG;EACA,ODltOJ;;ACqtOJ;EAII,qBDxtOC;;ACqtOD;EACI;;AAIR;AAAA;EAEI,oBD5tOC;;;ACmuOT;EAMI,cDzuOK;;ACouOL;EACI,cDruOC;ECsuOD,YDxuOE;ECyuOF,ODxuOA;;AC4uOR;EACI,cD5uOK;;AC+uOL;AAAA;EAEI,cDjvOC;ECkvOD,YDpvOE;ECqvOF,ODpvOA;;ACsvOJ;AAAA;EAEI,oBDvvOC;;AC0vOT;EACI,kBD7vOM;;AC+vOV;EACI,cD9vOK;;ACgwOT;EACI,cDjwOK;;ACmwOT;EACI,ODrwOI;;ACuwOR;EACI,kBDzwOM;EC0wON,cDxwOK;ECywOL;EACA,OD3wOI;EC4wOJ;;AAII;EAGI,cDlxOH;ECmxOG,YDrxOF;ECsxOE,ODrxOJ;;ACyxOR;EACI,kBD3xOM;;;ACkyON;EACI,YDnyOE;ECoyOF,cDlyOC;;ACmyOD;EACI;;AAEJ;EACI;;AAGR;EACI,kBD7yOE;;AC+yON;EACI,YDhzOE;;ACmzOV;EACI,cDlzOK;ECmzOL,kBDrzOM;ECszON;;AAEI;EACI,kBDvzOH;ECwzOG,qBDxzOH;;ACyzOG;EACI;;AAGR;EACI;EACA;;AAII;EAGI,kBD15Ob;;AC85OS;EAGI;;AAMZ;AAAA;EAEI,mBDr1OH;;AC01OG;EAGI;;AAIJ;EAGI;;;AAShB;EACI;EACA;;;AAMJ;EACI;;AAEJ;EACI;;AAGA;EACI;;AAEJ;EACI;;AAEJ;AAAA;EAEI,MDr4OA;;ACw4OR;EACI,YD14OM;EC24ON;EACA,OD34OI;;AC64OR;EAEQ;AAAA;AAAA;IAGI,kBDn5OF;ICo5OE,cDl5OH;ICm5OG;;;;AAOZ;EACI,mBDj8OC;;ACm8OL;EACI,mBDp8OC;;ACs8OL;EACI,oBDv8OC;ECw8OD,mBDx8OC;;AC08OL;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAGA;EACI;EACA;;;AAMR;EACI;;;AAGR;EACI,YDn8OO;;;ACq8OX;EACI;;;AAGA;EACI;EACA,YD18OM;;AC48OV;EACI,YDp9OI;;;ACu9OZ;EACI;EACA,ODzgPG;;;AC2gPP;EACI,kBD59OQ;;;AC89OZ;EACI,cD5/OK;EC6/OL,kBDh+OQ;;;ACm+OR;EACI;;AAGJ;EACI;;AAEJ;EACI,mBDxgPC;;AC2gPD;EACI;EACA;EACA,ODz+OA;;AC6+OJ;EACI;;;AAIZ;EACI,ODlhPK", "file": "dark-style.css", "sourcesContent": ["\r\n$background: #f0f0f5;\r\n$default-color:#282f53;\r\n$border: #e9edf4;\r\n\r\n/*Color variables*/\r\n\r\n$primary-1:var(--primary-bg-color);\r\n$primary-01:var(--primary01);\r\n$primary-02:var(--primary02);\r\n$primary-03:var(--primary03);\r\n$primary-06:var(--primary06);\r\n$primary-09:var(--primary09);\r\n$primary-005:var(--primary005);\r\n$primary-hover:var(--primary-bg-hover);\r\n$primary-border:var(--primary-bg-border);\r\n$primary-transparent:var(--primary-transparentcolor);\r\n$darkprimary-transparent:var(--darkprimary-transparentcolor);\r\n$transparentprimary-transparent:var(--transparentprimary-transparentcolor);\r\n$secondary:#05c3fb;\r\n$pink:#fc5296;\r\n$teal:#1caf9f;\r\n$purple:#8927ec;\r\n$success:#09ad95;\r\n$warning:#f7b731;\r\n$danger:#e82646;\r\n$info:#1170e4;\r\n$orange:#fc7303;\r\n$red:#e73827;\r\n$lime:#7bd235;\r\n$dark:#343a40;\r\n$indigo:#6574cd;\r\n$cyan:#007ea7;\r\n$azure:#45aaf2;\r\n$white:#fff;\r\n$black:#000;\r\n$light:#f2f2f9;\r\n$gray:#5a6970;\r\n$green:#4ecc48;\r\n$blue:#3223f1;\r\n$yellow:#FBB034;\r\n\r\n/*Gradient variables*/\r\n\r\n$primary-gradient-1:linear-gradient(to bottom right, $primary-1 0%, #8e77fa 100%);\r\n$secondary-gradient:linear-gradient(to bottom right, #82cff2 0%, #28b7f9 100%);\r\n$warning-gradient:linear-gradient(to bottom right, #f66b4e 0%, #fbc434 100%);\r\n$info-gradient:linear-gradient(to bottom right, #1e63c3 0%, #00f2fe 100%);\r\n$danger-gradient:linear-gradient(to bottom right, #b51b35 0%, #fd4a68 100%);\r\n$success-gradient:linear-gradient(to bottom right, #1ea38f 0%, #5cf9e2 100%);\r\n\r\n/*white variables*/\r\n\r\n$white-1:rgba(255, 255, 255, 0.1);\r\n$white-2:rgba(255, 255, 255, 0.2);\r\n$white-3:rgba(255, 255, 255, 0.3);\r\n$white-4:rgba(255, 255, 255, 0.4);\r\n$white-5:rgba(255, 255, 255, 0.5);\r\n$white-6:rgba(255, 255, 255, 0.6);\r\n$white-7:rgba(255, 255, 255, 0.7);\r\n$white-8:rgba(255, 255, 255, 0.8);\r\n$white-9:rgba(255, 255, 255, 0.9);\r\n$white-05:rgba(255, 255, 255, 0.05);\r\n$white-08:rgba(255, 255, 255, 0.08);\r\n$white-75:rgba(255, 255, 255, 0.075);\r\n\r\n/*black variables*/\r\n\r\n$black-1:rgba(0, 0, 0, 0.1);\r\n$black-2:rgba(0, 0, 0, 0.2);\r\n$black-3:rgba(0, 0, 0, 0.3);\r\n$black-4:rgba(0, 0, 0, 0.4);\r\n$black-5:rgba(0, 0, 0, 0.5);\r\n$black-6:rgba(0, 0, 0, 0.6);\r\n$black-7:rgba(0, 0, 0, 0.7);\r\n$black-8:rgba(0, 0, 0, 0.8);\r\n$black-9:rgba(0, 0, 0, 0.9);\r\n$black-05:rgba(0, 0, 0, 0.05);\r\n\r\n/*shadow variables*/\r\n\r\n$shadow:0 5px 15px 5px rgba(80, 102, 224, 0.08);\r\n$dark-theme:#1e2448;\r\n$dark-theme2:#16192f;\r\n$dark-theme3:#181d3e;\r\n\r\n/*Dark Theme Variables*/\r\n\r\n$dark-body:#1a1a3c;\r\n$dark-theme-1:#2a2a4a;\r\n$text-color:#dedefd;\r\n$border-dark:rgba(255, 255, 255, 0.1);\r\n$dark-card-shadow:0 3px 9px 0 rgba(28, 28, 51, 0.15);\r\n\r\n/*Transparent variables*/\r\n\r\n$transparent-primary:$primary-1;\r\n$transparent-theme:rgba(0, 0, 0, 0.2);\r\n$transparent-body:var(--transparent-body);\r\n$transparent-border:rgba(255, 255, 255, 0.2);", "@import \"../scss/variables\";\nbody.dark-mode {\n    color: $text-color;\n    background-color: $dark-body;\n}\n\nbody.dark-mode *::-webkit-scrollbar-thumb,\nbody.dark-mode *:hover::-webkit-scrollbar-thumb {\n    background: $dark-body;\n}\n\n.dark-mode {\n    table.dataTable>tbody>tr.child ul.dtr-details>li {\n        border-bottom: 1px solid $border-dark;\n    }\n    .dtr-bs-modal .dtr-details {\n        border: 1px solid $border-dark;\n    }\n    .dtr-bs-modal .dtr-details tr td:first-child {\n        border-right: 1px solid $border-dark;\n    }\n    caption {\n        color: #505662;\n    }\n    hr {\n        border-top-color: rgba(255, 255, 255, 0.2);\n        background-color: transparent;\n    }\n    mark,\n    .mark {\n        background-color: #fcf8e3;\n    }\n    .list-unstyled li {\n        border-bottom-color: $border-dark;\n    }\n    kbd {\n        color: $white;\n        background-color: $dark;\n    }\n    pre {\n        color: $text-color;\n    }\n    @media print {\n        pre,\n        blockquote {\n            border-color: #adb5bd;\n        }\n        .badge {\n            border-color: $black;\n        }\n        .table td,\n        .text-wrap table td,\n        .table th,\n        .text-wrap table th {\n            background-color: $dark-theme-1 !important;\n        }\n        .table-bordered th,\n        .text-wrap table th,\n        .table-bordered td,\n        .text-wrap table td {\n            border-color: $border-dark !important;\n        }\n    }\n    body * {\n        &::-webkit-scrollbar-thumb,\n        &:hover::-webkit-scrollbar-thumb {\n            background: $dark-theme-1;\n        }\n    }\n    code {\n        background: $dark-theme-1;\n        border-color: transparent;\n        color: $pink;\n    }\n    pre {\n        color: $dark;\n        background-color: $dark-theme-1;\n        text-shadow: 0 1px $dark-theme-1;\n    }\n    .section-nav {\n        background-color: #f8f9fa;\n        border-color: $border-dark;\n    }\n    /*------ Accordion -------*/\n    .accordionjs .acc_section {\n        border-color: $border-dark;\n        .acc_head h3:before {\n            color: $text-color;\n        }\n        .acc-header {\n            background: $dark-theme-1;\n        }\n        .acc_head {\n            background: $dark-theme-1;\n        }\n        &.acc_active > .acc_head {\n            color: $text-color !important;\n            border-bottom-color: $border-dark;\n        }\n    }\n    /* #accordion rotate icon option */\n    .accordion-item {\n        border-color: $border-dark;\n        background: $dark-theme-1;\n    }\n    .accordion-button {\n        color: $text-color;\n        &:focus {\n            border-color: $border-dark;\n        }\n        &:not(.collapsed) {\n            color: $primary-1;\n            background: #22223a;\n        }\n    }\n    #accordion .panel-default {\n        > .panel-heading {\n            border: 0px solid $border-dark;\n        }\n        .collapsing .panel-body {\n            border-top: 0px solid transparent;\n        }\n    }\n    /*----- Avatars -----*/\n    .avatar-status {\n        border: 2px solid $dark-theme-1;\n    }\n    .avatar-list-stacked .avatar {\n        box-shadow: 0 0 0 2px $dark-theme-1;\n    }\n    /*-----Badges-----*/\n    .btn-custom {\n        background: $dark-theme-1;\n        color: #9595b5;\n    }\n    .blockquote-footer {\n        color: #9595b5;\n    }\n    blockquote {\n        color: #9595b5;\n        border-color: $border-dark;\n    }\n    .blockquote-reverse {\n        border-color: $border-dark;\n    }\n    /*------ Breadcrumb ------*/\n    .breadcrumb-item {\n        a {\n            color: #9595b5;\n        }\n        + .breadcrumb-item::before {\n            color: #9595b5;\n        }\n    }\n    .breadcrumb1 {\n        background-color: $dark-body;\n    }\n    .breadcrumb-item1 {\n        a:hover {\n            color: $primary-1;\n        }\n        + .breadcrumb-item1::before {\n            color: #5b5b62;\n        }\n        &.active {\n            color: #9595b5;\n        }\n    }\n    .btn-default {\n        color: $default-color;\n        background: #e9e9f1;\n        border-color: #e9e9f1;\n        &:hover {\n            color: $default-color;\n            background-color: #e3e3ef;\n            border-color: #e3e3ef;\n        }\n        &:focus,\n        &.focus {\n            box-shadow: 0 0 0 2px #e9e9f1;\n        }\n        &.disabled,\n        &:disabled {\n            color: $default-color;\n            background-color: #e9e9f1;\n            border-color: #e9e9f1;\n        }\n        &:not(:disabled):not(.disabled) {\n            &:active,\n            &.active {\n                color: $default-color;\n                background-color: #e9e9f1;\n                border-color: #e9e9f1;\n            }\n        }\n    }\n    .btn-light {\n        color: $text-color;\n        background-color: #39395c;\n        border-color: $border-dark;\n        &:hover {\n            color: $text-color;\n            background-color: #2f2f4b;\n            border-color: $border-dark;\n        }\n        &:focus,\n        &.focus {\n            box-shadow: 0 0 0 2px rgba(248, 249, 250, 0.1);\n        }\n        &.disabled,\n        &:disabled {\n            color: #495057;\n            background-color: #f8f9fa;\n            border-color: #f8f9fa;\n        }\n        &:not(:disabled):not(.disabled) {\n            &:active,\n            &.active {\n                color: #495057;\n                background-color: $dark-theme-1;\n                border-color: $border-dark;\n            }\n        }\n    }\n    .btn-outline-default {\n        color: $text-color;\n        background: transparent;\n        border-color: $border-dark;\n        &:hover {\n            color: $default-color;\n            background: #e9e9f1;\n        }\n    }\n    .btn-white {\n        color: $text-color;\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        &:hover {\n            color: $text-color;\n            background-color: $dark-theme-1;\n            border-color: $border-dark;\n        }\n        &:focus,\n        &.focus {\n            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);\n        }\n        &.disabled,\n        &:disabled {\n            color: #495057;\n            background-color: $dark-theme-1;\n            border-color: $white;\n        }\n        &:not(:disabled):not(.disabled) {\n            &:active,\n            &.active {\n                color: #495057;\n                background-color: #545478;\n                border-color: $border-dark;\n            }\n        }\n    }\n    .show > .btn-white.dropdown-toggle {\n        color: #495057;\n        background-color: #545478;\n        border-color: $border-dark;\n    }\n    .btn-check {\n        &:active + .btn-outline-primary,\n        &:checked + .btn-outline-primary {\n            background-color: $primary-1;\n            border-color: $primary-1;\n        }\n    }\n    .btn-outline-primary {\n        border-color: $primary-1;\n        &.active,\n        &.dropdown-toggle.show,\n        &:active {\n            background-color: $primary-1;\n            border-color: $primary-1;\n        }\n    }\n    /*------ Card -------*/\n    .card {\n        background-color: $dark-theme-1;\n        border: inherit !important;\n        box-shadow: $dark-card-shadow;\n    }\n    .card-footer {\n        background-color: rgba(0, 0, 0, 0.03);\n        border-top-color: $border-dark;\n        color: #6e7687;\n    }\n    /*------ Default Card Values -------*/\n    .card .card {\n        border-color: $border-dark;\n    }\n    .card-body + .card-body {\n        border-top-color: $border-dark;\n    }\n    .card-header {\n        border-bottom-color: $border-dark;\n    }\n    .card-title {\n        small {\n            color: #9aa0ac;\n        }\n    }\n    .card-subtitle {\n        color: #9aa0ac;\n    }\n    .card-body + .card-table {\n        border-top-color: $border-dark;\n    }\n    .card-body + .card-list-group {\n        border-top-color: $border-dark;\n    }\n    .card-options {\n        color: #9aa0ac;\n        a:not(.btn) {\n            color: #9595b5;\n            &:hover {\n                color: #6e7687;\n            }\n        }\n    }\n    /*Card maps*/\n    .card-map {\n        background: #e9ecef;\n    }\n    .card .box {\n        h2 {\n            color: #262626;\n            span {\n                color: $white;\n            }\n        }\n        p {\n            color: #262626;\n        }\n    }\n    /*------ Card -------*/\n    .card-footer {\n        background-color: rgba(0, 0, 0, 0.03);\n        border-top-color: $border-dark;\n        color: #727293;\n    }\n    /*------ Default Card Values -------*/\n    .card .card {\n        border-color: $border-dark;\n        box-shadow: 0px 0px 10px #1c1c2a;\n    }\n    .card-body + .card-body {\n        border-top-color: $border-dark;\n    }\n    .card-title {\n        small {\n            color: #9aa0ac;\n        }\n    }\n    .card-subtitle {\n        color: #9aa0ac;\n    }\n    .card-body + .card-table {\n        border-top-color: $border-dark;\n    }\n    .card-body + .card-list-group {\n        border-top-color: $border-dark;\n    }\n    .card-options {\n        color: #9aa0ac;\n        a:not(.btn) {\n            color: #9595b5;\n            &:hover {\n                color: #6e7687;\n            }\n        }\n    }\n    /*Card maps*/\n    .card-map {\n        background: #e9ecef;\n    }\n    .card .box {\n        h2 {\n            color: #262626;\n            span {\n                color: $white;\n            }\n        }\n        p {\n            color: #262626;\n        }\n    }\n    .cardheader-tabs .card-header {\n        border-bottom-color: $border-dark;\n    }\n    /*------ Carousel -------*/\n    .carousel-control-prev,\n    .carousel-control-next {\n        color: $white;\n    }\n    .carousel-control-prev {\n        &:hover,\n        &:focus {\n            color: $white;\n        }\n    }\n    .carousel-control-next {\n        &:hover,\n        &:focus {\n            color: $white;\n        }\n    }\n    .carousel-indicators,\n    .carousel-indicators1,\n    .carousel-indicators2,\n    .carousel-indicators3,\n    .carousel-indicators4,\n    .carousel-indicators5 {\n        li {\n            background-color: rgba(255, 255, 255, 0.5);\n        }\n        .active {\n            background-color: $white;\n        }\n    }\n    .carousel-caption {\n        color: $white;\n    }\n    .carousel-item-background {\n        background: rgba(0, 0, 0, 0.5);\n    }\n    /*------ Carousel -------*/\n    .carousel-control-prev,\n    .carousel-control-next {\n        color: $white;\n    }\n    .carousel-control-prev {\n        &:hover,\n        &:focus {\n            color: $white;\n        }\n    }\n    .carousel-control-next {\n        &:hover,\n        &:focus {\n            color: $white;\n        }\n    }\n    .carousel-indicators,\n    .carousel-indicators1,\n    .carousel-indicators2,\n    .carousel-indicators3,\n    .carousel-indicators4,\n    .carousel-indicators5 {\n        li {\n            background-color: rgba(255, 255, 255, 0.5);\n        }\n        .active {\n            background-color: $white;\n        }\n    }\n    .carousel-caption {\n        color: $white;\n    }\n    .carousel-item-background {\n        background: rgba(0, 0, 0, 0.5);\n    }\n    /*------Drop Downs-------*/\n    .dropdown-menu {\n        color: #9595b5;\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .dropdown-divider {\n        border-top-color: $border-dark;\n    }\n    .drop-heading {\n        color: $text-color;\n    }\n    .dropdown-item {\n        color: #9595b5;\n    }\n    .dropdown-item {\n        &:hover,\n        &:focus,\n        &.active,\n        &:active {\n            background-color: #303052;\n        }\n        &.disabled,\n        &:disabled {\n            color: $white;\n        }\n    }\n    .dropdown-menu.show {\n        border-color: $border-dark;\n        box-shadow: 0 10px 40px 0 rgba(34, 34, 61, 0.8);\n    }\n    .dropdown-header {\n        color: #9595b5;\n    }\n    .dropdown-item-text {\n        color: $text-color;\n    }\n    /*-----List Of Dropdwons-----*/\n    btn.dropdown-toggle ~ .dropdown-menu,\n    ul.dropdown-menu li.dropdown ul.dropdown-menu {\n        background-color: #f4f4f4 !important;\n        background-color: white !important;\n        border: 0 solid #4285f4 !important;\n        box-shadow: 0px 0px 3px rgba(25, 25, 25, 0.3) !important;\n    }\n    .dropdown-menu {\n        background-color: $dark-theme-1;\n        -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\n        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\n        > li > a {\n            color: $text-color;\n            &:hover,\n            &:focus {\n                color: #9595b5;\n                background-color: $dark-theme-1;\n            }\n        }\n        .divider {\n            background-color: $border-dark;\n        }\n        .dropdown-plus-title {\n            color: $text-color !important;\n            border: 0 solid $border-dark !important;\n            border-bottom: 1px solid $border-dark !important;\n        }\n    }\n    .dropdown-menu-header {\n        border-bottom-color: $border-dark;\n        label {\n            color: #9595b5;\n        }\n    }\n    .dropdown-menu-arrow:before,\n    .dropdown-menu.header-search:before {\n        background: $dark-theme-1;\n        border-top-color: $border-dark;\n        border-left-color: $border-dark;\n    }\n    .dropdown-menu {\n        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n    }\n    .dropdown-toggle .dropdown-label {\n        background-color: $dark-theme-1;\n    }\n    /*------- Forms -------*/\n    .form-control {\n        color: $text-color;\n        background-color: #282848;\n        border-color: $border-dark !important;\n        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n        &::-ms-expand {\n            background-color: transparent;\n        }\n        &:focus {\n            color: #6f6e6e;\n            background-color: $dark-theme-1;\n            border-color: $border-dark;\n        }\n        &::placeholder {\n            color: #626281 !important;\n        }\n        &::-webkit-input-placeholder,\n        &::-moz-placeholder,\n        &:-ms-input-placeholder,\n        &::-ms-input-placeholder {\n            color: $white !important;\n        }\n        &:disabled,\n        &[readonly] {\n            background-color: #282848;\n        }\n        &::-webkit-file-upload-button {\n            color: $text-color;\n            background-color: #39395c;\n        }\n        &:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {\n            background-color: $dark-theme-1;\n        }\n    }\n    .form-control-plaintext {\n        color: #9595b5;\n    }\n    .form-check-input:disabled ~ .form-check-label {\n        color: #505662;\n    }\n    .was-validated .custom-control-input:valid:focus ~ .custom-control-label::before,\n    .custom-control-input.is-valid:focus ~ .custom-control-label::before {\n        box-shadow: 0 0 0 1px $dark-theme-1, 0 0 0 2px rgba(9, 173, 149, 0.25);\n    }\n    .was-validated .form-control:invalid,\n    .form-control.is-invalid,\n    .was-validated .form-select:invalid,\n    .form-select.is-invalid {\n        border-color: #dc3545;\n        background-repeat: no-repeat;\n    }\n    .was-validated .form-control:invalid:focus,\n    .form-control.is-invalid:focus,\n    .was-validated .form-select:invalid:focus,\n    .form-select.is-invalid:focus {\n        border-color: #dc3545;\n    }\n    .was-validated .form-check-input:invalid ~ .form-check-label,\n    .form-check-input.is-invalid ~ .form-check-label,\n    .was-validated .custom-control-input:invalid ~ .custom-control-label,\n    .custom-control-input.is-invalid ~ .custom-control-label {\n        color: #dc3545;\n    }\n    .was-validated .custom-control-input:invalid ~ .custom-control-label::before,\n    .custom-control-input.is-invalid ~ .custom-control-label::before {\n        background-color: #ec8080;\n    }\n    .was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before,\n    .custom-control-input.is-invalid:checked ~ .custom-control-label::before {\n        background-color: #e23e3d;\n    }\n    .was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before,\n    .custom-control-input.is-invalid:focus ~ .custom-control-label::before {\n        box-shadow: 0 0 0 1px $dark-theme-1, 0 0 0 2px rgba(232, 38, 70, 0.25);\n    }\n    .form-control.header-search {\n        background: $dark-theme-1;\n        border-color: rgba(225, 225, 225, 0.1);\n        color: $white;\n        &::placeholder {\n            color: $white;\n        }\n        &:hover,\n        &:focus {\n            border-color: rgba(225, 225, 225, 0.1);\n        }\n    }\n    .form-required {\n        color: #dc3545;\n    }\n    .form-check-input:focus {\n        border-color: $primary-1;\n    }\n    .form-check-input {\n        background: #41415c;\n        border-color: $border-dark;\n    }\n    .input-group-text {\n        color: $white;\n        background-color: $primary-1;\n        border-color: $border-dark;\n    }\n    .input-indec .input-group-btn > .btn {\n        border-color: $border-dark;\n    }\n    .input-group-text {\n        color: $white;\n        border-color: $border-dark !important;\n    }\n    .input-indec .input-group-btn > .btn {\n        border-color: $border-dark;\n    }\n    /*------ Modal -------*/\n    .modal-content {\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);\n    }\n    .modal-backdrop {\n        background-color: $black;\n    }\n    .modal-header {\n        border-bottom-color: $border-dark;\n    }\n    .modal-footer {\n        border-top-color: $border-dark;\n    }\n    .modal.effect-just-me {\n        .modal-content {\n            background-color: #16192f;\n            border-color: #4d4e50;\n        }\n        .btn-close {\n            color: $white;\n        }\n        .modal-header {\n            background-color: transparent;\n            border-bottom-color: $white-1;\n            h6 {\n                color: $white;\n            }\n        }\n        .modal-body {\n            color: rgba(255, 255, 255, 0.8);\n            h6 {\n                color: $white;\n            }\n        }\n        .modal-footer {\n            background-color: transparent;\n            border-top-color: $border-dark;\n        }\n    }\n    /*------ Navigation -------*/\n    .nav-tabs {\n        border-bottom-color: $border-dark;\n    }\n    .nav-pills {\n        .nav-link.active,\n        .show > .nav-link {\n            color: $white;\n        }\n    }\n    .nav.nav-pills.nav-stacked.labels-info p {\n        color: #9d9f9e;\n    }\n    .nav.nav-pills .nav-item .nav-link-icon {\n        color: $text-color;\n    }\n    .nav1 {\n        background: $dark-body;\n    }\n    .nav-item1 {\n        &:hover:not(.disabled),\n        &.active {\n            color: $white;\n        }\n    }\n    .nav-item1 .nav-link {\n        color: $text-color;\n        &.active {\n            color: $primary-1;\n        }\n        &.disabled {\n            color: #62628f;\n        }\n    }\n    .nav-tabs {\n        border-bottom-color: $border-dark;\n        .nav-item1 {\n            &.nav-link {\n                border-color: transparent;\n            }\n            .nav-link {\n                color: inherit;\n                color: #9595b5;\n                transition: 0.3s border-color;\n                &:hover:not(.disabled),\n                &.active {\n                    color: $white;\n                }\n            }\n        }\n        .nav-submenu .nav-item1 {\n            color: #9aa0ac;\n            &.active {\n                color: #467fcf;\n            }\n            &:hover {\n                color: #6e7687;\n                background: rgba(0, 0, 0, 0.024);\n            }\n        }\n    }\n    .nav-link {\n        color: $primary-1;\n        &.icon i {\n            color: #dcdfed;\n            &::after {\n                background: rgba($primary-1, 0);\n            }\n        }\n    }\n    .app-header .header-right-icons .nav-link.icon {\n        color: #dcdfed !important;\n    }\n    .app-header .header-right-icons .nav-link.icon:hover {\n        color: #dcdfed !important;\n    }\n    &.header-light .app-header .header-right-icons .nav-link.icon {\n        color: $primary-1 !important;\n    }\n    .app-header .header-right-icons .nav-link.icon:hover {\n        color: #dcdfed !important;\n    }\n    .nav-tabs {\n        color: $black;\n        .nav-link {\n            color: inherit;\n            color: #9595b5;\n            &:hover:not(.disabled),\n            &.active {\n                color: $white;\n            }\n            &.disabled {\n                color: #868e96;\n                background-color: transparent;\n                border-color: transparent;\n            }\n        }\n        .nav-submenu {\n            background: $dark-theme-1;\n            border-color: $border-dark;\n            border-top: none;\n            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n            .nav-item {\n                color: #9aa0ac;\n                &:hover {\n                    color: #6e7687;\n                    text-decoration: none;\n                    background: rgba(0, 0, 0, 0.024);\n                }\n            }\n        }\n    }\n    .page-link {\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        color: #9595b5;\n        &:hover {\n            background-color: $dark-theme-1;\n        }\n    }\n    .page-item {\n        &.active .page-link {\n            color: $white;\n        }\n        &.disabled .page-link {\n            color: #4f4f67;\n            background-color: #252542;\n            border-color: $border-dark;\n        }\n    }\n    .page-header {\n        border-color: transparent;\n    }\n    .panel-title-landing {\n        color: #1643a3 !important;\n    }\n    .panel-footer-landing {\n        border: solid 2px #1643a3 !important;\n        border-top: none !important;\n        background: #f7f7f7;\n    }\n    .panel-footer {\n        background-color: $dark-theme-1;\n        border-top-color: $border-dark;\n        border-left-color: $border-dark;\n        border-right-color: $border-dark;\n    }\n    .panel-group .panel,\n    .panel-group1 .panel {\n        border-color: $border-dark;\n    }\n    .panel-default > .panel-heading {\n        background-color: #313152;\n        border-color: $border-dark;\n        + .panel-collapse > .panel-body {\n            border: 0px solid $border-dark;\n        }\n    }\n    .panel1 {\n        border-color: $white;\n        &:last-child {\n            border-bottom: none;\n        }\n    }\n    .panel-body1 {\n        background: $dark-theme-1;\n    }\n    .panel-group1 .panel-body {\n        border: 0px solid $border-dark;\n    }\n    .panel-title1 a {\n        color: $white !important;\n    }\n    /* #bs-collapse icon scale option */\n    .panel-title a.accordion-toggle {\n        &:before,\n        &.collapsed:before {\n            color: $white;\n        }\n    }\n    /*--------panel----------*/\n    .expanel {\n        background-color: $dark-theme-1 !important;\n        border-color: $border-dark !important;\n        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.01) !important;\n    }\n    .expanel-default > .expanel-heading {\n        background-color: #3b3b5a !important;\n        border-color: $dark-theme-1 !important;\n    }\n    .expanel-heading {\n        border-bottom-color: $border-dark;\n    }\n    .expanel-footer {\n        background-color: #3b3b5a !important;\n        border-top-color: $border-dark !important;\n    }\n    .popover {\n        background-color: $dark-theme-1;\n        border-color: #dee3eb;\n        filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.1));\n    }\n    .bs-popover-top .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"top\"] .popover-arrow::before {\n        border-top-color: rgba(0, 0, 0, 0.25);\n    }\n    .bs-popover-top .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"top\"] .popover-arrow::after {\n        border-top-color: $dark-theme-1;\n    }\n    .bs-popover-end .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"right\"] .popover-arrow::before {\n        border-right-color: #dee3eb;\n    }\n    .bs-popover-end .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"right\"] .popover-arrow::after {\n        border-right-color: $dark-theme-1;\n    }\n    .bs-popover-bottom .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::before {\n        border-bottom-color: #dee3eb;\n    }\n    .bs-popover-bottom .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::after {\n        border-bottom-color: $dark-theme-1;\n    }\n    .bs-popover-bottom .popover-header::before,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-header::before {\n        border-bottom-color: $dark-theme-1;\n    }\n    .bs-popover-start .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"left\"] .popover-arrow::before {\n        border-left-color: #dee3eb;\n    }\n    .bs-popover-start .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"left\"] .popover-arrow::after {\n        border-left-color: $dark-theme-1;\n    }\n    .popover-header {\n        color: inherit;\n        background-color: $dark-theme-1;\n        border-bottom-color: #ebebeb;\n    }\n    .popover-body {\n        color: #6e7687;\n    }\n    /*-----progress-----*/\n    .progress {\n        background-color: $dark-body;\n    }\n    .progress-bar {\n        color: $white;\n    }\n    /*------ Tables -----*/\n    .table {\n        color: $text-color;\n        thead th {\n            border-bottom-color: $border-dark;\n        }\n    }\n    .text-wrap table thead th {\n        border-bottom-color: $border-dark;\n    }\n    .table tbody + tbody,\n    .text-wrap table tbody + tbody {\n        border-top: 2px solid $border-dark;\n    }\n    .table .table,\n    .text-wrap table .table,\n    .table .text-wrap table {\n        background-color: #1f1f3c;\n    }\n    .text-wrap {\n        .table table,\n        table table {\n            background-color: $dark-theme-1;\n        }\n    }\n    .table-bordered,\n    .text-wrap table,\n    .table-bordered th,\n    .text-wrap table th,\n    .table-bordered td,\n    .text-wrap table td {\n        border-color: $border-dark;\n    }\n    .table-striped tbody tr:nth-of-type(odd) {\n        background-color: rgba(0, 0, 0, 0.02);\n    }\n    .table-hover tbody {\n        tr:hover,\n        th {\n            background-color: $dark-body;\n            color: $text-color;\n        }\n    }\n    .table-dark {\n        background-color: $text-color;\n        > {\n            th,\n            td {\n                background-color: $dark-body;\n            }\n        }\n    }\n    .table-hover .table-dark:hover {\n        background-color: #b9bbbe;\n        > {\n            td,\n            th {\n                background-color: #b9bbbe;\n            }\n        }\n    }\n    .table > :not(:first-child) {\n        border-top-color: $border-dark;\n    }\n    .table-active {\n        background-color: rgba(0, 0, 0, 0.04);\n        > {\n            th,\n            td {\n                background-color: rgba(0, 0, 0, 0.04);\n            }\n        }\n    }\n    .table-hover .table-active:hover {\n        background-color: rgba(0, 0, 0, 0.04);\n        > {\n            td,\n            th {\n                background-color: rgba(0, 0, 0, 0.04);\n            }\n        }\n    }\n    .table .thead-dark th,\n    .text-wrap table .thead-dark th {\n        color: $text-color;\n        background-color: $text-color;\n        border-color: #32383e;\n    }\n    .table .thead-light th,\n    .text-wrap table .thead-light th {\n        color: #495057;\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .table-dark {\n        color: $dark-theme-1;\n        background-color: $dark-body;\n        th,\n        td,\n        thead th {\n            border-bottom-color: rgba(255, 255, 255, 0.05) !important;\n        }\n    }\n    .table-dark {\n        &.table-striped tbody tr {\n            &:nth-of-type(odd) {\n                background-color: $dark-theme-1;\n            }\n            &:nth-of-type(even) {\n                background-color: #272b2f;\n            }\n        }\n        &.table-hover tbody tr:hover {\n            background-color: $text-color;\n        }\n    }\n    .table-inbox {\n        border-color: $border-dark;\n        tr {\n            border-bottom-color: rgba(238, 238, 238, 0.7);\n            &:last-child {\n                border-bottom-color: $border-dark;\n            }\n            td .fa-star {\n                &.inbox-started,\n                &:hover {\n                    color: #f78a09;\n                }\n            }\n            &.unread td {\n                background: $dark-theme-1;\n            }\n        }\n    }\n    .table th,\n    .text-wrap table th {\n        color: $text-color;\n    }\n    .table-vcenter {\n        td,\n        th {\n            border-top-color: $border-dark;\n        }\n    }\n    .table-secondary {\n        tbody + tbody,\n        td,\n        th,\n        thead th {\n            border-color: rgba(255, 255, 255, 0.2);\n        }\n    }\n    .table-primary {\n        tbody + tbody,\n        td,\n        th,\n        thead th {\n            border-color: rgba(255, 255, 255, 0.2);\n        }\n    }\n    .table-striped tbody tr {\n        &:nth-of-type(odd) {\n            background: $dark-body;\n            color: $text-color;\n        }\n        &:nth-of-type(even) {\n            background-color: $dark-theme-1;\n        }\n    }\n    .table-calendar-link {\n        background: #f8f9fa;\n        color: #495057;\n        &:before {\n            background: #467fcf;\n        }\n        &:hover {\n            color: $white;\n            background: #467fcf;\n            &:before {\n                background: $dark-theme-1;\n            }\n        }\n    }\n    .table-header:hover,\n    .table-header-asc,\n    .table-header-desc {\n        color: #495057 !important;\n    }\n    .table-hover > tbody > tr:hover > * {\n        color: $text-color;\n    }\n    .table {\n        > {\n            :not(:last-child) > :last-child > * {\n                border-bottom-color: $border-dark;\n            }\n        }\n        tbody td {\n            border-color: $border-dark;\n        }\n    }\n    .table-striped > tbody > tr:nth-of-type(odd) > * {\n        color: $text-color;\n    }\n    /*---- Tags-----*/\n    .tag {\n        color: $text-color;\n        background-color: $dark-body;\n    }\n    a.tag:hover {\n        background-color: rgba(110, 118, 135, 0.2);\n        color: inherit;\n    }\n    .tag-addon {\n        color: inherit;\n    }\n    a.tag-addon:hover {\n        background: inherit;\n        color: inherit;\n    }\n    .tag-blue {\n        background-color: #467fcf !important;\n        color: $white;\n    }\n    .tag-indigo {\n        background-color: $indigo !important;\n        color: $white;\n    }\n    .tag-purple {\n        background-color: #867efc !important;\n        color: $white;\n    }\n    .tag-pink {\n        background-color: #ec82ef !important;\n        color: $white;\n    }\n    .tag-red {\n        background-color: #c21a1a !important;\n        color: $white;\n    }\n    .tag-orange {\n        background-color: $orange !important;\n        color: $white;\n    }\n    .tag-yellow {\n        background-color: #ecb403 !important;\n        color: $white;\n    }\n    .tag-green {\n        background-color: $green !important;\n        color: $white;\n    }\n    .tag-teal {\n        background-color: #2bcbba !important;\n        color: $white;\n    }\n    .tag-cyan {\n        background-color: #17a2b8 !important;\n        color: $white;\n    }\n    .tag-white {\n        background-color: $dark-theme-1;\n        color: $white;\n    }\n    .tag-gray {\n        background-color: #868e96 !important;\n        color: $white;\n    }\n    .tag-gray-dark {\n        background-color: #414160;\n        color: $white;\n    }\n    .tag-azure {\n        background-color: $azure !important;\n        color: $white;\n    }\n    .tag-lime {\n        background-color: $lime !important;\n        color: $white;\n    }\n    .tag-primary {\n        background-color: #467fcf;\n        color: $white;\n        background-color: $primary-1 !important;\n        color: $white;\n    }\n    .tag-secondary {\n        background-color: #868e96;\n        color: $white;\n    }\n    .tag-success {\n        background-color: $green !important;\n        color: $white;\n    }\n    .tag-info {\n        background-color: $azure;\n        color: $white;\n    }\n    .tag-warning {\n        background-color: #ecb403 !important;\n        color: $white;\n    }\n    .tag-danger {\n        background-color: #c21a1a !important;\n        color: $white;\n    }\n    .tag-light {\n        background-color: #f8f9fa;\n        color: $white;\n    }\n    .tag-dark {\n        background-color: #25253e;\n        color: $text-color;\n    }\n    .tag-round::before {\n        background-color: $dark-theme-1;\n    }\n    .tag-outline-info {\n        background-color: #c7e0fd;\n        color: $info;\n        border-color: $info;\n        &::before {\n            border-color: $info;\n        }\n    }\n    .tag-outline {\n        border-color: $border-dark;\n    }\n    .tag-border {\n        border-color: $border-dark;\n        background-color: $dark-theme-1;\n    }\n    /*---------Thumbnails----------*/\n    .thumbnail {\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .bs-popover-top .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"top\"] .popover-arrow::after {\n        border-top-color: $dark-theme-1;\n    }\n    .bs-popover-end .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"right\"] .popover-arrow::after {\n        border-right-color: $dark-theme-1;\n    }\n    .bs-popover-bottom .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::after {\n        border-bottom-color: $dark-theme-1;\n    }\n    .bs-popover-start .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"left\"] .popover-arrow::after {\n        border-left-color: $dark-theme-1;\n    }\n    .tooltip-static-demo {\n        background-color: $dark-theme-1;\n    }\n    .popover-static-demo {\n        background-color: $dark-body;\n        border-color: $border-dark;\n    }\n    .tooltip-primary {\n        .tooltip-inner {\n            background-color: $primary-1 !important;\n            color: $white;\n        }\n        &.bs-tooltip-top .tooltip-arrow::before,\n        &.bs-tooltip-auto[data-popper-placement^=\"top\"] .tooltip-arrow::before {\n            border-top-color: $primary-1;\n        }\n        &.bs-tooltip-bottom .tooltip-arrow::before,\n        &.bs-tooltip-auto[data-popper-placement^=\"bottom\"] .tooltip-arrow::before {\n            border-bottom-color: $primary-1;\n        }\n        &.bs-tooltip-start .tooltip-arrow::before,\n        &.bs-tooltip-auto[data-popper-placement^=\"left\"] .tooltip-arrow::before {\n            border-left-color: $primary-1;\n        }\n        &.bs-tooltip-end .tooltip-arrow::before,\n        &.bs-tooltip-auto[data-popper-placement^=\"right\"] .tooltip-arrow::before {\n            border-right-color: $primary-1;\n        }\n    }\n    .popover {\n        background-color: $dark-theme-1;\n        border: 0px solid $dark-theme-1;\n    }\n    .bs-popover-top > .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"top\"] > .popover-arrow::before {\n        border-top-color: $dark-theme-1;\n    }\n    .bs-popover-top > .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"top\"] > .popover-arrow::after {\n        border-top-color: $dark-theme-1;\n    }\n    .bs-popover-end > .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"right\"] > .popover-arrow::before {\n        border-right-color: $dark-theme-1;\n    }\n    .bs-popover-end > .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"right\"] > .popover-arrow::after {\n        border-right-color: $dark-theme-1;\n    }\n    .bs-popover-bottom > .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] > .popover-arrow::before {\n        border-bottom-color: $dark-theme-1;\n    }\n    .bs-popover-bottom > .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] > .popover-arrow::after {\n        border-bottom-color: $dark-theme-1;\n    }\n    .bs-popover-bottom .popover-header::before,\n    .bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-header::before {\n        border-bottom-color: $border-dark;\n    }\n    .bs-popover-start > .popover-arrow::before,\n    .bs-popover-auto[data-popper-placement^=\"left\"] > .popover-arrow::before {\n        border-width: 0.5rem 0 0.5rem 0.5rem;\n        border-left-color: $dark-theme-1;\n    }\n    .bs-popover-start > .popover-arrow::after,\n    .bs-popover-auto[data-popper-placement^=\"left\"] > .popover-arrow::after {\n        border-width: 0.5rem 0 0.5rem 0.5rem;\n        border-left-color: $dark-theme-1;\n    }\n    .popover-header {\n        color: $text-color;\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .popover-body {\n        color: #aeaecf;\n    }\n    .popover-head-primary {\n        .popover-header {\n            color: $white;\n            background-color: $primary-1 !important;\n        }\n        &.bs-popover-bottom .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::after {\n            border-bottom-color: $primary-1 !important;\n        }\n    }\n    .popover-head-secondary {\n        .popover-header {\n            color: $white;\n            background-color: $secondary !important;\n        }\n        &.bs-popover-bottom .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::after {\n            border-bottom-color: $secondary !important;\n        }\n    }\n    .popover-head-primary .popover-body,\n    .popover-head-secondary .popover-body {\n        border-color: rgba(20, 17, 45, 0.2);\n    }\n    .popover-primary {\n        background-color: $primary-1 !important;\n        .popover-header {\n            background-color: $primary-1 !important;\n            border-bottom-width: 0 !important;\n            color: $white;\n        }\n        &.bs-popover-top .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"top\"] .popover-arrow::after {\n            border-top-color: $primary-1 !important;\n        }\n        &.bs-popover-bottom .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-arrow::after {\n            border-bottom-color: $primary-1 !important;\n        }\n        &.bs-popover-start .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"left\"] .popover-arrow::after {\n            border-left-color: $primary-1 !important;\n        }\n        &.bs-popover-end .popover-arrow::after,\n        &.bs-popover-auto[data-popper-placement^=\"right\"] .popover-arrow::after {\n            border-right-color: $primary-1 !important;\n        }\n    }\n    /*----- Custom control -----*/\n    .custom-control-input {\n        &:checked ~ .custom-control-label::before {\n            color: $white;\n        }\n        &:active ~ .custom-control-label::before {\n            color: $white;\n            background-color: rgba(218, 201, 232, 0.5);\n        }\n        &:disabled ~ .custom-control-label {\n            color: #868e96;\n            &::before {\n                background-color: $border-dark;\n            }\n        }\n    }\n    .custom-control-label::before {\n        background-color: $border-dark;\n    }\n    .custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {\n        background-color: rgba(212, 182, 228, 0.5);\n    }\n    .form-select {\n        color: #9595b5;\n        border-color: $border-dark;\n        background: $dark-theme-1;\n        &:focus {\n            border-color: $border-dark;\n            box-shadow: none;\n            &::-ms-value {\n                color: #9595b5;\n                background-color: $dark-theme-1;\n            }\n        }\n        &:disabled {\n            color: #9fa7af;\n            background-color: $dark-theme-1;\n        }\n    }\n    .form-file-label {\n        color: #9595b5;\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        &::after {\n            color: $white;\n            border-left-color: $border-dark;\n        }\n    }\n    .form-range {\n        &::-webkit-slider-thumb {\n            background: $dark-theme-1;\n            box-shadow: none;\n            &:focus {\n                box-shadow: 0 0 0 1px $dark-theme-1, 0 0 0 2px rgba(98, 58, 162, 0.25);\n            }\n            &:active {\n                background-color: #d4e1f4;\n            }\n        }\n        &::-webkit-slider-runnable-track {\n            background-color: $border-dark;\n            background: #467fcf;\n        }\n        &::-moz-range-thumb {\n            background: $dark-theme-1;\n            &:focus {\n                box-shadow: 0 0 0 1px $dark-theme-1, 0 0 0 2px rgba(98, 58, 162, 0.25);\n            }\n            &:active {\n                background-color: #d4e1f4;\n            }\n        }\n        &::-moz-range-track {\n            background-color: $border-dark;\n            background: rgba(0, 50, 126, 0.12);\n        }\n        &::-ms-thumb {\n            background: $dark-theme-1;\n            border-color: rgba(0, 30, 75, 0.12);\n            &:focus {\n                box-shadow: 0 0 0 1px $dark-theme-1, 0 0 0 2px rgba(98, 58, 162, 0.25);\n            }\n            &:active {\n                background-color: #d4e1f4;\n            }\n        }\n        &::-ms-fill-lower {\n            background-color: $border-dark;\n            background: #467fcf;\n        }\n    }\n    .custom-control-label:before {\n        border-color: $border-dark;\n        background-color: rgba(255, 255, 255, 0.02);\n    }\n    .form-range {\n        &:focus {\n            &::-webkit-slider-thumb,\n            &::-moz-range-thumb,\n            &::-ms-thumb {\n                border-color: #467fcf;\n                background-color: #467fcf;\n            }\n        }\n        &::-moz-range-progress {\n            background: #467fcf;\n        }\n        &::-ms-fill-upper {\n            background: rgba(0, 50, 126, 0.12);\n            background-color: $border-dark;\n        }\n    }\n    .custom-switch-description {\n        color: #9595b5;\n    }\n    .custom-switch-input:checked ~ .custom-switch-description {\n        color: #9595b5;\n    }\n    .custom-switch-indicator {\n        background: $dark-theme-1;\n        border-color: $border-dark;\n        &:before {\n            background: $white;\n            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.4);\n        }\n    }\n    .custom-switch-input:focus ~ .custom-switch-indicator {\n        border-color: #60529f;\n    }\n    .custom-radio-md .custom-control-label::before,\n    .custom-radio-lg .custom-control-label::before {\n        background-color: $border-dark;\n    }\n    .custom-checkbox-md,\n    .custom-checkbox-lg {\n        .custom-control-label::before {\n            background-color: $border-dark;\n        }\n        .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {\n            background-color: rgba(212, 182, 228, 0.5);\n        }\n    }\n    .custom-switch-input:disabled ~ .custom-switch-indicator {\n        background: #e9ecef;\n    }\n    .custom-switch-input:checked ~ .custom-switch-indicator {\n        background: $primary-1;\n    }\n    .custom-switch-indicator-md,\n    .custom-switch-indicator-lg {\n        background: $dark-theme-1;\n        border-color: $border-dark;\n        &::before {\n            background: $white;\n            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.4);\n        }\n    }\n    .collapse:not(.show) {\n        background: $dark-theme-1;\n    }\n    .notifyimg {\n        color: $white;\n    }\n    /*----- Global Loader -----*/\n    .aside {\n        background: $dark-theme-1;\n        border-left-color: $border-dark;\n        box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.05);\n    }\n    a.icon:hover {\n        color: #dcdfed !important;\n    }\n    @media (max-width: 992px) {\n        .about-con {\n            border-bottom-color: $border-dark;\n        }\n    }\n    @media (max-width: 480px) {\n        .tabs-menu ul li a,\n        .tabs-menu1 ul li {\n            border-color: $border-dark;\n        }\n    }\n    @media (max-width: 320px) {\n        .construction .btn.btn-icon {\n            color: $white;\n        }\n    }\n    @media (max-width: 360px) {\n        .breadcrumb {\n            color: $white;\n        }\n    }\n    @media (max-width: 768px) {\n        .richText .richText-toolbar ul li a {\n            border: rgba(0, 40, 100, 0.12) solid 1px;\n        }\n        .richText .richText-toolbar ul li {\n            border-bottom: $border-dark solid 1px;\n        }\n        .richText .richText-toolbar {\n            border-bottom: 0 !important;\n        }\n    }\n    .stamp {\n        color: $white;\n        background: #868e96;\n    }\n    .example {\n        border-color: $border-dark;\n    }\n    .example-bg {\n        background: $dark-theme-1;\n    }\n    .colorinput-color {\n        border: 3px solid $border-dark;\n        color: $white;\n        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n    }\n    .colorinput-input:focus ~ .colorinput-color {\n        border-color: $border-dark;\n        box-shadow: 0 0 0 2px rgba(98, 58, 162, 0.25);\n    }\n    #back-to-top {\n        color: $white;\n        &:hover {\n            background: $white !important;\n        }\n    }\n    .features span {\n        color: #43414e;\n    }\n    .feature .border {\n        color: $white;\n    }\n    .actions:not(.a-alt) > li > a > i {\n        color: #939393;\n    }\n    /* --------Added--------- css*/\n    #sidebar li a.active {\n        background: $dark-theme-1;\n        a[data-toggle=\"collapse\"] {\n            background: $dark-theme-1;\n        }\n    }\n    /* line 77, C:/wamp/www/github/addSlider/src/partials/_addSlider.scss */\n    .line-divide {\n        border-color: rgba(218, 216, 219, 0.2);\n    }\n    .activity {\n        border-left-color: rgba(0, 0, 0, 0.125);\n    }\n    .username {\n        color: $white;\n        + p {\n            color: #f2f2f2;\n        }\n    }\n    #user-profile {\n        .profile-details ul > li > span {\n            color: #643ba2;\n        }\n    }\n    @media (max-width: 1024px) {\n        body {\n            &.search-show:before,\n            &.sidebar-show:before {\n                background-color: rgba(0, 0, 0, 0.4);\n            }\n        }\n    }\n    @media (max-width: 575.98px) {\n        .header .form-inline .search-element .form-control {\n            background: $dark-theme-1;\n        }\n        .form-control.header-search {\n            color: $black;\n        }\n        .header {\n            .form-inline {\n                .btn {\n                    color: #46494a !important;\n                }\n                .form-control::-webkit-input-placeholder {\n                    color: $white !important;\n                }\n            }\n            .navsearch i {\n                color: $white;\n            }\n        }\n    }\n    .settings {\n        color: $white;\n    }\n    .member {\n        background: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    @media screen and (max-width: 998px) and (min-width: 768px) {\n        .note-popover .popover-content,\n        .card-header.note-toolbar {\n            background: $dark-theme-1;\n        }\n    }\n    .material-switch > {\n        label {\n            &::before {\n                background: #9595b5;\n                box-shadow: inset 0px 0px 10px rgba(42, 38, 53, 0.5);\n            }\n            &::after {\n                background: #3d3d5a;\n                box-shadow: 0px 0px 5px rgba(42, 38, 53, 0.9);\n            }\n        }\n        input[type=\"checkbox\"]:checked + label {\n            &::before,\n            &::after {\n                background: inherit;\n            }\n        }\n    }\n    .sw-theme-default > ul.step-anchor > li > a::after {\n        background: none !important;\n    }\n    .border-transparet {\n        border-color: $white-1;\n    }\n    .breadcrumb .breadcrumb-item.active,\n    .breadcrumb-item1 a {\n        color: $primary-1;\n    }\n    .input-group.input-indec .form-control {\n        border-color: $border-dark;\n    }\n    /*********************IE***************************/\n    @media (min-width: 992px) {\n        .main-header-center .form-control {\n            background: $dark-theme-1;\n            border-color: $border-dark;\n        }\n        &.header-light .main-header-center .form-control {\n            background: $white;\n            border: 1px solid $border;\n        }\n    }\n    @media (min-width: 992px) {\n        .main-header-center .btn {\n            background-color: transparent;\n            color: #b4bdce;\n        }\n    }\n    .pulse-danger {\n        background: #ee335e;\n        &:before {\n            background: rgba(238, 51, 94, 0.8);\n            box-shadow: 0 0 0 rgba(238, 51, 94, 0.9);\n        }\n    }\n    .pulse {\n        background: #22c03c;\n        &:before {\n            background: rgba(34, 192, 60, 0.6);\n            box-shadow: 0 0 0 rgba(34, 192, 60, 0.9);\n        }\n    }\n    .progress {\n        &.info1 {\n            background: #fed5db;\n        }\n        &.secondary {\n            background: #f9d1bd;\n        }\n    }\n    .avatar {\n        color: $white;\n    }\n    .badge-success-light {\n        background-color: rgba(19, 191, 27, 0.15) !important;\n        color: #13bf1b;\n    }\n    .badge-orange-light {\n        color: #e17626;\n        background-color: rgba(225, 118, 38, 0.15);\n    }\n    .badge-danger-light {\n        color: #f33819;\n        background-color: rgba(243, 56, 25, 0.15);\n    }\n    .badge.bg-white {\n        background-color: $white !important;\n    }\n    .browser-stats img {\n        background: $dark-body;\n    }\n    .box-shadow-primary {\n        box-shadow: 0 5px 10px #42444a;\n    }\n    .box-shadow-secondary {\n        box-shadow: 0 5px 10px #4e4642;\n    }\n    .box-shadow-success {\n        box-shadow: 0 5px 10px #45504e;\n    }\n    .box-shadow-danger {\n        box-shadow: 0 5px 10px #524a4b;\n    }\n    .box-shadow-pink {\n        box-shadow: 0 5px 10px #544c4f;\n    }\n    .sidebar {\n        .tab-menu-heading {\n            background: $primary-1;\n            color: $white;\n        }\n    }\n    .sidebar-right .dropdown-item h6 {\n        color: $default-color;\n    }\n    .nav.panel-tabs {\n        a.active {\n            color: $primary-1;\n        }\n        &.panel-secondary a {\n            &.active {\n                background-color: $secondary !important;\n                color: $white;\n            }\n            color: $secondary;\n        }\n        &.panel-danger a {\n            &.active {\n                background-color: $danger !important;\n                color: $white;\n            }\n            color: $danger;\n        }\n        &.panel-success a {\n            color: $success;\n            &.active {\n                background-color: $success !important;\n                color: $white;\n            }\n        }\n        &.panel-info a {\n            color: $info;\n            &.active {\n                background-color: $info !important;\n                color: $white;\n            }\n        }\n    }\n    .task-list {\n        color: $text-color;\n        &:before {\n            border-left-color: $border-dark;\n        }\n    }\n    .mail-inbox .icons {\n        color: $text-color !important;\n    }\n    .table-inbox tr td {\n        i {\n            color: #4e4c6a;\n            &:hover {\n                color: #f7284a;\n            }\n        }\n        .fa-star:hover {\n            color: #fbc518;\n        }\n    }\n    .mail-option {\n        .btn-group a.btn,\n        .chk-all {\n            border-color: $border-dark;\n            color: $text-color;\n        }\n        .btn-group a.all {\n            box-shadow: none;\n        }\n    }\n    .inbox-pagination a.np-btn {\n        border-color: $border-dark;\n    }\n    .acc-header a {\n        &.collapsed {\n            border-color: $border-dark;\n        }\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .acc-body {\n        border-color: $border-dark;\n    }\n    .card-pay .tabs-menu li a {\n        &.active {\n            background: $primary-1;\n            color: $white;\n        }\n        border-color: $border-dark;\n        color: $text-color;\n    }\n    .main-content-label,\n    .card-table-two .card-title,\n    .card-dashboard-eight .card-title {\n        color: $text-color;\n    }\n    .social-login {\n        background: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .user-social-detail .social-profile {\n        background: #383862;\n    }\n    .sticky.sticky-pin .horizontal-main.hor-menu {\n        box-shadow: 5px 7px 26px -5px rgba(104, 113, 123, 0.1);\n    }\n    .mini-stat-icon {\n        color: $white;\n    }\n    .product-grid6 {\n        overflow: hidden;\n        .price span {\n            color: #9595b5;\n        }\n        .icons-wishlist li a {\n            i {\n                color: inherit;\n            }\n            &:hover,\n            &:after,\n            &:before {\n                color: $white;\n            }\n        }\n        .icons li a {\n            i {\n                color: inherit;\n            }\n            &:hover,\n            &:after,\n            &:before {\n                color: $white;\n            }\n        }\n    }\n    .apexcharts-radialbar-track.apexcharts-track path {\n        stroke: $dark-theme-1;\n    }\n    .apex-charts text {\n        fill: #000200;\n    }\n    /*--- Offcanvas ---*/\n    .offcanvas {\n        background: $dark-theme-1;\n    }\n    .offcanvas-start {\n        border-right-color: $border-dark;\n    }\n    .offcanvas-end {\n        border-left-color: $border-dark;\n    }\n    .offcanvas-bottom {\n        border-top-color: $border-dark;\n    }\n    .offcanvas-top {\n        border-bottom-color: $border-dark;\n    }\n    /*--Toast ---*/\n    .toast {\n        background: $dark-theme-1;\n        border-color: $border-dark;\n        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5);\n        .toast-header {\n            background: $dark-theme-1;\n            color: $text-color;\n            border-bottom-color: $border-dark;\n        }\n    }\n    .toast.show {\n        .btn-close {\n            color: #d5d5d5;\n        }\n    }\n    .task-icon1:first-child {\n        border: 2px solid $primary-1;\n    }\n    .nav.product-sale a.active {\n        background-color: $dark-theme-1;\n        border-bottom: none;\n    }\n    .profile-cover__img {\n        color: $white;\n        .profile-img-1 > img {\n            border: 5px solid #ffffff;\n        }\n        > .h3 {\n            color: #393939;\n        }\n    }\n    .profile-cover__info .nav li {\n        color: #464461;\n    }\n    .social.social-profile-buttons .social-icon {\n        background: #3b366c;\n        border-color: $border-dark;\n        color: #abb4c7;\n    }\n    .profile-share {\n        border-color: $border-dark;\n        background: $dark-theme-1;\n    }\n    .option-dots {\n        &:focus,\n        &:hover,\n        &[aria-expanded=\"true\"] {\n            background: #3b3b64;\n        }\n        color: #5c678f;\n    }\n    @media (min-width: 601px) {\n        .social-profile-buttons .nav {\n            color: #999;\n        }\n    }\n    .social-profile-buttons .nav li {\n        color: #464461;\n    }\n    .item2-gl-menu {\n        border-color: $border-dark;\n        li {\n            a {\n                color: #b0b0c5;\n            }\n            .active {\n                color: $primary-1;\n            }\n        }\n    }\n    .product-label {\n        background: rgba(178, 177, 183, 0.1);\n    }\n    .ui-state-hover,\n    .ui-widget-content .ui-state-hover,\n    .ui-widget-header .ui-state-hover,\n    .ui-state-focus,\n    .ui-widget-content .ui-state-focus,\n    .ui-widget-header .ui-state-focus {\n        border-color: $primary-1 !important;\n        background: $primary-1 !important;\n    }\n    .ui-widget-content {\n        background: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .product-list .icons li a {\n        &:after,\n        &:before {\n            color: $white;\n        }\n    }\n    .product-grid6 .card-footer .btn-outline-primary:hover {\n        color: $white;\n    }\n    .carousel-inner .carousel-item .thumb {\n        &.active {\n            border-color: $border-dark;\n        }\n        border-color: $border-dark;\n    }\n    .customer-services {\n        span {\n            background-color: $dark-theme-1;\n            color: $primary-1;\n        }\n        border-color: $border-dark;\n    }\n    .login-social-icon {\n        &::before,\n        &::after {\n            background-color: $border-dark;\n        }\n        span {\n            background: $dark-theme-1;\n        }\n    }\n    .custom-layout {\n        color: $white;\n        .nav-link.icon {\n            i {\n                color: $white !important;\n                box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);\n                background: rgba(255, 255, 255, 0.08);\n            }\n            i::after {\n                background-color: transparent;\n            }\n        }\n    }\n    .dark-mode .custom-layout .nav-link.icon i {\n        color: $white !important;\n    }\n    .country-selector .nav-link {\n        color: #495046;\n    }\n    .country {\n        color: #f7f7f7;\n    }\n    .country span:hover {\n        color: #f7f7f7;\n    }\n    .theme-container .active {\n        border-color: $primary-1;\n        background: $dark-theme-1;\n        color: $primary-1;\n    }\n    .theme-container1 .active {\n        border-color: $pink;\n        background: $dark-theme-1;\n        color: $pink;\n    }\n    .theme-container2 .active {\n        border-color: $secondary;\n        background: $dark-theme-1;\n        color: $secondary;\n    }\n    .settings-icon {\n        border-color: $primary-1;\n    }\n    .input-group-text.input-text-color {\n        background-color: $dark-theme-1;\n    }\n    .payment-icon {\n        &.active svg {\n            fill: $white;\n        }\n        svg {\n            fill: $text-color;\n        }\n    }\n    .notification {\n        &:before {\n            background: $primary-02;\n        }\n        .notification-time {\n            .date,\n            .time {\n                color: #8f8fb1;\n            }\n        }\n        .notification-icon a {\n            background: $dark-theme-1;\n            color: $white;\n            border: 3px solid $primary-1;\n        }\n        .notification-body {\n            box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.03);\n            background: $dark-theme-1;\n            &:before {\n                border: 10px solid transparent;\n                border-right-color: $dark-theme-1;\n            }\n        }\n    }\n    .notification-time-date {\n        color: #8f8fb1;\n    }\n    .btn-country {\n        border-color: $border-dark;\n        box-shadow: none !important;\n        color: $text-color;\n        box-shadow: 0px 1px 16px rgba(0, 0, 0, 0.1) !important;\n        &:hover {\n            border-color: $primary-1 !important;\n            box-shadow: 0px 1px 16px rgba(0, 0, 0, 0.1) !important;\n        }\n    }\n    .btn-check:checked + .btn-country {\n        border-color: $primary-1 !important;\n    }\n    .btn-country {\n        &.active,\n        &:active {\n            border-color: $primary-1 !important;\n        }\n    }\n    .email-icon {\n        border-color: $border-dark;\n        color: #5c678f;\n    }\n    .product-grid6 .card-footer .btn-outline-primary:hover .wishlist-icon {\n        color: $white;\n    }\n    .btn-outline-primary {\n        .wishlist-icon {\n            color: $primary-1 !important;\n        }\n        &:hover .wishlist-icon {\n            color: $white !important;\n        }\n    }\n    #chartZoom .btn-outline-primary:hover {\n        color: $white;\n    }\n    .file-image .icons li a {\n        color: $white;\n        &:after,\n        &:before,\n        &:hover {\n            color: $white;\n        }\n    }\n    .file-name {\n        color: $white;\n    }\n    .img-1 img {\n        border: 6px solid rgba(225, 225, 225, 0.5);\n    }\n    .profile-img {\n        border-color: rgba(167, 180, 201, 0.2);\n        background: rgba(225, 225, 225, 0.2);\n    }\n    /*-----Gallery-----*/\n    .demo-gallery {\n        > ul > li {\n            a {\n                border: 3px solid $white;\n                .demo-gallery-poster {\n                    background-color: rgba(0, 0, 0, 0.1);\n                }\n            }\n        }\n        &.dark > ul > li a {\n            border: 3px solid #04070a;\n        }\n    }\n    .gallery a img {\n        border-color: rgba(0, 0, 0, 0.2);\n    }\n    .example + .highlight {\n        border-top: none;\n    }\n    .highlight {\n        border-color: $border-dark;\n        border-top: none;\n        background: $dark-theme-1;\n        .hll {\n            background-color: #ffc;\n        }\n        .c {\n            color: #999;\n        }\n        .k {\n            color: #069;\n        }\n        .o {\n            color: #555;\n        }\n        .cm {\n            color: #999;\n        }\n        .cp {\n            color: #099;\n        }\n        .c1,\n        .cs {\n            color: #999;\n        }\n        .gd {\n            background-color: #fcc;\n            border-color: #c00;\n        }\n        .gr {\n            color: #f00;\n        }\n        .gh {\n            color: #030;\n        }\n        .gi {\n            background-color: #cfc;\n            border-color: #0c0;\n        }\n        .go {\n            color: #aaa;\n        }\n        .gp {\n            color: #009;\n        }\n        .gu {\n            color: #030;\n        }\n        .gt {\n            color: #9c6;\n        }\n        .kc,\n        .kd,\n        .kn,\n        .kp,\n        .kr {\n            color: #069;\n        }\n        .kt {\n            color: #078;\n        }\n        .m {\n            color: #f60;\n        }\n        .s {\n            color: #cc0099;\n        }\n        .na {\n            color: #00cc7a;\n        }\n        .nb {\n            color: #366;\n        }\n        .nc {\n            color: #0a8;\n        }\n        .no {\n            color: #360;\n        }\n        .nd {\n            color: #99f;\n        }\n        .ni {\n            color: #999;\n        }\n        .ne {\n            color: #c00;\n        }\n        .nf {\n            color: #c0f;\n        }\n        .nl {\n            color: #99f;\n        }\n        .nn {\n            color: #0cf;\n        }\n        .nt {\n            color: #e12020;\n        }\n        .nv {\n            color: #033;\n        }\n        .ow {\n            color: $black;\n        }\n        .w {\n            color: #bbb;\n        }\n        .mf,\n        .mh,\n        .mi,\n        .mo {\n            color: #f60;\n        }\n        .sb,\n        .sc,\n        .sd,\n        .s2,\n        .se,\n        .sh {\n            color: #c30;\n        }\n        .si {\n            color: #a00;\n        }\n        .sx {\n            color: #c30;\n        }\n        .sr {\n            color: #3aa;\n        }\n        .s1 {\n            color: #c30;\n        }\n        .ss {\n            color: #fc3;\n        }\n        .bp {\n            color: #366;\n        }\n        .vc,\n        .vg,\n        .vi {\n            color: #033;\n        }\n        .il {\n            color: #f60;\n        }\n        .css {\n            .o {\n                color: #999;\n                + .nt {\n                    color: #999;\n                }\n            }\n            .nt + .nt {\n                color: #999;\n            }\n        }\n        .language-bash::before,\n        .language-sh::before,\n        .language-powershell::before {\n            color: #009;\n        }\n    }\n    .label-default {\n        background: #d5e0ec;\n        color: $white;\n    }\n    .label-success {\n        background: $success;\n        color: $white;\n    }\n    .label-danger {\n        background: #f5334f;\n        color: $white;\n    }\n    .label-warning {\n        background: $warning;\n        color: $white;\n    }\n    .label-info {\n        background: $info;\n        color: $white;\n    }\n    /*-----Lists-----*/\n    .list-group-item.active {\n        background-color: #292948;\n        color: $text-color;\n        border-color: $border-dark;\n    }\n    .list-group-item-action {\n        color: #9595b5;\n        &:hover,\n        &:focus,\n        &:active {\n            color: #9595b5;\n            background-color: $dark-theme-1;\n        }\n    }\n    .list-group-item,\n    .listorder,\n    .listorder1,\n    .listunorder,\n    .listunorder1 {\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        color: #9595b5;\n    }\n    .list-group-item {\n        &.disabled,\n        &:disabled {\n            color: #545c74;\n            background-color: #252542;\n        }\n    }\n    .list-group-item-primary {\n        color: #24426c !important;\n        background-color: #cbdbf2;\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: #24426c;\n                background-color: #b7cded;\n            }\n            &.active {\n                color: $white;\n                background-color: #24426c;\n                border-color: #24426c;\n            }\n        }\n    }\n    .list-group-item-secondary {\n        color: #464a4e !important;\n        background-color: #dddfe2;\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: #464a4e;\n                background-color: #cfd2d6;\n            }\n            &.active {\n                color: $white;\n                background-color: #464a4e;\n                border-color: #464a4e;\n            }\n        }\n    }\n    .list-group-item-success {\n        color: $success !important;\n        background-color: rgba(9, 173, 149, 0.4);\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: $success;\n                background-color: rgba(9, 173, 149, 0.2);\n            }\n            &.active {\n                color: $white;\n                background-color: $success;\n                border-color: $success;\n            }\n        }\n    }\n    .list-group-item-info {\n        color: $info !important;\n        background-color: rgba(17, 112, 228, 0.4);\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: $info;\n                background-color: rgba(17, 112, 228, 0.2);\n            }\n            &.active {\n                color: $white;\n                background-color: rgba(17, 112, 228, 0.2);\n                border-color: rgba(17, 112, 228, 0.2);\n            }\n        }\n    }\n    .list-group-item-warning {\n        color: $warning !important;\n        background-color: rgba(247, 183, 49, 0.4);\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: $warning;\n                background-color: rgba(247, 183, 49, 0.2);\n            }\n            &.active {\n                color: $white;\n                background-color: $warning;\n                border-color: $warning;\n            }\n        }\n    }\n    .list-group-item-danger {\n        color: $danger !important;\n        background-color: rgba(232, 38, 70, 0.4);\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: $danger;\n                background-color: rgba(232, 38, 70, 0.2);\n            }\n            &.active {\n                color: $white;\n                background-color: $danger;\n                border-color: $danger;\n            }\n        }\n    }\n    .list-group-item-light {\n        color: #818182;\n        background-color: #fdfdfe;\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: #818182;\n                background-color: #ececf6;\n            }\n            &.active {\n                color: $white;\n                background-color: #818182;\n                border-color: #818182;\n            }\n        }\n    }\n    .list-group-item-dark {\n        color: #1b1e21;\n        background-color: #c6c8ca;\n        &.list-group-item-action {\n            &:hover,\n            &:focus {\n                color: #1b1e21;\n                background-color: #b9bbbe;\n            }\n            &.active {\n                color: $white;\n                background-color: #1b1e21;\n                border-color: #1b1e21;\n            }\n        }\n    }\n    .list-group-item {\n        border-color: $border-dark;\n        color: #9595b5;\n        &.active .icon {\n            color: inherit !important;\n        }\n        .icon {\n            color: $text-color !important;\n        }\n    }\n    .list-group-transparent {\n        .list-group-item {\n            &.active {\n                background: #3b3b60;\n                color: $text-color;\n            }\n        }\n        &.file-manager .list-group-item {\n            color: $text-color;\n        }\n    }\n    .list-group-transparent.file-manager.file-manager-border .list-group-item {\n        border-color: $border-dark;\n    }\n    .file-image-1 {\n        border-color: $border-dark;\n    }\n    .file-image-1 .file-name-1 {\n        color: $text-color;\n    }\n    .file-radius-attachments i {\n        color: $text-color;\n    }\n    .file-square-attachments a {\n        color: $text-color;\n    }\n    /*------ Media object ------*/\n    .btn-close {\n        color: #9595b5;\n        &:hover,\n        &:focus {\n            color: inherit;\n        }\n    }\n    .navbar-toggler {\n        background-color: transparent;\n        border-color: transparent;\n    }\n    @media (min-width: 992px) {\n        .responsive-navbar .navbar-collapse {\n            background: transparent;\n        }\n    }\n    .navbar-light {\n        .navbar-brand {\n            color: $text-color;\n            &:hover,\n            &:focus {\n                color: $text-color;\n            }\n        }\n        .navbar-nav {\n            .nav-link {\n                color: rgba(0, 0, 0, 0.5);\n                &:hover,\n                &:focus {\n                    color: rgba(0, 0, 0, 0.7);\n                }\n                &.disabled {\n                    color: rgba(0, 0, 0, 0.3);\n                }\n            }\n            .show > .nav-link,\n            .active > .nav-link {\n                color: rgba(0, 0, 0, 0.9);\n            }\n            .nav-link {\n                &.show,\n                &.active {\n                    color: rgba(0, 0, 0, 0.9);\n                }\n            }\n        }\n        .navbar-toggler {\n            color: rgba(0, 0, 0, 0.5);\n            border-color: rgba(0, 0, 0, 0.1);\n        }\n        .navbar-text {\n            color: rgba(0, 0, 0, 0.5);\n            a {\n                color: rgba(0, 0, 0, 0.9);\n                &:hover,\n                &:focus {\n                    color: rgba(0, 0, 0, 0.9);\n                }\n            }\n        }\n    }\n    .navbar-dark {\n        .navbar-brand {\n            color: $white;\n            &:hover,\n            &:focus {\n                color: $white;\n            }\n        }\n        .navbar-nav {\n            .nav-link {\n                color: rgba(255, 255, 255, 0.5);\n                &:hover,\n                &:focus {\n                    color: rgba(255, 255, 255, 0.75);\n                }\n                &.disabled {\n                    color: rgba(255, 255, 255, 0.25);\n                }\n            }\n            .show > .nav-link,\n            .active > .nav-link {\n                color: $white;\n            }\n            .nav-link {\n                &.show,\n                &.active {\n                    color: $white;\n                }\n            }\n        }\n        .navbar-toggler {\n            color: rgba(255, 255, 255, 0.5);\n            border-color: $white-1;\n        }\n        .navbar-text {\n            color: rgba(255, 255, 255, 0.5);\n            a {\n                color: $white;\n                &:hover,\n                &:focus {\n                    color: $white;\n                }\n            }\n        }\n    }\n    @media (max-width: 992px) {\n        .navresponsive-toggler span {\n            color: #dcdfed;\n            &:after {\n                background: rgba($primary-1, 0);\n            }\n        }\n        .responsive-navbar .navbar-collapse .icon.navsearch {\n            border-color: #e4e6f9;\n        }\n    }\n    @media (max-width: 991px) {\n        .responsive-navbar .navbar-collapse {\n            background: $dark-theme-1;\n            box-shadow: 0 12px 11px -3px rgba(104, 113, 123, 0.1);\n            border-top-color: $border-dark;\n        }\n    }\n    /*--scrollspy ---*/\n    .scrollspy-example {\n        border-right-color: $border-dark;\n        border-left-color: $border-dark;\n        border-bottom-color: $border-dark;\n    }\n    .scrollspy-example-2 {\n        border-color: $border-dark;\n    }\n    #navbar-example3 .nav-link {\n        color: $text-color;\n        &.active {\n            color: $white;\n        }\n    }\n    .nav-link {\n        &:focus,\n        &:hover {\n            color: $primary-1;\n        }\n    }\n    /*-----Pricing tables-----*/\n    .pricing {\n        color: $white;\n    }\n    .pricing1 {\n        color: #707070;\n    }\n    .pricing {\n        .list-unstyled li {\n            border-bottom-color: $white-1;\n        }\n    }\n    .pricing1 {\n        .list-unstyled li {\n            border-bottom-color: $border-dark;\n        }\n    }\n    /*------ Pricing Styles ---------*/\n    .panel-heading {\n        border-bottom-color: rgba(255, 255, 255, 0.2);\n        background: $dark-theme-1;\n    }\n    .panel.price {\n        box-shadow: 0 0.15rem 1.75rem 0 #0e0f2e;\n        background: $dark-theme-1;\n        > .panel-heading {\n            color: $white;\n        }\n    }\n    .price {\n        .panel-footer {\n            background-color: $dark-theme-1;\n        }\n        &.panel-color > .panel-body {\n            background-color: $dark-theme-1;\n        }\n    }\n    .ribbone1-price .ribbon span {\n        color: $white;\n        background: #79a70a;\n        background: $primary-1;\n        box-shadow: 0 3px 10px -5px black;\n        &::before {\n            border-left: 3px solid $primary-1;\n            border-right: 3px solid transparent;\n            border-bottom: 3px solid transparent;\n            border-top: 3px solid $primary-1;\n        }\n        &::after {\n            border-left: 3px solid transparent;\n            border-right: 3px solid $primary-1;\n            border-bottom: 3px solid transparent;\n            border-top: 3px solid $primary-1;\n        }\n    }\n    .secondary .pricing-divider {\n        background: $dark-theme-1;\n    }\n    .danger .pricing-divider {\n        background: $dark-theme-1 !important;\n    }\n    .primary .pricing-divider {\n        background: $dark-theme-1 !important;\n    }\n    .success .pricing-divider {\n        background: $dark-theme-1;\n    }\n    /*-- rating--*/\n    .rating-stars {\n        input {\n            color: #495057;\n            background-color: $dark-theme-1;\n            border-color: $border-dark;\n        }\n    }\n    .tabs-menu ul li {\n        a {\n            color: $text-color;\n        }\n        .active {\n            color: $primary-1;\n        }\n    }\n    .tabs-menu1 ul li a {\n        color: $text-color;\n    }\n    .tab-menu-heading {\n        border-bottom-color: $border-dark !important;\n    }\n    .tabs-menu2 ul li {\n        a {\n            color: #636262;\n        }\n        .fade {\n            color: #eeee;\n        }\n    }\n    .search-tabs ul li a {\n        &.active {\n            border-bottom: 3px solid $primary-1;\n            background-color: transparent !important;\n        }\n        &:hover {\n            background-color: transparent !important;\n        }\n    }\n    .tabs-menu-border ul li .active {\n        border-color: $border-dark;\n    }\n    .tabs-menu-boxed ul li {\n        a {\n            color: $text-color;\n            border-bottom-color: $border-dark;\n        }\n        .active {\n            border-color: $border-dark;\n            border-bottom-color: transparent;\n        }\n    }\n    .tab_wrapper {\n        .content_wrapper {\n            .accordian_header {\n                border-bottom-color: $border-dark;\n                border-top-color: $border-dark;\n            }\n            .accordian_header .arrow {\n                background: transparent;\n                border-top-color: $white-3;\n                border-left-color: $white-3;\n            }\n            .accordian_header.active {\n                border-color: $border-dark;\n            }\n        }\n    }\n    /***** time-line*****/\n    .timeline__item:after {\n        background: $white !important;\n    }\n    .timeline__content {\n        background-color: $dark-theme-1;\n    }\n    /*---- Time line -----*/\n    .timeline:before {\n        background-color: #e9ecef;\n    }\n    .timeline-item {\n        &:first-child:before,\n        &:last-child:before {\n            background: $dark-theme-1;\n        }\n    }\n    .timeline-badge {\n        border-color: $white;\n        background: #adb5bd;\n    }\n    .timeline-time {\n        color: #9aa0ac;\n    }\n    .timeline__item--right .timeline__content:before {\n        border-right: 12px solid rgba(238, 232, 239, 0.9);\n    }\n    ul.timeline {\n        &:before {\n            background: #d4d9df;\n        }\n        > li:before {\n            border-color: #6c6c6f;\n        }\n    }\n    /*----Timeline---*/\n    .vtimeline::before {\n        background-color: $border-dark;\n    }\n    .vtimeline .timeline-wrapper {\n        .timeline-panel {\n            background: $dark-theme-1;\n            box-shadow: 0 5px 12px 0 #101329;\n            &:after {\n                border-top: 10px solid transparent;\n                border-left-color: $border-dark;\n                border-right-color: $border-dark;\n                border-bottom: 10px solid transparent;\n            }\n        }\n        .timeline-badge {\n            border-color: $dark-body;\n            i {\n                color: $white;\n            }\n        }\n        &.timeline-inverted .timeline-panel:after {\n            border-left-width: 0;\n            border-right-width: 10px;\n        }\n    }\n    .timeline-wrapper-primary {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: $primary-1;\n        }\n    }\n    .timeline-wrapper-secondary {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: $secondary;\n        }\n    }\n    .timeline-wrapper-success {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: $success;\n        }\n    }\n    .timeline-wrapper-green {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: $green;\n        }\n    }\n    .timeline-wrapper-warning {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: #fcd539;\n        }\n    }\n    .timeline-wrapper-danger {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: #f16d75;\n        }\n    }\n    .timeline-wrapper-light {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: $dark-theme-1;\n        }\n    }\n    .timeline-wrapper-dark {\n        .timeline-panel:before,\n        .timeline-badge {\n            background: #828db1;\n        }\n    }\n    @media (max-width: 767px) {\n        .vtimeline .timeline-wrapper .timeline-panel:after {\n            border-right: 14px solid $white-2 !important;\n            border-left: 0 solid $white-2 !important;\n        }\n    }\n    /* ######## LAYOUT-STYLES ######## */\n    .footer {\n        background: transparent;\n        border-top-color: $border-dark;\n        .social ul li a {\n            border-color: $border-dark;\n            background: $dark-body;\n            color: $text-color;\n        }\n    }\n    .top-footer {\n        p {\n            color: #9595b5;\n        }\n        a {\n            color: #9595b5;\n            address {\n                color: #9595b5;\n            }\n        }\n        img {\n            border-color: $border-dark;\n            &:hover {\n                color: #8e9090;\n            }\n        }\n    }\n    .footer-payments a {\n        color: #a7a8c7;\n    }\n    .main-footer {\n        background-color: $dark-theme-1;\n        border-top-color: $border-dark;\n    }\n    .header {\n        background: $dark-theme-1;\n        border-bottom-color: $border-dark;\n    }\n    .header-brand,\n    .app-header .header-brand,\n    .header-brand:hover {\n        color: inherit;\n    }\n    @media (max-width: 767px) {\n        .header.hor-header {\n            border-bottom-color: $border-dark;\n            box-shadow: 0 8px 24px $dark-theme-1;\n        }\n        .hor-header .header-brand-img.light-logo {\n            margin: 0 auto;\n            margin-top: 6px;\n        }\n    }\n    .header {\n        .form-inline {\n            .form-control {\n                border-color: rgba(225, 225, 225, 0.1);\n                background: rgba(225, 225, 225, 0.3);\n                color: $black !important;\n            }\n            .btn {\n                border: 2px solid transparent;\n                box-shadow: none;\n                background: transparent;\n                color: $white;\n            }\n        }\n    }\n    @media (max-width: 767.98px) and (min-width: 576px) {\n        .header .navsearch i {\n            color: $white;\n        }\n        .search-element .form-control {\n            background: $white !important;\n            color: $black;\n        }\n        .header {\n            .form-inline .form-control::-webkit-input-placeholder {\n                color: #9595b5;\n            }\n            .navsearch i {\n                color: $white;\n            }\n            .form-inline .btn {\n                color: #46494a !important;\n            }\n        }\n    }\n    @media only screen and (max-width: 991px) {\n        .animated-arrow span {\n            &:before,\n            &:after {\n                background: $default-color;\n            }\n        }\n        .animated-arrow span {\n            &:before,\n            &:after {\n                background: $default-color;\n            }\n        }\n    }\n    @media only screen and (max-width: 991px) {\n        body {\n            background-color: $dark-theme-1;\n        }\n    }\n    .hor-header .header-brand1 {\n        color: inherit;\n    }\n    .header-right-icons {\n        .nav-link.icon:hover {\n            background: none;\n        }\n        .profile-user:hover {\n            box-shadow: none;\n        }\n    }\n    .hor-header .header-right-icons .nav-link.icon {\n        color: #dcdfed;\n    }\n    &.header-light .hor-header .header-right-icons .nav-link.icon {\n        color: $primary-1 !important;\n    }\n    .logo-horizontal .header-brand-img.desktop-logo {\n        display: block;\n    }\n    &.horizontal.header-light .logo-horizontal .header-brand-img.light-logo1 {\n        display: block;\n    }\n    &.horizontal.header-light .logo-horizontal .header-brand-img.desktop-logo {\n        display: none;\n    }\n    .hor-header .header-brand-img.light-logo {\n        display: block;\n    }\n    /*Logo-center header */\n    @media (max-width: 992px) {\n        .header {\n            border-bottom-color: rgba(255, 255, 255, 0.2);\n        }\n    }\n    .header {\n        .dropdown-menu {\n            box-shadow: 0 10px 40px 0 rgba(104, 113, 123, 0.2);\n            border-color: $border-dark;\n            .dropdown-item {\n                border-bottom-color: $border-dark;\n            }\n        }\n        .dropdown-item,\n        .notifications-menu h5,\n        .message-menu h5 {\n            color: $text-color;\n        }\n        .notifications-menu span,\n        .message-menu span {\n            color: #9595b5;\n        }\n        .dropdown-menu {\n            box-shadow: 0 10px 40px 0 rgba(104, 113, 123, 0.2);\n            border-color: $border-dark;\n            .dropdown-item {\n                border-bottom-color: $border-dark;\n            }\n        }\n        .profile-1 .dropdown-item .dropdown-icon {\n            color: $primary-1;\n            &::after {\n                background: rgba($primary-1, 0);\n            }\n        }\n    }\n    .responsive-navbar {\n        .notifications-menu h5,\n        .message-menu h5 {\n            color: $text-color;\n        }\n        .notifications-menu span,\n        .message-menu span {\n            color: #9595b5;\n        }\n        .dropdown-menu {\n            box-shadow: 0 10px 40px 0 rgba(34, 34, 61, 0.8);\n            border-color: $border-dark;\n            .dropdown-item {\n                border-bottom-color: $border-dark;\n            }\n        }\n        .profile-1 .dropdown-item .dropdown-icon {\n            color: $primary-1;\n            &::after {\n                background: rgba($primary-1, 0);\n            }\n        }\n    }\n    /* Desktop Search Bar */\n    /*==============================================================================\n                                Start Mobile CSS\n  ===============================================================================*/\n    /* ================== Mobile Menu Change Brake Point ================== */\n    @media only screen and (max-width: 991px) {\n        /* ================== Mobile Slide Down Links CSS ================== */\n        /* ================== Mobile Mega Menus CSS  ================== */\n        /* ================== Mobile Header CSS ================== */\n        .horizontal-header {\n            border-bottom-color: rgba(0, 0, 0, 0.1);\n            border-top-color: rgba(0, 0, 0, 0.1);\n        }\n        .callusbtn {\n            color: #a9a9a9;\n            &:hover .fa {\n                color: #a9a9a9;\n            }\n        }\n        /* Mobile Toggle Menu icon (X ICON) */\n        .animated-arrow span {\n            background: #9595b5;\n            &:before,\n            &:after {\n                background: #9595b5;\n            }\n        }\n        &.active .animated-arrow span {\n            background-color: transparent;\n        }\n        /* ================== Mobile Overlay/Drawer CSS ================== */\n        .horizontal-overlapbg {\n            background-color: rgba(0, 0, 0, 0.45);\n        }\n        /*End Media Query*/\n    }\n    /* Extra @Media Query*/\n    .mega-menubg {\n        background: $dark-theme-1;\n        box-shadow: 0 10px 40px 0 rgba(104, 113, 123, 0.1);\n        border-color: $border-dark;\n    }\n    .horizontal-main.hor-menu {\n        background: $dark-theme-1;\n        border-bottom-color: $border-dark;\n    }\n    .icons-list-item {\n        border-color: $border-dark;\n        i {\n            color: #8080a1;\n        }\n    }\n    /*------ Icons List ------*/\n    .browser {\n        background: no-repeat center/100% 100%;\n    }\n    .flag,\n    .payment {\n        box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);\n    }\n    .col-sm-3 a {\n        border-color: transparent;\n        &:hover {\n            border-color: #ff4647;\n            background: linear-gradient(rgba(56, 123, 131, 0.7), rgba(56, 123, 131, 0.7));\n        }\n    }\n    /* ######## LIB-STYLES ######## */\n    /*----- Date Picker ----*/\n    .ui-datepicker {\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        .ui-datepicker-header {\n            color: $text-color;\n            .ui-datepicker-next,\n            .ui-datepicker-prev {\n                text-indent: -99999px;\n                color: #6c757d;\n            }\n            .ui-datepicker-next {\n                &:hover::before,\n                &:focus::before {\n                    color: $dark;\n                }\n            }\n            .ui-datepicker-prev {\n                &:hover::before,\n                &:focus::before {\n                    color: $dark;\n                }\n            }\n            .ui-datepicker-next-hover,\n            .ui-datepicker-prev-hover {\n                color: #9595b5;\n            }\n        }\n        .ui-datepicker-calendar {\n            th {\n                color: #9595b5;\n            }\n            td {\n                border-color: $border-dark;\n                background-color: $border-dark;\n                span {\n                    background-color: $dark-theme-1;\n                    color: $text-color;\n                }\n                a {\n                    background-color: $dark-theme-1;\n                    color: #9595b5;\n                    &:hover {\n                        background-color: $dark-theme-1;\n                        color: $text-color;\n                    }\n                }\n            }\n            .ui-datepicker-today a {\n                background-color: $dark-theme-1;\n                color: $text-color;\n            }\n        }\n    }\n    .jvectormap-tip {\n        background: $text-color;\n        color: white;\n    }\n    .jvectormap-zoomin,\n    .jvectormap-zoomout,\n    .jvectormap-goback {\n        background: #45456c;\n        color: white;\n    }\n    .jvectormap-legend {\n        background: black;\n        color: white;\n    }\n    .select2-container--default {\n        .select2-selection--single {\n            background-color: $dark-theme-1;\n            border-color: $border-dark !important;\n        }\n        &.select2-container--focus .select2-selection--multiple {\n            background-color: $dark-theme-1;\n            border-color: #9ca3b1;\n            box-shadow: none;\n        }\n        .select2-selection--multiple {\n            background-color: $dark-theme-1;\n            border-color: $border-dark !important;\n        }\n        .select2-search--dropdown .select2-search__field {\n            border-color: $border-dark !important;\n            background: $dark-theme-1;\n        }\n        .select2-selection--multiple {\n            .select2-selection__choice,\n            .select2-selection__choice__remove {\n                color: $white !important;\n            }\n        }\n        .select2-results > .select2-results__options {\n            box-shadow: 0px 16px 18px rgba(104, 113, 123, 0.2);\n        }\n        .select2-selection--single .select2-selection__rendered {\n            color: $text-color;\n        }\n    }\n    .select2-container--default.select2-container--disabled .select2-selection--single {\n        background: $dark-theme-1;\n    }\n    .selectgroup-button {\n        border-color: $border-dark;\n        color: #9aa0ac;\n    }\n    .selectgroup-input {\n        &:checked + .selectgroup-button {\n            background: $dark-theme-1;\n        }\n        &:focus + .selectgroup-button {\n            box-shadow: 0 0 0 2px rgba(98, 58, 162, 0.25);\n        }\n    }\n    /*-----selectize ------*/\n    .selectize-dropdown {\n        color: #495057;\n    }\n    .selectize-input {\n        color: #495057;\n        background: $dark-theme-1;\n        input {\n            color: #495057;\n        }\n    }\n    .selectize-input {\n        border-color: $border-dark;\n        &.full {\n            background-color: $dark-theme-1;\n        }\n        &.focus {\n            border-color: #467fcf;\n            box-shadow: 0 0 0 2px rgba(98, 58, 162, 0.25);\n        }\n    }\n    .selectize-input {\n        &.dropdown-active::before {\n            background: #f0f0f0;\n        }\n    }\n    .selectize-dropdown {\n        border-color: $border-dark;\n        background: $dark-theme-1;\n        [data-selectable] .highlight {\n            background: rgba(125, 168, 208, 0.2);\n        }\n        .optgroup-header {\n            color: #495057;\n            background: $dark-theme-1;\n        }\n        .active {\n            background-color: #f1f4f8;\n            color: #467fcf;\n            &.create {\n                color: #495057;\n            }\n        }\n        .create {\n            color: rgba(48, 48, 48, 0.5);\n        }\n    }\n    .selectize-dropdown .image img,\n    .selectize-input .image img {\n        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.4);\n    }\n    /* ######## SIDEMENU-STYLES ######## */\n    @media (min-width: 992px) {\n        .side-header {\n            background: $dark-theme-1;\n        }\n    }\n    @media print {\n        .app-content {\n            background-color: $dark-theme-1;\n        }\n    }\n    .app-header {\n        border-bottom-color: $border-dark;\n        background: $dark-theme-1;\n    }\n    .app-header__logo {\n        color: $white;\n    }\n    .app-sidebar__toggle {\n        color: #dcdfed;\n        &:after {\n            background: rgba($primary-1, 0);\n        }\n    }\n    .app-sidebar {\n        color: $text-color;\n        background: $dark-theme-1 !important;\n        -webkit-box-shadow: 0px 8px 14.72px 1.28px rgba(42, 38, 53, 0.5);\n        box-shadow: 0px 8px 14.72px 1.28px rgba(42, 38, 53, 0.5);\n        border-right-color: $border-dark;\n        &::-webkit-scrollbar-thumb {\n            background: rgba(0, 0, 0, 0.2);\n        }\n    }\n    .app-sidebar__user {\n        color: #a8a8a8;\n        img {\n            box-shadow: 0 0 25px $white-1;\n            border-color: rgba(255, 255, 255, 0.2);\n            box-shadow: 0px 5px 5px 0px rgba(44, 44, 44, 0.2);\n        }\n    }\n    .app-sidebar__user-name,\n    .app-sidebar__user-designation {\n        color: #e5e9ec;\n    }\n    .side-menu__item {\n        color: $text-color;\n        &.active {\n            color: $primary-1;\n            &:hover,\n            &:focus {\n                color: $primary-1;\n            }\n        }\n        &:hover,\n        &:focus {\n            color: $primary-1;\n        }\n        &:hover {\n            .side-menu__icon,\n            .side-menu__label {\n                color: $primary-1 !important;\n            }\n        }\n        &:focus {\n            .side-menu__icon,\n            .side-menu__label {\n                color: $primary-1 !important;\n            }\n        }\n    }\n    .slide-item {\n        &.active,\n        &:hover,\n        &:focus {\n            color: #b5c1d2;\n        }\n    }\n    .slide-menu a.active {\n        color: $primary-1;\n    }\n    .slide-item {\n        &.active,\n        &:hover,\n        &:focus {\n            color: $primary-1 !important;\n        }\n    }\n    .sub-slide-item,\n    .sub-slide-item2,\n    .sub-side-menu__label,\n    .sub-side-menu__label1,\n    .sub-side-menu__label2 {\n        &.active,\n        &:hover,\n        &:focus {\n            color: $primary-1 !important;\n        }\n    }\n    .slide-menu li .slide-item:before {\n        color: #68798b;\n    }\n    .side-menu .side-menu__icon {\n        color: $text-color !important;\n    }\n    .slide-item {\n        color: $text-color;\n    }\n    @media (min-width: 992px) {\n        .sidebar-mini.sidenav-toggled {\n            .side-menu .side-menu__icon {\n                background: none !important;\n                box-shadow: none;\n            }\n            .sidebar-mini.sidenav-toggled.user-notification::before {\n                background: transparent;\n            }\n            .app-sidebar__user {\n                border-bottom-color: rgba(225, 225, 225, 0.05);\n            }\n        }\n    }\n    .app-title {\n        background-color: $dark-theme-1;\n        -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n    }\n    .app-breadcrumb {\n        background-color: transparent;\n    }\n    .user-info {\n        .text-dark {\n            color: #25252a !important;\n        }\n        .text-muted {\n            color: #9595b5 !important;\n        }\n    }\n    .side-header {\n        border-bottom-color: $border-dark;\n        border-right-color: $border-dark;\n    }\n    .side-menu .sub-category {\n        color: rgba(255, 255, 255, 0.3);\n    }\n    .side-menu .sub-side-menu__label,\n    .sub-slide-label,\n    .sub-side-menu__label2 {\n        color: $text-color;\n    }\n    .sub-slide-item,\n    .sub-slide-item2 {\n        color: $text-color;\n    }\n    /*-- Subslide ---*/\n    /*-- Subslide2 ---*/\n    /* ######## TEMP-STYLES ######## */\n    .richText {\n        border: $border-dark solid 1px;\n        background-color: $dark-theme-1 !important;\n        .richText-toolbar {\n            border-bottom: rgba(156, 162, 161, 0) solid 1px;\n            ul li a {\n                border-right: rgba(156, 162, 161, 0.2) solid 1px;\n            }\n        }\n    }\n    .dark-mode .cal1 .clndr .clndr-table tr .day.event:hover,\n    .cal1 .clndr .clndr-table tr .day.my-event:hover,\n    .dark-mode .cal1 .clndr .clndr-table tr .day.today,\n    .cal1 .clndr .clndr-table tr .day.my-today {\n        color: $text-color;\n    }\n    .cal1 .clndr {\n        .clndr-table {\n            .header-days .header-day {\n                border-left-color: $border-dark;\n                border-top-color: $border-dark;\n                border-right-color: $border-dark;\n                color: $text-color;\n            }\n            tr .day.event:hover,\n            .cal1 .clndr .clndr-table tr .day.my-event:hover {\n                color: $text-color;\n            }\n            tr:last-child .day,\n            .cal1 .clndr .clndr-table tr:last-child .my-day {\n                border-bottom-color: $border-dark;\n            }\n            tr {\n                .empty,\n                .adjacent-month,\n                .my-empty,\n                .my-adjacent-month {\n                    border-left-color: $border-dark;\n                    border-top-color: $border-dark;\n                    color: #9595b5;\n                }\n                .day {\n                    border-left-color: $border-dark;\n                    border-top-color: $border-dark;\n                    &.event,\n                    &.my-event {\n                        background: #252548 !important;\n                    }\n                    &:last-child {\n                        border-right-color: $border-dark;\n                    }\n                    &:hover {\n                        background: #2e2e4a;\n                    }\n                }\n            }\n        }\n        .clndr-controls {\n            border-color: $border-dark;\n            background-color: transparent;\n        }\n        .clndr-controls .clndr-control-button {\n            .clndr-previous-button,\n            .clndr-next-button {\n                color: $white;\n            }\n        }\n    }\n    .fc-unthemed {\n        .fc-content,\n        .fc-divider,\n        .fc-list-heading td,\n        .fc-list-view,\n        .fc-popover,\n        .fc-row,\n        tbody,\n        td,\n        th,\n        thead {\n            border-color: $border-dark;\n        }\n    }\n    .fc-event,\n    .fc-event-dot {\n        color: $border-dark !important;\n    }\n    .fc-unthemed {\n        .fc-divider,\n        .fc-list-heading td,\n        .fc-popover .fc-header {\n            background: $border-dark;\n        }\n    }\n    .fc-toolbar {\n        .fc-state-active,\n        .ui-state-active {\n            background: #b4b4b4;\n        }\n    }\n    .fc-today-button fc-button fc-state-default fc-corner-left fc-corner-right fc-state-disabled:focus {\n        border: none !important;\n        box-shadow: none !important;\n    }\n    .fc-unthemed .fc-list-item:hover td {\n        background-color: #eeeeee;\n    }\n    .cal1 .clndr .clndr-table tr {\n        .empty:hover,\n        .adjacent-month:hover,\n        .my-empty:hover,\n        .my-adjacent-month:hover {\n            background: #2f2f50;\n        }\n    }\n    /*------ Charts styles ------*/\n    .instagram {\n        background: linear-gradient(to right bottom, #de497b 0%, #e1164f 100%);\n    }\n    .linkedin {\n        background-image: linear-gradient(to right bottom, #0d97de 0%, #13547a 100%);\n    }\n    .twitter {\n        background-image: linear-gradient(to right bottom, #00f2fe 0%, #1e63c3 100%);\n    }\n    .facebook {\n        background-image: linear-gradient(to right bottom, #3d6cbf 0%, #1e3c72 100%);\n    }\n    .map-header:before {\n        background: linear-gradient(to bottom, rgba(245, 247, 251, 0) 5%, $dark-theme-1 95%);\n    }\n    /*----chart-drop-shadow----*/\n    .chart-dropshadow {\n        -webkit-filter: drop-shadow(-6px 5px 4px #2a2635);\n        filter: drop-shadow(-6px 5px 4px #2a2635);\n    }\n    .chart-dropshadow-primary {\n        -webkit-filter: drop-shadow((-6px) 12px 4px rgba(133, 67, 246, 0.2));\n        filter: drop-shadow((-6px) 12px 4px rgba(133, 67, 246, 0.2));\n    }\n    .chart-dropshadow-primary-1 {\n        -webkit-filter: drop-shadow((-6px) 12px 4px rgba(133, 67, 246, 0.2));\n        filter: drop-shadow((-6px) 12px 4px rgba(133, 67, 246, 0.2));\n    }\n    .chart-dropshadow-danger {\n        -webkit-filter: drop-shadow((-6px) 12px 4px rgba(244, 88, 91, 0.1));\n        filter: drop-shadow((-6px) 5px 4px rgba(244, 88, 91, 0.1));\n    }\n    .chart-dropshadow-warning {\n        -webkit-filter: drop-shadow((-6px) 5px 4px rgba(247, 183, 49, 0.1));\n        filter: drop-shadow((-6px) 5px 4px rgba(247, 183, 49, 0.1));\n    }\n    .BarChartShadow {\n        -webkit-filter: drop-shadow((-4px) 9px 4px rgba(0, 0, 0, 0.3));\n        filter: drop-shadow((-6px) 9px 4px rgba(0, 0, 0, 0.3));\n    }\n    /*----chart-drop-shadow----*/\n    .chart-dropshadow2 {\n        -webkit-filter: drop-shadow((-6px) 5px 4px rgba(0, 0, 0, 0.2));\n        filter: drop-shadow((-6px) 5px 4px rgba(0, 0, 0, 0.2));\n    }\n    .chart-dropshadow-secondary {\n        -webkit-filter: drop-shadow((-6px) 5px 4px rgba(130, 207, 242, 0.1));\n        filter: drop-shadow((-6px) 5px 4px rgba(130, 207, 242, 0.1));\n    }\n    .chart-dropshadow-success {\n        -webkit-filter: drop-shadow((-6px) 5px 4px rgba(19, 191, 166, 0.1));\n        filter: drop-shadow((-6px) 5px 4px rgba(19, 191, 166, 0.1));\n    }\n    .chart-dropshadow-info {\n        -webkit-filter: drop-shadow((-6px) 5px 4px rgba(7, 116, 248, 0.1));\n        filter: drop-shadow((-6px) 5px 4px rgba(7, 116, 248, 0.1));\n    }\n    .donutShadow {\n        -webkit-filter: drop-shadow((-5px) 4px 6px rgba(0, 0, 0, 0.5));\n        filter: drop-shadow((-1px) 0px 2px rgba(159, 120, 255, 0.5));\n    }\n    .donutShadow-yellow {\n        -webkit-filter: drop-shadow((-5px) 4px 6px rgba(0, 0, 0, 0.5));\n        filter: drop-shadow((-1px) 0px 2px rgba(251, 196, 52, 0.5));\n    }\n    .donutShadow-blue {\n        -webkit-filter: drop-shadow((-5px) 4px 6px rgba(0, 0, 0, 0.5));\n        filter: drop-shadow((-1px) 0px 2px rgba(36, 72, 135, 0.5));\n    }\n    /* ###### Chat  ###### */\n    .main-chat-contacts-wrapper {\n        border-bottom-color: $border-dark;\n    }\n    .main-chat-list {\n        .media {\n            border-color: $border-dark;\n            + .media {\n                border-top-color: $border-dark;\n            }\n            &.new {\n                background-color: $dark-theme-1;\n                .media-contact-name span:first-child {\n                    color: $text-color;\n                }\n                .media-body p {\n                    color: #8f9cc0;\n                }\n            }\n            &:hover,\n            &:focus {\n                background-color: $dark-theme-1;\n                border-top-color: $border-dark;\n                border-bottom-color: $border-dark;\n            }\n            &:hover:first-child,\n            &:focus:first-child {\n                border-top-color: transparent;\n            }\n            &.selected {\n                background-color: #252544;\n                border-top-color: $border-dark;\n                border-bottom-color: $border-dark;\n                &:first-child {\n                    border-top-color: transparent;\n                }\n                .media-contact-name span:first-child {\n                    color: $text-color;\n                }\n                .media-body p {\n                    color: #8f9cc0;\n                }\n            }\n        }\n        .main-img-user span {\n            color: $white;\n            background-color: $secondary;\n            box-shadow: 0 0 0 2px $dark-theme-1;\n        }\n        .media-body p {\n            color: #9595b5;\n        }\n        .media-contact-name span {\n            &:first-child {\n                color: $text-color;\n            }\n            &:last-child {\n                color: #9595b5;\n            }\n        }\n    }\n    .main-chat-header {\n        border-bottom-color: $border-dark;\n        .nav-link {\n            color: #9595b5;\n        }\n    }\n    .main-chat-msg-name small,\n    .main-chat-body .media-body > div:last-child {\n        color: #9595b5;\n    }\n    .main-chat-time {\n        span {\n            background: $dark-theme-1;\n        }\n        &::before ,&::after {\n            background-color: $border-dark;\n        }\n    }\n    .main-chat-footer {\n        border-top-color: $border-dark;\n        background-color: $dark-theme-1;\n        .nav-link {\n            color: #9595b5;\n        }\n        .form-control {\n            border-color: $border-dark;\n            &:hover,\n            &:focus {\n                box-shadow: none;\n            }\n        }\n    }\n    .main-content-title {\n        color: #170c6b;\n    }\n    .main-msg-wrapper {\n        background-color: $dark-body;\n    }\n    .main-chat-body .media.flex-row-reverse .main-msg-wrapper {\n        background-color: #4b4b66;\n        color: $text-color;\n    }\n    /* ###### Chat  ###### */\n    .chat-profile {\n        color: #9595b5;\n    }\n    .shared-files {\n        border-color: $border-dark;\n    }\n    .main-chat-list .media {\n        &:hover,\n        &:focus {\n            background: #252544 !important;\n        }\n    }\n    .authentication {\n        .card:hover {\n            box-shadow: 0 16px 26px 0 rgba(0, 0, 0, 0.4), 0 3px 6px 0 rgba(0, 0, 0, 0.4);\n        }\n        .form-control:focus {\n            box-shadow: none;\n        }\n        input::placeholder {\n            color: #9595b5;\n        }\n    }\n    .wrap-login100 {\n        background: $dark-theme-1;\n        box-shadow: 0 3px 9px 0 rgba(28, 28, 51, 0.15);\n    }\n    .login100-form-title {\n        color: $text-color;\n    }\n    .input100 {\n        color: #dedefd;\n        background: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .symbol-input100,\n    .wrap-input100 input::-webkit-input-placeholder {\n        color: #9595b5;\n    }\n    .construction .btn.btn-icon {\n        background: rgba(255, 255, 255, 0.08);\n        color: $white;\n    }\n    /*----- Range slider -------*/\n    .range {\n        background-color: whitesmoke;\n        -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        input[type=\"range\"] {\n            background-color: transparent;\n            &::-webkit-slider-thumb,\n            &::-moz-slider-thumb {\n                color: white;\n                background-color: #999999;\n            }\n        }\n        output {\n            color: white;\n            background-color: #999999;\n        }\n        &.range-success {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $green;\n                }\n            }\n            output {\n                background-color: $green;\n            }\n            input[type=\"range\"] {\n                outline-color: $green;\n            }\n        }\n        &.range-info {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $azure;\n                }\n            }\n            output {\n                background-color: $azure;\n            }\n            input[type=\"range\"] {\n                outline-color: $azure;\n            }\n        }\n        &.range-warning {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #ecb403;\n                }\n            }\n            output {\n                background-color: #ecb403;\n            }\n            input[type=\"range\"] {\n                outline-color: #ecb403;\n            }\n        }\n        &.range-danger {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #c21a1a;\n                }\n            }\n            output {\n                background-color: #c21a1a;\n            }\n            input[type=\"range\"] {\n                outline-color: #c21a1a;\n            }\n        }\n        background-color: whitesmoke;\n        -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        input[type=\"range\"] {\n            background-color: transparent;\n            &::-webkit-slider-thumb,\n            &::-moz-slider-thumb {\n                color: white;\n                background-color: #999999;\n            }\n        }\n        output {\n            color: white;\n            background-color: #999999;\n        }\n        &.range-success {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $green;\n                }\n            }\n            output {\n                background-color: $green;\n            }\n            input[type=\"range\"] {\n                outline-color: $green;\n            }\n        }\n        &.range-info {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $azure;\n                }\n            }\n            output {\n                background-color: $azure;\n            }\n            input[type=\"range\"] {\n                outline-color: $azure;\n            }\n        }\n        &.range-warning {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #ecb403;\n                }\n            }\n            output {\n                background-color: #ecb403;\n            }\n            input[type=\"range\"] {\n                outline-color: #ecb403;\n            }\n        }\n        &.range-danger {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #c21a1a;\n                }\n            }\n            output {\n                background-color: #c21a1a;\n            }\n            input[type=\"range\"] {\n                outline-color: #c21a1a;\n            }\n        }\n        background-color: whitesmoke;\n        -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        input[type=\"range\"] {\n            background-color: transparent;\n            &::-webkit-slider-thumb,\n            &::-moz-slider-thumb {\n                color: white;\n                background-color: #999999;\n            }\n        }\n        output {\n            color: white;\n            background-color: #999999;\n        }\n        &.range-success {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $green;\n                }\n            }\n            output {\n                background-color: $green;\n            }\n            input[type=\"range\"] {\n                outline-color: $green;\n            }\n        }\n        &.range-info {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $azure;\n                }\n            }\n            output {\n                background-color: $azure;\n            }\n            input[type=\"range\"] {\n                outline-color: $azure;\n            }\n        }\n        &.range-warning {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #ecb403;\n                }\n            }\n            output {\n                background-color: #ecb403;\n            }\n            input[type=\"range\"] {\n                outline-color: #ecb403;\n            }\n        }\n        &.range-danger {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #c21a1a;\n                }\n            }\n            output {\n                background-color: #c21a1a;\n            }\n            input[type=\"range\"] {\n                outline-color: #c21a1a;\n            }\n        }\n        background-color: whitesmoke;\n        -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        input[type=\"range\"] {\n            background-color: transparent;\n            &::-webkit-slider-thumb,\n            &::-moz-slider-thumb {\n                color: white;\n                background-color: #999999;\n            }\n        }\n        output {\n            color: white;\n            background-color: #999999;\n        }\n        &.range-success {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $green;\n                }\n            }\n            output {\n                background-color: $green;\n            }\n            input[type=\"range\"] {\n                outline-color: $green;\n            }\n        }\n        &.range-info {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $azure;\n                }\n            }\n            output {\n                background-color: $azure;\n            }\n            input[type=\"range\"] {\n                outline-color: $azure;\n            }\n        }\n        &.range-warning {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #ecb403;\n                }\n            }\n            output {\n                background-color: #ecb403;\n            }\n            input[type=\"range\"] {\n                outline-color: #ecb403;\n            }\n        }\n        &.range-danger {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #c21a1a;\n                }\n            }\n            output {\n                background-color: #c21a1a;\n            }\n            input[type=\"range\"] {\n                outline-color: #c21a1a;\n            }\n        }\n        background-color: whitesmoke;\n        -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n        input[type=\"range\"] {\n            background-color: transparent;\n            &::-webkit-slider-thumb,\n            &::-moz-slider-thumb {\n                color: white;\n                background-color: #999999;\n            }\n        }\n        output {\n            color: white;\n            background-color: #999999;\n        }\n        &.range-success {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $green;\n                }\n            }\n            output {\n                background-color: $green;\n            }\n            input[type=\"range\"] {\n                outline-color: $green;\n            }\n        }\n        &.range-info {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: $azure;\n                }\n            }\n            output {\n                background-color: $azure;\n            }\n            input[type=\"range\"] {\n                outline-color: $azure;\n            }\n        }\n        &.range-warning {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #ecb403;\n                }\n            }\n            output {\n                background-color: #ecb403;\n            }\n            input[type=\"range\"] {\n                outline-color: #ecb403;\n            }\n        }\n        &.range-danger {\n            input[type=\"range\"] {\n                &::-webkit-slider-thumb,\n                &::-moz-slider-thumb {\n                    background-color: #c21a1a;\n                }\n            }\n            output {\n                background-color: #c21a1a;\n            }\n            input[type=\"range\"] {\n                outline-color: #c21a1a;\n            }\n        }\n    }\n    /*----- Range slider -------*/\n    /*----- Range slider -------*/\n    /*----- Range slider -------*/\n    /*----- Range slider -------*/\n    .ribbon1 {\n        color: $white;\n        &:after {\n            border-left: 20px solid transparent;\n            border-right: 24px solid transparent;\n            border-top: 13px solid #f8463f;\n        }\n        span {\n            background: #f8463f;\n            &:before {\n                background: #f8463f;\n            }\n            &:after {\n                background: #c02031;\n            }\n        }\n    }\n    .ribbon span {\n        color: $white;\n        background: #79a70a;\n        background: linear-gradient(#f8463f 0%, #f8463f 100%);\n        box-shadow: 0 3px 10px -5px black;\n        &::before {\n            border-left: 3px solid #f8463f;\n            border-right: 3px solid transparent;\n            border-bottom: 3px solid transparent;\n            border-top: 3px solid #f8463f;\n        }\n        &::after {\n            border-left: 3px solid transparent;\n            border-right: 3px solid #f8463f;\n            border-bottom: 3px solid transparent;\n            border-top: 3px solid #f8463f;\n        }\n    }\n    /*--- WIZARD ELEMENTS ---*/\n    .wizard {\n        border-color: $border-dark;\n        background-color: $dark-theme-1;\n        > {\n            .steps {\n                a {\n                    color: $primary-1;\n                    &:hover,\n                    &:active {\n                        color: $primary-1;\n                    }\n                    .number,\n                    &:hover .number,\n                    &:active .number {\n                        background-color: #ededf3;\n                    }\n                }\n                .disabled a {\n                    color: #696e8d;\n                    &:hover,\n                    &:active {\n                        color: #696e8d;\n                    }\n                }\n                .current a {\n                    color: $primary-1;\n                    &:hover,\n                    &:active {\n                        color: $primary-1;\n                    }\n                    .number,\n                    &:hover .number,\n                    &:active .number {\n                        background-color: $primary-1;\n                        color: $white;\n                    }\n                }\n                .done a {\n                    color: $success;\n                    &:hover,\n                    &:active {\n                        color: $success;\n                    }\n                    .number,\n                    &:hover .number,\n                    &:active .number {\n                        background-color: $success;\n                        color: $white;\n                    }\n                }\n            }\n            .content {\n                border-top-color: $border-dark;\n                border-bottom-color: $border-dark;\n                > {\n                    .title {\n                        color: $default-color;\n                    }\n                    .body input.parsley-error {\n                        border-color: #ff5c77;\n                    }\n                }\n            }\n            .actions {\n                > ul > li:last-child a {\n                    background-color: $success;\n                }\n                a {\n                    background-color: $primary-1;\n                    color: $white;\n                    &:hover,\n                    &:active {\n                        background-color: $primary-1;\n                        color: $white;\n                    }\n                }\n                .disabled a {\n                    background-color: #e8ebf2;\n                    color: $default-color;\n                    &:hover,\n                    &:active {\n                        background-color: #e8ebf2;\n                        color: $default-color;\n                    }\n                }\n            }\n        }\n    }\n    @media (min-width: 576px) {\n        .wizard.vertical > .content {\n            border-left-color: $border-dark;\n            border-right-color: $border-dark;\n        }\n    }\n    @media (min-width: 576px) {\n        .wizard.vertical > .actions {\n            border-left-color: $border-dark;\n            border-right-color: $border-dark;\n        }\n    }\n    /****** EQUAL COLUMN WIDTH STEP INDICATOR *****/\n    /***** CUSTOM STYLES *****/\n    .wizard-style-1 > .steps > ul {\n        a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                color: #696e8d;\n                background-color: #f3f7fd;\n            }\n        }\n        .current a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                background-color: $primary-1;\n                color: $white;\n            }\n        }\n        .done a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                background-color: #643ab0;\n                color: $white;\n            }\n        }\n    }\n    .wizard-style-2 > .steps > ul {\n        a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                border: 2px solid #f3f7fd;\n                color: #696e8d;\n                background-color: $dark-theme-1;\n            }\n        }\n        .current a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                border-color: $primary-1;\n                color: $primary-1;\n            }\n        }\n        .done a {\n            .number,\n            &:hover .number,\n            &:active .number {\n                border-color: #8c3feb;\n                color: #8c3feb;\n            }\n        }\n    }\n    /*--- WIZARD ELEMENTS ---*/\n    .parsley-required {\n        color: #ff5c77;\n    }\n    .wizard-card .moving-tab {\n        background-color: $primary-1 !important;\n    }\n    .form-group label.control-label {\n        color: $primary-1;\n    }\n    .wizard-card.form-group .form-control {\n        background-image: linear-gradient(#c4c4c4, #c4c4c4, linear-gradient(#e1e1e2, #e1e1e2));\n    }\n    .wizard-container .wizard-navigation {\n        background: #ebeff8;\n    }\n    .wizard-card .choice {\n        &:hover .icon,\n        &.active .icon {\n            border-color: $primary-1 !important;\n            color: $primary-1 !important;\n        }\n    }\n    .widgets-cards .wrp.icon-circle i {\n        color: $white;\n    }\n    /* ######## UTILITIES-STYLES ######## */\n    .bg-secondary {\n        background: $secondary !important;\n    }\n    a.bg-secondary {\n        &:hover,\n        &:focus {\n            background-color: $secondary !important;\n        }\n    }\n    button.bg-secondary {\n        &:hover,\n        &:focus {\n            background-color: $secondary !important;\n        }\n    }\n    .bg-success {\n        background: $success !important;\n    }\n    a.bg-success {\n        &:hover,\n        &:focus {\n            background-color: #15bf42 !important;\n        }\n    }\n    button.bg-success {\n        &:hover,\n        &:focus {\n            background-color: #15bf42 !important;\n        }\n    }\n    .bg-info {\n        background: $info !important;\n    }\n    a.bg-info {\n        &:hover,\n        &:focus {\n            background-color: #1eb0e2 !important;\n        }\n    }\n    button.bg-info {\n        &:hover,\n        &:focus {\n            background-color: #1eb0e2 !important;\n        }\n    }\n    .bg-warning {\n        background: $warning !important;\n    }\n    a.bg-warning {\n        &:hover,\n        &:focus {\n            background-color: #e0a325 !important;\n        }\n    }\n    button.bg-warning {\n        &:hover,\n        &:focus {\n            background-color: #e0a325 !important;\n        }\n    }\n    .bg-danger {\n        background: $danger !important;\n    }\n    a.bg-danger {\n        &:hover,\n        &:focus {\n            background-color: #de223d !important;\n        }\n    }\n    button.bg-danger {\n        &:hover,\n        &:focus {\n            background-color: #de223d !important;\n        }\n    }\n    .bg-light {\n        background-color: $dark-body !important;\n    }\n    a.bg-light {\n        &:hover,\n        &:focus {\n            background-color: $dark-theme-1 !important;\n        }\n    }\n    button.bg-light {\n        &:hover,\n        &:focus {\n            background-color: $dark-theme-1 !important;\n        }\n    }\n    .bg-dark {\n        background-color: $dark !important;\n    }\n    a.bg-dark {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    button.bg-dark {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    .bg-facebook {\n        background: #2b4170 !important;\n    }\n    /*--- gradient-backgrounds --*/\n    .bg-secondary-gradient {\n        background: linear-gradient(to bottom right, #82cff2 0%, #28b7f9 100%) !important;\n    }\n    a.bg-secondary-gradient {\n        &:hover,\n        &:focus {\n            background-color: $secondary !important;\n        }\n    }\n    button.bg-secondary-gradient {\n        &:hover,\n        &:focus {\n            background-color: $secondary !important;\n        }\n    }\n    .bg-success-gradient {\n        background: linear-gradient(to bottom right, #1ea38f 0%, #5cf9e2 100%) !important;\n    }\n    a.bg-success-gradient {\n        &:hover,\n        &:focus {\n            background-color: #448700 !important;\n        }\n    }\n    button.bg-success-gradient {\n        &:hover,\n        &:focus {\n            background-color: #448700 !important;\n        }\n    }\n    .bg-info-gradient {\n        background: linear-gradient(to bottom right, #1e63c3 0%, #00f2fe 100%) !important;\n    }\n    a.bg-info-gradient {\n        &:hover,\n        &:focus {\n            background-color: #1594ef !important;\n        }\n    }\n    button.bg-info-gradient {\n        &:hover,\n        &:focus {\n            background-color: #1594ef !important;\n        }\n    }\n    .bg-warning-gradient {\n        background: linear-gradient(to bottom right, #f66b4e 0%, #fbc434 100%) !important;\n    }\n    a.bg-warning-gradient {\n        &:hover,\n        &:focus {\n            background-color: $yellow !important;\n        }\n    }\n    button.bg-warning-gradient {\n        &:hover,\n        &:focus {\n            background-color: $yellow !important;\n        }\n    }\n    .bg-danger-gradient {\n        background-image: linear-gradient(to bottom right, #b51b35 0%, #fd4a68 100%) !important;\n    }\n    a.bg-danger-gradient {\n        &:hover,\n        &:focus {\n            background-color: #a11918 !important;\n        }\n    }\n    button.bg-danger-gradient {\n        &:hover,\n        &:focus {\n            background-color: #a11918 !important;\n        }\n    }\n    .bg-light-gradient {\n        background-color: #f8f9fa !important;\n    }\n    a.bg-light-gradient {\n        &:hover,\n        &:focus {\n            background-color: #dae0e5 !important;\n        }\n    }\n    button.bg-light-gradient {\n        &:hover,\n        &:focus {\n            background-color: #dae0e5 !important;\n        }\n    }\n    .bg-dark-gradient {\n        background-color: $dark !important;\n    }\n    a.bg-dark-gradient {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    button.bg-dark-gradient {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    .bg-facebook-gradient {\n        background: linear-gradient(to bottom right, #3b5998, #2b4170) !important;\n    }\n    .bg-white {\n        background-color: $dark-body !important;\n    }\n    .bg-transparent {\n        background-color: transparent !important;\n    }\n    .bg1 {\n        background: linear-gradient(to right bottom, #163b7c 0%, #548beb 100%);\n    }\n    .bg2 {\n        background: linear-gradient(to bottom right, #00f2fe 0%, #1e63c3 100%) !important;\n    }\n    .bg3 {\n        background: linear-gradient(to bottom right, #f53e31, #dd4b39);\n    }\n    /*------ Background colors -------*/\n    .bg-purple {\n        background: $purple !important;\n        color: $white !important;\n    }\n    a.bg-purple {\n        &:hover,\n        &:focus {\n            background-color: #8c31e4 !important;\n        }\n    }\n    button.bg-purple {\n        &:hover,\n        &:focus {\n            background-color: #8c31e4 !important;\n        }\n    }\n    .bg-blue-lightest {\n        background-color: #edf2fa !important;\n    }\n    a.bg-blue-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c5d5ef !important;\n        }\n    }\n    button.bg-blue-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c5d5ef !important;\n        }\n    }\n    .bg-blue-lighter {\n        background-color: #c8d9f1 !important;\n    }\n    a.bg-blue-lighter {\n        &:hover,\n        &:focus {\n            background-color: #9fbde7 !important;\n        }\n    }\n    button.bg-blue-lighter {\n        &:hover,\n        &:focus {\n            background-color: #9fbde7 !important;\n        }\n    }\n    .bg-blue-light {\n        background-color: #7ea5dd !important;\n    }\n    a.bg-blue-light {\n        &:hover,\n        &:focus {\n            background-color: #5689d2 !important;\n        }\n    }\n    button.bg-blue-light {\n        &:hover,\n        &:focus {\n            background-color: #5689d2 !important;\n        }\n    }\n    .bg-blue-dark {\n        background-color: #3866a6 !important;\n    }\n    a.bg-blue-dark {\n        &:hover,\n        &:focus {\n            background-color: #2b4f80 !important;\n        }\n    }\n    button.bg-blue-dark {\n        &:hover,\n        &:focus {\n            background-color: #2b4f80 !important;\n        }\n    }\n    .bg-blue-darker {\n        background-color: #1c3353 !important;\n    }\n    a.bg-blue-darker {\n        &:hover,\n        &:focus {\n            background-color: #0f1c2d !important;\n        }\n    }\n    button.bg-blue-darker {\n        &:hover,\n        &:focus {\n            background-color: #0f1c2d !important;\n        }\n    }\n    .bg-blue-darkest {\n        background-color: #0e1929 !important;\n    }\n    a.bg-blue-darkest {\n        &:hover,\n        &:focus {\n            background-color: #010203 !important;\n        }\n    }\n    button.bg-blue-darkest {\n        &:hover,\n        &:focus {\n            background-color: #010203 !important;\n        }\n    }\n    .bg-purssianblue {\n        background-color: #362f71;\n    }\n    a.bg-purssianblue-lightest {\n        &:hover,\n        &:focus {\n            background-color: #3f3688 !important;\n        }\n    }\n    button.bg-purssianblue-lightest {\n        &:hover,\n        &:focus {\n            background-color: #3f3688 !important;\n        }\n    }\n    .bg-indigo-lightest {\n        background-color: #f0f1fa !important;\n    }\n    a.bg-indigo-lightest {\n        &:hover,\n        &:focus {\n            background-color: #cacded !important;\n        }\n    }\n    button.bg-indigo-lightest {\n        &:hover,\n        &:focus {\n            background-color: #cacded !important;\n        }\n    }\n    .bg-indigo-lighter {\n        background-color: #d1d5f0 !important;\n    }\n    a.bg-indigo-lighter {\n        &:hover,\n        &:focus {\n            background-color: #abb2e3 !important;\n        }\n    }\n    button.bg-indigo-lighter {\n        &:hover,\n        &:focus {\n            background-color: #abb2e3 !important;\n        }\n    }\n    .bg-indigo-light {\n        background-color: #939edc !important;\n    }\n    a.bg-indigo-light {\n        &:hover,\n        &:focus {\n            background-color: #6c7bd0 !important;\n        }\n    }\n    button.bg-indigo-light {\n        &:hover,\n        &:focus {\n            background-color: #6c7bd0 !important;\n        }\n    }\n    .bg-indigo-dark {\n        background-color: #515da4 !important;\n    }\n    a.bg-indigo-dark {\n        &:hover,\n        &:focus {\n            background-color: #404a82 !important;\n        }\n    }\n    button.bg-indigo-dark {\n        &:hover,\n        &:focus {\n            background-color: #404a82 !important;\n        }\n    }\n    .bg-indigo-darker {\n        background-color: #282e52 !important;\n    }\n    a.bg-indigo-darker {\n        &:hover,\n        &:focus {\n            background-color: #171b30 !important;\n        }\n    }\n    button.bg-indigo-darker {\n        &:hover,\n        &:focus {\n            background-color: #171b30 !important;\n        }\n    }\n    .bg-indigo-darkest {\n        background-color: #141729 !important;\n    }\n    a.bg-indigo-darkest {\n        &:hover,\n        &:focus {\n            background-color: #030407 !important;\n        }\n    }\n    button.bg-indigo-darkest {\n        &:hover,\n        &:focus {\n            background-color: #030407 !important;\n        }\n    }\n    .bg-purple-lightest {\n        background-color: #f6effd !important;\n    }\n    a.bg-purple-lightest {\n        &:hover,\n        &:focus {\n            background-color: #ddc2f7 !important;\n        }\n    }\n    button.bg-purple-lightest {\n        &:hover,\n        &:focus {\n            background-color: #ddc2f7 !important;\n        }\n    }\n    .bg-purple-lighter {\n        background-color: #e4cff9 !important;\n    }\n    a.bg-purple-lighter {\n        &:hover,\n        &:focus {\n            background-color: #cba2f3 !important;\n        }\n    }\n    button.bg-purple-lighter {\n        &:hover,\n        &:focus {\n            background-color: #cba2f3 !important;\n        }\n    }\n    .bg-purple-light {\n        background-color: #c08ef0 !important;\n    }\n    a.bg-purple-light {\n        &:hover,\n        &:focus {\n            background-color: #a761ea !important;\n        }\n    }\n    button.bg-purple-light {\n        &:hover,\n        &:focus {\n            background-color: #a761ea !important;\n        }\n    }\n    .bg-purple-dark {\n        background-color: #844bbb !important;\n    }\n    a.bg-purple-dark {\n        &:hover,\n        &:focus {\n            background-color: #6a3a99 !important;\n        }\n    }\n    button.bg-purple-dark {\n        &:hover,\n        &:focus {\n            background-color: #6a3a99 !important;\n        }\n    }\n    .bg-purple-darker {\n        background-color: #42265e !important;\n    }\n    a.bg-purple-darker {\n        &:hover,\n        &:focus {\n            background-color: #29173a !important;\n        }\n    }\n    button.bg-purple-darker {\n        &:hover,\n        &:focus {\n            background-color: #29173a !important;\n        }\n    }\n    .bg-purple-darkest {\n        background-color: #21132f !important;\n    }\n    a.bg-purple-darkest {\n        &:hover,\n        &:focus {\n            background-color: #08040b !important;\n        }\n    }\n    button.bg-purple-darkest {\n        &:hover,\n        &:focus {\n            background-color: #08040b !important;\n        }\n    }\n    .bg-pink-lightest {\n        background-color: #fef0f5 !important;\n    }\n    a.bg-pink-lightest {\n        &:hover,\n        &:focus {\n            background-color: #fbc0d5 !important;\n        }\n    }\n    button.bg-pink-lightest {\n        &:hover,\n        &:focus {\n            background-color: #fbc0d5 !important;\n        }\n    }\n    .bg-pink-lighter {\n        background-color: #fcd3e1 !important;\n    }\n    a.bg-pink-lighter {\n        &:hover,\n        &:focus {\n            background-color: #f9a3c0 !important;\n        }\n    }\n    button.bg-pink-lighter {\n        &:hover,\n        &:focus {\n            background-color: #f9a3c0 !important;\n        }\n    }\n    .bg-pink-light {\n        background-color: #f999b9 !important;\n    }\n    a.bg-pink-light {\n        &:hover,\n        &:focus {\n            background-color: #f66998 !important;\n        }\n    }\n    button.bg-pink-light {\n        &:hover,\n        &:focus {\n            background-color: #f66998 !important;\n        }\n    }\n    .bg-pink-dark {\n        background-color: #c5577c !important;\n    }\n    a.bg-pink-dark {\n        &:hover,\n        &:focus {\n            background-color: #ad3c62 !important;\n        }\n    }\n    button.bg-pink-dark {\n        &:hover,\n        &:focus {\n            background-color: #ad3c62 !important;\n        }\n    }\n    .bg-pink-darker {\n        background-color: #622c3e !important;\n    }\n    a.bg-pink-darker {\n        &:hover,\n        &:focus {\n            background-color: #3f1c28 !important;\n        }\n    }\n    button.bg-pink-darker {\n        &:hover,\n        &:focus {\n            background-color: #3f1c28 !important;\n        }\n    }\n    .bg-pink-darkest {\n        background-color: #31161f !important;\n    }\n    a.bg-pink-darkest {\n        &:hover,\n        &:focus {\n            background-color: #0e0609 !important;\n        }\n    }\n    button.bg-pink-darkest {\n        &:hover,\n        &:focus {\n            background-color: #0e0609 !important;\n        }\n    }\n    .bg-red-lightest {\n        background-color: #fae9e9 !important;\n    }\n    a.bg-red-lightest {\n        &:hover,\n        &:focus {\n            background-color: #f1bfbf !important;\n        }\n    }\n    button.bg-red-lightest {\n        &:hover,\n        &:focus {\n            background-color: #f1bfbf !important;\n        }\n    }\n    .bg-red-lighter {\n        background-color: #f0bcbc !important;\n    }\n    a.bg-red-lighter {\n        &:hover,\n        &:focus {\n            background-color: #e79292 !important;\n        }\n    }\n    button.bg-red-lighter {\n        &:hover,\n        &:focus {\n            background-color: #e79292 !important;\n        }\n    }\n    .bg-red-light {\n        background-color: #dc6362 !important;\n    }\n    a.bg-red-light {\n        &:hover,\n        &:focus {\n            background-color: #d33a38 !important;\n        }\n    }\n    button.bg-red-light {\n        &:hover,\n        &:focus {\n            background-color: #d33a38 !important;\n        }\n    }\n    .bg-red-dark {\n        background-color: #a41a19 !important;\n    }\n    a.bg-red-dark {\n        &:hover,\n        &:focus {\n            background-color: #781312 !important;\n        }\n    }\n    button.bg-red-dark {\n        &:hover,\n        &:focus {\n            background-color: #781312 !important;\n        }\n    }\n    .bg-red-darker {\n        background-color: #520d0c !important;\n    }\n    a.bg-red-darker {\n        &:hover,\n        &:focus {\n            background-color: #260605 !important;\n        }\n    }\n    button.bg-red-darker {\n        &:hover,\n        &:focus {\n            background-color: #260605 !important;\n        }\n    }\n    .bg-red-darkest {\n        background-color: #290606 !important;\n    }\n    a.bg-red-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-red-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-orange-lightest {\n        background-color: $dark-theme-1 !important;\n    }\n    a.bg-orange-lightest {\n        &:hover,\n        &:focus {\n            background-color: peachpuff !important;\n        }\n    }\n    button.bg-orange-lightest {\n        &:hover,\n        &:focus {\n            background-color: peachpuff !important;\n        }\n    }\n    .bg-orange-lighter {\n        background-color: #fee0c7 !important;\n    }\n    a.bg-orange-lighter {\n        &:hover,\n        &:focus {\n            background-color: #fdc495 !important;\n        }\n    }\n    button.bg-orange-lighter {\n        &:hover,\n        &:focus {\n            background-color: #fdc495 !important;\n        }\n    }\n    .bg-orange-light {\n        background-color: #feb67c !important;\n    }\n    a.bg-orange-light {\n        &:hover,\n        &:focus {\n            background-color: #fe9a49 !important;\n        }\n    }\n    button.bg-orange-light {\n        &:hover,\n        &:focus {\n            background-color: #fe9a49 !important;\n        }\n    }\n    .bg-orange-dark {\n        background-color: #ca7836 !important;\n    }\n    a.bg-orange-dark {\n        &:hover,\n        &:focus {\n            background-color: #a2602b !important;\n        }\n    }\n    button.bg-orange-dark {\n        &:hover,\n        &:focus {\n            background-color: #a2602b !important;\n        }\n    }\n    .bg-orange-darker {\n        background-color: #653c1b !important;\n    }\n    a.bg-orange-darker {\n        &:hover,\n        &:focus {\n            background-color: #3d2410 !important;\n        }\n    }\n    button.bg-orange-darker {\n        &:hover,\n        &:focus {\n            background-color: #3d2410 !important;\n        }\n    }\n    .bg-orange-darkest {\n        background-color: #331e0e !important;\n    }\n    a.bg-orange-darkest {\n        &:hover,\n        &:focus {\n            background-color: #0b0603 !important;\n        }\n    }\n    button.bg-orange-darkest {\n        &:hover,\n        &:focus {\n            background-color: #0b0603 !important;\n        }\n    }\n    .bg-yellow-lightest {\n        background-color: #fef9e7 !important;\n    }\n    a.bg-yellow-lightest {\n        &:hover,\n        &:focus {\n            background-color: #fcedb6 !important;\n        }\n    }\n    button.bg-yellow-lightest {\n        &:hover,\n        &:focus {\n            background-color: #fcedb6 !important;\n        }\n    }\n    .bg-yellow-lighter {\n        background-color: #fbedb7 !important;\n    }\n    a.bg-yellow-lighter {\n        &:hover,\n        &:focus {\n            background-color: #f8e187 !important;\n        }\n    }\n    button.bg-yellow-lighter {\n        &:hover,\n        &:focus {\n            background-color: #f8e187 !important;\n        }\n    }\n    .bg-yellow-light {\n        background-color: #f5d657 !important;\n    }\n    a.bg-yellow-light {\n        &:hover,\n        &:focus {\n            background-color: #f2ca27 !important;\n        }\n    }\n    button.bg-yellow-light {\n        &:hover,\n        &:focus {\n            background-color: #f2ca27 !important;\n        }\n    }\n    .bg-yellow-dark {\n        background-color: #c19d0c !important;\n    }\n    a.bg-yellow-dark {\n        &:hover,\n        &:focus {\n            background-color: #917609 !important;\n        }\n    }\n    button.bg-yellow-dark {\n        &:hover,\n        &:focus {\n            background-color: #917609 !important;\n        }\n    }\n    .bg-yellow-darker {\n        background-color: #604e06 !important;\n    }\n    a.bg-yellow-darker {\n        &:hover,\n        &:focus {\n            background-color: #302703 !important;\n        }\n    }\n    button.bg-yellow-darker {\n        &:hover,\n        &:focus {\n            background-color: #302703 !important;\n        }\n    }\n    .bg-yellow-darkest {\n        background-color: #302703 !important;\n    }\n    a.bg-yellow-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-yellow-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-green-lightest {\n        background-color: #eff8e6 !important;\n    }\n    a.bg-green-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d6edbe !important;\n        }\n    }\n    button.bg-green-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d6edbe !important;\n        }\n    }\n    .bg-green-lighter {\n        background-color: #cfeab3 !important;\n    }\n    a.bg-green-lighter {\n        &:hover,\n        &:focus {\n            background-color: #b6df8b !important;\n        }\n    }\n    button.bg-green-lighter {\n        &:hover,\n        &:focus {\n            background-color: #b6df8b !important;\n        }\n    }\n    .bg-green-light {\n        background-color: #8ecf4d !important;\n    }\n    a.bg-green-light {\n        &:hover,\n        &:focus {\n            background-color: #75b831 !important;\n        }\n    }\n    button.bg-green-light {\n        &:hover,\n        &:focus {\n            background-color: #75b831 !important;\n        }\n    }\n    .bg-green-dark {\n        background-color: #4b9500 !important;\n    }\n    a.bg-green-dark {\n        &:hover,\n        &:focus {\n            background-color: #316200 !important;\n        }\n    }\n    button.bg-green-dark {\n        &:hover,\n        &:focus {\n            background-color: #316200 !important;\n        }\n    }\n    .bg-green-darker {\n        background-color: #264a00 !important;\n    }\n    a.bg-green-darker {\n        &:hover,\n        &:focus {\n            background-color: #0c1700 !important;\n        }\n    }\n    button.bg-green-darker {\n        &:hover,\n        &:focus {\n            background-color: #0c1700 !important;\n        }\n    }\n    .bg-green-darkest {\n        background-color: #132500 !important;\n    }\n    a.bg-green-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-green-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-teal-lightest {\n        background-color: #eafaf8 !important;\n    }\n    a.bg-teal-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c1f0ea !important;\n        }\n    }\n    button.bg-teal-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c1f0ea !important;\n        }\n    }\n    .bg-teal-lighter {\n        background-color: #bfefea !important;\n    }\n    a.bg-teal-lighter {\n        &:hover,\n        &:focus {\n            background-color: #96e5dd !important;\n        }\n    }\n    button.bg-teal-lighter {\n        &:hover,\n        &:focus {\n            background-color: #96e5dd !important;\n        }\n    }\n    .bg-teal-light {\n        background-color: #6bdbcf !important;\n    }\n    a.bg-teal-light {\n        &:hover,\n        &:focus {\n            background-color: #42d1c2 !important;\n        }\n    }\n    button.bg-teal-light {\n        &:hover,\n        &:focus {\n            background-color: #42d1c2 !important;\n        }\n    }\n    .bg-teal-dark {\n        background-color: #22a295 !important;\n    }\n    a.bg-teal-dark {\n        &:hover,\n        &:focus {\n            background-color: #19786e !important;\n        }\n    }\n    button.bg-teal-dark {\n        &:hover,\n        &:focus {\n            background-color: #19786e !important;\n        }\n    }\n    .bg-teal-darker {\n        background-color: #11514a !important;\n    }\n    a.bg-teal-darker {\n        &:hover,\n        &:focus {\n            background-color: #082723 !important;\n        }\n    }\n    button.bg-teal-darker {\n        &:hover,\n        &:focus {\n            background-color: #082723 !important;\n        }\n    }\n    .bg-teal-darkest {\n        background-color: #092925 !important;\n    }\n    a.bg-teal-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-teal-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-cyan-lightest {\n        background-color: #e8f6f8 !important;\n    }\n    a.bg-cyan-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c1e7ec !important;\n        }\n    }\n    button.bg-cyan-lightest {\n        &:hover,\n        &:focus {\n            background-color: #c1e7ec !important;\n        }\n    }\n    .bg-cyan-lighter {\n        background-color: #b9e3ea !important;\n    }\n    a.bg-cyan-lighter {\n        &:hover,\n        &:focus {\n            background-color: #92d3de !important;\n        }\n    }\n    button.bg-cyan-lighter {\n        &:hover,\n        &:focus {\n            background-color: #92d3de !important;\n        }\n    }\n    .bg-cyan-light {\n        background-color: #5dbecd !important;\n    }\n    a.bg-cyan-light {\n        &:hover,\n        &:focus {\n            background-color: #3aabbd !important;\n        }\n    }\n    button.bg-cyan-light {\n        &:hover,\n        &:focus {\n            background-color: #3aabbd !important;\n        }\n    }\n    .bg-cyan-dark {\n        background-color: #128293 !important;\n    }\n    a.bg-cyan-dark {\n        &:hover,\n        &:focus {\n            background-color: #0c5a66 !important;\n        }\n    }\n    button.bg-cyan-dark {\n        &:hover,\n        &:focus {\n            background-color: #0c5a66 !important;\n        }\n    }\n    .bg-cyan-darker {\n        background-color: #09414a !important;\n    }\n    a.bg-cyan-darker {\n        &:hover,\n        &:focus {\n            background-color: #03191d !important;\n        }\n    }\n    button.bg-cyan-darker {\n        &:hover,\n        &:focus {\n            background-color: #03191d !important;\n        }\n    }\n    .bg-cyan-darkest {\n        background-color: #052025 !important;\n    }\n    a.bg-cyan-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-cyan-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-white-lightest {\n        background-color: white !important;\n    }\n    a.bg-white-lightest {\n        &:hover,\n        &:focus {\n            background-color: $dark-theme-1 !important;\n        }\n    }\n    button.bg-white-lightest {\n        &:hover,\n        &:focus {\n            background-color: $dark-theme-1 !important;\n        }\n    }\n    .bg-white-lighter {\n        background-color: white !important;\n    }\n    a.bg-white-lighter {\n        &:hover,\n        &:focus {\n            background-color: $dark-theme-1 !important;\n        }\n    }\n    button.bg-white-lighter {\n        &:hover,\n        &:focus {\n            background-color: $dark-theme-1 !important;\n        }\n    }\n    .bg-white-light {\n        background-color: white !important;\n    }\n    a.bg-white-light {\n        &:hover,\n        &:focus {\n            background-color: $dark-theme-1 !important;\n        }\n    }\n    button.bg-white-light {\n        &:hover,\n        &:focus {\n            background-color: $dark-theme-1 !important;\n        }\n    }\n    .bg-white-dark {\n        background-color: #cccccc !important;\n    }\n    a.bg-white-dark {\n        &:hover,\n        &:focus {\n            background-color: #b3b2b2 !important;\n        }\n    }\n    button.bg-white-dark {\n        &:hover,\n        &:focus {\n            background-color: #b3b2b2 !important;\n        }\n    }\n    .bg-white-darker {\n        background-color: #666666 !important;\n    }\n    a.bg-white-darker {\n        &:hover,\n        &:focus {\n            background-color: #4d4c4c !important;\n        }\n    }\n    button.bg-white-darker {\n        &:hover,\n        &:focus {\n            background-color: #4d4c4c !important;\n        }\n    }\n    .bg-white-darkest {\n        background-color: #333333 !important;\n    }\n    a.bg-white-darkest {\n        &:hover,\n        &:focus {\n            background-color: #1a1919 !important;\n        }\n    }\n    button.bg-white-darkest {\n        &:hover,\n        &:focus {\n            background-color: #1a1919 !important;\n        }\n    }\n    .bg-gray-lightest {\n        background-color: #f3f4f5 !important;\n    }\n    a.bg-gray-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d7dbde !important;\n        }\n    }\n    button.bg-gray-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d7dbde !important;\n        }\n    }\n    .bg-gray-lighter {\n        background-color: #dbdde0 !important;\n    }\n    a.bg-gray-lighter {\n        &:hover,\n        &:focus {\n            background-color: #c0c3c8 !important;\n        }\n    }\n    button.bg-gray-lighter {\n        &:hover,\n        &:focus {\n            background-color: #c0c3c8 !important;\n        }\n    }\n    .bg-gray-light {\n        background-color: #aab0b6 !important;\n    }\n    a.bg-gray-light {\n        &:hover,\n        &:focus {\n            background-color: #8f979e !important;\n        }\n    }\n    button.bg-gray-light {\n        &:hover,\n        &:focus {\n            background-color: #8f979e !important;\n        }\n    }\n    .bg-gray-dark {\n        background-color: #6b7278 !important;\n        background: $dark !important;\n    }\n    a.bg-gray-dark {\n        &:hover,\n        &:focus {\n            background-color: #53585d !important;\n        }\n    }\n    button.bg-gray-dark {\n        &:hover,\n        &:focus {\n            background-color: #53585d !important;\n        }\n    }\n    .bg-gray-darker {\n        background-color: #36393c !important;\n    }\n    a.bg-gray-darker {\n        &:hover,\n        &:focus {\n            background-color: #1e2021 !important;\n        }\n    }\n    button.bg-gray-darker {\n        &:hover,\n        &:focus {\n            background-color: #1e2021 !important;\n        }\n    }\n    .bg-gray-darkest {\n        background-color: #1b1c1e !important;\n    }\n    a.bg-gray-darkest {\n        &:hover,\n        &:focus {\n            background-color: #030303 !important;\n        }\n    }\n    button.bg-gray-darkest {\n        &:hover,\n        &:focus {\n            background-color: #030303 !important;\n        }\n    }\n    .bg-gray-dark-lightest {\n        background-color: #ebebec !important;\n    }\n    a.bg-gray-dark-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d1d1d3 !important;\n        }\n    }\n    button.bg-gray-dark-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d1d1d3 !important;\n        }\n    }\n    .bg-gray-dark-lighter {\n        background-color: #c2c4c6 !important;\n    }\n    a.bg-gray-dark-lighter {\n        &:hover,\n        &:focus {\n            background-color: #a8abad !important;\n        }\n    }\n    button.bg-gray-dark-lighter {\n        &:hover,\n        &:focus {\n            background-color: #a8abad !important;\n        }\n    }\n    .bg-gray-dark-light {\n        background-color: #717579 !important;\n    }\n    a.bg-gray-dark-light {\n        &:hover,\n        &:focus {\n            background-color: #585c5f !important;\n        }\n    }\n    button.bg-gray-dark-light {\n        &:hover,\n        &:focus {\n            background-color: #585c5f !important;\n        }\n    }\n    .bg-gray-dark-dark {\n        background-color: #2a2e33 !important;\n    }\n    a.bg-gray-dark-dark {\n        &:hover,\n        &:focus {\n            background-color: #131517 !important;\n        }\n    }\n    button.bg-gray-dark-dark {\n        &:hover,\n        &:focus {\n            background-color: #131517 !important;\n        }\n    }\n    .bg-gray-dark-darker {\n        background-color: #15171a !important;\n    }\n    a.bg-gray-dark-darker {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-gray-dark-darker {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-gray-dark-darkest {\n        background-color: #0a0c0d !important;\n    }\n    a.bg-gray-dark-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    button.bg-gray-dark-darkest {\n        &:hover,\n        &:focus {\n            background-color: black !important;\n        }\n    }\n    .bg-azure-lightest {\n        background-color: #ecf7fe !important;\n    }\n    a.bg-azure-lightest {\n        &:hover,\n        &:focus {\n            background-color: #bce3fb !important;\n        }\n    }\n    button.bg-azure-lightest {\n        &:hover,\n        &:focus {\n            background-color: #bce3fb !important;\n        }\n    }\n    .bg-azure-lighter {\n        background-color: #c7e6fb !important;\n    }\n    a.bg-azure-lighter {\n        &:hover,\n        &:focus {\n            background-color: #97d1f8 !important;\n        }\n    }\n    button.bg-azure-lighter {\n        &:hover,\n        &:focus {\n            background-color: #97d1f8 !important;\n        }\n    }\n    .bg-azure-light {\n        background-color: #7dc4f6 !important;\n    }\n    a.bg-azure-light {\n        &:hover,\n        &:focus {\n            background-color: #4daef3 !important;\n        }\n    }\n    button.bg-azure-light {\n        &:hover,\n        &:focus {\n            background-color: #4daef3 !important;\n        }\n    }\n    .bg-azure-dark {\n        background-color: #3788c2 !important;\n    }\n    a.bg-azure-dark {\n        &:hover,\n        &:focus {\n            background-color: #2c6c9a !important;\n        }\n    }\n    button.bg-azure-dark {\n        &:hover,\n        &:focus {\n            background-color: #2c6c9a !important;\n        }\n    }\n    .bg-azure-darker {\n        background-color: #1c4461 !important;\n    }\n    a.bg-azure-darker {\n        &:hover,\n        &:focus {\n            background-color: #112839 !important;\n        }\n    }\n    button.bg-azure-darker {\n        &:hover,\n        &:focus {\n            background-color: #112839 !important;\n        }\n    }\n    .bg-azure-darkest {\n        background-color: #0e2230 !important;\n    }\n    a.bg-azure-darkest {\n        &:hover,\n        &:focus {\n            background-color: #020609 !important;\n        }\n    }\n    button.bg-azure-darkest {\n        &:hover,\n        &:focus {\n            background-color: #020609 !important;\n        }\n    }\n    .bg-lime-lightest {\n        background-color: #f2fbeb !important;\n    }\n    a.bg-lime-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d6f3c1 !important;\n        }\n    }\n    button.bg-lime-lightest {\n        &:hover,\n        &:focus {\n            background-color: #d6f3c1 !important;\n        }\n    }\n    .bg-lime-lighter {\n        background-color: #d7f2c2 !important;\n    }\n    a.bg-lime-lighter {\n        &:hover,\n        &:focus {\n            background-color: #bbe998 !important;\n        }\n    }\n    button.bg-lime-lighter {\n        &:hover,\n        &:focus {\n            background-color: #bbe998 !important;\n        }\n    }\n    .bg-lime-light {\n        background-color: #a3e072 !important;\n    }\n    a.bg-lime-light {\n        &:hover,\n        &:focus {\n            background-color: #88d748 !important;\n        }\n    }\n    button.bg-lime-light {\n        &:hover,\n        &:focus {\n            background-color: #88d748 !important;\n        }\n    }\n    .bg-lime-dark {\n        background-color: #62a82a !important;\n    }\n    a.bg-lime-dark {\n        &:hover,\n        &:focus {\n            background-color: #4a7f20 !important;\n        }\n    }\n    button.bg-lime-dark {\n        &:hover,\n        &:focus {\n            background-color: #4a7f20 !important;\n        }\n    }\n    .bg-lime-darker {\n        background-color: #315415 !important;\n    }\n    a.bg-lime-darker {\n        &:hover,\n        &:focus {\n            background-color: #192b0b !important;\n        }\n    }\n    button.bg-lime-darker {\n        &:hover,\n        &:focus {\n            background-color: #192b0b !important;\n        }\n    }\n    .bg-lime-darkest {\n        background-color: #192a0b !important;\n    }\n    a.bg-lime-darkest {\n        &:hover,\n        &:focus {\n            background-color: #010200 !important;\n        }\n    }\n    button.bg-lime-darkest {\n        &:hover,\n        &:focus {\n            background-color: #010200 !important;\n        }\n    }\n    .bg-blue-1 {\n        background-color: #0061da;\n        color: $white !important;\n    }\n    .bg-blue {\n        background: $blue;\n        color: $white !important;\n    }\n    a.bg-blue {\n        &:hover,\n        &:focus {\n            background-color: #4032f1 !important;\n        }\n    }\n    button.bg-blue {\n        &:hover,\n        &:focus {\n            background-color: #4032f1 !important;\n        }\n    }\n    .bg-indigo {\n        background: $indigo;\n        color: $white !important;\n    }\n    a.bg-indigo {\n        &:hover,\n        &:focus {\n            background-color: #3f51c1 !important;\n        }\n    }\n    button.bg-indigo {\n        &:hover,\n        &:focus {\n            background-color: #3f51c1 !important;\n        }\n    }\n    .bg-purple-gradient {\n        background: linear-gradient(to bottom right, $purple 0%, #647dee 100%) !important;\n        color: $white !important;\n    }\n    a.bg-purple-gradient {\n        &:hover,\n        &:focus {\n            background-color: #8c31e4 !important;\n        }\n    }\n    button.bg-purple-gradient {\n        &:hover,\n        &:focus {\n            background-color: #8c31e4 !important;\n        }\n    }\n    .bg-pink {\n        background: $pink !important;\n        color: $white !important;\n    }\n    .bg-darkpink {\n        background-color: #14a485 !important;\n        color: $white !important;\n    }\n    a.bg-pink {\n        &:hover,\n        &:focus {\n            background-color: #f33d7a !important;\n        }\n    }\n    button.bg-pink {\n        &:hover,\n        &:focus {\n            background-color: #f33d7a !important;\n        }\n    }\n    .bg-red {\n        background: $red;\n        color: $white !important;\n    }\n    a.bg-red {\n        &:hover,\n        &:focus {\n            background-color: #a11918 !important;\n        }\n    }\n    button.bg-red {\n        &:hover,\n        &:focus {\n            background-color: #a11918 !important;\n        }\n    }\n    .bg-orange {\n        background: $orange;\n        color: $white !important;\n    }\n    a.bg-orange {\n        &:hover,\n        &:focus {\n            background-color: #fc7a12 !important;\n        }\n    }\n    button.bg-orange {\n        &:hover,\n        &:focus {\n            background-color: #fc7a12 !important;\n        }\n    }\n    .bg-yellow-1 {\n        background-color: $yellow;\n        color: $white !important;\n    }\n    .bg-yellow {\n        background: $yellow;\n        color: $white !important;\n    }\n    a.bg-yellow {\n        &:hover,\n        &:focus {\n            background-color: #c29d0b !important;\n        }\n    }\n    button.bg-yellow {\n        &:hover,\n        &:focus {\n            background-color: #c29d0b !important;\n        }\n    }\n    .bg-green-1 {\n        background-color: $green;\n        color: $white !important;\n    }\n    .bg-green {\n        background: $green;\n        color: $white !important;\n    }\n    a.bg-green {\n        &:hover,\n        &:focus {\n            background-color: #448700 !important;\n        }\n    }\n    button.bg-green {\n        &:hover,\n        &:focus {\n            background-color: #448700 !important;\n        }\n    }\n    .bg-teal {\n        background: $teal !important;\n    }\n    a.bg-teal {\n        &:hover,\n        &:focus {\n            background-color: #22a193 !important;\n        }\n    }\n    button.bg-teal {\n        &:hover,\n        &:focus {\n            background-color: #22a193 !important;\n        }\n    }\n    .bg-cyan {\n        background: $cyan !important;\n        color: $white !important;\n    }\n    a.bg-cyan {\n        &:hover,\n        &:focus {\n            background-color: #117a8b !important;\n        }\n    }\n    button.bg-cyan {\n        &:hover,\n        &:focus {\n            background-color: #117a8b !important;\n        }\n    }\n    a.bg-white {\n        &:hover,\n        &:focus {\n            background-color: $dark-theme-1 !important;\n        }\n    }\n    button.bg-white {\n        &:hover,\n        &:focus {\n            background-color: $dark-theme-1 !important;\n        }\n    }\n    .bg-gray {\n        background: $gray !important;\n    }\n    a.bg-gray {\n        &:hover,\n        &:focus {\n            background-color: #6c757d !important;\n        }\n    }\n    button.bg-gray {\n        &:hover,\n        &:focus {\n            background-color: #6c757d !important;\n        }\n    }\n    .bg-lightpink-red {\n        color: #ff7088 !important;\n    }\n    a.bg-gray-dark {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    button.bg-gray-dark {\n        &:hover,\n        &:focus {\n            background-color: #1d2124 !important;\n        }\n    }\n    .bg-azure {\n        background: $azure;\n    }\n    a.bg-azure {\n        &:hover,\n        &:focus {\n            background-color: #1594ef !important;\n        }\n    }\n    button.bg-azure {\n        &:hover,\n        &:focus {\n            background-color: #1594ef !important;\n        }\n    }\n    .bg-purple-1 {\n        background: $purple;\n    }\n    .bg-lime {\n        background: $lime;\n    }\n    a.bg-lime {\n        &:hover,\n        &:focus {\n            background-color: #63ad27 !important;\n        }\n    }\n    button.bg-lime {\n        &:hover,\n        &:focus {\n            background-color: #63ad27 !important;\n        }\n    }\n    .bg-square {\n        color: $white;\n        background: #868e96;\n    }\n    .bg-primary-light {\n        background: $dark-theme-1;\n    }\n    .bg-google-plus {\n        background-color: #dd4b39;\n    }\n    .bg-pinterest {\n        background: linear-gradient(to right bottom, #c51629 0%, #bd081c 100%);\n    }\n    .bg-light-gray {\n        background-color: $dark-theme-1;\n    }\n    .bg-progress-white {\n        background-color: #eaeceb;\n    }\n    .bg-dribbble {\n        background: linear-gradient(to bottom right, #ea4c89, #c93764) !important;\n    }\n    .bg-google {\n        background: linear-gradient(to bottom right, #e64522, #c33219) !important;\n        color: $white;\n    }\n    /*--bg-transparents--*/\n    .bg-success-transparent {\n        background-color: rgba(0, 230, 130, 0.1) !important;\n    }\n    .bg-info-transparent {\n        background-color: #123d68 !important;\n    }\n    .bg-warning-transparent {\n        background-color: rgba(255, 162, 43, 0.1) !important;\n    }\n    .bg-danger-transparent {\n        background-color: rgba(255, 56, 43, 0.1) !important;\n    }\n    .bg-pink-transparent {\n        background-color: rgba(213, 109, 252, 0.1) !important;\n    }\n    .bg-purple-transparent {\n        background-color: rgba(96, 77, 216, 0.1) !important;\n    }\n    .bg-dark-transparent {\n        background-color: rgba(0, 0, 0, 0.15) !important;\n    }\n    .bg-white-transparent {\n        background-color: rgba(255, 255, 255, 0.2) !important;\n    }\n    .bg-secondary-transparent {\n        background-color: rgba(5, 195, 251, 0.1) !important;\n    }\n    .shadow-primary {\n        box-shadow: 0 7px 30px $primary-03 !important;\n    }\n    .shadow-secondary {\n        box-shadow: 0 7px 30px rgba(130, 207, 242, 0.1) !important;\n    }\n    .shadow-warning {\n        box-shadow: 0 7px 30px rgba(251, 176, 52, 0.1) !important;\n    }\n    .shadow-info {\n        box-shadow: 0 7px 30px rgba(40, 146, 235, 0.5) !important;\n    }\n    .shadow-success {\n        box-shadow: 0 7px 30px rgba(26, 122, 16, 0.1) !important;\n    }\n    .shadow-danger {\n        box-shadow: 0 7px 30px rgba(245, 167, 184, 0.1) !important;\n    }\n    .google-plus1 {\n        background: linear-gradient(to right bottom, #dd4b39 0%, #ef6a5a 100%);\n    }\n    .pinterest1 {\n        background: linear-gradient(to right bottom, #bd081c 0%, #eb4553 100%);\n    }\n    .bg-default {\n        background: #e9e9f1;\n        color: $default-color;\n    }\n    /*--Box-shadow--*/\n    .border {\n        border-color: $border-dark !important;\n        border-color: $border-dark;\n    }\n    .border-top {\n        border-top-color: $border-dark !important;\n    }\n    .border-end {\n        border-right-color: $border-dark !important;\n    }\n    .border-bottom {\n        border-bottom-color: $border-dark !important;\n    }\n    .border-start {\n        border-left-color: $border-dark !important;\n    }\n    .border-secondary {\n        border: 1px solid $secondary !important;\n    }\n    .border-success {\n        border-color: #1bbfa7 !important;\n    }\n    .border-info {\n        border-color: $azure !important;\n    }\n    .border-warning {\n        border-color: #ecb403 !important;\n    }\n    .border-danger {\n        border-color: #f82649 !important;\n    }\n    .border-pink {\n        border-color: $pink !important;\n    }\n    .border-orange {\n        border-color: $orange !important;\n    }\n    .border-light {\n        border-color: #f8f9fa !important;\n    }\n    .border-transparent {\n        border-color: #656a71 !important;\n    }\n    .border-dark {\n        border-color: $dark !important;\n    }\n    .border-white {\n        border-color: $white !important;\n    }\n    .border-end-1 {\n        border-right-color: #d5dce3;\n    }\n    /*------- Alignments & values-------*/\n    .text-white-transparent,\n    .text-white-transparent-1 {\n        color: $white !important;\n    }\n    a.text-primary {\n        &:hover,\n        &:focus {\n            color: $primary-1 !important;\n        }\n    }\n    .text-secondary {\n        color: $secondary !important;\n    }\n    a.text-secondary {\n        &:hover,\n        &:focus {\n            color: $secondary !important;\n        }\n    }\n    .text-success {\n        color: $success !important;\n    }\n    a.text-success {\n        &:hover,\n        &:focus {\n            color: #448700 !important;\n        }\n    }\n    .text-info {\n        color: $info !important;\n    }\n    a.text-info {\n        &:hover,\n        &:focus {\n            color: #1594ef !important;\n        }\n    }\n    .text-warning {\n        color: $warning !important;\n    }\n    a.text-warning {\n        &:hover,\n        &:focus {\n            color: #c29d0b !important;\n        }\n    }\n    .text-danger {\n        color: $danger !important;\n    }\n    a.text-danger {\n        &:hover,\n        &:focus {\n            color: #d22827 !important;\n        }\n    }\n    .text-light {\n        color: #ebedef !important;\n    }\n    a.text-light {\n        &:hover,\n        &:focus {\n            color: #dae0e5 !important;\n        }\n    }\n    .text-dark {\n        color: $text-color !important;\n    }\n    a.text-dark {\n        &:hover,\n        &:focus {\n            color: $primary-1 !important;\n        }\n    }\n    .text-body {\n        color: #495057 !important;\n    }\n    .text-muted {\n        color: #9595b5 !important;\n    }\n    .text-black-50 {\n        color: rgba(0, 0, 0, 0.5) !important;\n    }\n    .text-white-50 {\n        color: rgba(255, 255, 255, 0.5) !important;\n    }\n    .text-hide {\n        color: transparent;\n        background-color: transparent;\n    }\n    /*----- Typography ------*/\n    .heading-inverse {\n        background-color: #333;\n        color: $white;\n    }\n    .heading-success {\n        background-color: #1643a3;\n        color: $white;\n    }\n    .heading-info {\n        background-color: $azure;\n        color: $white;\n    }\n    .heading-warning {\n        background-color: #ecb403;\n        color: $white;\n    }\n    .heading-danger {\n        background-color: #c21a1a;\n        color: $white;\n    }\n    .text-inherit {\n        color: inherit !important;\n    }\n    .text-default {\n        color: #7a7a92 !important;\n    }\n    .text-muted-dark {\n        color: #1c232f !important;\n    }\n    .text-fb-blue {\n        color: #234684 !important;\n    }\n    .text-blue {\n        color: #467fcf !important;\n    }\n    .text-indigo {\n        color: $indigo !important;\n    }\n    .text-purple {\n        color: #867efc !important;\n    }\n    .text-lightpink-red {\n        color: #ff7088 !important;\n    }\n    .text-lightgreen {\n        color: #26eda2 !important;\n    }\n    .text-pink {\n        color: #ec82ef !important;\n    }\n    .text-red {\n        color: #c21a1a !important;\n    }\n    .text-orange {\n        color: $orange !important;\n    }\n    .text-yellow {\n        color: #ecb403 !important;\n    }\n    .text-green {\n        color: $green !important;\n    }\n    .text-green-1 {\n        color: #0dff01 !important;\n    }\n    .text-teal {\n        color: #2bcbba !important;\n    }\n    .text-cyan {\n        color: #17a2b8 !important;\n    }\n    .text-white {\n        color: $text-color !important;\n    }\n    .text-gray {\n        color: #969696 !important;\n    }\n    .text-gray-dark {\n        color: $dark !important;\n    }\n    .text-azure {\n        color: $azure !important;\n    }\n    .text-lime {\n        color: $lime !important;\n    }\n    .text-transparent {\n        color: #332525;\n    }\n    .text-facebook {\n        color: #3b5998;\n    }\n    .text-google-plus {\n        color: #dd4b39;\n    }\n    .text-twitter {\n        color: #1da1f2;\n    }\n    .text-pinterest {\n        color: #bd081c;\n    }\n    .text-secondary-gradient {\n        background: linear-gradient(to bottom right, #82cff2 0%, #28b7f9 100%);\n        -webkit-text-fill-color: transparent;\n        background: linear-gradient(to right, #cd489c 0%, #ce4ba4 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    .text-info-gradient {\n        background: linear-gradient(to right bottom, #1e63c3 0%, #00f2fe 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    .text-success-gradient {\n        background: linear-gradient(to bottom right, #1ea38f 0%, #5cf9e2 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    .text-facebook-gradient {\n        background: linear-gradient(to right bottom, #1e3c72 0%, #3d6cbf 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    .text-twitter-gradient {\n        background: linear-gradient(to right bottom, #1e63c3 0%, #00f2fe 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    .text-google-plus-gradient {\n        background: linear-gradient(to right bottom, #dd4b39 0%, #ef6a5a 100%);\n        -webkit-text-fill-color: transparent;\n    }\n    /*--text-shadow--*/\n    .text-success-shadow {\n        text-shadow: 0 5px 10px rgba(19, 191, 166, 0.3) !important;\n    }\n    .text-info-shadow {\n        text-shadow: 0 5px 10px rgba(7, 116, 248, 0.3) !important;\n    }\n    .text-warning-shadow {\n        text-shadow: 0 5px 10px rgba(255, 162, 43, 0.3) !important;\n    }\n    .text-danger-shadow {\n        text-shadow: 0 5px 10px rgba(255, 56, 43, 0.3) !important;\n    }\n    .text-pink-shadow {\n        text-shadow: 0 5px 10px rgba(213, 109, 252, 0.3) !important;\n    }\n    .text-purple-shadow {\n        text-shadow: 0 5px 10px rgba(96, 77, 216, 0.3) !important;\n    }\n    .text-dark-shadow {\n        text-shadow: 0 5px 10px rgba(0, 0, 0, 0.3) !important;\n    }\n    .text-white-shadow {\n        text-shadow: 0 5px 10px rgba(255, 255, 255, 0.3) !important;\n    }\n    .text-secondary-shadow {\n        text-shadow: 0 5px 10px rgba(130, 207, 242, 0.3);\n    }\n\n    .alert-success {\n        hr {\n            border-top-color: $success;\n        }\n    }\n\n    .alert-info {\n        hr {\n            border-top-color: $info;\n        }\n    }\n\n    .alert-warning {\n        hr {\n            border-top-color: $warning;\n        }\n    }\n\n    .alert-danger {\n        hr {\n            border-top-color: $danger;\n        }\n    }\n\n    &.horizontal {\n        .main-sidemenu .slide-left,\n        .main-sidemenu .slide-right {\n            border-color: $border-dark;\n            svg {\n                fill: $text-color;\n            }\n        }\n        .logo-horizontal .header-brand-img.light-logo1 {\n            display: none;\n        }\n        .side-menu > li > a {\n            color: $text-color;\n        }\n    }\n}\n\n.dark-mode .sidebar {\n    background: $dark-theme-1;\n    border-color: $border-dark;\n    box-shadow: 0px 8px 14.72px 1.28px rgba(42, 38, 53, 0.5);\n}\n\n.dark-mode {\n    .light-layout {\n        display: block;\n    }\n    .dark-layout {\n        display: none;\n    }\n}\n\n.dark-mode .select2-container--default .select2-results__option[aria-selected=\"true\"] {\n    background-color: #30304d;\n}\n\n.dark-mode .select2-container--default .select2-results > .select2-results__options {\n    box-shadow: 0px 8px 14.72px 1.28px rgba(34, 34, 61, 0.8);\n}\n\n.dark-mode .select2-dropdown {\n    background-color: $dark-theme-1;\n    border-color: $white-1;\n}\n\n.dark-mode .side-header .header-brand-img.desktop-logo {\n    display: block !important;\n}\n\n.dark-mode .side-header .header-brand-img.light-logo1 {\n    display: none !important;\n}\n\n.dark-mode.app.sidebar-mini.sidenav-toggled.sidenav-toggled-open\n    .app-sidebar\n    .side-header\n    .header-brand-img.light-logo1 {\n    display: none !important;\n}\n\n.dark-mode .header-brand .header-brand-img.logo-3 {\n    display: none;\n}\n\n.dark-mode .header-brand .header-brand-img.logo {\n    display: block;\n}\n\n.dark-mode.sidebar-mini .app-header .light-logo1 {\n    display: none !important;\n}\n\n.dark-mode.sidenav-toggled .header-brand-img.light-logo {\n    display: none !important;\n}\n\n.dark-mode.light-menu.sidenav-toggled .header-brand-img.light-logo {\n    display: block !important;\n}\n\n.dark-mode.light-menu.sidenav-toggled-open .header-brand-img.light-logo {\n    display: none !important;\n}\n\n.dark-mode.sidebar-mini.sidenav-toggled .app-sidebar .side-header .header-brand-img.desktop-logo {\n    display: none !important;\n}\n\n.dark-mode.sidebar-mini.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img.desktop-logo {\n    display: block !important;\n}\n\n.dark-mode.sidebar-mini.hover-submenu.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img {\n    &.desktop-logo {\n        display: none !important;\n    }\n    &.light-logo {\n        display: none !important;\n    }\n    &.toggle-logo {\n        display: block !important;\n    }\n}\n\n.dark-mode.sidebar-mini.hover-submenu1.sidenav-toggled.sidenav-toggled-open\n    .app-sidebar\n    .side-header\n    .header-brand-img {\n    &.desktop-logo {\n        display: none !important;\n    }\n    &.light-logo {\n        display: none !important;\n    }\n    &.toggle-logo {\n        display: block !important;\n    }\n}\n\n.dark-mode.light-menu.sidebar-mini.sidenav-toggled .app-sidebar .side-header .header-brand-img.toggle-logo {\n    display: none;\n}\n\n.dark-mode.sidebar-mini.sidenav-toggled .app-sidebar .side-header .header-brand-img.toggle-logo {\n    display: block;\n}\n\n.dark-mode.sidebar-mini.sidenav-toggled.sidenav-toggled-open .app-sidebar .side-header .header-brand-img.toggle-logo {\n    display: none;\n}\n\n@media (max-width: 991px) {\n    .dark-mode {\n        .app-header.header .header-brand-img.desktop-logo {\n            display: block;\n        }\n    }\n}\n\n//FULL CALENDAR\n.dark-mode {\n    #external-events {\n        border-color: $border-dark;\n    }\n    .fc-theme-standard td,\n    .fc-theme-standard th {\n        border-color: $border-dark;\n    }\n    .fc-theme-standard .fc-scrollgrid {\n        border: 0px solid $border-dark;\n        border-top: 1px solid $border-dark;\n        border-left: 1px solid $border-dark;\n    }\n    .fc .fc-daygrid-day-number {\n        color: $text-color;\n    }\n    .fc .fc-daygrid-day.fc-day-today {\n        background-color: #272742;\n    }\n    .fc-theme-standard {\n        .fc-list {\n            border-color: $border-dark;\n        }\n        .fc-list-day-cushion {\n            background: #22223d;\n        }\n    }\n    .fc .fc-list-event:hover td {\n        background: #22223d;\n    }\n    .fc-list-event.fc-event {\n        color: $text-color !important;\n    }\n    .fc-direction-ltr .fc-list-day-text,\n    .fc-direction-rtl .fc-list-day-side-text {\n        color: $text-color;\n    }\n    .fc-direction-ltr .fc-list-day-side-text,\n    .fc-direction-rtl .fc-list-day-text {\n        color: $text-color;\n    }\n}\n\n//SWEET ALERT\n.dark-mode {\n    .sweet-alert {\n        background: $dark-theme-1;\n    }\n    .sweet-alert {\n        h2 {\n            color: $text-color;\n        }\n        P {\n            color: #9595b5;\n        }\n    }\n    .alert-default {\n        background-color: $dark-body;\n    }\n}\n\n//RANGE SLIDER\n.dark-mode {\n    .irs-line-mid,\n    .irs-line-right,\n    .irs-line-left {\n        background-color: $dark-body;\n    }\n    .irs-from,\n    .irs-to,\n    .irs-single {\n        background: $dark-body;\n    }\n    .irs-from,\n    .irs-to,\n    .irs-single {\n        color: $text-color;\n    }\n    .irs-min,\n    .irs-max {\n        color: $text-color;\n        background: $dark-body;\n    }\n    .irs-grid-text {\n        color: #9595b5;\n    }\n    .irs-modern .irs-slider,\n    .irs-outline .irs-slider {\n        background-color: $dark-body;\n    }\n}\n\n.dark-mode {\n    .tree {\n        li {\n            color: $text-color;\n            a {\n                color: $text-color;\n            }\n            &.branch li {\n                background: $dark-theme-1;\n            }\n        }\n        ul {\n            &:before {\n                border-left-color: $border-dark;\n            }\n            li:before {\n                border-top-color: $border-dark;\n            }\n        }\n    }\n    .dTree a {\n        color: $text-color;\n    }\n}\n\n//TABS\n.dark-mode {\n    .tab_wrapper {\n        .content_wrapper {\n            border-color: $border-dark;\n        }\n        &.right_side {\n            .content_wrapper {\n                border-color: $border-dark;\n            }\n            > ul li {\n                &.active {\n                    border-color: $border-dark;\n                }\n                &.active::before {\n                    background: $border-dark;\n                }\n                border-left-color: $border-dark;\n                &:after {\n                    background: $white-1;\n                }\n            }\n        }\n        > ul {\n            border-bottom-color: $border-dark !important;\n        }\n        > ul li {\n            border-color: $border-dark;\n            &.active:after {\n                background: transparent;\n            }\n        }\n    }\n}\n\n//FILE UPLOAD\n.dark-mode {\n    .dropify-wrapper {\n        color: $text-color;\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        .dropify-preview {\n            background-color: $dark-theme-1;\n        }\n    }\n    .dropify-wrapper .dropify-message span.file-icon {\n        color: #9595b5;\n    }\n    .dropify-wrapper:hover {\n        background-image: linear-gradient(\n            -45deg,\n            $dark-body 25%,\n            transparent 25%,\n            transparent 50%,\n            $dark-body 50%,\n            $dark-body 75%,\n            transparent 75%,\n            transparent\n        );\n    }\n    .ff_fileupload_wrap .ff_fileupload_dropzone {\n        &:focus,\n        &:active {\n            background-color: $dark-theme-1;\n            border-color: $border-dark;\n        }\n    }\n    .ff_fileupload_wrap .ff_fileupload_dropzone {\n        border-color: $border-dark;\n        background-color: $dark-theme-1;\n        &:hover {\n            background-color: $dark-theme-1;\n            border-color: $border-dark;\n        }\n    }\n    .sp-replacer {\n        border-color: $border-dark;\n        background: $dark-theme-1;\n        color: $white;\n        &:hover,\n        &.sp-active {\n            border-color: $border-dark;\n            color: $white;\n        }\n    }\n    .sp-container {\n        background: $dark-theme-1;\n        box-shadow: 0 10px 40px 0 rgba(34, 34, 61, 0.8);\n        border-color: $border-dark;\n    }\n    .sp-picker-container {\n        border-left: 0;\n    }\n    .ui-timepicker-wrapper {\n        background: #23223a;\n        border-color: rgba(255, 255, 255, 0.07);\n        box-shadow: 0 16px 18px 0 #0e0f2e;\n    }\n    .ui-timepicker-list li {\n        color: rgba(255, 255, 255, 0.8);\n    }\n    .datepicker {\n        .datepicker-switch,\n        td,\n        th {\n            color: $text-color !important;\n        }\n    }\n    .datepicker-dropdown.datepicker-orient-top {\n        &:after,\n        &:before {\n            border-top-color: #30304d;\n        }\n    }\n    .SumoSelect {\n        > .CaptionCont {\n            border-color: $border-dark;\n            background-color: $dark-theme-1;\n            color: #9595b5;\n        }\n        &.disabled > .CaptionCont {\n            border-color: $border-dark;\n            background: #22223c;\n            border-radius: 0;\n        }\n    }\n    .SumoSelect {\n        &.open > .optWrapper {\n            background: $dark-theme-1;\n        }\n        > .optWrapper {\n            border-color: $border-dark;\n            > .options li.opt {\n                border-bottom-color: $border-dark;\n                &:hover {\n                    background-color: #262641;\n                }\n            }\n        }\n    }\n    .SumoSelect {\n        &.open .search-txt {\n            background: $dark-theme-1;\n            color: #9595b5;\n        }\n        .select-all {\n            background-color: $dark-theme-1;\n            border-bottom-color: $border-dark;\n        }\n        > .optWrapper {\n            > .MultiControls {\n                border-top: 1px solid rgba(255, 255, 255, 0.12);\n                background-color: $dark-theme-1;\n            }\n            &.multiple > .MultiControls > p:hover {\n                background-color: #393958;\n            }\n        }\n    }\n    datepicker-dropdown {\n        &.datepicker-orient-top {\n            &:after,\n            &:before {\n                border-top: 7px solid #3e3e50;\n            }\n        }\n        &:after,\n        &:before {\n            border-bottom-color: #2e2e4a;\n        }\n    }\n    .datepicker table tr td span {\n        &:hover,\n        &.focused {\n            background: #21213c !important;\n            color: #dedefd !important;\n        }\n    }\n    .datepicker .prev,\n    .datepicker .next {\n        background: #39395c;\n    }\n    .datepicker-dropdown:after {\n        border-bottom-color: $border-dark;\n    }\n    .datepicker-dropdown::before {\n        border-bottom-color: $border-dark;\n    }\n    .ms-choice {\n        color: #9595b5;\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        &.disabled {\n            background-color: #2d2d48;\n            border-color: $border-dark;\n        }\n    }\n    .ms-drop {\n        &.bottom {\n            box-shadow: 0px 8px 14.72px 1.28px rgba(34, 34, 61, 0.8);\n        }\n        color: #9595b5;\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .select2-dropdown {\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .select2-container--default {\n        .select2-results__option[aria-selected=\"true\"] {\n            background-color: #30304d;\n        }\n        &.select2-container--disabled .select2-selection--single {\n            background-color: #2d2d48;\n        }\n    }\n    .ms-search input {\n        background: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .transfer-double {\n        background-color: $dark-theme-1;\n    }\n    .transfer-double-content-tabs {\n        .tab-item-name.tab-active {\n            background: #252336;\n        }\n        border-bottom-color: $border-dark;\n    }\n    .transfer-double-content-left,\n    .transfer-double-content-right {\n        border-color: $border-dark;\n    }\n    .transfer-double-list-footer {\n        border-top-color: $border-dark;\n    }\n    .transfer-double-list-search-input {\n        border-color: $border-dark;\n        background-color: $dark-theme-1;\n    }\n    .transfer-double-list-main::-webkit-scrollbar-track {\n        background-color: $dark-theme-1;\n    }\n    .checkbox-group label:before {\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .transfer-double-content-param {\n        border-bottom-color: $border-dark;\n    }\n    .transfer-double-selected-list-search-input {\n        border-color: $border-dark;\n        background-color: $dark-theme-1;\n    }\n    .btn-select-arrow {\n        color: $text-color;\n        background: $dark-theme-1;\n        border-color: $border-dark;\n    }\n    .multi-wrapper {\n        .search-input {\n            border-bottom-color: $border-dark;\n            background: $dark-theme-1;\n        }\n        border-color: $border-dark;\n        .item-1 {\n            color: $text-color;\n            background: $dark-theme-1;\n        }\n        .selected-wrapper,\n        .item-group-1 .group-label {\n            background: $dark-body;\n        }\n    }\n    .multi-wrapper .non-selected-wrapper {\n        background: $dark-theme-1;\n        border-right-color: $border-dark;\n    }\n    .iti input {\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        color: $text-color;\n        &[type=\"tel\"],\n        &[type=\"text\"] {\n            border-color: $border-dark;\n            background-color: $dark-theme-1;\n            border-right-color: $border-dark;\n            color: $text-color;\n        }\n    }\n    .iti__selected-flag {\n        border-right-color: $border-dark;\n        border-left-color: $border-dark;\n    }\n    .iti--allow-dropdown .iti__flag-container:hover .iti__selected-flag {\n        background-color: $dark-theme-1;\n        border-top-color: $border-dark;\n    }\n    .iti__country-list {\n        border-color: $border-dark;\n        background-color: $dark-theme-1;\n        border-right-color: $border-dark;\n    }\n    .iti__country.iti__highlight {\n        background-color: $dark-theme-1;\n    }\n    .iti__divider {\n        border-bottom-color: $border-dark;\n    }\n}\n\n//SUMMERNOTE\n.dark-mode {\n    .note-editor.note-frame.panel.panel-default .panel-heading {\n        background-color: $dark-theme-1;\n        border-bottom-color: $border-dark;\n    }\n    .note-btn.btn-default {\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        color: $text-color;\n    }\n    .note-editor.note-airframe,\n    .note-editor.note-frame {\n        border-color: $border-dark;\n    }\n    .note-editor .btn-default:not(:disabled):not(.disabled):active {\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        color: $text-color;\n    }\n    .form-control-file::-webkit-file-upload-button {\n        background-color: $dark-body;\n        color: $text-color;\n        border-color: $border-dark;\n    }\n    .note-editor.note-airframe .note-editing-area .note-codable,\n    .note-editor.note-frame .note-editing-area .note-codable {\n        background-color: $dark-body;\n        color: $white-7;\n    }\n}\n\n//RICHTEXT\n.dark-mode {\n    .richText {\n        .richText-editor {\n            background-color: $dark-theme-1;\n            border-left-color: rgba(255, 255, 255, 0);\n        }\n        .richText-toolbar ul {\n            border-bottom-color: $border-dark;\n        }\n        .richText-toolbar ul li a {\n            color: $text-color;\n            &:hover {\n                background-color: $dark-theme-1;\n            }\n            .richText-dropdown-outer .richText-dropdown {\n                background-color: $dark-theme-1;\n                border-color: $border-dark;\n                .richText-dropdown-close {\n                    background: $dark-theme-1;\n                    color: #9595b5;\n                }\n            }\n        }\n        .richText-form {\n            input {\n                &[type=\"text\"],\n                &[type=\"file\"],\n                &[type=\"number\"] {\n                    border-color: $border-dark;\n                    background: #1b1b2d;\n                    color: $text-color;\n                }\n            }\n            select {\n                border-color: $border-dark;\n                background: #1b1b2d;\n                color: $text-color;\n            }\n        }\n        .richText-toolbar ul li a .richText-dropdown-outer ul.richText-dropdown li a {\n            &:hover {\n                background: #1b1b2d;\n            }\n            border-bottom-color: $border-dark;\n        }\n        .richText-undo,\n        .richText-redo {\n            border-right-color: $border-dark;\n        }\n    }\n}\n\n//QUILL EDITOR\n.dark-mode {\n    .ql-toolbar.ql-snow {\n        .ql-picker-label {\n            border-color: $border-dark;\n            background: $dark-theme-1;\n            color: $text-color;\n        }\n        border-color: $border-dark;\n    }\n    .ql-container.ql-snow {\n        border-color: $border-dark;\n    }\n    .ql-snow {\n        &.ql-toolbar button,\n        .ql-toolbar button {\n            border-color: $border-dark;\n            background: $dark-theme-1;\n            color: $text-color;\n        }\n        &.ql-toolbar button:last-child,\n        .ql-toolbar button:last-child {\n            border-right-color: $border-dark;\n        }\n    }\n    .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {\n        background-color: $dark-theme-1;\n    }\n    .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {\n        border-color: $border-dark;\n    }\n    .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {\n        border-color: $border-dark;\n    }\n    .ql-snow .ql-formats {\n        color: $text-color;\n    }\n    .ql-snow .ql-tooltip {\n        background-color: $dark-theme-1;\n        border-color: $border-dark;\n        box-shadow: 0px 0px 5px $black;\n        color: $text-color;\n        margin-left: 100px;\n    }\n    .ql-tooltip.ql-editing {\n        input {\n            &[type=\"text\"],\n            &[type=\"file\"],\n            &[type=\"number\"] {\n                border-color: $border-dark;\n                background: $dark-theme-1;\n                color: $text-color;\n            }\n        }\n    }\n    .ql-bubble .ql-tooltip {\n        background-color: $dark-theme-1;\n    }\n}\n\n//FORM WIZARD\n.dark-mode {\n    .sw-theme-dots {\n        > ul.step-anchor {\n            background: $dark-theme-1;\n            border-color: $border-dark;\n            &:before {\n                background-color: #3e3e63;\n            }\n            > li > a:before {\n                background: #3e3e63;\n            }\n        }\n        .step-content {\n            background-color: $dark-theme-1;\n        }\n        .sw-toolbar {\n            background: $dark-theme-1;\n        }\n    }\n    .wizard {\n        border-color: $border-dark;\n        background-color: $dark-theme-1;\n        border-radius: 3px;\n        > {\n            .content {\n                border-top-color: $border-dark;\n                border-bottom-color: $border-dark;\n                > .title {\n                    color: #dedefd;\n                }\n            }\n            .actions .disabled a {\n                background-color: #404062;\n                color: rgba(255, 255, 255, 0.62);\n            }\n            .steps {\n                .current a {\n                    .number,\n                    &:active .number,\n                    &:hover .number {\n                        background-color: $primary-1;\n                    }\n                }\n                a {\n                    .number,\n                    &:active .number,\n                    &:hover .number {\n                        background-color: #404062;\n                    }\n                }\n            }\n        }\n        &.vertical > {\n            .content,\n            .actions {\n                border-left-color: $border-dark;\n            }\n        }\n        > .steps {\n            .done a {\n                .number,\n                &:active .number,\n                &:hover .number {\n                    background-color: #0dcd94;\n                }\n            }\n            a {\n                .number,\n                &:active .number,\n                &:hover .number {\n                    background-color: #404062;\n                }\n            }\n        }\n    }\n}\n\n//OWL CAROUSEL\n.dark-mode {\n    .owl-nav button {\n        background: #5b5b83 !important;\n        border-color: $border-dark !important;\n    }\n}\n\n//CHARTS\n.dark-mode {\n    .flot-text {\n        color: #9595b5 !important;\n    }\n    tspan {\n        fill: #9595b5 !important;\n    }\n    .nvd3 {\n        text {\n            fill: #9595b5;\n        }\n        .nv-axis line {\n            stroke: rgba(119, 119, 142, 0.2);\n        }\n        .nv-discretebar .nv-groups text,\n        .nv-multibarHorizontal .nv-groups text {\n            fill: $text-color;\n        }\n    }\n    .countdown li {\n        background: $dark-theme-1;\n        border: 5px solid $border-dark;\n        color: $text-color;\n    }\n    @media(min-width:992px){\n        &.horizontal {\n            .horizontal-main .slide .slide-menu,\n            .horizontal-main .slide .sub-slide-menu,\n            .horizontal-main .slide .sub-slide-menu2 {\n                background-color: $dark-theme-1;\n                border-color: $border-dark;\n                box-shadow: 0px 16px 18px rgba(0, 0, 0, 0.3) !important;\n            }\n        }\n    }\n}\n\n.rtl.dark-mode {\n    .app-sidebar {\n        border-left-color: $white-1;\n    }\n    .side-header {\n        border-left-color: $white-1;\n    }\n    .vtimeline .timeline-wrapper .timeline-panel:after {\n        border-right-color: $white-1;\n        border-left-color: $white-1;\n    }\n    .vtimeline .timeline-wrapper.timeline-inverted .timeline-panel:after {\n        border-right-color:$white-2 !important;\n        border-left-color: $white-2 !important;\n        right: -10px !important;\n        left: auto;\n    }\n    .notification .notification-body:before {\n        border-left-color: #2a2a4a;\n        border-right-color: transparent;\n    }\n    .border-end {\n        border-left-color: $white-1 !important;\n        border-right-color: transparent !important;\n    }\n    .card-group{\n        .card.border-end{\n            border-left: 1px solid $border-dark !important;\n            border-right: inherit !important;\n        }\n    }\n}\n\n.rtl.dark-mode .main-content-body-chat {\n    .border-start-0 {\n        border-left-color: $border-dark !important;\n    }\n}\n.dark-mode .dataTables_wrapper .selected {\n    background: $dark-body;\n}\n.dark-mode .table .table-primary th {\n    color: #7373dd;\n}\n.dark-mode {\n    .fc-theme-standard .fc-popover {\n        border: 1px solid $white-1;\n        background: $dark-theme-1;\n    }\n    .fc-theme-standard .fc-popover-header {\n        background: $dark-theme;\n    }\n}\n.dark-mode .ff_fileupload_wrap table.ff_fileupload_uploads td.ff_fileupload_summary .ff_fileupload_filename input {\n    background: transparent;\n    color: $white;\n}\n.dark-mode .ff_fileupload_wrap table.ff_fileupload_uploads td.ff_fileupload_preview .ff_fileupload_preview_image {\n    background-color: $dark-theme;\n}\n.dark-mode .ff_fileupload_wrap table.ff_fileupload_uploads td.ff_fileupload_actions button {\n    border-color: $white-1;\n    background-color: $dark-theme;\n}\n.dark-mode {\n    .dropify-wrapper .dropify-preview .dropify-render img {\n        background-color: transparent;\n    }\n\n    &.rtl .table.border-dashed thead th:last-child {\n        border-left-color: transparent;\n    }\n    &.rtl .table thead th:last-child {\n        border-left-color: $white-1;\n    }\n    .handle-counter{\n        input{\n            background-color: transparent;\n            border: 1px solid $border-dark;\n            color: $text-color;\n        }\n    }\n    .card-group{\n        .card.border-end{\n            border-right: 1px solid $border-dark !important;\n        }\n    }\n}\n.dark-mode .list-group-item a {\n    color: $white-7;\n}\n"]}