/* All Dictations Page Styles */

/* Dictation Grid */
.dictations-grid {
    min-height: 400px;
}

/* Dictation Cards */
.dictation-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 3px solid transparent;
    overflow: hidden;
    position: relative;
}


.dictation-card.border-success {
    border: 4px solid #10b981 !important;
    background: linear-gradient(135deg, #fff 0%, #fff 100%) !important;
}

.dictation-card.border-danger {
    border: 4px solid #c82333 !important;
    background: linear-gradient(135deg, #fff 0%, #fff 100%) !important;
}
/* End of Selection */

/* Card Header */
.card-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.dictation-number {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6b7280;
    background: #f3f4f6;
    padding: 4px 12px;
    border-radius: 20px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-completed {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.status-pending {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.dictation-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1f2937;
    margin: 12px 0 8px 0;
    line-height: 1.3;
}

.difficulty-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.sentence-count {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Card Body */
.card-body {
    padding: 20px 24px;
}

/* Score Section */
.score-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.score-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 1.1rem;
    color: white;
    flex-shrink: 0;
    position: relative;
}

.score-circle.excellent {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.score-circle.good {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.score-circle.average {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.score-circle.poor {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.score-circle.fail {
    background: linear-gradient(135deg, #991b1b 0%, #7f1d1d 100%);
}

.score-details {
    flex: 1;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 6px 0;
}

.score-item:last-child {
    margin-bottom: 0;
}

.score-label {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 500;
}

.score-number {
    color: #1f2937;
    font-weight: 600;
    font-size: 0.9rem;
}

.error-count {
    background: #fee2e2;
    color: #dc2626;
    padding: 2px 8px;
    border-radius: 8px;
}

/* Score item stilleri */
.error-info {
    background: rgba(239, 68, 68, 0.1);
    border-left: 3px solid #ef4444;
    padding-left: 8px;
    margin-left: -8px;
    border-radius: 0 8px 8px 0;
}

.class-comparison {
    background: rgba(59, 130, 246, 0.1);
    border-left: 3px solid #3b82f6;
    padding-left: 8px;
    margin-left: -8px;
    border-radius: 0 8px 8px 0;
}

.class-rank {
    background: rgba(168, 85, 247, 0.1);
    border-left: 3px solid #a855f7;
    padding-left: 8px;
    margin-left: -8px;
    border-radius: 0 8px 8px 0;
}

.date-info {
    background: rgba(16, 185, 129, 0.1);
    border-left: 3px solid #10b981;
    padding-left: 8px;
    margin-left: -8px;
    border-radius: 0 8px 8px 0;
}

.rank-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-weight: 600;
}

.score-item.error-info .score-label,
.score-item.class-comparison .score-label,
.score-item.class-rank .score-label,
.score-item.date-info .score-label {
    font-weight: 600;
    color: #1e293b;
}

.score-item.error-info .score-number {
    color: #ef4444;
    font-weight: 600;
}

.score-item.class-comparison .score-number {
    color: #3b82f6;
    font-weight: 600;
}

.score-item.class-rank .score-number {
    color: #a855f7;
    font-weight: 600;
}

.score-item.date-info .score-number {
    color: #10b981;
    font-weight: 600;
}

/* Pending Section */
.pending-section {
    text-align: center;
    padding: 20px 0;
}

.pending-icon {
    color: #ef4444;
    margin-bottom: 12px;
    opacity: 0.7;
}

.pending-text {
    color: #6b7280;
    font-size: 1rem;
    margin-bottom: 16px;
    font-style: italic;
}

.pending-info {
    background: #f9fafb;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-label {
    color: #6b7280;
    font-size: 0.85rem;
}

.info-value {
    color: #1f2937;
    font-weight: 500;
    font-size: 0.85rem;
}

/* Card Footer */
.card-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.btn-action {
    width: 100%;
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-view {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-view:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-solve {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-solve:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

/* Responsive */
@media (max-width: 768px) {
    .dictation-card {
        margin-bottom: 20px;
    }
    
    .card-header {
        padding: 16px 20px 12px;
    }
    
    .card-body {
        padding: 16px 20px;
    }
    
    .card-footer {
        padding: 12px 20px 16px;
    }
    
    .dictation-title {
        font-size: 1.1rem;
    }
    
    .score-section {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .score-details {
        width: 100%;
    }
    
    .score-circle {
        width: 70px;
        height: 70px;
        font-size: 1rem;
    }
}

/* Animation delays for grid items - UK-Scrollspy ile uyumlu */
.dictation-card {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* UK-Scrollspy animasyonunu override et */
[uk-scrollspy] .dictation-card {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* Smooth hover transition */
.dictation-card {
    transition: all 0.3s ease !important;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Yeşil neon animasyonu */
@keyframes greenNeonPulse {
    0% {
        box-shadow: 
            0 0 5px rgba(16, 185, 129, 0.2),
            0 0 10px rgba(16, 185, 129, 0.1),
            0 0 15px rgba(16, 185, 129, 0.05),
            inset 0 0 5px rgba(16, 185, 129, 0.03);
    }
    100% {
        box-shadow: 
            0 0 15px rgba(16, 185, 129, 0.4),
            0 0 25px rgba(16, 185, 129, 0.3),
            0 0 35px rgba(16, 185, 129, 0.2),
            inset 0 0 15px rgba(16, 185, 129, 0.08);
    }
}

/* Kırmızı neon animasyonu */
@keyframes redNeonPulse {
    0% {
        box-shadow: 
            0 0 5px rgba(239, 68, 68, 0.2),
            0 0 10px rgba(239, 68, 68, 0.1),
            0 0 15px rgba(239, 68, 68, 0.05),
            inset 0 0 5px rgba(239, 68, 68, 0.03);
    }
    100% {
        box-shadow: 
            0 0 15px rgba(239, 68, 68, 0.4),
            0 0 25px rgba(239, 68, 68, 0.3),
            0 0 35px rgba(239, 68, 68, 0.2),
            inset 0 0 15px rgba(239, 68, 68, 0.08);
    }
}

