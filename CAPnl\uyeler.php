<?php
require 'header.php';
require 'solmenu.php';
require_once '../db/encryption.php'; // Şifreleme sınıfını dahil et
?>

<div class="main-content app-content mt-0">
    <div class="side-app">
        <div class="main-container container-fluid">
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-1">Üyeler</h1>
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Ana Sayfa</a></li>
                        <li class="breadcrumb-item active">Üyeler</li>
                    </ol>
                </div>
            </div>

            <!-- Üyeler Tablosu -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header border-bottom-0 bg-transparent bg-mavi d-flex justify-content-between align-items-center">
                            <div class="card-title text-white"><PERSON>ye Listesi</div>
                            <button class="btn btn-white" data-bs-toggle="modal" data-bs-target="#yeniUyeModal">
                                <i class="ri-add-line me-1"></i>Yeni Üye Ekle
                            </button>
                        </div>
                        <div class="card-body p-0">
                            <!-- Filtreleme Formu -->
                            <div class="p-3 border-bottom">
                                <form id="filtreForm" class="row g-3">
                                    <div class="col-md-2">
                                        <input type="text" class="form-control" id="filtre" placeholder="Ad, Soyad, E-posta, Telefon veya TC Kimlik ile ara...">
                                    </div>
                                    <div class="col-md">
                                        <select class="form-control" id="durum">
                                            <option value="">Tüm Durumlar</option>
                                            <option value="0">Ödeme Yapılması Bekleniyor</option>
                                            <option value="1">Aktif</option>
                                            <option value="2">Havale Bekleniyor</option>
                                            <option value="3">Pasif</option>
                                            <option value="4">Dondurulmuş Üye</option>
                                        </select>
                                    </div>
                                    <div class="col-md">
                                        <select class="form-control" id="ucretsiz_uye">
                                            <option value="">Tüm Üyelik Tipleri</option>
                                            <option value="0">Öğrenciler</option>
                                            <option value="1">Ücretsiz Üye</option>
                                        </select>
                                    </div>
                                    <div class="col-md">
                                        <select class="form-control" id="uye_tipi">
                                            <option value="">Tüm Üye Tipleri</option>
                                            <option value="0">Normal</option>
                                            <option value="1">Burslu</option>
                                        </select>
                                    </div>
                                    <div class="col-md">
                                        <select class="form-control" id="donem_id">
                                            <option value="">Tüm Dönemler</option>
                                            <option value="hicbiri">Hiçbir döneme kaydı olmayanlar</option>
                                            <?php
                                            $donemler = $db->query("SELECT * FROM donemler ORDER BY id DESC")->fetchAll(PDO::FETCH_ASSOC);
                                            foreach ($donemler as $donem) {
                                                echo "<option value='{$donem['id']}'>{$donem['donem_adi']}</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <div class="col-md">
                                        <select class="form-control" id="sozlesme_onay">
                                            <option value="">Tüm Sözleşme Durumları</option>
                                            <option value="1">Sözleşmeyi Onayladı</option>
                                            <option value="0">Sözleşmeyi Onaylamadı</option>
                                        </select>
                                    </div>
                                    <div class="col-md">
                                        <select class="form-control" id="sayfa_basina">
                                            <option value="10">10 Kayıt</option>
                                            <option value="20">20 Kayıt</option>
                                            <option value="50">50 Kayıt</option>
                                            <option value="100">100 Kayıt</option>
                                            <option value="999999">Tümü</option>
                                        </select>
                                    </div>
                                    <div class="col-md">
                                        <button type="submit" class="btn btn-primary w-100">Filtrele</button>
                                    </div>
                                </form>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover border-0 mb-0">
                                    <thead class="bg-light">
                                        <tr>
                                            <th class="border-top-0 siralama" data-siralama="ad">Üye <i class="ri-arrow-up-down-line"></i></th>
                                            <th class="border-top-0 siralama" data-siralama="tc_kimlik">TC Kimlik <i class="ri-arrow-up-down-line"></i></th>
                                            <th class="border-top-0 siralama" data-siralama="email">İletişim <i class="ri-arrow-up-down-line"></i></th>
                                            <th class="border-top-0 siralama" data-siralama="kredi">Kredi <i class="ri-arrow-up-down-line"></i></th>
                                            <th class="border-top-0 siralama" data-siralama="grup_adi">Session <i class="ri-arrow-up-down-line"></i></th>
                                            <th class="border-top-0 siralama" data-siralama="durum">Durum <i class="ri-arrow-up-down-line"></i></th>
                                            <th class="border-top-0 siralama" data-siralama="sozlesme_onay">Sözleşme <i class="ri-arrow-up-down-line"></i></th>
                                            <th class="border-top-0 siralama" data-siralama="son_giris">Son Giriş <i class="ri-arrow-up-down-line"></i></th>
                                            <th class="border-top-0 text-end">İşlemler</th>
                                        </tr>
                                    </thead>
                                    <tbody id="uyeListesi">
                                        <tr>
                                            <td colspan="9" class="text-center py-5">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Yükleniyor...</span>
                                                </div>
                                                <div class="mt-3 text-muted">Lütfen Bekleyin, Üye Listesi Yükleniyor...</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="d-flex justify-content-between align-items-center p-3 border-top">
                                    <div class="text-muted" id="uyeSayisi"></div>
                                    <div class="btn-group" id="sayfalamaButtons">
                                        <!-- AJAX ile doldurulacak -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Yeni Üye Ekleme Modal -->
<div class="modal fade" id="yeniUyeModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title">Yeni Üye Ekle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="yeniUyeForm">
                <div class="modal-body p-4">
                    <div class="row">
                        <!-- Sol Sütun: Üye Bilgileri -->
                        <div class="col-md-6 border-end pe-4">
                            <h6 class="mb-3 text-primary">Kişisel Bilgiler</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Ad</label>
                                        <input type="text" name="ad" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Soyad</label>
                                        <input type="text" name="soyad" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">TC Kimlik</label>
                                        <input type="text" name="tc_kimlik" class="form-control" maxlength="11">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Telefon</label>
                                        <input type="tel" name="telefon" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label class="form-label">E-posta</label>
                                        <input type="email" name="email" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label class="form-label">Şifre</label>
                                        <input type="password" name="sifre" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Durum</label>
                                        <select name="durum" class="form-control" required>
                                            <option value="0">Ödeme Yapılması Bekleniyor</option>
                                            <option value="1">Aktif</option>
                                            <option value="2">Havale Bekleniyor</option>
                                            <option value="3">Pasif</option>
                                            <option value="4">Dondurulmuş Üye</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Üyelik Tipi</label>
                                        <select name="ucretsiz_uye" class="form-control" required>
                                            <option value="0">Öğrenciler</option>
                                            <option value="1">Ücretsiz Üye</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Üye Tipi</label>
                                        <select name="uye_tipi" class="form-control" required>
                                            <option value="0">Normal</option>
                                            <option value="1">Burslu</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Doğrulama Durumu</label>
                                        <select name="dogrulama_durumu" class="form-control" required>
                                            <option value="0">Doğrulanmamış</option>
                                            <option value="1">Doğrulanmış</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sağ Sütun: Session/Dönem Bilgileri -->
                        <div class="col-md-6 ps-4">
                            <h6 class="mb-3 text-primary">Session ve Dönem Bilgileri</h6>
                            <input type="hidden" name="donem_ayar_yapiliyor" id="yeni_donem_ayar_yapiliyor" value="0">
                            
                            <div class="mb-4">
                                <button type="button" id="yeniDonemAyarlariBtn" class="btn btn-outline-primary w-100">
                                    <i class="ri-settings-3-line me-1"></i> Dönem Ayarları
                                </button>
                            </div>
                            
                            <div id="yeniDonemAyarlariContainer" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">Dönem</label>
                                    <select name="donem_id" id="yeni_donem_id" class="form-control">
                                        <option value="">Dönem Seçin</option>
                                        <?php
                                        foreach ($donemler as $donem) {
                                            echo "<option value='{$donem['id']}'>{$donem['donem_adi']}</option>";
                                        }
                                        ?>
                                    </select>
                                </div>

                                <!-- Dönem Kredisi alanı -->
                                <div class="mb-3 yeni-kredi-container" style="display: none;">
                                    <label class="form-label">Dönem Kredisi</label>
                                    <input type="number" name="donem_kredi" id="yeni_donem_kredi" class="form-control" min="0" value="5">
                                    <small class="text-muted d-block mt-2">
                                        <i class="ri-information-line me-1"></i>
                                        Bu dönem için kullanıcının kredi miktarı
                                    </small>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label">Sessionlar</label>
                                    <div id="yeni_grup_container" class="card p-3" style="max-height: 200px; overflow-y: auto;  scrollbar-width:thin !important;">
                                        <div class="text-muted">Önce dönem seçin</div>
                                    </div>
                                    <small class="text-muted">Üyenin dahil olmasını istediğiniz sessionları seçin</small>
                                </div>

                                <div class="mb-3 yeni-mevcut-session-container" style="display: none;">
                                    <label class="form-label">Mevcut Session</label>
                                    <select name="mevcut_grup_id" id="yeni_mevcut_grup_id" class="form-control">
                                        <option value="">Önce sessionları seçin</option>
                                    </select>
                                    <small class="text-muted d-block mt-2">
                                        <i class="ri-information-line me-1"></i>
                                        Bu kullanıcı için aktif olacak ana session
                                    </small>
                                </div>

                                <div class="mb-4 border-top pt-3" id="yeni_ders_listesi_container" style="display: none;">
                                    <label class="form-label fw-bold">Ders Başlangıç Ayarları</label>
                                    <div class="form-check mb-2">
                                        <input type="radio" name="ders_baslangic_secimi" id="yeni_ilk_dersten_baslat" value="ilk" class="form-check-input" checked onclick="$('#yeni_baslangic_ders_id').prop('disabled', true);">
                                        <label class="form-check-label" for="yeni_ilk_dersten_baslat" onclick="$('#yeni_ilk_dersten_baslat').prop('checked', true).trigger('click');">İlk dersten başlat</label>
                                        <small class="d-block text-muted ms-4">Üye dersleri 1. dersten itibaren görüntüleyebilecek</small>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input type="radio" name="ders_baslangic_secimi" id="yeni_secili_dersten_baslat" value="secili" class="form-check-input" onclick="$('#yeni_baslangic_ders_id').prop('disabled', false);">
                                        <label class="form-check-label" for="yeni_secili_dersten_baslat" onclick="$('#yeni_secili_dersten_baslat').prop('checked', true).trigger('click');">Seçili dersten başlat</label>
                                        <small class="d-block text-muted ms-4">Üye sadece seçilen dersten itibaren görüntüleyebilecek</small>
                                    </div>
                                    <div class="mb-0">
                                        <select name="baslangic_ders_id" id="yeni_baslangic_ders_id" class="form-control" disabled>
                                            <option value="">Ders seçin</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Kaydet</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Üye Düzenleme Modal -->
<div class="modal fade" id="uyeDuzenleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title">Üye Düzenle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="uyeDuzenleForm" method="post">
                <div class="modal-body p-4">
                    <input type="hidden" name="id" id="duzenle_id">
                    <div class="row">
                        <!-- Sol Sütun: Üye Bilgileri -->
                        <div class="col-md-6 border-end pe-4">
                            <h6 class="mb-3 text-primary">Kişisel Bilgiler</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Ad</label>
                                        <input type="text" name="ad" id="duzenle_ad" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Soyad</label>
                                        <input type="text" name="soyad" id="duzenle_soyad" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">TC Kimlik</label>
                                        <input type="text" name="tc_kimlik" id="duzenle_tc_kimlik" class="form-control" maxlength="11">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Telefon</label>
                                        <input type="tel" name="telefon" id="duzenle_telefon" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label class="form-label">E-posta</label>
                                        <input type="email" name="email" id="duzenle_email" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label class="form-label">Şifre</label>
                                        <input type="password" name="sifre" class="form-control" placeholder="Değiştirmek istemiyorsanız boş bırakın">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Durum</label>
                                        <select name="durum" id="duzenle_durum" class="form-control" required>
                                            <option value="0">Ödeme Yapılması Bekleniyor</option>
                                            <option value="1">Aktif</option>
                                            <option value="2">Havale Bekleniyor</option>
                                            <option value="3">Pasif</option>
                                            <option value="4">Dondurulmuş Üye</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Üyelik Tipi</label>
                                        <select name="ucretsiz_uye" id="duzenle_ucretsiz_uye" class="form-control" required>
                                            <option value="0">Öğrenciler</option>
                                            <option value="1">Ücretsiz Üye</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Üye Tipi</label>
                                        <select name="uye_tipi" id="duzenle_uye_tipi" class="form-control" required>
                                            <option value="0">Normal</option>
                                            <option value="1">Burslu</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Doğrulama Durumu</label>
                                        <select name="dogrulama_durumu" id="duzenle_dogrulama_durumu" class="form-control" required>
                                            <option value="0">Doğrulanmamış</option>
                                            <option value="1">Doğrulanmış</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sağ Sütun: Session/Dönem Bilgileri -->
                        <div class="col-md-6 ps-4">
                            <h6 class="mb-3 text-primary">Session ve Dönem Bilgileri</h6>
                            <input type="hidden" name="donem_ayar_yapiliyor" id="duzenle_donem_ayar_yapiliyor" value="0">
                            
                            <div class="mb-4">
                                <button type="button" id="duzenleDonemAyarlariBtn" class="btn btn-outline-primary w-100">
                                    <i class="ri-settings-3-line me-1"></i> Dönem Ayarları
                                </button>
                            </div>
                            
                            <div id="duzenleDonemAyarlariContainer" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">Dönem</label>
                                    <select name="donem_id" id="duzenle_donem_id" class="form-control">
                                        <option value="">Dönem Seçin</option>
                                        <?php
                                        foreach ($donemler as $donem) {
                                            echo "<option value='{$donem['id']}'>{$donem['donem_adi']}</option>";
                                        }
                                        ?>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label">Sessionlar</label>
                                    <div id="duzenle_grup_container" class="card p-3" style="max-height: 200px; overflow-y: auto;  scrollbar-width:thin !important;">
                                        <div class="text-muted">Önce dönem seçin</div>
                                    </div>
                                    <small class="text-muted">Üyenin dahil olmasını istediğiniz sessionları seçin</small>
                                </div>

                                <div class="mb-3 mevcut-session-container" style="display: none;">
                                    <label class="form-label">Mevcut Session</label>
                                    <select name="mevcut_grup_id" id="duzenle_mevcut_grup_id" class="form-control">
                                        <option value="">Önce sessionları seçin</option>
                                    </select>
                                    <small class="text-muted d-block mt-2">
                                        <i class="ri-information-line me-1"></i>
                                        Bu kullanıcı için aktif olacak ana session
                                    </small>
                                </div>

                                <div class="mb-4 border-top pt-3" id="ders_listesi_container" style="display: none;">
                                    <label class="form-label fw-bold">Ders Başlangıç Ayarları</label>
                                    <div class="form-check mb-2">
                                        <input type="radio" name="ders_baslangic_secimi" id="ilk_dersten_baslat" value="ilk" class="form-check-input" checked onclick="$('#baslangic_ders_id').prop('disabled', true);">
                                        <label class="form-check-label" for="ilk_dersten_baslat" onclick="$('#ilk_dersten_baslat').prop('checked', true).trigger('click');">İlk dersten başlat</label>
                                        <small class="d-block text-muted ms-4">Üye dersleri 1. dersten itibaren görüntüleyebilecek</small>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input type="radio" name="ders_baslangic_secimi" id="secili_dersten_baslat" value="secili" class="form-check-input" onclick="$('#baslangic_ders_id').prop('disabled', false);">
                                        <label class="form-check-label" for="secili_dersten_baslat" onclick="$('#secili_dersten_baslat').prop('checked', true).trigger('click');">Seçili dersten başlat</label>
                                        <small class="d-block text-muted ms-4">Üye sadece seçilen dersten itibaren görüntüleyebilecek</small>
                                    </div>
                                    <div class="mb-0">
                                        <select name="baslangic_ders_id" id="baslangic_ders_id" class="form-control" disabled>
                                            <option value="">Ders seçin</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="mb-3 alert alert-warning">
                                    <div class="form-check">
                                        <input type="checkbox" name="onceki_grup_videolari" id="duzenle_onceki_grup_videolari" class="form-check-input">
                                        <label class="form-check-label text-danger" for="duzenle_onceki_grup_videolari">Üyenin Önceki Dönem ve Sessionlara Ait Tüm Kayıtlarını Sil.</label>
                                        <div>
                                            <small class="text-muted">Bu seçeneği işaretlerseniz bu üyeye ait tüm ders kayıtları silinecektir. Bu seçenek işaretlenirken bir dönem veya grup seçili ise oraya yeni kaydı yapılacaktır.</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Kredi alanı -->
                                <div class="mb-3 kredi-container" style="display: none;">
                                    <label class="form-label">Dönem Kredisi</label>
                                    <input type="number" name="donem_kredi" id="duzenle_donem_kredi" class="form-control" min="0" value="">
                                    <small class="text-muted d-block mt-2">
                                        <i class="ri-information-line me-1"></i>
                                        Bu dönem için kullanıcının kredi miktarı
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" id="uyeDuzenleSubmitBtn" class="btn btn-primary">Güncelle</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Üye Grupları Modal -->
<div class="modal fade" id="uyeGruplariModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Üye Grupları</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Dönem</th>
                                <th>Grup</th>
                                <th>Satın Alma Tarihi</th>
                                <th>Durum</th>
                            </tr>
                        </thead>
                        <tbody id="uyeGruplariListesi">
                            <tr>
                                <td colspan="4" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Yükleniyor...</span>
                                    </div>
                                    <p class="mt-2">Yükleniyor...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // TC Kimlik alanlarına sadece sayısal değer girilebilmesi için
        $('input[name="tc_kimlik"]').on('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        // Dönem Ayarları butonları için click event'leri
        $('#yeniDonemAyarlariBtn').on('click', function() {
            $('#yeniDonemAyarlariContainer').slideToggle(300, function() {
                // Toggle tamamlandıktan sonra görünürlük durumunu kontrol et
                const isVisible = $('#yeniDonemAyarlariContainer').is(':visible');
                $('#yeni_donem_ayar_yapiliyor').val(isVisible ? '1' : '0');
            });
            
            const isVisible = $('#yeniDonemAyarlariContainer').is(':visible');
            $(this).toggleClass('btn-outline-primary btn-primary');
            
            if (!isVisible) {
                $(this).html('<i class="ri-close-line me-1"></i> Dönem Ayarlarını Gizle');
            } else {
                $(this).html('<i class="ri-settings-3-line me-1"></i> Dönem Ayarları');
                // Dönem ayarlarını gizlerken değeri 0 yap
                $('#yeni_donem_ayar_yapiliyor').val('0');
            }
        });
        
        $('#duzenleDonemAyarlariBtn').on('click', function() {
            $('#duzenleDonemAyarlariContainer').slideToggle(300, function() {
                // Toggle tamamlandıktan sonra görünürlük durumunu kontrol et
                const isVisible = $('#duzenleDonemAyarlariContainer').is(':visible');
                $('#duzenle_donem_ayar_yapiliyor').val(isVisible ? '1' : '0');
            });
            
            const isVisible = $('#duzenleDonemAyarlariContainer').is(':visible');
            $(this).toggleClass('btn-outline-primary btn-primary');
            
            if (!isVisible) {
                $(this).html('<i class="ri-close-line me-1"></i> Dönem Ayarlarını Gizle');
            } else {
                $(this).html('<i class="ri-settings-3-line me-1"></i> Dönem Ayarları');
                // Dönem ayarlarını gizlerken değeri 0 yap
                $('#duzenle_donem_ayar_yapiliyor').val('0');
            }
        });

        let currentPage = 1;
        let currentLimit = 10;
        let currentSort = 'id';
        let currentSortDirection = 'DESC';

        function uyeleriGetir(sayfa) {
            currentPage = sayfa;

            Swal.fire({
                title: 'Lütfen Bekleyiniz',
                text: 'Üye listesi yükleniyor...',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: 'islemler/uye_listesi_getir.php',
                type: 'POST',
                data: {
                    sayfa: sayfa,
                    limit: currentLimit,
                    filtre: $('#filtre').val(),
                    durum: $('#durum').val(),
                    ucretsiz_uye: $('#ucretsiz_uye').val(),
                    uye_tipi: $('#uye_tipi').val(),
                    donem_id: $('#donem_id').val(),
                    sozlesme_onay: $('#sozlesme_onay').val(),
                    siralama: currentSort,
                    siralama_yonu: currentSortDirection,
                    decrypt_tc: true // TC Kimlik numaralarını çözmek için parametre eklendi
                },
                dataType: 'json',
                success: function(response) {
                    Swal.close();
                    if (response.success) {
                        let html = '';
                        response.uyeler.forEach(function(row) {
                            html += `<tr>
                            <td>
                                <div class='d-flex align-items-center'>
                                    <div class='avatar avatar-lg me-4'>
                                        ${row.fotograf ? 
                                            `<img src='/${row.fotograf}' class='' alt='${row.ad} ${row.soyad}'>` :
                                            `<div class='avatar-title rounded-circle bg-soft-primary'>
                                                ${row.ad.charAt(0).toUpperCase()}
                                            </div>`
                                        }
                                    </div>
                                    <div>
                                        <h6 class='mb-0'>${row.ad} ${row.soyad}</h6>
                                        <small class='text-muted'>${row.kayit_tarihi}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class='text-muted'>
                                    ${row.tc_kimlik}
                                </div>
                            </td>
                            <td>
                                <div class='text-muted'>
                                    <div><i class='ri-mail-line me-1'></i>${row.email}</div>
                                    <div><i class='ri-phone-line me-1'></i>${row.telefon}</div>
                                </div>
                            </td>
                            <td>
                                ${row.kredi && row.kredi !== '0 Kredi' ? 
                                    row.kredi.split(', ').map(kredi => `<div class="mb-1"><span class='bg-soft-info text-info px-2 py-1 rounded'>${kredi}</span></div>`).join('') : 
                                    `<span class='text-muted'>0 Kredi</span>`
                                }
                            </td>
                            <td>
                                ${row.aktif_gruplar && row.aktif_gruplar.length > 0 ? 
                                    `<div>
                                        ${row.aktif_gruplar.map(grup => 
                                            `<div class='mb-2'>
                                                <div class='text-muted'>${grup.donem_adi || ''}</div>
                                                <div class='bg-soft-primary text-primary'>${grup.grup_adi}</div>
                                            </div>`
                                        ).join('')}
                                    </div>` : 
                                    '-'
                                }
                            </td>
                            <td>
                                ${row.durum == 1 ? 
                                    '<span class="bg-soft-success text-success">Aktif</span>' : 
                                    row.durum == 0 ?
                                    '<span class="bg-soft-warning text-warning">Ödeme Yapılması Bekleniyor</span>' :
                                    row.durum == 2 ?
                                    '<span class="bg-soft-info text-info">Havale Bekleniyor</span>' :
                                    row.durum == 4 ?
                                    '<span class="bg-soft-primary text-primary">Dondurulmuş Üye</span>' :
                                    '<span class="bg-soft-danger text-danger">Pasif</span>'
                                }
                                <div class="mt-1">
                                    ${row.uye_tipi == 1 ?
                                        '<span class="bg-soft-secondary text-secondary">Burslu</span>' :
                                        '<span class="bg-soft-dark text-dark">Normal</span>'
                                    }
                                </div>
                            </td>
                            <td>
                                ${row.sozlesme_onay == 1 ? 
                                    '<span class="bg-soft-success text-success">Onayladı</span>' : 
                                    '<span class="bg-soft-danger text-danger">Onaylamadı</span>'
                                }
                            </td>
                            <td>
                                <div class='text-muted'>
                                    ${row.son_giris || '-'}
                                </div>
                            </td>
                            <td>
                                <div class='d-flex gap-1 justify-content-end'>
                                <a href='uye_detay.php?id=${row.id}'>
                                   <button class='btn btn-md btn-soft-danger' data-id='${row.id}'>
                                        İncele
                                    </button>
                                    </a>
                                    <button class='btn btn-md btn-soft-info uye-gruplari' data-id='${row.id}'>
                                        <i class='ri-team-line'></i> Gruplar
                                    </button>
                                    <button class='btn btn-md btn-soft-primary uye-duzenle' 
                                            data-id='${row.id}'
                                            data-ad='${row.ad}'
                                            data-soyad='${row.soyad}'
                                            data-tc-kimlik='${row.tc_kimlik}'
                                            data-email='${row.email}'
                                            data-telefon='${row.telefon}'
                                            data-kredi='${row.kredi}'
                                            data-durum='${row.durum}'
                                            data-ucretsiz-uye='${row.ucretsiz_uye}'
                                            data-dogrulama-durumu='${row.dogrulama_durumu}'
                                            data-aktif-gruplar='${JSON.stringify(row.aktif_gruplar || []).replace(/'/g, "&#39;")}'
                                            data-uye-tipi='${row.uye_tipi}'>
                                        <i class='ri-edit-line'></i> Düzenle
                                    </button>
                                    <button class='btn btn-md btn-soft-danger uye-sil' data-id='${row.id}'>
                                        <i class='ri-delete-bin-line'></i> Sil
                                    </button>
                                </div>
                            </td>
                        </tr>`;
                        });

                        $('#uyeListesi').html(html);
                        $('#uyeSayisi').text(`Toplam ${response.toplam_kayit} üye`);

                        // Sayfalama butonlarını oluştur
                        let sayfalamaHtml = '';
                        const toplamSayfa = Math.ceil(response.toplam_kayit / currentLimit);

                        if (toplamSayfa > 1) {
                            // Önceki sayfa butonu
                            sayfalamaHtml += `<button type="button" class="btn btn-sm ${sayfa == 1 ? 'btn-secondary disabled' : 'btn-primary'}" 
                                                 data-sayfa="${sayfa - 1}"
                                                 ${sayfa == 1 ? 'disabled' : ''}>
                                            <i class="ri-arrow-left-s-line"></i>
                                        </button>`;

                            // İlk sayfa
                            if (sayfa > 3) {
                                sayfalamaHtml += `<button type="button" class="btn btn-sm btn-secondary" data-sayfa="1">1</button>`;
                                if (sayfa > 4) {
                                    sayfalamaHtml += `<span class="btn btn-sm btn-secondary disabled">...</span>`;
                                }
                            }

                            // Aktif sayfanın etrafındaki sayfalar
                            for (let i = Math.max(1, sayfa - 2); i <= Math.min(toplamSayfa, sayfa + 2); i++) {
                                sayfalamaHtml += `<button type="button" class="btn btn-sm ${i == sayfa ? 'btn-primary active' : 'btn-secondary'}" 
                                                 data-sayfa="${i}">
                                                ${i}
                                            </button>`;
                            }

                            // Son sayfa
                            if (sayfa < toplamSayfa - 2) {
                                if (sayfa < toplamSayfa - 3) {
                                    sayfalamaHtml += `<span class="btn btn-sm btn-secondary disabled">...</span>`;
                                }
                                sayfalamaHtml += `<button type="button" class="btn btn-sm btn-secondary" data-sayfa="${toplamSayfa}">${toplamSayfa}</button>`;
                            }

                            // Sonraki sayfa butonu
                            sayfalamaHtml += `<button type="button" class="btn btn-sm ${sayfa == toplamSayfa ? 'btn-secondary disabled' : 'btn-primary'}" 
                                                 data-sayfa="${sayfa + 1}"
                                                 ${sayfa == toplamSayfa ? 'disabled' : ''}>
                                            <i class="ri-arrow-right-s-line"></i>
                                        </button>`;
                        }

                        $('#sayfalamaButtons').html(sayfalamaHtml);

                        // Sayfalama butonlarına tıklama eventi ekle
                        $('#sayfalamaButtons button').on('click', function() {
                            if (!$(this).hasClass('disabled')) {
                                const yeniSayfa = $(this).data('sayfa');
                                if (yeniSayfa !== currentPage) {
                                    uyeleriGetir(yeniSayfa);
                                }
                            }
                        });
                    }
                }
            });
        }

        // Filtreleme formu submit
        $('#filtreForm').on('submit', function(e) {
            e.preventDefault();
            currentPage = 1; // Filtreleme yapıldığında ilk sayfaya dön
            uyeleriGetir(1);
        });

        // Sayfa başına kayıt sayısı değiştiğinde
        $('#sayfa_basina').on('change', function() {
            currentLimit = $(this).val();
            currentPage = 1; // İlk sayfaya dön
            uyeleriGetir(1);
        });

        // Sıralama başlıklarına tıklandığında
        $('.siralama').on('click', function() {
            const yeniSiralama = $(this).data('siralama');

            if (currentSort === yeniSiralama) {
                // Aynı sütuna tıklandığında sıralama yönünü değiştir
                currentSortDirection = currentSortDirection === 'ASC' ? 'DESC' : 'ASC';
            } else {
                // Yeni sütuna tıklandığında varsayılan olarak artan sırala
                currentSort = yeniSiralama;
                currentSortDirection = 'ASC';
            }

            currentPage = 1; // İlk sayfaya dön
            uyeleriGetir(1);
        });

        // Sayfa yüklendiğinde ilk sayfayı getir
        uyeleriGetir(1);

        // Yeni üye formunu işle
        $('#yeniUyeForm').on('submit', function(e) {
            e.preventDefault();

            // Dönem ayarları yapılıyor mu kontrol et
            const donemAyarYapiliyor = $('#yeni_donem_ayar_yapiliyor').val() === '1';
            
            // Dönem ayarları yapılıyorsa session kontrollerini yap
            if (donemAyarYapiliyor) {
                // Seçili sessionlar var mı kontrol et
                const seciliSessionlar = $('input[name="yeni_grup_ids[]"]:checked');
                if (seciliSessionlar.length > 0 && !$('#yeni_mevcut_grup_id').val()) {
                    Swal.fire({
                        title: 'Uyarı!',
                        text: 'Session seçtiniz ancak mevcut session seçmediniz. Lütfen mevcut session seçiniz.',
                        icon: 'warning',
                        confirmButtonText: 'Tamam'
                    });
                    return;
                }

                // Ders başlangıç kontrolü
                if ($('#yeni_mevcut_grup_id').val() && $('#yeni_secili_dersten_baslat').prop('checked') && !$('#yeni_baslangic_ders_id').val()) {
                    Swal.fire({
                        title: 'Uyarı!',
                        text: 'Seçili dersten başlat seçeneğini işaretlediniz fakat ders seçmediniz. Lütfen ders seçin.',
                        icon: 'warning',
                        confirmButtonText: 'Tamam'
                    });
                    return;
                }
            }

            // Form verilerini al
            var formData = new FormData(this);

            // Dönem ayarları yapılıyorsa ilgili verileri ekle
            if (donemAyarYapiliyor) {
                // Checkbox'ları formdan al
                const seciliSessionlar = $('input[name="yeni_grup_ids[]"]:checked');
                seciliSessionlar.each(function() {
                    formData.append('yeni_grup_ids[]', $(this).val());
                });

                // Mevcut session ve seçenekleri ekle
                if (seciliSessionlar.length > 0) {
                    formData.append('mevcut_grup_id', $('#yeni_mevcut_grup_id').val());
                    formData.append('ders_baslangic_secimi', $('input[name="ders_baslangic_secimi"]:checked').val());
                    if ($('#yeni_secili_dersten_baslat').prop('checked') && $('#yeni_baslangic_ders_id').val()) {
                        formData.append('baslangic_ders_id', $('#yeni_baslangic_ders_id').val());
                    }
                }
            }

            // Butonu devre dışı bırak ve yükleniyor göster
            const saveButton = $(this).find('button[type="submit"]');
            const originalButtonText = saveButton.html();
            saveButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2"></span>Kaydediliyor...');

            // SweetAlert ile bekleyiniz mesajı göster
            Swal.fire({
                title: 'Lütfen Bekleyiniz',
                text: 'Kullanıcı kaydediliyor...',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            // AJAX isteği
            $.ajax({
                url: 'islemler/uye_ekle.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    // Butonu sıfırla
                    saveButton.prop('disabled', false).html(originalButtonText);

                    if (response.success) {
                        // Başarılı mesajı göster
                        Swal.fire({
                            title: 'Başarılı!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'Tamam'
                        }).then((result) => {
                            // Kısa bir gecikme ile işlemleri gerçekleştir
                            setTimeout(function() {
                                // Modalı kapat
                                $('#yeniUyeModal').modal('hide');
                                // Formu sıfırla
                                $('#yeniUyeForm')[0].reset();
                                // Session container'ları sıfırla
                                $('#yeni_grup_container').html('<div class="text-muted">Önce dönem seçin</div>');
                                $('.yeni-mevcut-session-container').hide();
                                $('.yeni-kredi-container').hide();
                                $('#yeni_ders_listesi_container').hide();
                                // Dönem ayarları container'ını gizle
                                $('#yeniDonemAyarlariContainer').hide();
                                $('#yeni_donem_ayar_yapiliyor').val('0');
                                $('#yeniDonemAyarlariBtn').removeClass('btn-primary').addClass('btn-outline-primary');
                                $('#yeniDonemAyarlariBtn').html('<i class="ri-settings-3-line me-1"></i> Dönem Ayarları');
                                // Sayfayı yenile
                                location.reload();
                            }, 100);
                        });
                    } else {
                        // Hata mesajı göster
                        Swal.fire({
                            title: 'Hata!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'Tamam'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    // Butonu sıfırla
                    saveButton.prop('disabled', false).html(originalButtonText);

                    // Hata mesajını çözümle
                    let errorMessage = 'Sunucudan veri alınamadı.';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response && response.message) errorMessage = response.message;
                    } catch (e) {
                        errorMessage = xhr.responseText || error || 'Bilinmeyen hata';
                    }

                    // Hata mesajı göster
                    Swal.fire({
                        title: 'Hata!',
                        text: errorMessage,
                        icon: 'error',
                        confirmButtonText: 'Tamam'
                    });
                }
            });
        });

        // Üye düzenleme modalını aç
        $(document).on('click', '.uye-duzenle', function() {
            var uye = $(this).data();
            console.log('Üye düzenle butonu tıklandı. Veriler:', uye);

            // Mevcut session ve başlangıç ders bilgileri için
            var uye_id = uye.id;
            
            // Aktif gruplar
            try {
                uye.aktifGruplar = JSON.parse(uye.aktifGruplar || '[]');
                console.log('Aktif gruplar:', uye.aktifGruplar);
            } catch (e) {
                console.error('Aktif gruplar parse hatası:', e);
                uye.aktifGruplar = [];
            }

            $('#duzenle_id').val(uye_id);
            $('#duzenle_ad').val(uye.ad);
            $('#duzenle_soyad').val(uye.soyad);
            $('#duzenle_tc_kimlik').val(uye.tcKimlik);
            $('#duzenle_email').val(uye.email);
            $('#duzenle_telefon').val(uye.telefon);
            $('#duzenle_kredi').val(uye.kredi);
            $('#duzenle_durum').val(uye.durum);
            $('#duzenle_ucretsiz_uye').val(uye.ucretsizUye);
            $('#duzenle_dogrulama_durumu').val(uye.dogrulamaDurumu);
            $('#duzenle_uye_tipi').val(uye.uyeTipi);

            // Kredi alanını gizle
            $('[name="kredi"]').closest('.mb-3').hide();

            // Modal'ı göster - önce modalı gösteriyoruz sonra AJAX ile içeriğini güncelleyeceğiz
            $('#uyeDuzenleModal').modal('show');

            // SweetAlert ile bekleyiniz mesajı göster
            Swal.fire({
                title: 'Lütfen Bekleyiniz',
                text: 'Üye bilgileri yükleniyor...',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            // Önce üyenin mevcut_grup=1 olan grup ve dönem bilgisini al
            $.ajax({
                url: 'islemler/uye_gruplari_getir.php',
                type: 'POST',
                data: {
                    uye_id: uye_id,
                    detayli: 1, // Detaylı bilgi için
                    only_active: 1 // Sadece aktif session
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Üye aktif session bilgileri:', response);

                    if (response.success && response.gruplar && response.gruplar.length > 0) {
                        // Aktif dönem bilgisini al - her dönem için bir aktif grup olabilir
                        var donemVar = false;
                        
                        // Aktif grupları dönemlere göre gruplandır
                        var donemGruplari = {};
                        var ilkDonemId = null;
                        
                        for (var i = 0; i < response.gruplar.length; i++) {
                            var grup = response.gruplar[i];
                            var donemId = grup.donem_id;
                            
                            if (!donemGruplari[donemId]) {
                                donemGruplari[donemId] = [];
                                if (ilkDonemId === null) {
                                    ilkDonemId = donemId;
                                }
                            }
                            
                            donemGruplari[donemId].push(grup);
                            donemVar = true;
                        }
                        
                        // İlk dönem varsa o dönemin bilgilerini kullan
                        if (ilkDonemId !== null) {
                            var aktifDonemId = ilkDonemId;
                            
                            // Dönem seçimini yap
                            $('#duzenle_donem_id').val(aktifDonemId).trigger('change');
                            
                            // Bu dönem'e ait tüm grupları getir ve checkbox'ları işaretle
                            gruplariGetir(aktifDonemId, 'duzenle_grup_container', function() {
                                console.log('Gruplar getirildi, şimdi seçim yapılacak');
                                
                                // Üyenin tüm sessionlarını al 
                                $.ajax({
                                    url: 'islemler/uye_gruplari_getir.php',
                                    type: 'POST',
                                    data: {
                                        uye_id: uye_id,
                                        detayli: 1
                                    },
                                    dataType: 'json',
                                    success: function(sessionResponse) {
                                        if (sessionResponse.success && sessionResponse.gruplar) {
                                            var uyeninGruplari = sessionResponse.gruplar.map(function(grup) {
                                                return grup.grup_id;
                                            });

                                            console.log('Üyenin tüm grupları:', uyeninGruplari);

                                            // Checkbox'ları işaretle
                                            uyeninGruplari.forEach(function(grupId) {
                                                $(`#grup_${grupId}`).prop('checked', true);
                                            });

                                            // Mevcut session seçimini güncelle
                                            updateMevcutSessionSelect();
                                            toggleMevcutSessionContainer();
                                            
                                            // İlk dönemin ilk grubunu seç (varsayılan olarak)
                                            if (donemGruplari[aktifDonemId] && donemGruplari[aktifDonemId].length > 0) {
                                                var ilkGrup = donemGruplari[aktifDonemId][0];
                                                var ilkGrupId = ilkGrup.grup_id;
                                                var baslangicDersId = ilkGrup.baslangic_ders_id || null;
                                                
                                                setTimeout(function() {
                                                    $('#duzenle_mevcut_grup_id').val(ilkGrupId).trigger('change');
                                                    
                                                    // Eğer başlangıç dersi varsa ilgili alanları doldur
                                                    if (baslangicDersId) {
                                                        setTimeout(function() {
                                                            $('#secili_dersten_baslat').prop('checked', true).trigger('change');
                                                            $('#baslangic_ders_id').prop('disabled', false);

                                                            // Ders listesi yüklendikten sonra başlangıç dersini seç
                                                            setTimeout(function() {
                                                                $('#baslangic_ders_id').val(baslangicDersId);
                                                                // Tüm işlemler tamamlandığında SweetAlert'i kapat
                                                                Swal.close();
                                                            }, 500);
                                                        }, 300);
                                                    } else {
                                                        // Başlangıç dersi yoksa SweetAlert'i kapat
                                                        Swal.close();
                                                    }
                                                }, 300);
                                            } else {
                                                // Mevcut dönem grubu yoksa SweetAlert'i kapat
                                                Swal.close();
                                            }
                                        } else {
                                            // Session bilgisi alınamadığında SweetAlert'i kapat
                                            Swal.close();
                                        }
                                    },
                                    error: function(xhr, status, error) {
                                        console.error('Üye session bilgileri alınamadı:', error);
                                        // Hata durumunda SweetAlert'i kapat ve hata göster
                                        Swal.fire({
                                            title: 'Hata!',
                                            text: 'Üye session bilgileri alınamadı: ' + error,
                                            icon: 'error',
                                            confirmButtonText: 'Tamam'
                                        });
                                    }
                                });
                            });
                        } else {
                            console.log('Üyenin aktif dönem bilgisi bulunamadı');
                            // Aktif dönem bilgisi bulunamadığında SweetAlert'i kapat
                            Swal.close();
                        }
                    } else {
                        console.log('Üyenin aktif session bilgisi bulunamadı');
                        // Aktif session bilgisi bulunamadığında SweetAlert'i kapat
                        Swal.close();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Üye aktif session bilgileri alınamadı:', error);
                    // Hata durumunda SweetAlert'i kapat ve hata göster
                    Swal.fire({
                        title: 'Hata!',
                        text: 'Üye aktif session bilgileri alınamadı: ' + error,
                        icon: 'error',
                        confirmButtonText: 'Tamam'
                    });
                }
            });
        });

        // Mevcut session select değiştiğinde
        $(document).on('change', '#duzenle_mevcut_grup_id', function() {
            const grupId = $(this).val();
            if (grupId) {
                getDersListesi(grupId);
            } else {
                $('#ders_listesi_container').hide();
            }
        });

        // Ders başlangıç seçimi değiştiğinde
        $(document).on('change', '#ilk_dersten_baslat, #secili_dersten_baslat', function() {
            const secim = $('input[name="ders_baslangic_secimi"]:checked').val();
            if (secim === 'secili') {
                $('#baslangic_ders_id').prop('disabled', false);
            } else {
                $('#baslangic_ders_id').prop('disabled', true);
            }
        });

        // Ders listesini getir
        function getDersListesi(grupId, tumDersler = false) {
            $('#ders_listesi_container').show();

            // SweetAlert ile bekleyiniz mesajı göster
            Swal.fire({
                title: 'Lütfen Bekleyiniz',
                text: 'Ders listesi yükleniyor...',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: 'islemler/grup_dersleri_getir.php',
                type: 'POST',
                data: {
                    grup_id: grupId,
                    tum_dersler: 1 // Her zaman tüm dersleri getir
                },
                dataType: 'json',
                success: function(response) {
                    // SweetAlert'i kapat
                    Swal.close();

                    if (response.success) {
                        if (response.dersler.length > 0) {
                            // Başlangıç ders seçimini doldur
                            const baslangicSelect = $('#baslangic_ders_id');
                            baslangicSelect.empty().append('<option value="">Ders seçin</option>');

                            response.dersler.forEach(function(ders) {
                                // Tarihi formatla: gün.ay.yıl saat:dakika
                                let tarih = '';
                                if (ders.ders_tarihi) {
                                    const tarihObj = new Date(ders.ders_tarihi);
                                    if (!isNaN(tarihObj.getTime())) {
                                        const gun = String(tarihObj.getDate()).padStart(2, '0');
                                        const ay = String(tarihObj.getMonth() + 1).padStart(2, '0');
                                        const yil = tarihObj.getFullYear();
                                        const saat = String(tarihObj.getHours()).padStart(2, '0');
                                        const dakika = String(tarihObj.getMinutes()).padStart(2, '0');
                                        tarih = `${gun}.${ay}.${yil} ${saat}:${dakika}`;
                                    } else {
                                        tarih = ders.ders_tarihi;
                                    }
                                } else {
                                    tarih = 'Tarih belirtilmemiş';
                                }

                                baslangicSelect.append(`<option value="${ders.id}">${ders.ders_no}. Ders - ${ders.ders_adi} - ${tarih}</option>`);
                            });
                        } else {
                            $('#ders_listesi_container').hide();
                        }
                    } else {
                        $('#ders_listesi_container').hide();
                    }
                },
                error: function() {
                    $('#ders_listesi_container').hide();
                }
            });
        }

        // Modal kapatılmaya çalışıldığında kontrolü
        // $('#uyeDuzenleModal').on('hide.bs.modal', function(e) {
        //     // Eğer form gönderimi sırasında modal kapatılmaya çalışılıyorsa engelle
        //     if (Swal.isLoading()) {
        //         e.preventDefault();
        //         return false;
        //     }
        // });

        // Üye düzenleme
        $('#uyeDuzenleForm').on('submit', function(e) {
            e.preventDefault();
            submitUyeDuzenleForm();
        });

        // Güncelle butonuna tıklanınca da formu gönder
        $(document).on('click', '#uyeDuzenleSubmitBtn', function(e) {
            e.preventDefault();
            submitUyeDuzenleForm();
        });

        // Üye düzenleme formunu gönderen fonksiyon
        function submitUyeDuzenleForm() {
            console.log('Form gönderiliyor...');

            // Form verilerini al
            var formData = $('#uyeDuzenleForm').serialize();
            console.log('Form verileri:', formData);
            
            // Dönem ayarları yapılıyor mu kontrol et
            const donemAyarYapiliyor = $('#duzenle_donem_ayar_yapiliyor').val() === '1';
            
            // Eğer dönem ayarları yapılmıyorsa, dönem_id parametresini kaldır
            if (!donemAyarYapiliyor) {
                formData = formData.replace(/&donem_id=[^&]*/, '');
            }

            Swal.fire({
                title: 'Lütfen Bekleyiniz',
                text: 'Üye güncelleniyor...',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: 'islemler/uye_duzenle.php',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    Swal.close();
                    console.log('Sunucu yanıtı:', response);
                    if (response.success) {
                        Swal.fire({
                            title: 'Başarılı!',
                            text: 'Üye başarıyla güncellendi.',
                            icon: 'success',
                            confirmButtonText: 'Tamam'
                        }).then((result) => {
                            // Kısa bir gecikme ile modalı kapat
                            setTimeout(function() {
                                // Dönem ayarları container'ını gizle ve sıfırla
                                $('#duzenleDonemAyarlariContainer').hide();
                                $('#duzenle_donem_ayar_yapiliyor').val('0');
                                $('#duzenleDonemAyarlariBtn').removeClass('btn-primary').addClass('btn-outline-primary');
                                $('#duzenleDonemAyarlariBtn').html('<i class="ri-settings-3-line me-1"></i> Dönem Ayarları');
                                
                                // Modalı kapat
                                $('#uyeDuzenleModal').modal('hide');
                                uyeleriGetir(currentPage);
                            }, 100);
                        });
                    } else {
                        Swal.fire({
                            title: 'Hata!',
                            text: response.message || 'Bir hata oluştu.',
                            icon: 'error',
                            confirmButtonText: 'Tamam'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    Swal.close();
                    console.error('AJAX hatası:', xhr.responseText);
                    Swal.fire({
                        title: 'Hata!',
                        text: 'Sunucu isteği başarısız: ' + error,
                        icon: 'error',
                        confirmButtonText: 'Tamam'
                    });
                }
            });
        }

        // Üye silme
        $(document).on('click', '.uye-sil', function() {
            var uye_id = $(this).data('id');
            Swal.fire({
                title: 'Üye Silmek İstiyor musunuz?',
                text: "Üyeye dair tüm ilişkili kayıtlar da silinecektir. (Bulunduğu gruplar, kayıtlı içerikleri vs) Geri dönüşü yoktur. Kabul ediyor musunuz?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Evet, sil!',
                cancelButtonText: 'İptal'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'Lütfen Bekleyiniz',
                        text: 'Üye siliniyor...',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        willOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    $.ajax({
                        url: 'islemler/uye_sil.php',
                        type: 'POST',
                        data: {
                            uye_id: uye_id
                        },
                        dataType: 'json',
                        success: function(response) {
                            Swal.close();
                            if (response.success) {
                                Swal.fire({
                                    title: 'Silindi!',
                                    text: 'Üye başarıyla silindi.',
                                    icon: 'success',
                                    confirmButtonText: 'Tamam'
                                }).then((result) => {
                                    uyeleriGetir(currentPage);
                                });
                            } else {
                                Swal.fire({
                                    title: 'Hata!',
                                    text: response.message || 'Bir hata oluştu.',
                                    icon: 'error',
                                    confirmButtonText: 'Tamam'
                                });
                            }
                        }
                    });
                }
            });
        });

        // Üye gruplarını getir
        $(document).on('click', '.uye-gruplari', function() {
            var uye_id = $(this).data('id');

            Swal.fire({
                title: 'Lütfen Bekleyiniz',
                text: 'Üye grupları yükleniyor...',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: 'islemler/uye_gruplari_getir.php',
                type: 'POST',
                data: {
                    uye_id: uye_id
                },
                dataType: 'json',
                success: function(response) {
                    Swal.close();
                    // Kısa bir gecikme ile modalı göster
                    setTimeout(function() {
                        $('#uyeGruplariModal').modal('show');
                    }, 100);
                    if (response.success) {
                        let html = '';
                        response.gruplar.forEach(function(grup) {
                            html += `<tr>
                                <td>${grup.donem_adi}</td>
                                <td>${grup.grup_adi}</td>
                                <td>${grup.satin_alma_tarihi}</td>
                                <td>
                                    ${grup.mevcut_grup == 1 ? 
                                        '<span class="bg-soft-success text-success">Mevcut Grubu</span>' : 
                                        '<span class="bg-soft-secondary text-warning">Geçmiş Grubu</span>'
                                    }
                                </td>
                            </tr>`;
                        });
                        $('#uyeGruplariListesi').html(html);
                    } else {
                        Swal.fire({
                            title: 'Hata!',
                            text: response.message || 'Bir hata oluştu.',
                            icon: 'error',
                            confirmButtonText: 'Tamam'
                        });
                    }
                }
            });
        });

        // Session checkbox'ları için event listener
        $(document).on('change', 'input[name="grup_ids[]"]', function() {
            updateMevcutSessionSelect();
            toggleMevcutSessionContainer();

            // Checkbox'ın durumuna göre session-checkbox div'inin sınıfını güncelle
            const checkboxDiv = $(this).closest('.session-checkbox');
            if ($(this).prop('checked')) {
                checkboxDiv.addClass('active');
            } else {
                checkboxDiv.removeClass('active');
            }
        });

        // Session checkbox kutusunun tamamına tıklandığında checkbox'ı seç
        $(document).on('click', '.session-checkbox', function(e) {
            // Eğer doğrudan checkbox veya label'a tıklanmadıysa
            if (!$(e.target).is('input[type="checkbox"]') && !$(e.target).is('label')) {
                const checkbox = $(this).find('input[type="checkbox"]');
                // Checkbox'ın durumunu tersine çevir
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });

        // Mevcut session seçimini güncelle
        function updateMevcutSessionSelect() {
            const seciliSessionlar = $('input[name="grup_ids[]"]:checked');
            const mevcutSessionSelect = $('#duzenle_mevcut_grup_id');

            // Önce select'i temizle
            mevcutSessionSelect.empty().append('<option value="">Mevcut session seçin</option>');

            if (seciliSessionlar.length > 0) {
                // Seçili session'ları select'e ekle
                seciliSessionlar.each(function() {
                    const grupId = $(this).val();
                    const grupAdi = $(this).closest('.form-check').find('label').text().trim();
                    mevcutSessionSelect.append(`<option value="${grupId}">${grupAdi}</option>`);
                });

                // İlk session'ı seç (varsayılan olarak)
                if (seciliSessionlar.length === 1) {
                    mevcutSessionSelect.val(seciliSessionlar.first().val()).trigger('change');
                }
            }
        }

        // Mevcut session container'ını göster/gizle
        function toggleMevcutSessionContainer() {
            const seciliSessionlar = $('input[name="grup_ids[]"]:checked');

            if (seciliSessionlar.length > 0) {
                $('.mevcut-session-container').show();
            } else {
                $('.mevcut-session-container').hide();
                $('#ders_listesi_container').hide();
            }
        }

        // Yeni üye için ders listesini getir
        function getYeniDersListesi(grupId, tumDersler = false) {
            $('#yeni_ders_listesi_container').show();

            // SweetAlert ile bekleyiniz mesajı göster
            Swal.fire({
                title: 'Lütfen Bekleyiniz',
                text: 'Ders listesi yükleniyor...',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: 'islemler/grup_dersleri_getir.php',
                type: 'POST',
                data: {
                    grup_id: grupId,
                    tum_dersler: 1 // Her zaman tüm dersleri getir
                },
                dataType: 'json',
                success: function(response) {
                    // SweetAlert'i kapat
                    Swal.close();

                    if (response.success) {
                        if (response.dersler.length > 0) {
                            // Başlangıç ders seçimini doldur
                            const baslangicSelect = $('#yeni_baslangic_ders_id');
                            baslangicSelect.empty().append('<option value="">Ders seçin</option>');

                            response.dersler.forEach(function(ders) {
                                // Tarihi formatla: gün.ay.yıl saat:dakika
                                let tarih = '';
                                if (ders.ders_tarihi) {
                                    const tarihObj = new Date(ders.ders_tarihi);
                                    if (!isNaN(tarihObj.getTime())) {
                                        const gun = String(tarihObj.getDate()).padStart(2, '0');
                                        const ay = String(tarihObj.getMonth() + 1).padStart(2, '0');
                                        const yil = tarihObj.getFullYear();
                                        const saat = String(tarihObj.getHours()).padStart(2, '0');
                                        const dakika = String(tarihObj.getMinutes()).padStart(2, '0');
                                        tarih = `${gun}.${ay}.${yil} ${saat}:${dakika}`;
                                    } else {
                                        tarih = ders.ders_tarihi;
                                    }
                                } else {
                                    tarih = 'Tarih belirtilmemiş';
                                }

                                baslangicSelect.append(`<option value="${ders.id}">${ders.ders_no}. Ders - ${ders.ders_adi} - ${tarih}</option>`);
                            });
                        } else {
                            $('#yeni_ders_listesi_container').hide();
                        }
                    } else {
                        $('#yeni_ders_listesi_container').hide();
                    }
                },
                error: function() {
                    $('#yeni_ders_listesi_container').hide();
                }
            });
        }

        // Yeni üye için mevcut session container'ını göster/gizle
        function toggleYeniMevcutSessionContainer() {
            const seciliSessionlar = $('input[name="yeni_grup_ids[]"]:checked');

            if (seciliSessionlar.length > 0) {
                $('.yeni-mevcut-session-container').show();
            } else {
                $('.yeni-mevcut-session-container').hide();
                $('#yeni_ders_listesi_container').hide();
            }
        }

        // Yeni üye için grupları getir
        function gruplariGetirYeni(donemId, containerId, callback = null) {
            console.log('gruplariGetirYeni çağrıldı', {
                donemId,
                containerId
            });

            if (!donemId) {
                $(`#${containerId}`).html('<div class="text-muted">Önce dönem seçin</div>');
                return;
            }

            // SweetAlert mesajı (eğer zaten gösterilmiyorsa)
            if (!Swal.isVisible()) {
                Swal.fire({
                    title: 'Lütfen Bekleyiniz',
                    text: 'Session bilgileri yükleniyor...',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });
            }

            // Yükleniyor göstergesi
            $(`#${containerId}`).html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><div class="mt-2">Sessionlar yükleniyor...</div></div>');

            $.ajax({
                url: 'islemler/gruplari_getir.php',
                type: 'POST',
                data: {
                    donem_id: donemId
                },
                dataType: 'json',
                success: function(response) {
                    console.log('gruplari_getir.php yanıtı:', response);

                    // Sweet Alert mesajını her zaman kapat
                    Swal.close();

                    if (response.success) {
                        // Yeni tarzda checkbox container için (yeni üye ekleme)
                        let html = '';

                        if (response.gruplar.length === 0) {
                            html = '<div class="text-muted">Bu döneme ait session bulunamadı</div>';
                            console.log('Bu döneme ait session bulunamadı');
                        } else {
                            html = '<div class="row">';
                            console.log(`${response.gruplar.length} adet session bulundu`);

                            response.gruplar.forEach(function(grup) {
                                html += `
                                <div class="col-md-6 col-lg-4 mb-2">
                                    <div class="form-check session-checkbox">
                                        <input type="checkbox" name="yeni_grup_ids[]" value="${grup.id}" 
                                               id="yeni_grup_${grup.id}" class="form-check-input">
                                        <label class="form-check-label w-100" for="yeni_grup_${grup.id}">
                                            ${grup.grup_adi}
                                        </label>
                                    </div>
                                </div>`;
                            });

                            html += '</div>';
                        }

                        console.log('yeni_grup_container içeriği:', html);
                        $(`#${containerId}`).html(html);

                        if (callback) {
                            callback();
                        }
                    } else {
                        console.error('gruplari_getir.php hatası:', response.message);
                        $(`#${containerId}`).html('<div class="alert alert-danger p-2">Hata: Session bilgisi alınamadı. Hata: ' + response.message + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX hata:', {
                        xhr,
                        status,
                        error
                    });
                    let errorMessage = 'Sunucudan veri alınamadı.';

                    try {
                        // JSON hata mesajını çözümlemeye çalış
                        const response = JSON.parse(xhr.responseText);
                        if (response && response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        // JSON çözümlemesi başarısız olursa, ham hata mesajını kullan
                        errorMessage = xhr.responseText || error || 'Bilinmeyen hata';
                    }

                    // Hata mesajını göster
                    Swal.fire({
                        title: 'Hata!',
                        text: 'Session bilgileri alınamadı: ' + errorMessage,
                        icon: 'error',
                        confirmButtonText: 'Tamam'
                    });

                    $(`#${containerId}`).html(`<div class="alert alert-danger p-2">Hata: Session bilgisi alınamadı. Server: ${errorMessage}</div>`);
                }
            });
        }

        // Session checkbox'ları için event listener (yeni üye)
        $(document).on('change', 'input[name="yeni_grup_ids[]"]', function() {
            updateYeniMevcutSessionSelect();
            toggleYeniMevcutSessionContainer();

            // Checkbox'ın durumuna göre session-checkbox div'inin sınıfını güncelle
            const checkboxDiv = $(this).closest('.session-checkbox');
            if ($(this).prop('checked')) {
                checkboxDiv.addClass('active');
            } else {
                checkboxDiv.removeClass('active');
            }
        });

        // Yeni üye için mevcut session seçimini güncelle
        function updateYeniMevcutSessionSelect() {
            const seciliSessionlar = $('input[name="yeni_grup_ids[]"]:checked');
            const mevcutSessionSelect = $('#yeni_mevcut_grup_id');

            // Önce select'i temizle
            mevcutSessionSelect.empty().append('<option value="">Mevcut session seçin</option>');

            if (seciliSessionlar.length > 0) {
                // Seçili session'ları select'e ekle
                seciliSessionlar.each(function() {
                    const grupId = $(this).val();
                    const grupAdi = $(this).closest('.form-check').find('label').text().trim();
                    mevcutSessionSelect.append(`<option value="${grupId}">${grupAdi}</option>`);
                });

                // İlk session'ı seç (varsayılan olarak)
                if (seciliSessionlar.length === 1) {
                    mevcutSessionSelect.val(seciliSessionlar.first().val()).trigger('change');
                }
            }
        }

        // Yeni üye formunda dönem seçimi
        $('#yeni_donem_id').on('change', function() {
            const donemId = $(this).val();
            if (donemId) {
                // Kredi alanını göster
                $('.yeni-kredi-container').show();
                
                // Eğer SweetAlert gösterilmiyorsa göster
                if (!Swal.isVisible()) {
                    Swal.fire({
                        title: 'Lütfen Bekleyiniz',
                        text: 'Session bilgileri yükleniyor...',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        willOpen: () => {
                            Swal.showLoading();
                        }
                    });
                }

                gruplariGetirYeni(donemId, 'yeni_grup_container', function() {
                    // Callback sonrası seçili sessionları göster/gizle
                    toggleYeniMevcutSessionContainer();

                    // Eğer hala açıksa SweetAlert'i kapat
                    if (Swal.isVisible()) {
                        Swal.close();
                    }
                });
            } else {
                // Kredi alanını gizle
                $('.yeni-kredi-container').hide();
                
                $('#yeni_grup_container').html('<div class="text-muted">Önce dönem seçin</div>');
                $('.yeni-mevcut-session-container').hide();
                $('#yeni_ders_listesi_container').hide();
            }
        });

        // Dönem seçildiğinde grupları getir
        function gruplariGetir(donemId, containerId, callback = null) {
            console.log('gruplariGetir çağrıldı', {
                donemId,
                containerId
            });

            if (!donemId) {
                if (containerId === 'grupSelect') {
                    // Eski tarzda select input için
                    $(`#${containerId}`).prop('disabled', true).html('<option value="">Önce Dönem Seçin</option>');
                } else {
                    // Yeni tarzda checkbox container için
                    $(`#${containerId}`).html('<div class="text-muted">Önce dönem seçin</div>');
                }
                return;
            }

            const uyeId = $('#duzenle_id').val() || '';
            console.log('Üye ID:', uyeId);

            // SweetAlert mesajı (eğer zaten gösterilmiyorsa)
            if (!Swal.isVisible()) {
                Swal.fire({
                    title: 'Lütfen Bekleyiniz',
                    text: 'Session bilgileri yükleniyor...',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });
            }

            // Yükleniyor göstergesi
            if (containerId === 'grupSelect') {
                $(`#${containerId}`).prop('disabled', true).html('<option value="">Yükleniyor...</option>');
            } else {
                $(`#${containerId}`).html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><div class="mt-2">Sessionlar yükleniyor...</div></div>');
            }

            $.ajax({
                url: 'islemler/gruplari_getir.php',
                type: 'POST',
                data: {
                    donem_id: donemId,
                    uye_id: uyeId
                },
                dataType: 'json',
                success: function(response) {
                    console.log('gruplari_getir.php yanıtı:', response);

                    // Sweet Alert mesajını her zaman kapat
                    Swal.close();

                    if (response.success) {
                        if (containerId === 'grupSelect') {
                            // Eski tarzda select input için (yeni üye ekleme)
                            let html = '<option value="">Session Seçin</option>';
                            response.gruplar.forEach(function(grup) {
                                html += `<option value="${grup.id}">${grup.grup_adi}</option>`;
                            });
                            $(`#${containerId}`).prop('disabled', false).html(html);
                        } else {
                            // Yeni tarzda checkbox container için (üye düzenleme)
                            let html = '';

                            if (response.gruplar.length === 0) {
                                html = '<div class="text-muted">Bu döneme ait session bulunamadı</div>';
                                console.log('Bu döneme ait session bulunamadı');
                            } else {
                                html = '<div class="row">';
                                console.log(`${response.gruplar.length} adet session bulundu`);

                                response.gruplar.forEach(function(grup) {
                                    const isChecked = grup.uye_kayitli ? 'checked' : '';
                                    const activeClass = isChecked ? 'active' : '';

                                    html += `
                                    <div class="col-md-6 col-lg-4 mb-2">
                                        <div class="form-check session-checkbox ${activeClass}">
                                            <input type="checkbox" name="grup_ids[]" value="${grup.id}" 
                                                   id="grup_${grup.id}" class="form-check-input" ${isChecked}>
                                            <label class="form-check-label w-100" for="grup_${grup.id}">
                                                ${grup.grup_adi}
                                            </label>
                                        </div>
                                    </div>`;
                                });

                                html += '</div>';
                            }

                            console.log('duzenle_grup_container içeriği:', html);
                            $(`#${containerId}`).html(html);
                        }

                        if (callback) {
                            callback();
                        }
                    } else {
                        console.error('gruplari_getir.php hatası:', response.message);
                        if (containerId === 'grupSelect') {
                            $(`#${containerId}`).prop('disabled', true).html('<option value="">Hata: Session bilgisi alınamadı</option>');
                        } else {
                            $(`#${containerId}`).html('<div class="alert alert-danger p-2">Hata: Session bilgisi alınamadı. Hata: ' + response.message + '</div>');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX hata:', {
                        xhr,
                        status,
                        error
                    });
                    let errorMessage = 'Sunucudan veri alınamadı.';

                    try {
                        // JSON hata mesajını çözümlemeye çalış
                        const response = JSON.parse(xhr.responseText);
                        if (response && response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        // JSON çözümlemesi başarısız olursa, ham hata mesajını kullan
                        errorMessage = xhr.responseText || error || 'Bilinmeyen hata';
                    }

                    // Hata mesajını göster
                    Swal.fire({
                        title: 'Hata!',
                        text: 'Session bilgileri alınamadı: ' + errorMessage,
                        icon: 'error',
                        confirmButtonText: 'Tamam'
                    });

                    if (containerId === 'grupSelect') {
                        $(`#${containerId}`).prop('disabled', true).html('<option value="">Hata: Session bilgisi alınamadı</option>');
                    } else {
                        $(`#${containerId}`).html(`<div class="alert alert-danger p-2">Hata: Session bilgisi alınamadı. Server: ${errorMessage}</div>`);
                    }
                }
            });
        }

        // Düzenleme modalında dönem seçimi
        $('#duzenle_donem_id').on('change', function() {
            const donemId = $(this).val();
            if (donemId) {
                // Eğer SweetAlert gösterilmiyorsa göster
                if (!Swal.isVisible()) {
                    Swal.fire({
                        title: 'Lütfen Bekleyiniz',
                        text: 'Session bilgileri yükleniyor...',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        willOpen: () => {
                            Swal.showLoading();
                        }
                    });
                }

                gruplariGetir(donemId, 'duzenle_grup_container', function() {
                    // Callback sonrası seçili sessionları göster/gizle
                    toggleMevcutSessionContainer();

                    // Eğer hala açıksa SweetAlert'i kapat
                    if (Swal.isVisible()) {
                        Swal.close();
                    }
                });
            } else {
                $('#duzenle_grup_container').html('<div class="text-muted">Önce dönem seçin</div>');
                $('.mevcut-session-container').hide();
                $('#ders_listesi_container').hide();
            }
        });

        // Session checkbox'ları için event listener
        $(document).on('change', 'input[name="grup_ids[]"]', function() {
            updateMevcutSessionSelect();
            toggleMevcutSessionContainer();

            // Checkbox'ın durumuna göre session-checkbox div'inin sınıfını güncelle
            const checkboxDiv = $(this).closest('.session-checkbox');
            if ($(this).prop('checked')) {
                checkboxDiv.addClass('active');
            } else {
                checkboxDiv.removeClass('active');
            }
        });

        // Mevcut session seçimini güncelle
        function updateMevcutSessionSelect() {
            const seciliSessionlar = $('input[name="grup_ids[]"]:checked');
            const mevcutSessionSelect = $('#duzenle_mevcut_grup_id');

            // Önce select'i temizle
            mevcutSessionSelect.empty().append('<option value="">Mevcut session seçin</option>');

            if (seciliSessionlar.length > 0) {
                // Seçili session'ları select'e ekle
                seciliSessionlar.each(function() {
                    const grupId = $(this).val();
                    const grupAdi = $(this).closest('.form-check').find('label').text().trim();
                    mevcutSessionSelect.append(`<option value="${grupId}">${grupAdi}</option>`);
                });

                // İlk session'ı seç (varsayılan olarak)
                if (seciliSessionlar.length === 1) {
                    mevcutSessionSelect.val(seciliSessionlar.first().val()).trigger('change');
                }
            }
        }

        // Mevcut session container'ını göster/gizle
        function toggleMevcutSessionContainer() {
            const seciliSessionlar = $('input[name="grup_ids[]"]:checked');

            if (seciliSessionlar.length > 0) {
                $('.mevcut-session-container').show();
            } else {
                $('.mevcut-session-container').hide();
                $('#ders_listesi_container').hide();
            }
        }

        // Yeni üye formunda mevcut session değiştiğinde
        $(document).on('change', '#yeni_mevcut_grup_id', function() {
            const grupId = $(this).val();
            if (grupId) {
                getYeniDersListesi(grupId);
            } else {
                $('#yeni_ders_listesi_container').hide();
            }
        });

        // Ders başlangıç seçimi değiştiğinde (yeni üye)
        $(document).on('change', '#yeni_ilk_dersten_baslat, #yeni_secili_dersten_baslat', function() {
            const secim = $('input[name="ders_baslangic_secimi"]:checked').val();
            if (secim === 'secili') {
                $('#yeni_baslangic_ders_id').prop('disabled', false);
            } else {
                $('#yeni_baslangic_ders_id').prop('disabled', true);
            }
        });

        // SweetAlert2 kapandıktan sonra modalları güvenli şekilde kapatmak için global düzenlemeler
        $(document).ready(function() {
            // SweetAlert2'nin kapanma olayını dinle
            $(document).on('click', '.swal2-confirm, .swal2-cancel, .swal2-close', function() {
                // SweetAlert kapandıktan sonra herhangi bir modal işlemi yapmadan önce kısa bir süre bekle
                setTimeout(function() {
                    // Modal işlemlerini güvenli bir şekilde yap
                }, 100);
            });
        });

        function filtrele() {
            var isim = $('#filtre').val();
            var durum = $('#durum').val();
            var uyelik_tipi = $('#ucretsiz_uye').val();
            var donem_id = $('#donem_id').val();
            var etiket = $('#etiket').val();
            var mezun = $('#mezun').is(":checked") ? 1 : 0;
            
            var hic_kaydi_olmayanlar = 0;
            if (donem_id === "hicbiri") {
                hic_kaydi_olmayanlar = 1;
                donem_id = "";
            }

            showLoader();
            $.post("islemler/uye_listesi_getir.php", {
                isim: isim,
                durum: durum,
                uyelik_tipi: uyelik_tipi,
                donem_id: donem_id,
                etiket: etiket,
                mezun: mezun,
                hic_kaydi_olmayanlar: hic_kaydi_olmayanlar
            }, function (response) {
                $('#uyeler_listesi').html(response);
                hideLoader();
            });
        }

        // Dönem ve session seçildiğinde kredi alanını göster
        $(document).on('change', '#duzenle_donem_id, #duzenle_mevcut_grup_id', function() {
            const donemId = $('#duzenle_donem_id').val();
            const grupId = $('#duzenle_mevcut_grup_id').val();
            
            if (donemId && grupId) {
                // Kredi alanını göster
                $('.kredi-container').show();
                
                // Mevcut kredi değerini getir
                $.ajax({
                    url: 'islemler/donem_kredi_getir.php',
                    type: 'POST',
                    data: {
                        uye_id: $('#duzenle_id').val(),
                        donem_id: donemId,
                        grup_id: grupId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            $('#duzenle_donem_kredi').val(response.kredi || 0);
                        }
                    }
                });
            } else {
                $('.kredi-container').hide();
            }
        });
    });
</script>
<style>
    .card {
        border-radius: 10px;
    }

    .card-header {
        border-bottom: 1px solid rgba(0, 0, 0, .05);
    }

    .table> :not(caption)>*>* {
        padding: 1rem;
    }

    .btn-group-sm>.btn {
        padding: .25rem .5rem;
    }

    .list-group-item {
        border-left: none;
        border-right: none;
    }

    .badge {
        padding: .5em 1em;
    }

    .bg-mavi {
        background: #3F51B5 !important;
    }

    .session-checkbox {
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.2s ease;
        cursor: pointer;
        border: 1px solid #dee2e6;
        background-color: #ffffff;
    }

    .session-checkbox:hover {
        background-color: #f5f5f5;
        border-color: #adb5bd;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    }

    .session-checkbox .form-check-input:checked+label {
        font-weight: bold;
        color: #198754;
    }

    .session-checkbox .form-check-input {
        margin-right: 8px;
    }

    .session-checkbox.border {
        border-color: #dee2e6;
    }

    .session-checkbox.active {
        background-color: #e8f5e9;
        border-color: #198754;
    }

    .session-checkbox input:checked+label {
        font-weight: bold;
    }
</style>

<?php require 'footer.php'; ?>
