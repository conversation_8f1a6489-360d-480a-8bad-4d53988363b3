<?php
require 'view.php';
require_once 'db/kredi_fonksiyonlari.php';

// Oturum kontrolü
if (!oturumKontrol()) {
    $_SESSION['sinav_referer'] = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
    header('Location: /giris-yap');
    exit;
}

// Kullanıcı bilgilerini al
$stmt = $db->prepare("SELECT * FROM uyeler WHERE id = ?");
$stmt->execute([$_SESSION['uye_id']]);
$uye = $stmt->fetch(PDO::FETCH_ASSOC);

if ($uye["durum"] == 0) {
    header('Location: /odeme');
    exit;
}

if ($uye["durum"] == 2) {
    header('Location: /odeme-bekliyor');
    exit;
}

// Eğer kullanıcı doğrulanmamışsa doğrulama sayfasına yönlendir
if ($uye['dogrulama_durumu'] == 0) {
    header('Location: /dogrulama');
    exit;
}

// URL'den sınav ID'sini al
$sinav_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Sınav ID'sine göre sınav bilgilerini getir
$sinavSorgu = $db->prepare("SELECT * FROM seviye_sinavlari WHERE id = ?");
$sinavSorgu->execute([$sinav_id]);
$sinav = $sinavSorgu->fetch(PDO::FETCH_ASSOC);

// Sınav bulunamadıysa 404 sayfasına yönlendir
if (!$sinav) {
    header('Location: /404');
    exit;
}

// Seviye bilgisini al
$seviyeSorgu = $db->prepare("SELECT baslik_tr FROM sayfalar WHERE id = ?");
$seviyeSorgu->execute([$sinav['seviye_id']]);
$seviye = $seviyeSorgu->fetch(PDO::FETCH_ASSOC);
$seviye_adi = $seviye['baslik_tr'] ?? 'A1';

// Kullanıcının bu sınava erişim yetkisi olup olmadığını kontrol et
$uye_id = $_SESSION['uye_id'];
$secilen_donem_id = seciliDonem($uye_id);
$seciliGrup = getSeciliDonemGrubu($uye_id, $secilen_donem_id);

// Sınav atamasını kontrol et
$atamaKontrolSorgu = $db->prepare("
    SELECT ssa.*, g.grup_adi, d.ders_adi, ssa.atama_tarihi
    FROM seviye_sinav_atamalari ssa
    JOIN gruplar g ON ssa.grup_id = g.id
    JOIN dersler d ON ssa.ders_id = d.id
    WHERE ssa.sinav_id = ? AND ssa.donem_id = ? AND ssa.grup_id <= ? AND ssa.durum = 'aktif'
");
$atamaKontrolSorgu->execute([$sinav_id, $secilen_donem_id, $seciliGrup['grup_id']]);
$atama = $atamaKontrolSorgu->fetch(PDO::FETCH_ASSOC);

// Kullanıcının bu sınava erişim yetkisi yoksa anasayfaya yönlendir
if (!$atama) {
    header('Location: /seviye-sinavlari');
    exit;
}

// Sınav tarih kontrolü
$simdi = new DateTime();
$atama_tarihi = new DateTime($atama['atama_tarihi']);

// 1. sınav: atama tarihinden 1 hafta
$birinci_sinav_baslangic = clone $atama_tarihi;
$birinci_sinav_bitis = clone $atama_tarihi;
$birinci_sinav_bitis->add(new DateInterval('P7D'));

// 2. sınav: 1. sınav bittikten 1 hafta sonra başlar, 1 hafta sürer
$ikinci_sinav_baslangic = clone $birinci_sinav_bitis;
$ikinci_sinav_baslangic->add(new DateInterval('P7D'));
$ikinci_sinav_bitis = clone $ikinci_sinav_baslangic;
$ikinci_sinav_bitis->add(new DateInterval('P7D'));

// Kullanıcının daha önce bu sınava girip girmediğini kontrol et
$sonucSorgu = $db->prepare("SELECT * FROM seviye_sinav_sonuclari WHERE sinav_id = ? AND uye_id = ? ORDER BY sinav_denemesi DESC");
$sonucSorgu->execute([$sinav_id, $uye_id]);
$oncekiSonuc = $sonucSorgu->fetch(PDO::FETCH_ASSOC);

// Sınav durumlarını kontrol et
$birinci_sinav_aktif = ($simdi >= $birinci_sinav_baslangic && $simdi <= $birinci_sinav_bitis);
$ikinci_sinav_aktif = ($simdi >= $ikinci_sinav_baslangic && $simdi <= $ikinci_sinav_bitis);

// Hangi sınavı yapacağını belirle
$ikinci_sinav_mi = false;

// 1. sınav henüz başlamadıysa yönlendir
if ($simdi < $birinci_sinav_baslangic) {
    header('Location: /seviye-sinavlari');
    exit;
}

// 2. sınav aktifse ve 1. sınav başarısızsa veya hiç girilmemişse 2. sınava gir
if ($ikinci_sinav_aktif && (!$oncekiSonuc || ($oncekiSonuc && $oncekiSonuc['sinav_denemesi'] == 1 && $oncekiSonuc['puan'] < 70))) {
    $ikinci_sinav_mi = true;
    $oncekiSonuc = null; // 2. sınav için yeni sınav başlat
}
// 1. sınav aktifse ve hiç sınava girilmemişse 1. sınava gir
elseif ($birinci_sinav_aktif && !$oncekiSonuc) {
    $ikinci_sinav_mi = false;
}
// Sınav sonucu varsa ve hiçbir sınav aktif değilse sonuçları göster
elseif ($oncekiSonuc && !$birinci_sinav_aktif && !$ikinci_sinav_aktif) {
    // 1. sınav sonucu varsa
    if ($oncekiSonuc['sinav_denemesi'] == 1) {
        $ikinci_sinav_mi = false;
    }
    // 2. sınav sonucu varsa
    elseif ($oncekiSonuc['sinav_denemesi'] == 2) {
        $ikinci_sinav_mi = true;
    }
}
// Diğer durumlar için seviye-sinavlari sayfasına yönlendir
else {
    header('Location: /seviye-sinavlari');
    exit;
}


if ($oncekiSonuc) {
    // Sınav cevaplarını al
    $cevapSorgu = $db->prepare("
        SELECT 
            ssc.*, 
            s.soru, 
            s.soru_tipi, 
            s.dogru_cevap,
            ssc.dogru_mu
        FROM seviye_sinav_cevaplar ssc
        JOIN sorular s ON s.id = ssc.soru_id
        WHERE ssc.sonuc_id = ?
        ORDER BY ssc.id ASC
    ");
    $cevapSorgu->execute([$oncekiSonuc['id']]);
    $sorular = $cevapSorgu->fetchAll(PDO::FETCH_ASSOC);

    $title = $sinav['sinav_adi'] . " - " . ($oncekiSonuc['sinav_denemesi'] == 2 ? "2. Sınav" : "1. Sınav") . " Sonuçları";
    $description = $sinav['sinav_adi'] . ($oncekiSonuc['sinav_denemesi'] == 2 ? "2. sınav" : "1. sınav") . " sonuçları";

    // Otomatik çeviriyi engelleme meta etiketleri ekle
    $no_translate = true;


    head();
?>

    <!-- PDF.js Kütüphanesi -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // PDF.js worker'ını ayarla
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    </script>

    <div class="uk-position-relative circle-background circle-background-2" uk-height-viewport="offset-top: true; ratio: false;">
        <div class="uk-position-absolute uk-visible@l" id="gradients-container">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
            <div class="circle circle-4"></div>
            <div class="circle circle-5"></div>
            <div class="circle circle-6"></div>
        </div>
        <div class="uk-container uk-container-large uk-position-relative z-9 icsayfa-padding">
            <div class=" uk-text-center">
                <h1 class="fw9 icsayfa-baslik"><?php echo $sinav['sinav_adi'] ?> <?= $oncekiSonuc['sinav_denemesi'] == 2 ? '2. Sınav' : '1. Sınav' ?> Sonuçları</h1>
            </div>

            <div class="sonuc-container uk-margin-large-top uk-margin-large-bottom">
                <div class="uk-text-center uk-margin-medium-bottom">
                    <div class="yuzdeli-progress" style="--progress: <?php echo $oncekiSonuc['puan'] * 3.6; ?>deg">
                        <span class="yuzde-deger">%<?php echo (int)$oncekiSonuc['puan']; ?></span>
                    </div>
                    <p class="uk-text-black fw5">Başarı Oranınız</p>
                </div>
                <?php
                // Sınav süresini hesapla
                $baslama = new DateTime($oncekiSonuc['baslama_zamani']);
                $bitis = new DateTime($oncekiSonuc['bitis_zamani']);
                $sure = $baslama->diff($bitis);
                ?>
                <div class="uk-grid-small uk-grid-match uk-child-width-1-2@s uk-child-width-1-4@m uk-text-center uk-margin-medium-bottom" uk-grid>
                    <div>
                        <div class="sonuc-ozet-kutu">
                            <h4 class="sonuc-ozet-baslik">Toplam Soru</h4>
                            <p class="sonuc-ozet-deger"><?php echo count($sorular); ?></p>
                        </div>
                    </div>
                    <div>
                        <div class="sonuc-ozet-kutu">
                            <h4 class="sonuc-ozet-baslik">Doğru Cevap</h4>
                            <p class="sonuc-ozet-deger"><?php echo $oncekiSonuc['dogru_sayisi']; ?></p>
                        </div>
                    </div>
                    <div>
                        <div class="sonuc-ozet-kutu">
                            <h4 class="sonuc-ozet-baslik">Sınav Tamamlama Süresi</h4>
                            <p class="sonuc-ozet-deger">
                                <?php
                                if ($sure->h > 0) {
                                    echo $sure->format('%h saat %i dakika %s saniye');
                                } else if ($sure->i > 0) {
                                    echo $sure->format('%i dk %s sn');
                                } else {
                                    echo $sure->format('%s saniye');
                                }
                                ?>
                            </p>
                        </div>
                    </div>
                    <div>
                        <div class="sonuc-ozet-kutu">
                            <h4 class="sonuc-ozet-baslik">Başarı Yüzdesi</h4>
                            <p class="sonuc-ozet-deger">%<?php echo (int)$oncekiSonuc['puan']; ?></p>
                        </div>
                    </div>
                </div>

                <div id="soru-sonuclari" class="uk-margin-medium-top">
                    <?php if (count($sorular) > 0): ?>
                        <?php foreach ($sorular as $index => $soru): ?>
                            <div class="soru-sonuc <?php echo $soru['dogru_mu'] == 1 ? 'dogru' : 'yanlis'; ?>">
                                <div class="soru-sonuc-baslik">
                                    <span class="soru-no"><?php echo $index + 1; ?>.</span>
                                    <span class="sonuc-badge <?php echo $soru['dogru_mu'] == 1 ? 'dogru-badge' : 'yanlis-badge'; ?>">
                                        <?php echo $soru['dogru_mu'] == 1 ? 'DOĞRU' : 'YANLIŞ'; ?>
                                    </span>
                                </div>
                                <div class="soru-metin fw4"><?php echo htmlspecialchars($soru['soru'] ?? ''); ?></div>
                                <div class="cevap-bilgi">
                                    <strong>Sizin Cevabınız:</strong> <?php echo htmlspecialchars($soru['ogrenci_cevabi']); ?><br>
                                    <strong>Doğru Cevap:</strong> <?php echo htmlspecialchars($soru['dogru_cevap']); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="uk-text-center uk-margin-medium-top">
                            <div class="uk-alert uk-alert-warning">
                                <p><strong>Detaylı soru sonuçları henüz mevcut değil.</strong></p>
                                <p>Genel sınav sonuçlarınız yukarıda gösterilmektedir.</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="uk-text-center uk-margin-medium-top">
                    <a href="/seviye-sinavlari" class="filmlere-don-btn">Seviye Sınavlarına Dön</a>
                </div>
            </div>
        </div>
    </div>

<?php
    foot();
    exit;
} else {
    // Seviyeye ve sınav numarasına göre soruları getir
    $sinav_no = $ikinci_sinav_mi ? 2 : 1; // İkinci sınav mı değişkenine göre sınav numarasını belirle
    
    // Seviye bilgisini al
    $seviye_kodu = $sinav['seviye_adi']; // Örneğin: A1, A2, B1, B2_Lower, B2_Midpoint, B2_Upper, C1
    
    $sorularSorgu = $db->prepare("
        SELECT * FROM sorular 
        WHERE grup_id = ? 
        AND soru_tipi IN ('coktan_secmeli', 'cumle')
        AND sinav = ? 
        AND (seviye = ? OR seviye IS NULL OR seviye = '') 
        ORDER BY RAND()
    ");
    $sorularSorgu->execute([$sinav['seviye_id'], $sinav_no, $seviye_kodu]);
    $tumSorular = $sorularSorgu->fetchAll(PDO::FETCH_ASSOC);

    // Soru tiplerini ayırma
    $coktanSecmeliSorular = [];
    $cumleTamamlamaSorular = [];

    foreach ($tumSorular as $soru) {
        if ($soru['soru_tipi'] == 'coktan_secmeli' && count($coktanSecmeliSorular) < $sinav['coktan_secmeli_soru_sayisi']) {
            $coktanSecmeliSorular[] = $soru;
        } elseif ($soru['soru_tipi'] == 'cumle' && count($cumleTamamlamaSorular) < $sinav['cumle_soru_sayisi']) {
            $cumleTamamlamaSorular[] = $soru;
        }
    }

    // Soruları birleştirme
    $sorular = array_merge($coktanSecmeliSorular, $cumleTamamlamaSorular);

    // Soruları karıştır
    shuffle($sorular);

    // Soru sayısı kontrolü
    $toplam_soru = $sinav['coktan_secmeli_soru_sayisi'] + $sinav['cumle_soru_sayisi'];
    if (count($sorular) < $toplam_soru) {
        header('Location: /seviye-sinavlari');
        exit;
    }

    // 2. sınav mı kontrol et - yukarıdaki koşullardan gelen $ikinci_sinav_mi değişkenini kullan

    $title = $sinav['sinav_adi'] . " - " . $sinav['seviye_adi'] . " Seviye Sınavı" . ($ikinci_sinav_mi ? " - 2. Sınav" : "");
    $description = $sinav['sinav_adi'] . " " . $sinav['seviye_adi'] . " seviye sınavı" . ($ikinci_sinav_mi ? " 2. sınav sayfası" : " sayfası");

    // Otomatik çeviriyi engelleme meta etiketleri ekle
    $no_translate = true;

    head();
?>

    <!-- PDF.js Kütüphanesi -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // PDF.js worker'ını ayarla
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    </script>

    <div class="uk-position-relative circle-background circle-background-2" uk-height-viewport="offset-top: true; ratio: false;">
        <div class="uk-position-absolute uk-visible@l">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
            <div class="circle circle-4"></div>
            <div class="circle circle-5"></div>
            <div class="circle circle-6"></div>
        </div>
        <div class="uk-container uk-container-large uk-position-relative z-9 icsayfa-padding">
            <div class=" uk-text-center">
                <h1 class="fw9 icsayfa-baslik"><?php echo $sinav['sinav_adi'] ?> <?= $ikinci_sinav_mi ? ' - 2. Sınav' : '' ?></h1>
            </div>

            <!-- Sınav Giriş Ekranı (Başlangıçta görünür) -->
            <div id="sinav-giris-ekrani" class="uk-margin-large-top uk-margin-large-bottom">
                <div class="sinav-giris-container">
                    <h2 class="sinav-giris-baslik"><?= $ikinci_sinav_mi ? '2. Sınav Bilgileri' : 'Sınav Bilgileri' ?></h2>

                    <div class="uk-grid-small uk-child-width-1-2@s uk-child-width-1-3@m uk-text-center uk-margin-medium-bottom" uk-grid>
                        <div>
                            <div class="sinav-bilgi-kutu">
                                <h4 class="sinav-bilgi-baslik">Toplam Soru</h4>
                                <p class="sinav-bilgi-deger"><?php echo count($sorular); ?></p>
                            </div>
                        </div>
                        <div>
                            <div class="sinav-bilgi-kutu">
                                <h4 class="sinav-bilgi-baslik">Sınav Süresi</h4>
                                <p class="sinav-bilgi-deger"><?php echo $sinav['sure_dakika']; ?> Dakika</p>
                            </div>
                        </div>
                        <div class="uk-width-1-1@s uk-width-1-3@m">
                            <div class="sinav-bilgi-kutu">
                                <h4 class="sinav-bilgi-baslik">Sınav Seviyesi</h4>
                                <p class="sinav-bilgi-deger"><?php echo $sinav['seviye_adi'] ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="uk-text-center uk-margin-medium-top">
                        <p class="uk-text-emphasis fw5"><?php echo sayfalar(181, "baslik_$dil") ?></p>
                        <p class="uk-text-emphasis fw5">Sınav süresi <?php echo $sinav['sure_dakika']; ?> dakikadır ve süre dolduğunda sınav otomatik olarak sonlandırılacaktır.</p>

                        <button id="sinava-basla" class="uk-button gradient-button uk-margin-medium-top"><?= $ikinci_sinav_mi ? '2. Sınava Başla' : 'Sınava Başla' ?></button>
                    </div>
                </div>
            </div>

            <!-- Sonuç Ekranı (Başlangıçta gizli) -->
            <div id="sonuc-ekrani" class="uk-margin-large-top uk-margin-large-bottom" style="display:none;">
                <div class="sonuc-container">
                    <h2 class="sonuc-baslik">Sınav Sonuçlarınız</h2>

                    <div class="uk-text-center uk-margin-medium-bottom">
                        <div class="yuzdeli-progress">
                            <span class="yuzde-deger">%0</span>
                        </div>
                        <p class="uk-text-black fw5">Başarı Oranınız</p>
                    </div>

                    <!-- Başarı/Başarısızlık Mesajı -->
                    <div id="basari-mesaji" class="uk-text-center uk-margin-medium-bottom uk-hidden">
                        <div class="basari-badge">
                            <div uk-icon="icon: check; ratio: 2.5"></div>
                            <h3 class="uk-margin-small-top">Tebrikler! <?= $sinav['seviye_adi'] ?> Sertifikası Almaya Hak Kazandınız! 🎉</h3>
                            <p><?php echo sayfalar(182, "baslik_$dil") ?></p>
                            <div class="badge-confetti"></div>
                        </div>
                        <div class="uk-margin-medium-top">
                            <p class="uk-text-black fs15 fw5"><?php echo sayfalar(182, "icerik_$dil") ?></p>
                        </div>
                    </div>

                    <div id="orta-basari-mesaji" class="uk-text-center uk-margin-medium-bottom uk-hidden">
                        <div class="orta-basari-badge">
                            <div uk-icon="icon: plus; ratio: 2.5"></div>
                            <h3 class="uk-margin-small-top"><?php echo sayfalar(183, "baslik_$dil") ?></h3>
                            <p><?php echo sayfalar(183, "icerik_$dil") ?></p>
                        </div>
                    </div>

                    <div id="basarisiz-mesaji" class="uk-text-center uk-margin-medium-bottom uk-hidden">
                        <div class="basarisiz-badge">
                            <div uk-icon="icon: refresh; ratio: 2.5"></div>
                            <h3 class="uk-margin-small-top" id="basarisiz-baslik">İyi bir denemeydi...</h3>
                            <div id="basarisiz-icerik" class="uk-margin-small-top">
                                <p class="fw5">Sevgili öğrencimiz <strong><?php echo htmlspecialchars($uye['ad'] . ' ' . $uye['soyad']); ?></strong>,</p>
                                <p class="fw5">Girmiş olduğun seviye sınavındaki performansın iyi bir denemeydi.</p>
                                <p class="fw5">Ancak <span id="sinav-numarasi">2.</span> sınavında daha iyi çalışarak bu kez başarabileceğine inanıyorum.</p>
                                <p class="fw5">İkinci sınav hakkın: <strong><span id="ikinci-sinav-tarihi"><?php 
                                    $ikinci_sinav_baslangic = clone $atama_tarihi;
                                    $ikinci_sinav_baslangic->add(new DateInterval('P14D'));
                                    echo $ikinci_sinav_baslangic->format('d.m.Y H:i');
                                ?></span></strong> tarihindedir. İyi çalışmalar!</p>
                            </div>
                        </div>
                    </div>

                    <div class="uk-grid-small uk-child-width-1-2@s uk-child-width-1-3@m uk-text-center uk-margin-medium-bottom" uk-grid>
                        <div>
                            <div class="sonuc-ozet-kutu">
                                <h4 class="sonuc-ozet-baslik">Toplam Soru</h4>
                                <p class="sonuc-ozet-deger" id="toplam-soru-sayisi">0</p>
                            </div>
                        </div>
                        <div>
                            <div class="sonuc-ozet-kutu">
                                <h4 class="sonuc-ozet-baslik">Doğru Cevap</h4>
                                <p class="sonuc-ozet-deger" id="dogru-cevap-sayisi">0</p>
                            </div>
                        </div>
                        <div class="uk-width-1-1@s uk-width-1-3@m">
                            <div class="sonuc-ozet-kutu">
                                <h4 class="sonuc-ozet-baslik">Başarı Yüzdesi</h4>
                                <p class="sonuc-ozet-deger" id="basari-orani">%0</p>
                            </div>
                        </div>
                    </div>

                    <!-- Sertifika Bölümü (Sadece başarılı olanlarda görünür) -->
                    <div id="sertifika-bolumu" class="uk-margin-large-top uk-hidden">
                        <div class="uk-card uk-card-default uk-card-body br20">
                            <div class="uk-flex uk-flex-between uk-flex-middle uk-margin-bottom">
                                <h3 class="uk-card-title">🏆 Başarı Sertifikanız</h3>
                                <div class="uk-button-group">
                                    <button id="sertifikaIndirBtn" class="uk-button gradient-small-button hover-top">
                                        <span uk-icon="download"></span>Sertifikayı İndir
                                    </button>
                                </div>
                            </div>

                            <!-- PDF.js Viewer Container -->
                            <div id="pdfViewerContainer" style="border: 2px solid #e5e5e5; border-radius: 8px; overflow: hidden; position: relative;">
                                <div id="pdfViewer" style="width: 100%; height: 100%;"></div>
                                <div id="pdfLoading" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                                    <div uk-spinner="ratio: 1.5"></div>
                                    <p class="uk-margin-small-top">Sertifika yükleniyor...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="soru-sonuclari" class="uk-margin-medium-top">
                        <!-- Soru sonuçları burada dinamik olarak gösterilecek -->
                    </div>



                    <div class="uk-text-center uk-margin-medium-top">
                        <a href="/seviye-sinavlari" class="filmlere-don-btn">Seviye Sınavlarına Dön</a>
                    </div>
                </div>
            </div>

            <!-- Sınav Ekranı (Başlangıçta gizli) -->
            <div id="sinav-ekrani" class="snv-fs altbilgi-icerik" style="display:none;">
                <div class="uk-position-relative" uk-slider="finite: true; draggable:false;">
                    <!-- Geri Sayım - Sol üst köşeye eklendi -->
                    <div id="geri-sayim" class="countdown-timer uk-position-absolute">
                        Kalan Süre: <span id="dakika"></span>:<span id="saniye"></span>
                    </div>

                    <div class="uk-slider-container uk-height-auto">
                        <ul class="uk-slider-items uk-child-width-1-1 uk-height-auto">
                            <?php
                            foreach ($sorular as $index => $soru):
                                $soruNo = $index + 1;
                                $soruMetni = htmlspecialchars($soru['soru'], ENT_QUOTES);
                            ?>
                                <li class="uk-margin-medium-top">
                                    <div class="uk-text-center fs25 fw7 uk-margin-medium-top uk-margin-large-bottom soru-metni">
                                        <?php echo $soruNo; ?>.<?php echo $soruMetni; ?>
                                    </div>

                                    <?php
                                    // Soruya ait fotoğrafları getir
                                    $fotograflarSorgu = $db->prepare("SELECT * FROM soru_fotograflari WHERE soru_id = ? ORDER BY sira ASC");
                                    $fotograflarSorgu->execute([$soru['id']]);
                                    $fotograflar = $fotograflarSorgu->fetchAll(PDO::FETCH_ASSOC);

                                    if (count($fotograflar) > 0):
                                    ?>
                                        <div class="soru-fotograflari uk-margin-medium-bottom">
                                            <div class="uk-grid-small uk-child-width-1-2@s uk-child-width-1-3@m uk-flex-center" uk-grid>
                                                <?php foreach ($fotograflar as $foto): ?>
                                                    <div>
                                                        <div class="fotograf-container" uk-lightbox>
                                                            <a href="<?= $foto['fotograf_yolu'] ?>">
                                                                <img src="<?= $foto['fotograf_yolu'] ?>" alt="Soru Görseli" class="soru-fotograf">
                                                            </a>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($soru['soru_tipi'] == 'coktan_secmeli'): ?>
                                        <div class="uk-grid-small uk-child-width-1-1 uk-grid-match" uk-grid>
                                            <?php
                                            $secenekler = json_decode($soru['secenekler'], true);
                                            $secenekSayisi = count($secenekler);
                                            $secenekHarfleri = ['A', 'B', 'C', 'D', 'E'];

                                            $seceneklerKarisik = $secenekler;
                                            shuffle($seceneklerKarisik);

                                            for ($i = 0; $i < $secenekSayisi; $i++):
                                                $secenekHarfi = $secenekHarfleri[$i];
                                                $secenekMetni = $seceneklerKarisik[$i];
                                            ?>
                                                <div class="secenek-container">
                                                    <label class="uk-flex uk-flex-row uk-flex-middle">
                                                        <div class="uk-text-large uk-margin-small-right"><span class="option"><?php echo $secenekHarfi; ?></span></div>
                                                        <input class="uk-radio uk-hidden" type="radio" name="soru<?php echo $soruNo; ?>"
                                                            value="<?php echo $secenekMetni; ?>" data-soru-id="<?php echo $soru['id']; ?>">
                                                        <span class="secenek-metni"><?php echo $secenekMetni; ?></span>
                                                    </label>
                                                </div>
                                            <?php endfor; ?>
                                        </div>

                                    <?php elseif ($soru['soru_tipi'] == 'cumle'): ?>
                                        <?php
                                        $dogruCumle = $soru['dogru_cevap'];
                                        $kelimeler = explode(' ', $dogruCumle);

                                        if (count($kelimeler) > 0) {
                                            $sonKelimeIndex = count($kelimeler) - 1;
                                            $kelimeler[$sonKelimeIndex] = rtrim($kelimeler[$sonKelimeIndex], '.');
                                        }

                                        $karisikKelimeler = $kelimeler;
                                        shuffle($karisikKelimeler);
                                        ?>
                                        <div class="uk-text-center uk-margin-medium-bottom">
                                            <p class="fs20 fw6">Aşağıdaki kelimeleri doğru sırayla yerleştirerek cümleyi tamamlayın.</p>
                                        </div>

                                        <div id="sentence-<?php echo $soruNo; ?>" class="uk-flex uk-flex-center uk-flex-wrap uk-margin-bottom"
                                            data-soru-id="<?php echo $soru['id']; ?>">
                                            <?php for ($i = 0; $i < count($kelimeler); $i++): ?>
                                                <div class="drop-zone" data-index="<?php echo $i; ?>"></div>
                                            <?php endfor; ?>
                                        </div>

                                        <div id="word-bank-<?php echo $soruNo; ?>" class="uk-flex uk-flex-center uk-flex-wrap">
                                            <?php foreach ($karisikKelimeler as $kelime): ?>
                                                <div class="draggable-word uk-text-black" draggable="true" data-word="<?php echo $kelime; ?>">
                                                    <?php echo $kelime; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>

                    <!-- Navigasyon Butonları -->
                    <div class="uk-flex uk-flex-center uk-flex-middle uk-flex-between uk-margin-large-top">
                        <a href="#" class="next-prev-fs fw6" uk-slider-item="previous">
                            <span uk-icon="icon: arrow-left; ratio: 1.5"></span> <span class="uk-visible@m">Önceki Soru</span>
                        </a>
                        <div class="uk-text-center">
                            <button id="sinav-bitir" class="uk-button gradient-button" style="display: none;">Sınavı Bitir</button>
                        </div>
                        <a href="#" class="next-prev-fs fw6" uk-slider-item="next">
                            <span class="uk-visible@m">Sonraki Soru</span> <span uk-icon="icon: arrow-right; ratio: 1.5"></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sınava başla butonuna tıklama olayı
            document.getElementById('sinava-basla').addEventListener('click', function() {
                // Giriş ekranını gizle
                document.getElementById('sinav-giris-ekrani').style.display = 'none';

                // Sınav ekranını göster
                document.getElementById('sinav-ekrani').style.display = 'block';

                // Süre sayacını başlat
                baslaSinavSuresi();

                // Sekme değişim kontrolünü aktifleştir
                sekmeAktif = true;
            });
        });

        // Tarayıcı çevirisini engelleme
        function blockBrowserTranslation() {
            // Sayfanın çevrilip çevrilmediğini kontrol et
            function checkIfTranslated() {
                // Google Translate veya diğer çeviri araçları tarafından eklenen class ve elementleri kontrol et
                if (document.documentElement.classList.contains('translated-rtl') ||
                    document.documentElement.classList.contains('translated-ltr') ||
                    document.body.classList.contains('translated-rtl') ||
                    document.body.classList.contains('translated-ltr') ||
                    document.getElementsByClassName('skiptranslate').length > 0 ||
                    document.getElementById('BIAS_TRANSSLATE') !== null) {


                    // Çeviri cookiesini temizle
                    document.cookie = "googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
                    document.cookie = "googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=" + window.location.hostname + ";";
                    document.cookie = "googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=." + window.location.hostname + ";";

                    // HTML elementinin lang değerini kontrol et
                    if (document.documentElement.lang !== 'tr') {
                        document.documentElement.lang = 'tr';
                        // Sayfayı yenile
                        location.reload(true);
                    }
                }

                // Google Translate dilinin original olmadığını kontrol et
                if (document.cookie.indexOf('googtrans=') > -1 && document.cookie.indexOf('googtrans=/auto/tr') === -1) {
                    // Çeviri cookiesini temizle
                    document.cookie = "googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
                    document.cookie = "googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=" + window.location.hostname + ";";
                    document.cookie = "googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=." + window.location.hostname + ";";
                    // Sayfayı yenile
                    location.reload(true);
                }
            }

            // Çeviri olayını izle
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // Yeni eklenen nodelar arasında Google Translate elementleri var mı kontrol et
                        for (let i = 0; i < mutation.addedNodes.length; i++) {
                            const node = mutation.addedNodes[i];
                            if (node.nodeType === 1) { // Element node
                                if (node.classList &&
                                    (node.classList.contains('skiptranslate') ||
                                        node.id === 'google_translate_element' ||
                                        node.id === 'BIAS_TRANSSLATE')) {
                                    checkIfTranslated();
                                    break;
                                }
                            }
                        }
                    }
                });
            });

            // Tüm DOM değişikliklerini izle
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Periyodik olarak kontrol et (her 3 saniyede bir)
            setInterval(checkIfTranslated, 3000);

            // Sayfa yüklendiğinde ilk kontrol
            checkIfTranslated();

            // Sayfa yüklendiğinde uyarı göster (tarayıcı dili Türkçe değilse)
            if (window.navigator.language &&
                window.navigator.language.toLowerCase() !== 'tr' &&
                window.navigator.language.toLowerCase() !== 'tr-tr' &&
                !localStorage.getItem('translation_warning_shown')) {

                Swal.fire({
                    title: 'Uyarı!',
                    text: 'Bu sınavda otomatik çeviri özelliği devre dışı bırakılmıştır. Lütfen tarayıcınızın çeviri özelliğini kullanmayınız.',
                    icon: 'warning',
                    confirmButtonText: 'Anladım',
                    confirmButtonColor: '#9733EE'
                });

                // Uyarının tekrar gösterilmemesi için
                localStorage.setItem('translation_warning_shown', 'true');
            }

            // Google Translate API yüklenirse devre dışı bırak
            if (window.google && window.google.translate) {
                window.google.translate = undefined;
            }
        }

        // Sayfa yüklendiğinde çalıştır
        document.addEventListener('DOMContentLoaded', blockBrowserTranslation);

        // Konfeti Animasyonu
        class ConfettiManager {
            constructor() {
                this.canvas = document.createElement("canvas");
                this.canvas.style = "position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 1000; pointer-events: none;";
                document.body.appendChild(this.canvas);
                this.context = this.canvas.getContext("2d");
                this.confetti = [];
                this.lastUpdated = Date.now();
                window.addEventListener("resize", this.debounce(() => this.resizeCanvas(), 200));
                this.resizeCanvas();
                requestAnimationFrame(() => this.loop());
            }

            resizeCanvas() {
                this.canvas.width = window.innerWidth * window.devicePixelRatio;
                this.canvas.height = window.innerHeight * window.devicePixelRatio;
            }

            debounce(func, delay) {
                let timeout;
                return (...args) => {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => func(...args), delay);
                };
            }

            getRandomInRange(min, max, precision = 0) {
                const multiplier = Math.pow(10, precision);
                const randomValue = Math.random() * (max - min) + min;
                return Math.floor(randomValue * multiplier) / multiplier;
            }

            getRandomItem(array) {
                return array[Math.floor(Math.random() * array.length)];
            }

            getScaleFactor() {
                return Math.log(window.innerWidth) / Math.log(1920);
            }

            addConfetti(config = {}) {
                const confettiesNumber = config.confettiesNumber || 650;
                const confettiRadius = config.confettiRadius || 5;
                const confettiColors = config.confettiColors || [
                    "#fcf403", "#62fc03", "#f4fc03", "#03e7fc", "#03fca5", "#a503fc", "#fc03ad", "#fc03c2"
                ];
                const emojies = config.emojies || [];
                const svgIcon = config.svgIcon || null;

                const baseY = (5 * window.innerHeight) / 7;
                for (let i = 0; i < confettiesNumber / 2; i++) {
                    this.confetti.push(new Confetti({
                        initialPosition: {
                            x: 0,
                            y: baseY
                        },
                        direction: "right",
                        radius: confettiRadius,
                        colors: confettiColors,
                        emojis: emojies,
                        svgIcon,
                        manager: this
                    }));
                    this.confetti.push(new Confetti({
                        initialPosition: {
                            x: window.innerWidth,
                            y: baseY
                        },
                        direction: "left",
                        radius: confettiRadius,
                        colors: confettiColors,
                        emojis: emojies,
                        svgIcon,
                        manager: this
                    }));
                }
            }

            resetAndStart(config = {}) {
                // Clear existing confetti
                this.confetti = [];
                // Add new confetti
                this.addConfetti(config);
            }

            loop() {
                const currentTime = Date.now();
                const deltaTime = currentTime - this.lastUpdated;
                this.lastUpdated = currentTime;

                this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);

                this.confetti = this.confetti.filter((item) => {
                    item.updatePosition(deltaTime, currentTime);
                    item.draw(this.context);
                    return item.isVisible(this.canvas.height);
                });

                requestAnimationFrame(() => this.loop());
            }
        }

        class Confetti {
            constructor({
                initialPosition,
                direction,
                radius,
                colors,
                emojis,
                svgIcon,
                manager
            }) {
                this.manager = manager;
                const speedFactor = this.manager.getRandomInRange(0.9, 1.7, 3) * this.manager.getScaleFactor();
                this.speed = {
                    x: speedFactor,
                    y: speedFactor
                };
                this.finalSpeedX = this.manager.getRandomInRange(0.2, 0.6, 3);
                this.rotationSpeed = emojis && emojis.length || svgIcon ? 0.01 : this.manager.getRandomInRange(0.03, 0.07, 3) * this.manager.getScaleFactor();
                this.dragCoefficient = this.manager.getRandomInRange(0.0005, 0.0009, 6);
                this.radius = {
                    x: radius,
                    y: radius
                };
                this.initialRadius = radius;
                this.rotationAngle = direction === "left" ? this.manager.getRandomInRange(0, 0.2, 3) : this.manager.getRandomInRange(-0.2, 0, 3);
                this.emojiRotationAngle = this.manager.getRandomInRange(0, 2 * Math.PI);
                this.radiusYDirection = "down";

                const DEG_TO_RAD = Math.PI / 180;
                const angle = direction === "left" ? this.manager.getRandomInRange(82, 15) * DEG_TO_RAD : this.manager.getRandomInRange(-15, -82) * DEG_TO_RAD;
                this.absCos = Math.abs(Math.cos(angle));
                this.absSin = Math.abs(Math.sin(angle));

                const offset = this.manager.getRandomInRange(-150, 0);
                const position = {
                    x: initialPosition.x + (direction === "left" ? -offset : offset) * this.absCos,
                    y: initialPosition.y - offset * this.absSin
                };

                this.position = {
                    ...position
                };
                this.initialPosition = {
                    ...position
                };
                this.color = emojis && emojis.length || svgIcon ? null : this.manager.getRandomItem(colors);
                this.emoji = emojis && emojis.length ? this.manager.getRandomItem(emojis) : null;
                this.svgIcon = null;

                // Preload SVG if provided
                if (svgIcon) {
                    this.svgImage = new Image();
                    this.svgImage.src = svgIcon;
                    this.svgImage.onload = () => {
                        this.svgIcon = this.svgImage; // Mark as ready once loaded
                    };
                }

                this.createdAt = Date.now();
                this.direction = direction;
            }

            draw(context) {
                const {
                    x,
                    y
                } = this.position;
                const {
                    x: radiusX,
                    y: radiusY
                } = this.radius;
                const scale = window.devicePixelRatio;

                if (this.svgIcon) {
                    context.save();
                    context.translate(scale * x, scale * y);
                    context.rotate(this.emojiRotationAngle);
                    context.drawImage(this.svgIcon, -radiusX, -radiusY, radiusX * 2, radiusY * 2);
                    context.restore();
                } else if (this.color) {
                    context.fillStyle = this.color;
                    context.beginPath();
                    context.ellipse(x * scale, y * scale, radiusX * scale, radiusY * scale, this.rotationAngle, 0, 2 * Math.PI);
                    context.fill();
                } else if (this.emoji) {
                    context.font = `${radiusX * scale}px serif`;
                    context.save();
                    context.translate(scale * x, scale * y);
                    context.rotate(this.emojiRotationAngle);
                    context.textAlign = "center";
                    context.fillText(this.emoji, 0, radiusY / 2); // Adjust vertical alignment
                    context.restore();
                }
            }

            updatePosition(deltaTime, currentTime) {
                const elapsed = currentTime - this.createdAt;

                if (this.speed.x > this.finalSpeedX) {
                    this.speed.x -= this.dragCoefficient * deltaTime;
                }

                this.position.x += this.speed.x * (this.direction === "left" ? -this.absCos : this.absCos) * deltaTime;
                this.position.y = this.initialPosition.y - this.speed.y * this.absSin * elapsed + 0.00125 * Math.pow(elapsed, 2) / 2;

                if (!this.emoji && !this.svgIcon) {
                    this.rotationSpeed -= 1e-5 * deltaTime;
                    this.rotationSpeed = Math.max(this.rotationSpeed, 0);

                    if (this.radiusYDirection === "down") {
                        this.radius.y -= deltaTime * this.rotationSpeed;
                        if (this.radius.y <= 0) {
                            this.radius.y = 0;
                            this.radiusYDirection = "up";
                        }
                    } else {
                        this.radius.y += deltaTime * this.rotationSpeed;
                        if (this.radius.y >= this.initialRadius) {
                            this.radius.y = this.initialRadius;
                            this.radiusYDirection = "down";
                        }
                    }
                }
            }

            isVisible(canvasHeight) {
                return this.position.y < canvasHeight + 100;
            }
        }

        let confettiManager = null;

        // Başarım oranına göre, konfeti gösterilip gösterilmeyeceğini belirleyecek fonksiyon
        function showResultMessage(dogruCevapSayisi, toplamSoruSayisi) {
            // Calculate success percentage
            const puan = (dogruCevapSayisi / toplamSoruSayisi) * 100;

            const basariMesaji = document.getElementById('basari-mesaji');
            const ortaBasariMesaji = document.getElementById('orta-basari-mesaji');
            const basarisizMesaji = document.getElementById('basarisiz-mesaji');

            // Hide all messages first
            basariMesaji.classList.add('uk-hidden');
            ortaBasariMesaji.classList.add('uk-hidden');
            basarisizMesaji.classList.add('uk-hidden');

            if (puan >= 70) {
                // Başarılı oldu - her iki sınavda da aynı başarı mesajını göster
                basariMesaji.classList.remove('uk-hidden');
                
                // Start confetti animation
                if (!confettiManager) {
                    confettiManager = new ConfettiManager();
                }
                confettiManager.resetAndStart();
            } else {
                // %70 altında kalan öğrenciler için başarısızlık mesajı
                basarisizMesaji.classList.remove('uk-hidden');

                // Sınav denemesine göre mesaj güncelle
                const sinavDenemesi = <?php echo $ikinci_sinav_mi ? '2' : '1'; ?>;
                const sinavNumarasi = document.getElementById('sinav-numarasi');
                
                if (sinavDenemesi == 2) {
                    // 2. sınavda başarısız - ikinci sınav hakkı yok, kibar mesaj
                    sinavNumarasi.textContent = '2.';
                    document.getElementById('basarisiz-icerik').innerHTML = `
                        <p class="fw5">Sevgili öğrencimiz <strong><?php echo htmlspecialchars($uye['ad'] . ' ' . $uye['soyad']); ?></strong>,</p>
                        <p class="fw5"><?php echo sayfalar(185, "icerik_$dil") ?></p>
                    `;
                } else {
                    // 1. sınavda başarısız - ikinci sınav hakkı var
                    sinavNumarasi.textContent = '2.';
                    // Mesaj zaten HTML'de tanımlı, sadece tarih güncellenir
                }
            }
        }

        // Sınav süresi
        let kalanDakika = <?php echo $sinav['sure_dakika']; ?>;
        let kalanSaniye = 0;
        let sureSayaci;
        let sinavBitti = false;

        // Sınav başlama zamanı değişkeni (başlangıçta boş)
        let baslamaZamani;
        const sinav_id = <?php echo $sinav_id; ?>;



        // Geri sayım fonksiyonu
        function geriSayim() {
            if (kalanDakika === 0 && kalanSaniye === 0) {
                clearInterval(sureSayaci);
                // Süre bittiğinde sınavı otomatik bitir
                sinaviBitir();
                return;
            }

            if (kalanSaniye === 0) {
                kalanDakika--;
                kalanSaniye = 59;
            } else {
                kalanSaniye--;
            }

            document.getElementById('dakika').textContent = kalanDakika.toString().padStart(2, '0');
            document.getElementById('saniye').textContent = kalanSaniye.toString().padStart(2, '0');

            // Son 5 dakikada rengi değiştir
            if (kalanDakika < 5) {
                document.getElementById('geri-sayim').style.background = 'linear-gradient(135deg, #f0506e 0%, #d84a67 100%)';
            }
        }

        // Geri sayımı başlat
        function baslaSinavSuresi() {
            sureSayaci = setInterval(geriSayim, 1000);
            geriSayim(); // İlk değerleri göster

            // Sınav başlama zamanını kaydet
            baslamaZamani = new Date();
        }

        // Sağ tık menüsünü engelle
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });



        // Tarayıcı kapatma/yenileme kontrolü
        window.addEventListener('beforeunload', function(e) {
            if (!sinavBitti) {
                e.preventDefault();
                e.returnValue = 'Sınavınız devam ediyor. Sayfadan ayrılmak istediğinize emin misiniz?';
            }
        });



        document.addEventListener('DOMContentLoaded', function() {
            var slider = UIkit.slider(document.querySelector('[uk-slider]'));
            var totalSlides = document.querySelectorAll('.uk-slider-items > li').length;
            var bitirButton = document.getElementById('sinav-bitir');
            var nextButton = document.querySelector('.next-prev-fs[uk-slider-item="next"]');
            var prevButton = document.querySelector('.next-prev-fs[uk-slider-item="previous"]');

            // Soru yanıtlarını izleme dizisi
            var yanitlanmisSorular = new Array(totalSlides).fill(false);

            // Radyo butonlarının durumunu izle
            var seciliCevaplar = {};

            // Radyo butonlarının her birini dinle
            document.querySelectorAll('input[type="radio"]').forEach(function(input) {
                input.addEventListener('change', function() {
                    var soruNo = this.name.replace('soru', '');
                    seciliCevaplar[soruNo] = this.value;

                    // Seçilen seçeneği görsel olarak işaretle
                    var currentIndex = slider.index;
                    yanitlanmisSorular[currentIndex] = true;

                    // Seçili seçeneğin etiketini bul ve stil uygula
                    document.querySelectorAll('input[name="' + this.name + '"]').forEach(function(radio) {
                        var label = radio.closest('label');
                        var option = label.querySelector('.option');

                        if (radio.checked) {
                            option.classList.add('selected-option');
                        } else {
                            option.classList.remove('selected-option');
                        }
                    });
                });
            });

            function updateNavigation(index) {
                if (bitirButton) {
                    if (index === totalSlides - 1) {
                        bitirButton.style.display = 'inline-block';
                        if (nextButton) nextButton.style.display = 'none';
                    } else {
                        bitirButton.style.display = 'none';
                        if (nextButton) nextButton.style.display = 'inline-block';
                    }
                }

                if (prevButton) {
                    prevButton.style.display = index === 0 ? 'none' : 'inline-block';
                }

                // Geçiş sonrası mevcut slide'daki seçenekleri güncelle
                updateSlideSelections(index);
            }

            // Sorudaki seçili radyo düğmelerini günceller
            function updateSlideSelections(index) {
                var currentSlide = document.querySelectorAll('.uk-slider-items > li')[index];

                // Bu slayt içindeki radyo butonlarını bul
                var radios = currentSlide.querySelectorAll('input[type="radio"]');
                if (radios.length > 0) {
                    var soruNo = radios[0].name.replace('soru', '');

                    // Daha önce bir cevap seçilmişse, görsel olarak işaretle
                    if (seciliCevaplar[soruNo]) {
                        radios.forEach(function(radio) {
                            var label = radio.closest('label');
                            var option = label.querySelector('.option');

                            if (radio.value === seciliCevaplar[soruNo]) {
                                radio.checked = true;
                                option.classList.add('selected-option');
                            } else {
                                option.classList.remove('selected-option');
                            }
                        });
                    }
                }

                // Cümle tamamlama soruları için kontrol
                var dropZones = currentSlide.querySelectorAll('.drop-zone');
                if (dropZones.length > 0) {
                    var tumDolu = true;
                    dropZones.forEach(function(zone) {
                        if (!zone.dataset.word) {
                            tumDolu = false;
                        }
                    });

                    if (tumDolu) {
                        yanitlanmisSorular[index] = true;
                    }
                }
            }

            updateNavigation(0);



            // UIkit slider event'lerini override et
            if (nextButton) {
                nextButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopImmediatePropagation();
                    var currentIndex = slider.index;
                    var currentSlide = document.querySelectorAll('.uk-slider-items > li')[currentIndex];

                    if (yanitlanmisSorular[currentIndex]) {
                        // Eğer soru yanıtlanmışsa bir sonraki soruya geç
                        slider.show(currentIndex + 1);
                    } else {
                        // Soru yanıtlanma durumunu kontrol et
                        var soruTipi = currentSlide.querySelector('.draggable-word') ? 'cumle' : 'coktan_secmeli';
                        var cevaplanmis = false;

                        if (soruTipi === 'coktan_secmeli') {
                            // Çoktan seçmeli soru için kontrol
                            var radioButtons = currentSlide.querySelectorAll('input[type="radio"]');
                            for (var i = 0; i < radioButtons.length; i++) {
                                if (radioButtons[i].checked) {
                                    cevaplanmis = true;
                                    break;
                                }
                            }
                        } else if (soruTipi === 'cumle') {
                            // Cümle tamamlama sorusu için kontrol
                            var dropZones = currentSlide.querySelectorAll('.drop-zone');
                            cevaplanmis = true;
                            for (var i = 0; i < dropZones.length; i++) {
                                if (!dropZones[i].dataset.word) {
                                    cevaplanmis = false;
                                    break;
                                }
                            }
                        }

                        if (cevaplanmis) {
                            yanitlanmisSorular[currentIndex] = true;
                            slider.show(currentIndex + 1);
                        } else {
                            // Uyarı göster
                            Swal.fire({
                                title: 'Uyarı!',
                                text: 'Lütfen soruyu cevaplayınız.',
                                icon: 'warning',
                                confirmButtonText: 'Tamam',
                                confirmButtonColor: '#9733EE'
                            });
                        }
                    }
                }, true); // capture phase'de çalıştır
            }

            // Önceki düğmesine tıklandığında önceki soruya geç
            if (prevButton) {
                prevButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopImmediatePropagation();
                    var currentIndex = slider.index;
                    if (currentIndex > 0) {
                        slider.show(currentIndex - 1);
                    }
                }, true); // capture phase'de çalıştır
            }

            // Cümle tamamlama sorularını izle
            document.querySelectorAll('.drop-zone').forEach(function(zone) {
                var observer = new MutationObserver(function(mutations) {
                    var currentIndex = slider.index;
                    var currentSlide = document.querySelectorAll('.uk-slider-items > li')[currentIndex];
                    var dropZones = currentSlide.querySelectorAll('.drop-zone');
                    var tumDolu = true;

                    dropZones.forEach(function(zone) {
                        if (!zone.dataset.word) {
                            tumDolu = false;
                        }
                    });

                    if (tumDolu) {
                        yanitlanmisSorular[currentIndex] = true;
                    }
                });

                observer.observe(zone, {
                    attributes: true,
                    attributeFilter: ['data-word']
                });
            });

            // Slider geçişlerini izle
            UIkit.util.on('.uk-slider', 'itemshown', function(e, el) {
                var currentIndex = slider.index;
                updateNavigation(currentIndex);

                // Tüm fotoğraf alanlarını gizle
                document.querySelectorAll('.soru-fotograflari').forEach(function(fotografAlani) {
                    fotografAlani.style.display = 'none';
                });

                // Mevcut slide'daki fotoğraf alanını göster
                var currentSlide = document.querySelectorAll('.uk-slider-items > li')[currentIndex];
                if (currentSlide) {
                    var fotografAlani = currentSlide.querySelector('.soru-fotograflari');
                    if (fotografAlani) {
                        fotografAlani.style.display = 'block';
                    }
                }
            });

            // Sayfa yüklendiğinde ilk slide için fotoğraf alanını ayarla
            document.addEventListener('DOMContentLoaded', function() {
                // Sadece ilk slide'ın fotoğrafını göster, diğerlerini gizle
                var slides = document.querySelectorAll('.uk-slider-items > li');
                slides.forEach(function(slide, index) {
                    var fotografAlani = slide.querySelector('.soru-fotograflari');
                    if (fotografAlani) {
                        fotografAlani.style.display = index === 0 ? 'block' : 'none';
                    }
                });
            });

            // Bitir düğmesine tıklamadan önce tüm soruların cevaplanıp cevaplanmadığını kontrol et
            bitirButton.addEventListener('click', function(e) {
                e.preventDefault();
                var currentIndex = slider.index;
                var currentSlide = document.querySelectorAll('.uk-slider-items > li')[currentIndex];

                // Son sorunun yanıtlanma durumunu kontrol et
                if (!yanitlanmisSorular[currentIndex]) {
                    var soruTipi = currentSlide.querySelector('.draggable-word') ? 'cumle' : 'coktan_secmeli';
                    var cevaplanmis = false;

                    if (soruTipi === 'coktan_secmeli') {
                        var radioButtons = currentSlide.querySelectorAll('input[type="radio"]');
                        for (var i = 0; i < radioButtons.length; i++) {
                            if (radioButtons[i].checked) {
                                cevaplanmis = true;
                                break;
                            }
                        }
                    } else if (soruTipi === 'cumle') {
                        var dropZones = currentSlide.querySelectorAll('.drop-zone');
                        cevaplanmis = true;
                        for (var i = 0; i < dropZones.length; i++) {
                            if (!dropZones[i].dataset.word) {
                                cevaplanmis = false;
                                break;
                            }
                        }
                    }

                    if (cevaplanmis) {
                        yanitlanmisSorular[currentIndex] = true;
                    } else {
                        // Uyarı göster
                        Swal.fire({
                            title: 'Uyarı!',
                            text: 'Lütfen soruyu cevaplayınız.',
                            icon: 'warning',
                            confirmButtonText: 'Tamam',
                            confirmButtonColor: '#9733EE'
                        });
                        return;
                    }
                }

                // Tüm soruların yanıtlanmış olup olmadığını kontrol et
                var tumSorularCevaplandi = yanitlanmisSorular.every(function(yanit) {
                    return yanit;
                });

                if (tumSorularCevaplandi) {
                    sinaviBitir();
                } else {
                    // Yanıtlanmamış soruları bul
                    var yanitlanmamisSorular = [];
                    yanitlanmisSorular.forEach(function(yanit, index) {
                        if (!yanit) {
                            yanitlanmamisSorular.push(index + 1);
                        }
                    });

                    Swal.fire({
                        title: 'Uyarı!',
                        html: 'Yanıtlanmamış sorularınız var: ' + yanitlanmamisSorular.join(', ') + '<br>Sınavı bitirmek için tüm soruları cevaplamalısınız.',
                        icon: 'warning',
                        confirmButtonText: 'Tamam',
                        confirmButtonColor: '#9733EE'
                    });
                }
            });
        });

        function sinaviBitir() {
            clearInterval(sureSayaci);

            var cevaplar = {};
            var dogruCevapSayisi = 0;
            var yanlisCevapSayisi = 0;
            var toplamSoruSayisi = <?php echo count($sorular); ?>;
            var sonucHTML = '';

            // Çoktan seçmeli soruların cevaplarını topla
            document.querySelectorAll('input[type="radio"]').forEach(function(input) {
                var soruId = input.getAttribute('data-soru-id');
                if (input.checked) {
                    cevaplar[soruId] = {
                        verilen_cevap: input.value
                    };
                }
            });

            // Cümle tamamlama sorularının cevaplarını topla
            document.querySelectorAll('[id^="sentence-"]').forEach(function(sentence) {
                var soruId = sentence.getAttribute('data-soru-id');
                var kelimeDizisi = [];
                sentence.querySelectorAll('.drop-zone').forEach(function(zone) {
                    if (zone.dataset.word) {
                        kelimeDizisi.push(zone.dataset.word);
                    }
                });
                cevaplar[soruId] = {
                    verilen_cevap: kelimeDizisi.join(' ')
                };
            });

            // Bitiş zamanını kaydet
            const bitisZamani = new Date();

            // Tarayıcı ve cihaz bilgilerini al
            const userAgent = navigator.userAgent;
            const platform = navigator.platform;

            // Sonuçları sunucuya gönder
            $.ajax({
                url: '/islemler/seviye_sinav_kullanici_islemleri.php',
                type: 'POST',
                dataType: 'json',
                data: {
                    islem: 'sinav_sonucu_kaydet',
                    sinav_id: <?php echo $sinav_id; ?>,
                    baslama_zamani: baslamaZamani.toISOString(),
                    bitis_zamani: bitisZamani.toISOString(),
                    cevaplar: JSON.stringify(cevaplar),
                    tarayici: userAgent,
                    cihaz: platform,
                    ikinci_sinav: <?= $ikinci_sinav_mi ? 'true' : 'false' ?>,

                    csrf_token: '<?= $_SESSION['csrf_token'] ?? '' ?>'
                },
                success: function(response) {
                    if (response.success) {
                        // Sınav bittiğini işaretle
                        sinavBitti = true;

                        // LocalStorage'daki sekme değişim sayısını sıfırla
                        localStorage.setItem('sekme_degisim_' + <?php echo $sinav['id']; ?>, '0');

                        // Sonuç ekranını göster
                        document.getElementById('toplam-soru-sayisi').textContent = response.toplam_soru;
                        document.getElementById('dogru-cevap-sayisi').textContent = response.dogru_sayisi;
                        document.getElementById('basari-orani').textContent = '%' + response.puan;

                        // Progress bar'ı güncelle
                        document.querySelector('.yuzdeli-progress').style.setProperty('--progress', response.puan * 3.6 + 'deg');
                        document.querySelector('.yuzde-deger').textContent = '%' + response.puan;

                        // Başarı durumunu kontrol et ve uygun mesajı göster
                        showResultMessage(response.dogru_sayisi, response.toplam_soru);

                        // Soru sonuçlarını göster - sonuc_html kaldırıldığı için seviye_sinav_cevaplar tablosundan veri çekilecek
                        // Sayfa yenilendiğinde zaten PHP tarafında gösteriliyor

                        // Sınav ekranını gizle, sonuç ekranını göster
                        document.getElementById('sinav-ekrani').style.display = 'none';
                        document.getElementById('sonuc-ekrani').style.display = 'block';

                        // Eğer sertifika kazanıldıysa sertifika bölümünü göster
                        if (response.sertifika_kazandi) {
                            setTimeout(function() {
                                showSertifikaBolumu();
                            }, 2000); // 2 saniye sonra göster
                        }
                    } else {
                        alert('Sınav sonuçları kaydedilirken bir hata oluştu: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    alert('Sunucu hatası: ' + error);
                }
            });
        }

        document.addEventListener("DOMContentLoaded", function() {
            const words = document.querySelectorAll(".draggable-word");
            const dropZones = document.querySelectorAll(".drop-zone");

            // UIkit slider'ın sürükle bırak ile çakışmasını önlemek için
            const slider = document.querySelector("[uk-slider]");
            if (slider) {
                // Sürükle bırak işlemi sırasında slider'ın draggable özelliğini geçici olarak devre dışı bırak
                words.forEach(word => {
                    word.addEventListener("dragstart", function() {
                        slider.setAttribute("data-original-draggable", slider.getAttribute("draggable") || "true");
                        UIkit.slider(slider).options.draggable = false;
                    });

                    word.addEventListener("dragend", function() {
                        // İşlem bittiğinde slider'ın draggable özelliğini geri yükle
                        setTimeout(() => {
                            UIkit.slider(slider).options.draggable =
                                slider.getAttribute("data-original-draggable") === "true";
                        }, 100);
                    });
                });
            }

            words.forEach(word => {
                word.addEventListener("dragstart", function(event) {
                    // Eğer kelime zaten kullanılmışsa sürüklemeyi engelle
                    if (this.classList.contains('used')) {
                        event.preventDefault();
                        return false;
                    }

                    event.dataTransfer.setData("text", event.target.dataset.word);
                    event.dataTransfer.setData("elementId", event.target.id);
                    event.dataTransfer.setData("sourceElementId", this.getAttribute('data-id'));
                    // Sürükleme sırasında seçili olduğunu göster
                    this.classList.add("selected-option");
                    // Sürükleme sırasında slider'ın kaydırılmasını engelle
                    event.stopPropagation();
                });

                word.addEventListener("dragend", function(event) {
                    // Sürükleme bittiğinde seçili sınıfını kaldır
                    this.classList.remove("selected-option");
                });

                // Basılı tutma efekti için
                word.addEventListener("mousedown", function() {
                    if (!this.classList.contains('used')) {
                        this.classList.add("selected-option");
                    }
                });

                word.addEventListener("mouseup", function() {
                    // Sürükleme başlamadıysa seçili sınıfını kaldır
                    if (!this.classList.contains("dragging")) {
                        this.classList.remove("selected-option");
                    }
                });

                // Dokunmatik cihazlar için
                word.addEventListener("touchstart", function() {
                    if (!this.classList.contains('used')) {
                        this.classList.add("selected-option");
                    }
                });

                word.addEventListener("touchend", function() {
                    // Sürükleme başlamadıysa seçili sınıfını kaldır
                    if (!this.classList.contains("dragging")) {
                        this.classList.remove("selected-option");
                    }
                });

                // Her kelimeye bir benzersiz ID ekle
                word.setAttribute('data-id', 'word-' + Math.random().toString(36).substr(2, 9));
            });

            dropZones.forEach(zone => {
                zone.addEventListener("dragover", function(event) {
                    event.preventDefault();
                    event.stopPropagation(); // Slider'ın olayları yakalamasını engelle
                    // Sürükleme sırasında drop zone'un üzerine gelince görsel geri bildirim
                    this.style.background = "rgba(249, 136, 231, 0.2)";
                });

                zone.addEventListener("dragleave", function(event) {
                    // Sürükleme sırasında drop zone'dan çıkınca eski haline getir
                    this.style.background = "none";
                });

                zone.addEventListener("drop", function(event) {
                    event.preventDefault();
                    event.stopPropagation(); // Slider'ın olayları yakalamasını engelle
                    const wordText = event.dataTransfer.getData("text");
                    const sourceElementId = event.dataTransfer.getData("sourceElementId");

                    // Arka plan rengini normale döndür
                    this.style.background = "none";

                    // Eğer boşsa bırak
                    if (!zone.dataset.word) {
                        // Kelimeyi drop zone'a ekle
                        zone.innerText = wordText;
                        zone.dataset.word = wordText;
                        zone.dataset.sourceElementId = sourceElementId;

                        // Orijinal kelimeyi kullanılmış olarak işaretle
                        const sourceElement = document.querySelector(`[data-id="${sourceElementId}"]`);
                        if (sourceElement) {
                            sourceElement.classList.add('used');
                            sourceElement.setAttribute('draggable', 'false');
                        }

                        // Çarpı işaretini ekle
                        const removeButton = document.createElement('div');
                        removeButton.className = 'remove-word';
                        removeButton.innerHTML = '×';
                        removeButton.addEventListener('click', function() {
                            clearDropZone(zone);
                        });
                        zone.appendChild(removeButton);
                    }
                });

                // Çift tıklama ve dokunmatik için tek tıklama işlevi
                zone.addEventListener("dblclick", function() {
                    clearDropZone(this);
                });

                // Mobil cihazlar için dokunma işlevi
                zone.addEventListener("touchstart", function(event) {
                    // Dokunma başlangıç zamanını kaydet
                    this.touchStartTime = new Date().getTime();
                    this.touchStartX = event.touches[0].clientX;
                    this.touchStartY = event.touches[0].clientY;
                });

                zone.addEventListener("touchend", function(event) {
                    // Dokunma süresi ve mesafesini hesapla
                    const touchEndTime = new Date().getTime();
                    const touchTime = touchEndTime - this.touchStartTime;

                    // Eğer kısa bir dokunma ise ve içerik doluysa temizle
                    if (touchTime < 300 && this.dataset.word) {
                        clearDropZone(this);
                        event.preventDefault(); // Diğer olayları engelle
                    }
                });
            });

            // Drop zone'u temizleme fonksiyonu
            function clearDropZone(zone) {
                if (zone.dataset.word) {
                    // Orijinal kelimeyi tekrar kullanılabilir yap
                    const sourceElementId = zone.dataset.sourceElementId;
                    const sourceElement = document.querySelector(`[data-id="${sourceElementId}"]`);
                    if (sourceElement) {
                        sourceElement.classList.remove('used');
                        sourceElement.setAttribute('draggable', 'true');
                    }

                    // Çarpı işaretini kaldır
                    const removeButton = zone.querySelector('.remove-word');
                    if (removeButton) {
                        removeButton.remove();
                    }

                    // Drop zone'u temizle
                    zone.innerText = "";
                    delete zone.dataset.word;
                    delete zone.dataset.sourceElementId;
                }
            }
        });

        // Çoktan seçmeli sorular için seçenek seçme işlevselliği
        $(document).ready(function() {
            $('input[type="radio"]').on('change', function() {
                // Önce tüm seçeneklerden eski sınıfı kaldır
                $('.option').removeClass('selected-option');

                // Seçili olan input'un en yakınındaki 'uk-text-large' div'ine sınıf ekle
                $(this).closest('label').find('.option').addClass('selected-option');
            });

            // Sürükle-bırak ve dokunmatik olayları başlat
            initDragDropTouchEvents();
        });

        // Sürükle-bırak ve dokunmatik olaylar
        function initDragDropTouchEvents() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            // Kelime elemanlarını hazırla
            $('.draggable-word').each(function() {
                // Her kelimeye benzersiz ID ata
                $(this).attr('data-id', 'word-' + Math.random().toString(36).substr(2, 9));

                // Masaüstünde sürüklenebilir yap
                $(this).attr('draggable', 'true');

                // Masaüstü için sürükleme olayları
                $(this)[0].addEventListener('dragstart', function(e) {
                    // Kullanılmış kelimeyse sürüklemeyi engelle
                    if ($(this).hasClass('used')) {
                        e.preventDefault();
                        return false;
                    }

                    e.dataTransfer.setData('text', $(this).data('word'));
                    e.dataTransfer.setData('sourceElementId', $(this).attr('data-id'));
                    $(this).addClass('selected-option');
                });

                $(this)[0].addEventListener('dragend', function() {
                    $(this).removeClass('selected-option');
                });

                // Mobil için tıklama olayı
                if (isMobile) {
                    $(this).on('click touchstart', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Kullanılmış kelimeyse işlem yapma
                        if ($(this).hasClass('used')) {
                            return false;
                        }

                        // Cümleyi bul
                        const sentenceId = $(this).closest("li").find("[id^='sentence-']").attr('id');

                        // Boş kutuları bul
                        const emptyBoxes = $(`#${sentenceId} .drop-zone:not([data-word])`);

                        if (emptyBoxes.length > 0) {
                            // İlk boş kutuyu al
                            const emptyBox = emptyBoxes.first();

                            // Kelimeyi yerleştir
                            placeWord($(this), emptyBox);
                        }
                    });
                }
            });

            // Kutuları hazırla
            $('.drop-zone').each(function() {
                // Masaüstü için sürükleme-bırakma olayları
                $(this)[0].addEventListener('dragover', function(e) {
                    e.preventDefault();
                    $(this).css('background-color', 'rgba(249, 136, 231, 0.2)');
                });

                $(this)[0].addEventListener('dragleave', function() {
                    $(this).css('background-color', '');
                });

                $(this)[0].addEventListener('drop', function(e) {
                    e.preventDefault();

                    // Kutuda zaten bir kelime varsa işlem yapma
                    if ($(this).attr('data-word')) {
                        return false;
                    }

                    const wordText = e.dataTransfer.getData('text');
                    const sourceId = e.dataTransfer.getData('sourceElementId');
                    const wordElement = $(`[data-id="${sourceId}"]`);

                    // Kelimeyi yerleştir
                    placeWord(wordElement, $(this));

                    // Arka plan rengini temizle
                    $(this).css('background-color', '');
                });

                // Mobil için tıklama olayı
                if (isMobile) {
                    $(this).on('click touchstart', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Kutuda kelime varsa temizle
                        if ($(this).attr('data-word')) {
                            clearBox($(this));
                        }
                    });
                } else {
                    // Masaüstünde çift tıklama ile temizle
                    $(this).on('dblclick', function() {
                        if ($(this).attr('data-word')) {
                            clearBox($(this));
                        }
                    });
                }
            });
        }

        // Kelimeyi kutuya yerleştir
        function placeWord(word, box) {
            const wordText = word.data('word');
            const wordId = word.attr('data-id');

            // Kutuya kelimeyi ekle
            box.text(wordText);
            box.attr('data-word', wordText);
            box.attr('data-source-element-id', wordId);

            // Kelimeyi kullanılmış olarak işaretle
            word.addClass('used');
            word.attr('draggable', 'false');

            // Çarpı işaretini ekle
            const removeButton = $('<div class="remove-word">×</div>');
            removeButton.on('click touchstart', function(e) {
                e.preventDefault();
                e.stopPropagation();
                clearBox($(this).parent());
            });

            box.append(removeButton);
        }

        // Kutuyu temizle
        function clearBox(box) {
            const sourceId = box.attr('data-source-element-id');

            // Kelimeyi tekrar kullanılabilir yap
            const word = $(`[data-id="${sourceId}"]`);
            word.removeClass('used');
            word.attr('draggable', 'true');

            // Kutuyu temizle
            box.text('');
            box.removeAttr('data-word');
            box.removeAttr('data-source-element-id');
        }

        // Sertifika bölümünü göster ve PDF.js ile sertifikayı yükle
        function showSertifikaBolumu() {
            document.getElementById('sertifika-bolumu').classList.remove('uk-hidden');

            // Sertifikayı PDF.js ile yükle
            loadSertifikaWithPDFJS();

            // Event listener'ları ekle
            setTimeout(() => {
                const sertifikaIndirBtn = document.getElementById('sertifikaIndirBtn');

                if (sertifikaIndirBtn) {
                    sertifikaIndirBtn.onclick = () => sertifikaIndir();
                }
            }, 100);
        }



        // PDF.js ile sertifika yükle
        async function loadSertifikaWithPDFJS() {
            const pdfViewer = document.getElementById('pdfViewer');
            const pdfLoading = document.getElementById('pdfLoading');

            try {
                // Loading göster
                pdfLoading.style.display = 'block';
                pdfViewer.innerHTML = '';

                // Sertifika dosya yolunu al
                const response = await fetch('/islemler/seviye_sinav_kullanici_islemleri.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        islem: 'sertifika_goster',
                        sinav_id: <?php echo $sinav_id ?? 0; ?>,
                        csrf_token: '<?= $_SESSION['csrf_token'] ?? '' ?>'
                    })
                });

                const data = await response.json();

                if (data.success && data.sertifika_dosya_yolu) {
                    // PDF dosyasını yükle
                    const pdfUrl = '/sertifika-indir?dosya=' + encodeURIComponent(data.sertifika_dosya_yolu) + '&preview=1';

                    // PDF.js ile PDF'i yükle
                    const loadingTask = pdfjsLib.getDocument(pdfUrl);
                    const pdf = await loadingTask.promise;

                    // İlk sayfayı al
                    const page = await pdf.getPage(1);

                    // Canvas oluştur
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');

                    // Sayfa boyutlarını al
                    const viewport = page.getViewport({
                        scale: 1.5
                    });
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    // PDF'i canvas'a çiz
                    const renderContext = {
                        canvasContext: context,
                        viewport: viewport
                    };

                    await page.render(renderContext).promise;

                    // Canvas'ı viewer'a ekle
                    pdfViewer.appendChild(canvas);

                    // Loading'i gizle
                    pdfLoading.style.display = 'none';

                } else {
                    // Hata mesajı göster
                    pdfViewer.innerHTML = '<div style="text-align:center;padding:50px;"><h3>Sertifika bulunamadı</h3><p>Sertifika dosyası henüz oluşturulmamış.</p></div>';
                    pdfLoading.style.display = 'none';
                }

            } catch (error) {
                console.error('PDF yükleme hatası:', error);
                pdfViewer.innerHTML = '<div style="text-align:center;padding:50px;"><h3>Hata</h3><p>Sertifika yüklenirken bir hata oluştu.</p></div>';
                pdfLoading.style.display = 'none';
            }
        }



        // Sertifika indirme fonksiyonu
        function sertifikaIndir(dosyaYolu) {
            if (dosyaYolu) {
                window.open('/sertifika-indir?dosya=' + encodeURIComponent(dosyaYolu), '_blank');
            } else {
                // Dosya yolu yoksa, mevcut sertifikayı indir
                $.ajax({
                    url: '/islemler/seviye_sinav_kullanici_islemleri.php',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        islem: 'sertifika_goster',
                        sinav_id: <?php echo $sinav_id ?? 0; ?>,
                        csrf_token: '<?= $_SESSION['csrf_token'] ?? '' ?>'
                    },
                    success: function(response) {
                        if (response.success && response.sertifika_dosya_yolu) {
                            window.open('/sertifika-indir?dosya=' + encodeURIComponent(response.sertifika_dosya_yolu), '_blank');
                        } else {
                            alert('Sertifika dosyası bulunamadı.');
                        }
                    },
                    error: function() {
                        alert('Sertifika bilgileri alınırken bir hata oluştu.');
                    }
                });
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Sertifika yeniden oluştur butonu
            const sertifikaYenidenOlusturBtn = document.getElementById('sertifikaYenidenOlusturBtn');
            if (sertifikaYenidenOlusturBtn) {
                sertifikaYenidenOlusturBtn.addEventListener('click', sertifikaYenidenOlustur);
            }

            // Sertifika indir butonu
            const sertifikaIndirBtn = document.getElementById('sertifikaIndirBtn');
            if (sertifikaIndirBtn) {
                sertifikaIndirBtn.addEventListener('click', function() {
                    sertifikaIndir();
                });
            }
        });
    </script>



<?php } ?>

<link rel="stylesheet" href="/css/sinavlar.css" />

<?php foot(); ?>