<?php
require 'view.php';
require_once 'db/kredi_fonksiyonlari.php';
require_once 'db/donem_fiyat_fonksiyon.php';

// KDV oranını dönem ID'sine göre belirle
$kdv_orani = 0; // Varsayılan olarak 0


// URL'den session ID parametresini al
$direktSessionId = isset($_GET['id']) ? intval($_GET['id']) : 0;
$tekSessionSatisi = false;

if (!oturumKontrol()) {
    header('Location: /giris-yap');
    exit;
}

// Kullanıcı bilgilerini al
$stmt = $db->prepare("SELECT * FROM uyeler WHERE id = ?");
$stmt->execute([$_SESSION['uye_id']]);
$uye = $stmt->fetch(PDO::FETCH_ASSOC);

// TC kimlik kontrolü - şifrelenmişse çöz
$tc_kimlik_eksik = false;
if (!empty($uye['tc_kimlik'])) {
    require_once 'db/encryption.php';
    $encryption = new Encryption();
    try {
        $decrypted_tc = $encryption->decrypt($uye['tc_kimlik']);
        if (empty($decrypted_tc) || strlen($decrypted_tc) != 11) {
            $tc_kimlik_eksik = true;
        }
    } catch (Exception $e) {
        $tc_kimlik_eksik = true;
    }
} else {
    $tc_kimlik_eksik = true;
}

if ($uye["durum"] == 0) {
    header('Location: /odeme');
    exit;
}

if ($uye["durum"] == 2) {
    header('Location: /odeme-bekliyor');
    exit;
}

$seciliDonemId = seciliDonem($_SESSION['uye_id']);

// KDV oranını paket fiyat bilgisinden alacağız (session satışında dinamik olarak belirlenecek)
$kdv_orani = 0; // Varsayılan olarak 0

// Detaylı bilgileri bu değişkende toplayalım
$ogrenciBilgileri = "";
$ogrenciBilgileri .= "<div style='background-color: #f8f8f8; border: 1px solid #ddd; padding: 15px; margin-bottom: 20px; font-family: Arial, sans-serif; color:#000'>";
$ogrenciBilgileri .= "<h3 style='color: #000; margin-top: 0;'>Öğrenci Session ve Kredi Bilgileri</h3>";

// Kullanıcı bilgileri
$ogrenciBilgileri .= "<p><strong>Kullanıcı ID:</strong> " . $_SESSION['uye_id'] . "</p>";
$ogrenciBilgileri .= "<p><strong>Kullanıcı Adı:</strong> " . $uye['ad'] . " " . $uye['soyad'] . "</p>";

// Mevcut dönem bilgisi
$donemSorgu = $db->prepare("SELECT * FROM donemler WHERE id = ?");
$donemSorgu->execute([$seciliDonemId]);
$donemBilgi = $donemSorgu->fetch(PDO::FETCH_ASSOC);
if ($donemBilgi) {
    $ogrenciBilgileri .= "<p><strong>Seçili Dönem:</strong> " . $donemBilgi['donem_adi'] . " (ID: " . $seciliDonemId . ")</p>";
}

// Kullanıcının mevcut dönemdeki kredisi
$donemKrediSorgu = $db->prepare("SELECT * FROM uye_donem_kredileri WHERE uye_id = ? AND donem_id = ?");
$donemKrediSorgu->execute([$_SESSION['uye_id'], $seciliDonemId]);
$donemKredisi = $donemKrediSorgu->fetch(PDO::FETCH_ASSOC);
if ($donemKredisi) {
    $ogrenciBilgileri .= "<p><strong>Mevcut Dönem Kredisi:</strong> " . $donemKredisi['kredi'] . " kredi</p>";
    $ogrenciBilgileri .= "<p><strong>Toplam Alınan Kredi:</strong> " . $donemKredisi['toplam_alinan'] . " kredi</p>";
    $ogrenciBilgileri .= "<p><strong>Toplam Kullanılan Kredi:</strong> " . $donemKredisi['toplam_kullanilan'] . " kredi</p>";
}

// Eğer URL'den session ID gelirse, bu session'a ait dönemi bul
if ($direktSessionId > 0) {
    // Session ID'ye göre grup ve dönem bilgisini al
    $sessionSor = $db->prepare("SELECT g.*, d.id as donem_id, d.donem_adi as donem_adi FROM gruplar g 
                               INNER JOIN donemler d ON g.donem_id = d.id 
                               WHERE g.id = ? AND g.durum = 1 LIMIT 1");
    $sessionSor->execute([$direktSessionId]);
    $sessionBilgi = $sessionSor->fetch(PDO::FETCH_ASSOC);

    if ($sessionBilgi) {
        $ogrenciBilgileri .= "<div style='background-color: #e9f7fe; padding: 10px; margin-top: 15px;'>";
        $ogrenciBilgileri .= "<h4 style='color: #0066cc; margin-top: 0;'>Belirli Session İçin Satış</h4>";
        $ogrenciBilgileri .= "<p><strong>Satın Alınacak Session:</strong> " . $sessionBilgi['grup_adi'] . " (ID: " . $direktSessionId . ")</p>";
        $ogrenciBilgileri .= "<p><strong>Session Dönem:</strong> " . $sessionBilgi['donem_adi'] . " (ID: " . $sessionBilgi['donem_id'] . ")</p>";

        // Bu dönemden herhangi bir session satın alınmış mı kontrol et
        $donemSatinAlmaSor = $db->prepare("
            SELECT COUNT(*) AS sayi FROM uye_donem_kredileri 
            WHERE uye_id = ? AND donem_id = ?
        ");
        $donemSatinAlmaSor->execute([$_SESSION['uye_id'], $sessionBilgi['donem_id']]);
        $donemSatinAlma = $donemSatinAlmaSor->fetch(PDO::FETCH_ASSOC);

        // Bu session ID satın alınmış mı kontrol et
        $sessionSatinAlmaSor = $db->prepare("
            SELECT COUNT(*) AS sayi FROM ogrenci_grup 
            WHERE uye_id = ? AND grup_id = ?
        ");
        $sessionSatinAlmaSor->execute([$_SESSION['uye_id'], $direktSessionId]);
        $sessionSatinAlma = $sessionSatinAlmaSor->fetch(PDO::FETCH_ASSOC);

        $ogrenciBilgileri .= "<p><strong>Bu Dönemi Daha Önce Satın Almış mı:</strong> " . ($donemSatinAlma['sayi'] > 0 ? "Evet" : "Hayır") . "</p>";
        $ogrenciBilgileri .= "<p><strong>Bu Session'ı Daha Önce Satın Almış mı:</strong> " . ($sessionSatinAlma['sayi'] > 0 ? "Evet" : "Hayır") . "</p>";

        // YENİ KOD: Kullanıcı bu session'ı satın almış mı?
        $tekSessionSatisi = false;
        $gorebildigiDersSayisi = 0;
        $toplamDersSayisi = 0;
        $goremedigiDersSayisi = 0;

        // İki durum var: ya session satın alınmış ama bazı dersleri göremiyoruz, ya da session hiç alınmamış
        if ($sessionSatinAlma['sayi'] > 0) {
            // Kullanıcı bu session'ı satın almış, görebildiği ve göremediği dersleri hesapla

            // 1. Toplam işlenmiş ders sayısını bul
            $islenmisDersSayisiSor = $db->prepare("
                SELECT COUNT(*) as toplam FROM dersler 
                WHERE grup_id = ? AND ders_islendi = 1
            ");
            $islenmisDersSayisiSor->execute([$direktSessionId]);
            $toplamDersSayisi = $islenmisDersSayisiSor->fetch(PDO::FETCH_ASSOC)['toplam'];

            $ogrenciBilgileri .= "<p><strong>Toplam İşlenmiş Ders Sayısı:</strong> " . $toplamDersSayisi . "</p>";

            // 2. Kullanıcının satın alma tarihini bul
            $satinAlmaTarihiSor = $db->prepare("
                SELECT satin_alma_tarihi FROM ogrenci_grup 
                WHERE uye_id = ? AND grup_id = ?
            ");
            $satinAlmaTarihiSor->execute([$_SESSION['uye_id'], $direktSessionId]);
            $satinAlmaTarihi = $satinAlmaTarihiSor->fetch(PDO::FETCH_COLUMN);

            $ogrenciBilgileri .= "<p><strong>Satın Alma Tarihi:</strong> " . $satinAlmaTarihi . "</p>";

            // 3. Bu gruptaki tüm işlenmiş dersleri ders numarasına göre sıralı getir
            $islenmisDerslerSor = $db->prepare("
                SELECT id, ders_tarihi, ders_no, ders_adi FROM dersler 
                WHERE grup_id = ? AND ders_islendi = 1
                ORDER BY ders_no ASC
            ");
            $islenmisDerslerSor->execute([$direktSessionId]);
            $islenmisDersler = $islenmisDerslerSor->fetchAll(PDO::FETCH_ASSOC);

            // 4. Görebildiği ve göremediği dersleri hesapla
            $gorebildigiDersler = array();
            $gruptakiGoremedigiDersIdleri = array();

            // Kullanıcının göremediği dersleri al
            $goremedigiDerslerSor = $db->prepare("SELECT goremedigi_dersler FROM uyeler WHERE id = ?");
            $goremedigiDerslerSor->execute([$_SESSION['uye_id']]);
            $goremedigiDerslerVeri = $goremedigiDerslerSor->fetch(PDO::FETCH_ASSOC);
            $goremedigiDerslerArray = !empty($goremedigiDerslerVeri['goremedigi_dersler']) ?
                json_decode($goremedigiDerslerVeri['goremedigi_dersler'], true) : [];

            $ogrenciBilgileri .= "<p><strong>Kullanıcının Tüm Göremediği Dersler:</strong> " . (empty($goremedigiDerslerArray) ? "Yok" : implode(", ", $goremedigiDerslerArray)) . "</p>";

            // Satın alma tarihini DateTime formatına çevir
            if ($satinAlmaTarihi) {
                $satinAlmaTarihi = new DateTime($satinAlmaTarihi);

                // HATA AYIKLAMA: Göremediği dersleri doğru hesaplayalım
                // Önce tüm işlenmiş dersleri alıp satın alma tarihi ve özel durumlarına göre sınıflandıralım
                $gorebildigiDersler = [];
                $goremedigiDersler = [];

                foreach ($islenmisDersler as $ders) {
                    $dersId = $ders['id'];
                    $dersTarihi = new DateTime($ders['ders_tarihi']);

                    // Kullanıcının göremediği dersler listesinde mi?
                    if (in_array($dersId, $goremedigiDerslerArray)) {
                        $goremedigiDersler[] = $dersId;
                    }
                    // Satın alma tarihinden önceki dersler (offline olmayan gruplarda)
                    else if ($dersTarihi < $satinAlmaTarihi && $sessionBilgi['offline'] != 1) {
                        $goremedigiDersler[] = $dersId;
                    }
                    // Diğer tüm dersler görülebilir
                    else {
                        $gorebildigiDersler[] = $dersId;
                    }
                }

                $goremedigiDersSayisi = count($goremedigiDersler);
                $gorebildigiDersSayisi = count($gorebildigiDersler);

                $ogrenciBilgileri .= "<p><strong>Görebildiği Ders Sayısı:</strong> " . $gorebildigiDersSayisi . "</p>";
                $ogrenciBilgileri .= "<p><strong>Göremediği Ders Sayısı:</strong> " . $goremedigiDersSayisi . "</p>";
                $ogrenciBilgileri .= "<p><strong>Göremediği Dersler:</strong> " . (empty($goremedigiDersler) ? "Yok" : implode(", ", $goremedigiDersler)) . "</p>";

                // Debug kontrolü - doğru değerleri ayarlayalım
                $ogrenciBilgileri .= "<p><strong>Debug Bilgi:</strong> Görebildiği: " . $gorebildigiDersSayisi . ", Göremediği: " . $goremedigiDersSayisi . "</p>";

                // Tek ders satışını aktifleştir
                if ($goremedigiDersSayisi > 0) {
                    $tekSessionSatisi = true;
                    $seciliDonemId = $sessionBilgi['donem_id']; // Dönem ID'yi güncelle
                    $seciliGrup = ['grup_id' => $direktSessionId]; // Seçili grup bilgisini güncelle

                    // Göremediği dersler için eklenecek kredi
                    $toplamGerekliKredi = $goremedigiDersSayisi;
                    $eksikKredi = 0;

                    $ogrenciBilgileri .= "<p><strong>İşlem Türü:</strong> Tek session satışı (Göremediği dersleri tamamlama)</p>";
                    $ogrenciBilgileri .= "<p><strong>Gerekli Kredi Sayısı:</strong> " . $toplamGerekliKredi . " kredi</p>";

                    // Aradaki session dizilerini temizle ve sadece bu session'ı ekle
                    $aradakiSessionIds = [$direktSessionId];
                    $sessionAciklamalari = [[
                        'id' => $direktSessionId,
                        'adi' => $sessionBilgi['grup_adi'],
                        'islenmis_ders' => $toplamDersSayisi,
                        'goremedigi_ders' => $goremedigiDersSayisi
                    ]];
                }
            } else {
                // Satın alma tarihi yoksa, henüz hiç ders satın alınmamış
                $goremedigiDersSayisi = $toplamDersSayisi;
                $gorebildigiDersSayisi = 0;

                $ogrenciBilgileri .= "<p><strong>Durum:</strong> Satın alma tarihi bulunamadı. Satın alma işlemi tamamlanmamış olabilir.</p>";
                $ogrenciBilgileri .= "<p><strong>Görebildiği Ders Sayısı:</strong> 0</p>";
                $ogrenciBilgileri .= "<p><strong>Göremediği Ders Sayısı:</strong> " . $toplamDersSayisi . " (Tüm dersler)</p>";
            }
        } else if ($donemSatinAlma['sayi'] > 0 && $sessionSatinAlma['sayi'] == 0) {
            // Kullanıcı bu dönemden herhangi bir session satın almış ama bu session'ı almamışsa

            // 1. Toplam işlenmiş ders sayısını bul
            $islenmisDersSayisiSor = $db->prepare("
                SELECT COUNT(*) as toplam FROM dersler 
                WHERE grup_id = ? AND ders_islendi = 1
            ");
            $islenmisDersSayisiSor->execute([$direktSessionId]);
            $toplamDersSayisi = $islenmisDersSayisiSor->fetch(PDO::FETCH_ASSOC)['toplam'];

            // Session hiç satın alınmamış, tüm dersler görünmez durumda
            $goremedigiDersSayisi = $toplamDersSayisi;
            $gorebildigiDersSayisi = 0;

            $ogrenciBilgileri .= "<p><strong>Durum:</strong> Dönem satın alınmış, ancak bu session satın alınmamış.</p>";
            $ogrenciBilgileri .= "<p><strong>Toplam İşlenmiş Ders Sayısı:</strong> " . $toplamDersSayisi . "</p>";
            $ogrenciBilgileri .= "<p><strong>Görebildiği Ders Sayısı:</strong> 0</p>";
            $ogrenciBilgileri .= "<p><strong>Göremediği Ders Sayısı:</strong> " . $toplamDersSayisi . " (Tüm dersler)</p>";

            $tekSessionSatisi = true;
            $seciliDonemId = $sessionBilgi['donem_id']; // Dönem ID'yi güncelle
            $seciliGrup = ['grup_id' => $direktSessionId]; // Seçili grup bilgisini güncelle

            // Sabit 10 kredi satışı için değişkenleri ayarla
            $toplamGerekliKredi = $goremedigiDersSayisi > 0 ? $goremedigiDersSayisi : 10;
            $eksikKredi = 0;

            $ogrenciBilgileri .= "<p><strong>İşlem Türü:</strong> Tek session satışı (Yeni session alımı)</p>";
            $ogrenciBilgileri .= "<p><strong>Gerekli Kredi Sayısı:</strong> " . $toplamGerekliKredi . " kredi</p>";

            // Aradaki session dizilerini temizle ve sadece bu session'ı ekle
            $aradakiSessionIds = [$direktSessionId];
            $sessionAciklamalari = [[
                'id' => $direktSessionId,
                'adi' => $sessionBilgi['grup_adi'],
                'islenmis_ders' => $toplamDersSayisi,
                'goremedigi_ders' => $goremedigiDersSayisi
            ]];
        }
        $ogrenciBilgileri .= "</div>"; // Belirli session bilgileri div kapanışı
    }
}

// Kullanıcının onay bekleyen ödemesi var mı kontrol et
$bekleyenOdemeSor = $db->prepare("SELECT * FROM odemeler WHERE uye_id = ? AND odeme_yontemi = 'bankaHavalesi' AND durum = 0 ORDER BY id DESC LIMIT 1");
$bekleyenOdemeSor->execute([$_SESSION['uye_id']]);
$bekleyenOdeme = $bekleyenOdemeSor->fetch(PDO::FETCH_ASSOC);

if ($bekleyenOdeme) {
    header('Location: /dekont-yukle');
    exit;
}

// Eğer kullanıcı doğrulanmamışsa doğrulama sayfasına yönlendir
if ($uye['dogrulama_durumu'] == 0) {
    header('Location: /dogrulama');
    exit;
}

// Kullanıcının eksik kredilerini hesapla
// Mevcut grup için kalan derslerin tamamlanması için yeterli kredi olup olmadığını kontrol et
$seciliGrup = getSeciliDonemGrubu($_SESSION['uye_id'], $seciliDonemId);
$eksikKredi = 0;
$eksikKrediMesaji = '';

// Normal session satışı için özetlemeye devam et
$ogrenciBilgileri .= "<div style='background-color: #f0f9e8; padding: 10px; margin-top: 15px;'>";
$ogrenciBilgileri .= "<h4 style='color: #000; margin-top: 0;'>Normal Session Satışı Bilgileri</h4>";

if ($seciliGrup) {
    $grupId = $seciliGrup['grup_id'];

    // Seçili grup bilgisini sorgula
    $grupSorgu = $db->prepare("SELECT g.*, d.donem_adi 
                              FROM gruplar g 
                              INNER JOIN donemler d ON g.donem_id = d.id 
                              WHERE g.id = ?");
    $grupSorgu->execute([$grupId]);
    $grupBilgisi = $grupSorgu->fetch(PDO::FETCH_ASSOC);

    $ogrenciBilgileri .= "<p><strong>Seçili Grup ID:</strong> " . $grupId . "</p>";
    if ($grupBilgisi) {
        $ogrenciBilgileri .= "<p><strong>Seçili Grup Adı:</strong> " . $grupBilgisi['grup_adi'] . "</p>";
        $ogrenciBilgileri .= "<p><strong>Grup Dönemi:</strong> " . $grupBilgisi['donem_adi'] . "</p>";
    }

    // İşlenmiş (görülmüş) ders sayısını bul
    $islenmisKalanDersSayisiSor = $db->prepare("
        SELECT COUNT(*) as islenmis_ders
        FROM dersler 
        WHERE grup_id = ? AND ders_islendi = 1
    ");
    $islenmisKalanDersSayisiSor->execute([$grupId]);
    $islenmisKalanDersSayisi = $islenmisKalanDersSayisiSor->fetch(PDO::FETCH_ASSOC)['islenmis_ders'];

    $ogrenciBilgileri .= "<p><strong>İşlenmiş Ders Sayısı:</strong> " . $islenmisKalanDersSayisi . "</p>";

    // Kullanıcının göremediği dersler
    $goremedigiDerslerSor = $db->prepare("SELECT goremedigi_dersler FROM uyeler WHERE id = ?");
    $goremedigiDerslerSor->execute([$_SESSION['uye_id']]);
    $goremedigiDerslerVeri = $goremedigiDerslerSor->fetch(PDO::FETCH_ASSOC);
    $goremedigiDerslerArray = !empty($goremedigiDerslerVeri['goremedigi_dersler']) ?
        json_decode($goremedigiDerslerVeri['goremedigi_dersler'], true) : [];

    // Bu grup içindeki göremediği dersleri say
    $gruptakiGoremedigiDersSayisi = 0;
    if (!empty($goremedigiDerslerArray)) {
        $gruptakiDersIdleriSor = $db->prepare("SELECT id FROM dersler WHERE grup_id = ? AND ders_islendi = 1");
        $gruptakiDersIdleriSor->execute([$grupId]);
        $gruptakiDersIdleri = $gruptakiDersIdleriSor->fetchAll(PDO::FETCH_COLUMN);

        $gruptakiGoremedigiDersIdleri = array_intersect($goremedigiDerslerArray, $gruptakiDersIdleri);
        $gruptakiGoremedigiDersSayisi = count($gruptakiGoremedigiDersIdleri);

        $ogrenciBilgileri .= "<p><strong>Gruptaki Göremediği Dersler:</strong> " . (empty($gruptakiGoremedigiDersIdleri) ? "Yok" : implode(", ", $gruptakiGoremedigiDersIdleri)) . "</p>";
        $ogrenciBilgileri .= "<p><strong>Gruptaki Göremediği Ders Sayısı:</strong> " . $gruptakiGoremedigiDersSayisi . "</p>";
    }

    // Kullanıcının görebildiği ders sayısı: İşlenmiş dersler - göremediği dersler
    $gorebildigiDersSayisi = $islenmisKalanDersSayisi - $gruptakiGoremedigiDersSayisi;
    $ogrenciBilgileri .= "<p><strong>Görebildiği Ders Sayısı:</strong> " . $gorebildigiDersSayisi . "</p>";

    // --------- YENİ DÜZELTME BAŞLANGIÇ --------------
    // Her session için sabit 10 kredi olacak şekilde düzenleme

    // Kullanıcının mevcut session için kaç krediye sahip olduğunu hesaplayalım
    // Görebildiği ders sayısı = sahip olduğu kredi miktarı
    $mevcutSessionKredi = $gorebildigiDersSayisi;
    $ogrenciBilgileri .= "<p><strong>Mevcut Session'daki Mevcut Kredi:</strong> " . $mevcutSessionKredi . "</p>";

    // Her session için 10 kredi gerekli, kullanıcının eksik kredisi:
    $gerekliKredi = 10 - $mevcutSessionKredi;

    // Eğer kredisi negatif olursa (10'dan fazla derse erişimi varsa) 0 olarak ayarla
    if ($gerekliKredi < 0) {
        $gerekliKredi = 0;
    }

    $ogrenciBilgileri .= "<p><strong>Mevcut Session için Gerekli Kredi:</strong> " . $gerekliKredi . "</p>";
    // --------- YENİ DÜZELTME BİTİŞ --------------

    // Kullanıcının dönem kredisini al
    $donemKrediSor = $db->prepare("SELECT kredi FROM uye_donem_kredileri WHERE uye_id = ? AND donem_id = ?");
    $donemKrediSor->execute([$_SESSION['uye_id'], $seciliDonemId]);
    $donemKredi = $donemKrediSor->fetch(PDO::FETCH_ASSOC);
    $mevcutDonemKredi = $donemKredi ? $donemKredi['kredi'] : 0;
    $ogrenciBilgileri .= "<p><strong>Mevcut Dönemdeki Toplam Kredi:</strong> " . $mevcutDonemKredi . "</p>";

    // Eksik kredi hesapla - mevcut session için ne kadar kredi gerektiğini hesapla
    if ($mevcutDonemKredi < $gerekliKredi) {
        // Eksik kredi diğer yerde kullanılacak - değişken adı çakışmasını önlemek için yeniden tanımlanıyor
        $eksikKredi = $gerekliKredi - $mevcutDonemKredi;
        $eksikKrediMesaji = "Mevcut dersleri tamamlamak için " . $eksikKredi . " derse ve bir sonraki session için 10 derse ihtiyacınız var.";
        $ogrenciBilgileri .= "<p><strong>Eksik Kredi Durumu:</strong> Mevcut session için " . $eksikKredi . " krediye ihtiyaç var.</p>";
    } else {
        // Eğer mevcut session için yeterli kredi varsa
        $kacKrediKalacak = $mevcutDonemKredi - $gerekliKredi;
        $eksikKrediMesaji = "Bir sonraki session için " . max(0, 10 - $kacKrediKalacak) . " derse daha ihtiyacınız var.";
        // Bir sonraki session için eksik kredi (10 - kalan kredi)
        $eksikKredi = $kacKrediKalacak >= 10 ? 0 : (10 - $kacKrediKalacak);
        $ogrenciBilgileri .= "<p><strong>Eksik Kredi Durumu:</strong> Mevcut session için yeterli kredi mevcut. Kalan kredi: " . $kacKrediKalacak . "</p>";
        $ogrenciBilgileri .= "<p><strong>Bir Sonraki Session için Eksik Kredi:</strong> " . max(0, 10 - $kacKrediKalacak) . "</p>";
    }
    $ogrenciBilgileri .= "<p><strong>Eksik Kredi Mesajı:</strong> " . $eksikKrediMesaji . "</p>";

    // Aradaki sessionları belirlemek için
    $aradakiSessionIds = [];
    $sessionAciklamalari = [];

    // ARADAKI SESSIONLARI BUL 
    // Mevcut sessiondan sonraki tüm sessionları bul (paket_satisi=1 olanı bulana kadar)
    $sonrakiSessionlarSor = $db->prepare("
        SELECT g.id, g.grup_adi, g.paket_satisi, g.id as session_id,
        (SELECT COUNT(*) FROM dersler WHERE grup_id = g.id AND ders_islendi = 1) as islenmisDersSayisi
        FROM gruplar g
        WHERE g.donem_id = ?
        AND g.id > ?
        AND g.durum = 1
        ORDER BY g.id ASC
    ");
    $sonrakiSessionlarSor->execute([$seciliDonemId, $grupId]);
    $sonrakiSessionlar = $sonrakiSessionlarSor->fetchAll(PDO::FETCH_ASSOC);

    $ogrenciBilgileri .= "<p><strong>Sonraki Sessionlar:</strong> " . (count($sonrakiSessionlar) > 0 ? count($sonrakiSessionlar) . " session bulundu" : "Sonraki session bulunamadı") . "</p>";

    if (count($sonrakiSessionlar) > 0) {
        $ogrenciBilgileri .= "<ul>";
    }

    // Session bilgilerini topla
    foreach ($sonrakiSessionlar as $session) {
        // Session'ı ekle
        $aradakiSessionIds[] = $session['id'];

        // Session açıklaması için format oluştur
        $sessionAciklamalari[] = [
            'id' => $session['id'],
            'adi' => $session['grup_adi'],
            'islenmis_ders' => $session['islenmisDersSayisi']
        ];

        $ogrenciBilgileri .= "<li><strong>Session ID:</strong> " . $session['id'] . ", <strong>Adı:</strong> " . $session['grup_adi'] . ", <strong>İşlenmiş Ders:</strong> " . $session['islenmisDersSayisi'] . ", <strong>Paket Satışı:</strong> " . ($session['paket_satisi'] == 1 ? "Evet" : "Hayır") . "</li>";

        // paket_satisi = 1 olan bir session bulunca döngüden çık, 
        // ama döngüden çıkmadan önce bu session da dahil edilmeli
        if ($session['paket_satisi'] == 1) {
            break;
        }
    }

    if (count($sonrakiSessionlar) > 0) {
        $ogrenciBilgileri .= "</ul>";
    }
}

// Toplam Kredi Hesaplaması bölümüne başlamadan önce, lokal değişkenlerimizi oluşturalım ve mevcut değerleri alalım
$mevcutSessionIcinEksikKredi = ($mevcutDonemKredi < $gerekliKredi) ? ($gerekliKredi - $mevcutDonemKredi) : 0;   // Mevcut session için gereken eksik kredi
$sonrakiSessionIcinKredi = 0;                // Sonraki session için gereken kredi (başlangıçta 0)
$toplamGerekliKredi = 0;                     // Toplam gerekli kredi

// Mevcut session için eksik kredi varsa, toplam krediye ekle
if ($mevcutSessionIcinEksikKredi > 0) {
    $toplamGerekliKredi += $mevcutSessionIcinEksikKredi;
}

$ogrenciBilgileri .= "<div style='background-color: #ffe8f0; padding: 10px; margin-top: 15px;'>";
$ogrenciBilgileri .= "<h4 style='color: #d32f2f; margin-top: 0;'>Toplam Kredi Hesaplaması</h4>";

// Mevcut session için eksik kredi bilgisini göster
if ($mevcutSessionIcinEksikKredi > 0) {
    $ogrenciBilgileri .= "<p><strong>Mevcut Session için Eksik Kredi:</strong> " . $mevcutSessionIcinEksikKredi . "</p>";
} else {
    $ogrenciBilgileri .= "<p><strong>Mevcut Session için Eksik Kredi:</strong> 0 (Yeterli kredi mevcut)</p>";
}

// Sonraki session için kredi hesaplaması
if (count($aradakiSessionIds) > 0) {
    // Her session için varsayılan 10 kredi ihtiyacı tanımla
    foreach ($sessionAciklamalari as &$session) {
        $session['ihtiyac'] = 10;
    }

    if (isset($sessionAciklamalari[0])) {
        // Mevcut session'dan kalan krediyi sonraki session için kullan
        if (isset($kacKrediKalacak) && $kacKrediKalacak > 0) {
            $sonrakiSessionIcinKredi = max(0, 10 - $kacKrediKalacak);
            $sessionAciklamalari[0]['ihtiyac'] = $sonrakiSessionIcinKredi;
        } else {
            $sonrakiSessionIcinKredi = 10;
            $sessionAciklamalari[0]['ihtiyac'] = 10;
        }

        // Sonraki session için gereken krediyi toplama ekle
        $toplamGerekliKredi += $sonrakiSessionIcinKredi;

        $ogrenciBilgileri .= "<p><strong>Bir Sonraki Session için Gerekli Kredi:</strong> " . $sonrakiSessionIcinKredi . "</p>";
        $ogrenciBilgileri .= "<p><strong>Bir Sonraki Session Adı:</strong> " . $sessionAciklamalari[0]['adi'] . "</p>";
        $ogrenciBilgileri .= "<p><strong>Kalan Kredi ile Hesaplama:</strong> Mevcut session'dan kalan " . (isset($kacKrediKalacak) ? $kacKrediKalacak : "0") . " kredi, sonraki session için gerekli " . $sonrakiSessionIcinKredi . " kredi</p>";
    }
}

// Toplam gerekli kredi hesaplamasının açıklaması
$ogrenciBilgileri .= "<p><strong>Toplam Gerekli Kredi Hesaplama:</strong> ";
if ($mevcutSessionIcinEksikKredi > 0) {
    $ogrenciBilgileri .= "Mevcut session eksik kredi (" . $mevcutSessionIcinEksikKredi . ") + ";
} else {
    $ogrenciBilgileri .= "Mevcut session yeterli (0) + ";
}
$ogrenciBilgileri .= "Sonraki session için gerekli kredi (" . $sonrakiSessionIcinKredi . ") = " . $toplamGerekliKredi . "</p>";

// Toplam kredi ve session sayısı bilgilerini ekle
$ogrenciBilgileri .= "<p><strong>Toplam Gerekli Kredi:</strong> " . $toplamGerekliKredi . "</p>";
$ogrenciBilgileri .= "<p><strong>Session Sayısı:</strong> " . count($aradakiSessionIds) . "</p>";

$ogrenciBilgileri .= "</div>"; // Toplam kredi hesaplaması div kapanışı

$ogrenciBilgileri .= "</div>"; // Ana div kapanışı


$title = "Session Satışı";
$description = "Session Satışı";
head();
?>
<link rel="stylesheet" href="/sozlesmeler/sozlesme.css">

<div class="uk-position-relative circle-background">
    <div class="uk-position-absolute uk-visible@l" id="gradients-container">
        <div class="gradient-mask"></div>
        <div class="gradient-bg">
            <svg xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <filter id="goo">
                        <feGaussianBlur in="SourceGraphic" stdDeviation="10" result="blur" />
                        <feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -8" result="goo" />
                        <feBlend in="SourceGraphic" in2="goo" />
                    </filter>
                </defs>
            </svg>
            <div class="gradients-container">
                <div class="g1"></div>
                <div class="g2"></div>
                <div class="g3"></div>
                <div class="g4"></div>
                <div class="interactive"></div>
            </div>
        </div>
    </div>
    <div class="uk-container uk-container-large uk-position-relative z-9 icsayfa-padding">
        <div class="uk-grid-small login-mh-1" uk-grid>
            <div class="uk-width-expand" uk-scrollspy="cls: uk-animation-slide-bottom-medium; delay: 100; repeat: false">
                <div class="uk-grid uk-grid-large uk-margin-large-top">
                    <div class="uk-width-expand uk-visible@m" uk-scrollspy="cls: uk-animation-slide-bottom-medium; delay: 100; repeat: false">
                        <div class="hesabim-menu" uk-sticky="end: #cnt; offset: 200;media: @m">
                            <?php echo getSidebar(); ?>
                        </div>
                    </div>
                    <div class="uk-width-expand uk-hidden@m uk-margin-small-bottom" uk-scrollspy="cls: uk-animation-slide-bottom-medium; delay: 100; repeat: false">
                        <?php echo getMobileSidebar(); ?>
                    </div>
                    <div class="uk-width-3-4@m uk-position-relative" uk-scrollspy="cls: uk-animation-slide-bottom-medium; delay: 100; repeat: false" id="cnt">
                        <div class="account-form-container account-title-padding">
                            <div class="fs20 fw3 border-bottom-green ">Session Satışı</div>
                            <div class="uk-margin-medium-top">
                                <div class="uk-grid uk-grid-small uk-margin-medium-bottom" uk-grid>
                                    <div class="uk-width-1-1">
                                        <?php
                                        // Aktif dönemi getir
                                        $donemSor = $db->prepare("SELECT * FROM donemler WHERE id = $seciliDonemId LIMIT 1");
                                        $donemSor->execute();
                                        $donem = $donemSor->fetch(PDO::FETCH_ASSOC);

                                        $paket_gosterildi = false;

                                        if ($donem) {
                                            $donem_tip_id = $donem['donem_tip_id'];
                                            $donem_offline = $donem['offline'];
                                            // Eğer dönemin 40'lık paket durumu aktifse, hem standart hem 40'lık paketi göster
                                            if ($donem['kirk_paket_durum'] == 1) {
                                                $paketSor = $db->prepare("SELECT * FROM paketler WHERE (sure = 40 OR sure = 10) AND durum = 1 AND offline = :offline AND paket_tip_id = :paket_tip_id ORDER BY standart DESC");
                                                $paketSor->bindParam(':offline', $donem_offline, PDO::PARAM_INT);
                                                $paketSor->bindParam(':paket_tip_id', $donem_tip_id, PDO::PARAM_INT);
                                            } else {
                                                // Sadece standart paketi göster
                                                $paketSor = $db->prepare("SELECT * FROM paketler WHERE standart = 1 AND durum = 1 AND offline = :offline AND paket_tip_id = :paket_tip_id LIMIT 1");
                                                $paketSor->bindParam(':offline', $donem_offline, PDO::PARAM_INT);
                                                $paketSor->bindParam(':paket_tip_id', $donem_tip_id, PDO::PARAM_INT);
                                            }
                                            $paketSor->execute();
                                            $paketler = $paketSor->fetchAll(PDO::FETCH_ASSOC);

                                            if ($paketler) {
                                                if (isset($ikiliPaketGosterildi) && $ikiliPaketGosterildi) {
                                                    // Eğer ikili paket gösterildiyse, standart paketi gösterme
                                                    // Diğer paketler (standart olmayan) için hala gösterim lazımsa
                                                    $kalanPaketSayisi = 0;
                                                    foreach ($paketler as $p) {
                                                        if ($p['standart'] != 1) {
                                                            $kalanPaketSayisi++;
                                                        }
                                                    }

                                                    if ($kalanPaketSayisi > 0) {
                                                        echo '<div class="uk-grid uk-grid-small uk-child-width-1-expand" uk-grid>';
                                                    }
                                                } else {
                                                    echo '<div class="uk-grid uk-grid-small uk-child-width-1-1" uk-grid>';
                                                }

                                                foreach ($paketler as $paket) {
                                                    // Dönem bazlı fiyat kontrolü
                                                    $gosterilecek_fiyat = $paket['fiyat']; // Varsayılan fiyat
                                                    $fiyat_bilgisi = getDonemFiyat($paket['id'], $seciliDonemId);
                                                    if ($fiyat_bilgisi) {
                                                        $gosterilecek_fiyat = $fiyat_bilgisi['fiyat'];
                                                    }
                                                    
                                                    // KDV oranını paket fiyat bilgisinden al
                                                    $paket_kdv_orani = $fiyat_bilgisi['kdv_orani'] ?? 0;
                                                    
                                                    // Bir kredinin fiyatını hesapla - dönem bazlı fiyat üzerinden
                                                    $birKrediFiyat = $gosterilecek_fiyat / 10;

                                                    // 40'lık paket için özel işlem
                                                    if ($paket['sure'] == 40) {
                                                        // 40'lık paket için dönem bazlı fiyat kullan
                                                        $krediSayisi = $paket['sure'];
                                                        $fiyat = $gosterilecek_fiyat;

                                                        // KDV hesaplamaları - sistemdeki fiyat KDV'siz, KDV'yi üzerine ekliyoruz
                                                        $kdvsiz_fiyat = $gosterilecek_fiyat; // Sistemdeki fiyat KDV'siz
                                                        $kdv_tutari = $gosterilecek_fiyat * ($paket_kdv_orani / 100); // KDV tutarı hesapla
                                                        $kdvli_fiyat = $gosterilecek_fiyat + $kdv_tutari; // KDV dahil toplam fiyat

                                                        // 40'lık paket için açıklama
                                                        $aciklama = '<div class="uk-margin-small-top">';
                                                        $aciklama .= '<div class="uk-margin-small-bottom">' . (!empty($paket['paket_aciklama']) ? $paket['paket_aciklama'] : '') . '</div>';
                                                        $aciklama .= '<div class="mb3 mt3">Toplam: <span class="uk-text-warning uk-text-bold">' . $paket['sure'] . ' ders</span></div>';
                                                        $aciklama .= '</div>';

                                                        // Paket adını belirle
                                                        $paketAdi = !empty($paket['paket_adi']) ? $paket['paket_adi'] : '';

                                                        // 40'lık paketi göster
                                                        echo '<div>';
                                                        echo '<label class="packet-label mb2 paket-secim" data-paket-id="' . $paket['id'] . '">';
                                                        echo '<input type="radio" name="paket_secim" value="' . $paket['id'] . '" ';
                                                        echo 'data-kredi="' . $paket['sure'] . '" ';
                                                        echo 'data-fiyat="' . $kdvli_fiyat . '" ';
                                                        echo 'data-sessioncount="4" ';
                                                        echo 'class="uk-radio">';
                                                        echo '<div class="packet-label-content" style="background-color: #000035;">';
                                                        echo '<div class="fs15 fw6">' . $paketAdi . ' (' . $paket['sure'] . ' ders)</div>';
                                                        echo '<div class="uk-text-small uk-text-white fw5">' . $aciklama . '</div>';
                                                        echo '<div class="fw5">';
                                                        echo '<div class="uk-text-small uk-text-muted">KDV\'siz: ' . number_format($kdvsiz_fiyat, 0, ',', '.') . ' ₺</div>';
                                                        echo '<div class="uk-text-small uk-text-muted">KDV (%' . $paket_kdv_orani . '): +' . number_format($kdv_tutari, 0, ',', '.') . ' ₺</div>';
                                                        echo '<div class="uk-text-bold uk-text-white"><b>' . number_format($kdvli_fiyat, 0, ',', '.') . '</b> ₺ (KDV Dahil)</div>';
                                                        echo '</div>';
                                                        echo '</div>';
                                                        echo '</label>';
                                                        echo '</div>';

                                                        // 40'lık paket gösterildi bayrağını ayarla (standart paketi tekrar göstermemek için)
                                                        $kirklikPaketGosterildi = true;

                                                        continue; // 40'lık paket için işlem bitti, sonraki pakete geç
                                                    }

                                                    // Bu paket için gereken toplam kredi ve fiyat hesabı
                                                    $krediSayisi = $toplamGerekliKredi;
                                                    $fiyat = $birKrediFiyat * $krediSayisi;

                                                    // Eğer özel paket fiyatı varsa dönem bazlı fiyat üzerinden hesapla
                                                    if (!empty($gosterilecek_fiyat) && $gosterilecek_fiyat > 0) {
                                                        $fiyat = $gosterilecek_fiyat * ($krediSayisi / 10);
                                                    }

                                                    // KDV hesaplamaları - sistemdeki fiyat KDV'siz, KDV'yi üzerine ekliyoruz
                                                    $kdvsiz_fiyat = $fiyat; // Sistemdeki fiyat KDV'siz
                                                    $kdv_tutari = $fiyat * ($paket_kdv_orani / 100); // KDV tutarı hesapla
                                                    $kdvli_fiyat = $fiyat + $kdv_tutari; // KDV dahil toplam fiyat

                                                    // Paket adı belirle
                                                    $paketAdi = !empty($paket['baslik']) ? $paket['baslik'] : 'Standart paket';

                                                    // Açıklama için session bilgilerini kullan
                                                    $aciklama = '<div class="uk-margin-small-top">';

                                                    // Eğer tek session satışı ise sadece o sessionun fiyatlanması lazım
                                                    if ($tekSessionSatisi) {
                                                        // Tek session satışında göremediği ders sayısı kadar kredi gerekir
                                                        // Göremediği ders sayısı değişkenini tekrar kontrol edelim

                                                        // Eğer göremediği ders sayısı tanımsız ya da beklenmedik bir değer aldıysa ayrıca hesapla
                                                        if ($goremedigiDersSayisi <= 0 || $goremedigiDersSayisi > $toplamDersSayisi) {
                                                            // İşlenmiş ders sayısını tekrar kontrol et
                                                            $islenmisDersSayisiSor = $db->prepare("
                                                                SELECT COUNT(*) as toplam FROM dersler 
                                                                WHERE grup_id = ? AND ders_islendi = 1
                                                            ");
                                                            $islenmisDersSayisiSor->execute([$direktSessionId]);
                                                            $islenmisToplam = $islenmisDersSayisiSor->fetch(PDO::FETCH_ASSOC)['toplam'];

                                                            // Kullanıcının görebildiği ders sayısını tekrar kontrol et
                                                            $kullaniciDersSayisiSor = $db->prepare("
                                                                SELECT COUNT(*) as goruldugu_ders FROM dersler d
                                                                WHERE d.grup_id = ? AND d.ders_islendi = 1
                                                                AND d.id NOT IN (
                                                                    SELECT CAST(value AS UNSIGNED) 
                                                                    FROM JSON_TABLE(
                                                                        (SELECT goremedigi_dersler FROM uyeler WHERE id = ?),
                                                                        '$[*]' COLUMNS(value VARCHAR(255) PATH '$')
                                                                    ) as jt
                                                                )
                                                            ");
                                                            $kullaniciDersSayisiSor->execute([$direktSessionId, $_SESSION['uye_id']]);
                                                            $kullaniciGorebildigiDers = $kullaniciDersSayisiSor->fetch(PDO::FETCH_ASSOC)['goruldugu_ders'];

                                                            // Göremediği ders sayısını hesapla
                                                            $goremedigiDersSayisi = $islenmisToplam - $kullaniciGorebildigiDers;
                                                        }

                                                        // Eksik kredi sayısını direkt göremediği ders sayısı olarak belirle
                                                        $krediSayisi = $goremedigiDersSayisi;
                                                        $fiyat = $birKrediFiyat * $krediSayisi;

                                                        // Session açıklaması
                                                        $aciklama .= '<div class="uk-margin-small-bottom">Satın alacağınız session: <span class="uk-text-warning uk-text-bold">' . $sessionBilgi['grup_adi'] . '</span></div>';

                                                        if ($goremedigiDersSayisi > 0) {
                                                            $aciklama .= '<div>Göremediğiniz ders sayısı: <span class="uk-text-warning uk-text-bold">' . $goremedigiDersSayisi . ' ders</span></div>';
                                                            $aciklama .= '<div>Gereken kredi: <span class="uk-text-warning uk-text-bold">' . $krediSayisi . ' kredi</span></div>';
                                                        } else {
                                                            $aciklama .= '<div class="uk-margin-small-bottom">Bu session için göremediğiniz ders bulunmamaktadır.</div>';
                                                        }

                                                        $aciklama .= '</div>';

                                                        $paket_gosterildi = true;
                                                    } else {
                                                        // Normal çoklu session satışı
                                                        // Eğer eksik kredi varsa mevcut session için de göster
                                                        if ($mevcutSessionIcinEksikKredi > 0) {
                                                            $aciklama .= '<div class="uk-margin-small-bottom">Mevcut session için: <span class="uk-text-warning uk-text-bold">' . $mevcutSessionIcinEksikKredi . ' ders</span></div>';
                                                            $paket_gosterildi = true;
                                                        } else {
                                                            // $aciklama .= '<div class="uk-margin-small-bottom">Mevcut session için: <span class="uk-text-success">Ek krediye ihtiyaç yok</span></div>';
                                                        }

                                                        // Tüm session bilgilerini listele - sadece birden fazla session varsa göster
                                                        if (count($sessionAciklamalari) > 0) {
                                                            $aciklama .= '<div class="uk-margin-small-bottom">Satın almanız gereken sessionlar:</div>';
                                                            $aciklama .= '<ul class="uk-list uk-list-divider uk-margin-remove" style="max-height: 150px; overflow-y: auto; font-size: 0.9rem;">';

                                                            // Sadece bir sonraki session'ı göster, diğer sessionları yoksay
                                                            if (count($sessionAciklamalari) > 0) {
                                                                $aciklama .= '<li><span uk-icon="check" class="uk-margin-small-right uk-text-success"></span>' . $sessionAciklamalari[0]['adi'];

                                                                // İlk sonraki session için kalan gerekli kredi
                                                                if (isset($ilkSonrakiSessionKrediGereksinimi)) {
                                                                    $aciklama .= ': <span class="uk-text-warning">' . $ilkSonrakiSessionKrediGereksinimi . ' ders</span>';
                                                                } else {
                                                                    $aciklama .= ': <span class="uk-text-warning">10 ders</span>';
                                                                }

                                                                $aciklama .= '</li>';
                                                            }
                                                        }

                                                        $aciklama .= '</ul>';
                                                        $aciklama .= '<div class="mb3 mt3">Toplam: <span class="uk-text-warning uk-text-bold">' . $toplamGerekliKredi . ' ders</span></div>';
                                                        $aciklama .= '</div>';
                                                    }

                                                    // İki seçenek sunmak için kontrol yapalım - sadece çoklu session için
                                                    // Tek session satışında iki seçenek sunmuyoruz
                                                    if (!$tekSessionSatisi && count($sessionAciklamalari) > 1 && $paket['standart'] == 1) {
                                                        // İki farklı paket oluşturalım
                                                        // 1. Sadece mevcut session ve bir sonraki session için gerekli kredi (küçük paket)
                                                        // 2. Tüm sessionlar için gerekli kredi (büyük paket)

                                                        // Küçük paket için toplam kredi hesabı:
                                                        // - Mevcut session için eksik kredi
                                                        // - Bir sonraki session için gerekli kredi
                                                        $kucukPaketMevcutSessionKredi = $mevcutSessionIcinEksikKredi;
                                                        $kucukPaketSonrakiSessionKredi = $sonrakiSessionIcinKredi;
                                                        $kucukPaketKrediSayisi = $kucukPaketMevcutSessionKredi + $kucukPaketSonrakiSessionKredi;
                                                        $kucukPaketFiyat = $birKrediFiyat * $kucukPaketKrediSayisi;

                                                        // Küçük paket için KDV hesaplamaları
                                                        $kucukPaketKdvsizFiyat = $kucukPaketFiyat;
                                                        $kucukPaketKdvTutari = $kucukPaketFiyat * ($paket_kdv_orani / 100);
                                                        $kucukPaketKdvliFiyat = $kucukPaketFiyat + $kucukPaketKdvTutari;

                                                        // Küçük paket açıklaması
                                                        $kucukPaketAciklama = '<div class="uk-margin-small-top">';

                                                        // Mevcut session için eksik kredi
                                                        if ($kucukPaketMevcutSessionKredi > 0) {
                                                            $kucukPaketAciklama .= '<div class="uk-margin-small-bottom">Mevcut session için: <span class="uk-text-warning uk-text-bold">' . $kucukPaketMevcutSessionKredi . ' ders</span></div>';
                                                        } else {
                                                            // $kucukPaketAciklama .= '<div class="uk-margin-small-bottom">Mevcut session için: <span class="uk-text-success">Ek krediye ihtiyaç yok</span></div>';
                                                        }

                                                        // Küçük pakette sadece bir sonraki session'ı listele
                                                        if (isset($sessionAciklamalari[0])) {
                                                            $kucukPaketAciklama .= '<div class="uk-margin-small-bottom">Satın almanız gereken bir sonraki session:</div>';
                                                            $kucukPaketAciklama .= '<ul class="uk-list uk-list-divider uk-margin-remove" style="max-height: 150px; overflow-y: auto; font-size: 0.9rem;">';
                                                            $kucukPaketAciklama .= '<li><span uk-icon="check" class="uk-margin-small-right uk-text-success"></span>' . $sessionAciklamalari[0]['adi'];
                                                            $kucukPaketAciklama .= ': <span class="uk-text-warning">' . $kucukPaketSonrakiSessionKredi . ' ders</span>';
                                                            $kucukPaketAciklama .= '</li>';
                                                            $kucukPaketAciklama .= '</ul>';
                                                        }

                                                        $kucukPaketAciklama .= '<div class="mb3 mt3">Toplam: <span class="uk-text-warning uk-text-bold">' . $kucukPaketKrediSayisi . ' ders</span></div>';
                                                        $kucukPaketAciklama .= '</div>';

                                                        // Büyük paket için - mevcut session için eksik kredi + tüm sonraki sessionlar (her biri için 10 kredi)
                                                        $buyukPaketMevcutSessionKredi = $mevcutSessionIcinEksikKredi;
                                                        $buyukPaketSonrakiSessionlarKredi = 0;

                                                        // Tüm sonraki sessionlar için gerekli kredi hesapla
                                                        for ($i = 0; $i < count($sessionAciklamalari); $i++) {
                                                            if ($i == 0 && isset($kacKrediKalacak) && $kacKrediKalacak > 0) {
                                                                // İlk session için kalan kredi düşülür
                                                                $buyukPaketSonrakiSessionlarKredi += max(0, 10 - $kacKrediKalacak);
                                                            } else {
                                                                // Diğer sessionlar için tam 10 kredi
                                                                $buyukPaketSonrakiSessionlarKredi += 10;
                                                            }
                                                        }

                                                        // Toplam büyük paket kredisi
                                                        $buyukPaketKrediSayisi = $buyukPaketMevcutSessionKredi + $buyukPaketSonrakiSessionlarKredi;
                                                        $buyukPaketFiyat = $birKrediFiyat * $buyukPaketKrediSayisi;

                                                        // Büyük paket için KDV hesaplamaları
                                                        $buyukPaketKdvsizFiyat = $buyukPaketFiyat;
                                                        $buyukPaketKdvTutari = $buyukPaketFiyat * ($paket_kdv_orani / 100);
                                                        $buyukPaketKdvliFiyat = $buyukPaketFiyat + $buyukPaketKdvTutari;

                                                        // Büyük paket açıklaması
                                                        $buyukPaketAciklama = '<div class="uk-margin-small-top">';

                                                        // Mevcut session için eksik kredi
                                                        if ($buyukPaketMevcutSessionKredi > 0) {
                                                            $buyukPaketAciklama .= '<div class="uk-margin-small-bottom">Mevcut session için: <span class="uk-text-warning uk-text-bold">' . $buyukPaketMevcutSessionKredi . ' ders</span></div>';
                                                        } else {
                                                            // $buyukPaketAciklama .= '<div class="uk-margin-small-bottom">Mevcut session için: <span class="uk-text-success">Ek krediye ihtiyaç yok</span></div>';
                                                        }

                                                        // Büyük pakette tüm sonraki sessionları listele
                                                        if (count($sessionAciklamalari) > 0) {
                                                            $buyukPaketAciklama .= '<div class="uk-margin-small-bottom">Satın almanız gereken sessionlar:</div>';
                                                            $buyukPaketAciklama .= $debugSessionBilgisi;

                                                            $buyukPaketAciklama .= '<ul class="uk-list uk-list-divider uk-margin-remove" style="max-height: 150px; overflow-y: auto; font-size: 0.9rem;">';

                                                            // DÜZELTME: Görünmeyen session için özel işlem ekleyelim
                                                            // Session 5 ve Session 6'yı elle ekleyelim (sadece geçici bir çözüm)
                                                            foreach ($aradakiSessionIds as $sessionId) {
                                                                // Session bilgisini veritabanından çek
                                                                $sessionSorgu = $db->prepare("SELECT id, grup_adi FROM gruplar WHERE id = ?");
                                                                $sessionSorgu->execute([$sessionId]);
                                                                $sessionBilgisi = $sessionSorgu->fetch(PDO::FETCH_ASSOC);

                                                                if ($sessionBilgisi) {
                                                                    $buyukPaketAciklama .= '<li><span uk-icon="check" class="uk-margin-small-right uk-text-success"></span>' . $sessionBilgisi['grup_adi'];
                                                                    $buyukPaketAciklama .= ': <span class="uk-text-warning">10 ders</span>';
                                                                    $buyukPaketAciklama .= '</li>';
                                                                }
                                                            }

                                                            $buyukPaketAciklama .= '</ul>';
                                                        }

                                                        $buyukPaketAciklama .= '<div class="mb3 mt3">Toplam: <span class="uk-text-warning uk-text-bold">' . $buyukPaketKrediSayisi . ' ders</span></div>';
                                                        $buyukPaketAciklama .= '</div>';

                                                        // Paket seçeneklerini göstermek için grid container başlat
                                                        echo '<div class="uk-grid uk-grid-small uk-child-width-expand@m" uk-grid>';

                                                        // Ekonomik paket
                                                        echo '<div>';
                                                        echo '<label class="packet-label mb2 paket-secim" data-paket-id="' . $paket['id'] . '">';

                                                        // Küçük paket için session sayısını belirle
                                                        $kucukPaketSessionCount = 1;

                                                        // Eğer küçük paket kredi sayısı 15'ten büyükse sadece mevcut session için eksik krediyi sat
                                                        if ($kucukPaketKrediSayisi > 15) {
                                                            // Sadece mevcut session için eksik kredi (8 kredi)
                                                            $kucukPaketKrediSayisi = $buyukPaketMevcutSessionKredi;
                                                            $kucukPaketFiyat = $birKrediFiyat * $kucukPaketKrediSayisi;

                                                            // Küçük paket açıklamasını güncelle - sadece mevcut session için
                                                            $kucukPaketAciklama = '<div class="uk-margin-small-top">';
                                                            $kucukPaketAciklama .= '<div class="uk-margin-small-bottom">Mevcut session için: <span class="uk-text-warning uk-text-bold">' . $buyukPaketMevcutSessionKredi . ' ders</span></div>';
                                                            $kucukPaketAciklama .= '<div class="mb3 mt3">Toplam: <span class="uk-text-warning uk-text-bold">' . $kucukPaketKrediSayisi . ' ders</span></div>';
                                                            $kucukPaketAciklama .= '</div>';
                                                        } else {
                                                            // 15'ten küçükse mevcut session + bir sonraki session (10 kredi)
                                                            $kucukPaketSessionCount = isset($sessionAciklamalari[0]) ? 2 : 1;
                                                        }

                                                        echo '<input type="radio" name="paket_secim" value="' . $paket['id'] . '" data-kredi="' . $kucukPaketKrediSayisi . '" data-fiyat="' . $kucukPaketKdvliFiyat . '" data-sessioncount="' . $kucukPaketSessionCount . '" class="uk-radio">';
                                                        echo '<div class="packet-label-content" style="background-color: #000035;">';
                                                        echo '<div class="fs15 fw6">' . $paketAdi . ' (Küçük) (' . $kucukPaketKrediSayisi . ' ders)</div>';
                                                        if (!empty($kucukPaketAciklama)) {
                                                            echo '<div class="uk-text-small uk-text-white fw5">' . $kucukPaketAciklama . '</div>';
                                                        }
                                                        echo '<div class="fw5">';
                                                        echo '<div class="uk-text-small uk-text-muted">KDV\'siz: ' . number_format($kucukPaketKdvsizFiyat, 0, ',', '.') . ' ₺</div>';
                                                        echo '<div class="uk-text-small uk-text-muted">KDV (%' . $paket_kdv_orani . '): +' . number_format($kucukPaketKdvTutari, 0, ',', '.') . ' ₺</div>';
                                                        echo '<div class="uk-text-bold uk-text-white"><b>' . number_format($kucukPaketKdvliFiyat, 0, ',', '.') . '</b> ₺ (KDV Dahil)</div>';
                                                        echo '</div>';
                                                        echo '</div>';
                                                        echo '</label>';
                                                        echo '</div>';

                                                        // Tam paket
                                                        echo '<div>';
                                                        echo '<label class="packet-label mb2 paket-secim" data-paket-id="' . $paket['id'] . '">';
                                                        echo '<input type="radio" name="paket_secim" value="' . $paket['id'] . '" data-kredi="' . $buyukPaketKrediSayisi . '" data-fiyat="' . $buyukPaketKdvliFiyat . '" data-sessioncount="' . count($sessionAciklamalari) . '" class="uk-radio" checked>';
                                                        echo '<div class="packet-label-content" style="background-color: #000035;">';
                                                        echo '<div class="fs15 fw6">' . $paketAdi . ' (Tüm Dersler) (' . $buyukPaketKrediSayisi . ' ders)</div>';
                                                        if (!empty($buyukPaketAciklama)) {
                                                            echo '<div class="uk-text-small uk-text-white fw5">' . $buyukPaketAciklama . '</div>';
                                                        }
                                                        echo '<div class="fw5">';
                                                        echo '<div class="uk-text-small uk-text-muted">KDV\'siz: ' . number_format($buyukPaketKdvsizFiyat, 0, ',', '.') . ' ₺</div>';
                                                        echo '<div class="uk-text-small uk-text-muted">KDV (%' . $paket_kdv_orani . '): +' . number_format($buyukPaketKdvTutari, 0, ',', '.') . ' ₺</div>';
                                                        echo '<div class="uk-text-bold uk-text-white"><b>' . number_format($buyukPaketKdvliFiyat, 0, ',', '.') . '</b> ₺ (KDV Dahil)</div>';
                                                        echo '</div>';
                                                        echo '</div>';
                                                        echo '</label>';
                                                        echo '</div>';

                                                        // Grid container kapat
                                                        echo '</div>';

                                                        // Bu paketi artık daha sonra standart formu kullanarak göstermeyelim
                                                        $ikiliPaketGosterildi = true;
                                                        $paket_gosterildi = true;
                                                        continue; // Aşağıdaki kodu çalıştırmayıp bir sonraki paket için devam et
                                                    }


                                                    // Eğer ikili paket gösterildiyse veya 40'lık paket gösterildiyse ve bu standart paketse gösterme
                                                    // Bu koşul, ekonomik/tam paket çifti veya 40'lık paket gösterildikten sonra standart 10'luk paketi tekrar göstermemek için
                                                    if (!((isset($ikiliPaketGosterildi) && $ikiliPaketGosterildi || isset($kirklikPaketGosterildi) && $kirklikPaketGosterildi) && $paket['standart'] == 1) && $krediSayisi != 0) {
                                                        // Eğer diğer paket tek başınaysa grid içine al
                                                        if (!isset($tekliPaketGrid)) {
                                                            echo '<div class="uk-grid uk-grid-small uk-child-width-expand@m" uk-grid>';
                                                            $tekliPaketGrid = true;
                                                        }

                                                        echo '<div>';
                                                        echo '<label class="packet-label mb2 paket-secim" data-paket-id="' . $paket['id'] . '">';

                                                        // Tek session satışında sessionCount=1, çoklu satışta tüm sessionlar
                                                        $sessionCount = $tekSessionSatisi ? 1 : count($sessionAciklamalari);

                                                        echo '<input type="radio" name="paket_secim" value="' . $paket['id'] . '" ';
                                                        echo 'data-kredi="' . $krediSayisi . '" ';
                                                        echo 'data-fiyat="' . $kdvli_fiyat . '" ';
                                                        echo 'data-sessioncount="' . $sessionCount . '" ';
                                                        echo 'class="uk-radio" ' . ($paket['standart'] == 1 && !isset($ikiliPaketGosterildi) ? 'checked' : '') . '>';

                                                        echo '<div class="packet-label-content asdasdsadas" style="background-color: #000035;">';
                                                        echo '<div class="fs15 fw6">' . $paketAdi . ' (' . $krediSayisi . ' ders)</div>';
                                                        if (!empty($aciklama)) {
                                                            echo '<div class="uk-text-small uk-text-white fw5">' . $aciklama . '</div>';
                                                        }
                                                        echo '<div class="fw5">';
                                                        echo '<div class="uk-text-small uk-text-muted">KDV\'siz: ' . number_format($kdvsiz_fiyat, 0, ',', '.') . ' ₺</div>';
                                                        echo '<div class="uk-text-small uk-text-muted">KDV (%' . $paket_kdv_orani . '): +' . number_format($kdv_tutari, 0, ',', '.') . ' ₺</div>';
                                                        echo '<div class="uk-text-bold uk-text-white"><b>' . number_format($kdvli_fiyat, 0, ',', '.') . '</b> ₺ (KDV Dahil)</div>';
                                                        echo '</div>';
                                                        echo '</div>';
                                                        echo '</label>';
                                                        echo '</div>';
                                                        $paket_gosterildi = true;
                                                    }
                                                }

                                                // Eğer tekli paket gridi açıldıysa kapat
                                                if (isset($tekliPaketGrid)) {
                                                    echo '</div>';
                                                }

                                                echo '</div>';
                                            } else {
                                                echo '<div class="custom-alert-box error-alert uk-width-1-1">
                                                <div class="alert-icon">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                        <path d="M15 9L9 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                        <path d="M9 9L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </div>
                                                <div class="alert-content">
                                                    <strong>Bilgi!</strong>
                                                    <p>Şu anda aktif paket bulunmamaktadır.</p>
                                                </div>
                                            </div>';
                                            }
                                        } else {
                                            echo '<div class="custom-alert-box error-alert uk-width-1-1">
                                            <div class="alert-icon">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <path d="M15 9L9 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <path d="M9 9L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            </div>
                                            <div class="alert-content">
                                                <strong>Bilgi!</strong>
                                                <p>Şu anda aktif dönem bulunmamaktadır.</p>
                                            </div>
                                        </div>';
                                        }
                                        ?>
                                    </div>

                                    <?php if (!$paket_gosterildi && !$kirklikPaketGosterildi) { ?>
                                        <script>
                                            window.location.href = "/hesabim";
                                        </script>
                                    <?php } ?>


                                    <?php if (count($sessionAciklamalari) > 1 && $krediSayisi > 10 && !$goremedigiDersSayisi) { ?>
                                        <div class="custom-alert-box error-alert uk-width-1-1">
                                            <div class="alert-icon">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                    <path d="M15 9L9 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                    <path d="M9 9L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>
                                            </div>
                                            <div class="alert-content">
                                                <strong>Bilgi!</strong>
                                                <div class="fw5" style="color:#6cf32a">Eğer sessionları toplu almak istemiyorsanız kayıtlı derslerim sayfasından tek tek satın alabilirsiniz.</div>
                                            </div>
                                        </div>
                                    <?php } ?>

                                    <!-- İndirim kuponu alanı paketlerin altına taşındı -->
                                    <div class="uk-width-1-1 uk-margin-small-top">
                                        <div id="indirimKoduGiris" class="uk-flex uk-flex-between uk-flex-middle">
                                            <input placeholder="İndirim Kodu" class="border-input-k w-100 indirimkuponinput" type="text" id="indirimKodu">
                                            <button class="uk-button small-button uk-margin-small-left" type="button" id="applyDiscount">Uygula</button>
                                        </div>
                                        <style>
                                            .indirimkuponinput::placeholder {
                                                color: white !important;
                                            }
                                        </style>
                                        <div id="indirimKoduGosterim" class="uk-flex uk-flex-between uk-flex-middle uk-margin-small-top" style="display: none;">
                                            <div class="uk-flex uk-flex-middle uk-flex-1">
                                                <span class="uk-badge gradient-button uk-margin-small-right" id="indirimKoduBadge"></span>
                                                <div id="indirimDetay" class="uk-text-small"></div>
                                            </div>
                                            <button class="uk-button uk-button-danger uk-button-small br10" type="button" id="removeDiscount">Kaldır</button>
                                        </div>
                                        <div id="indirimSonucu" class="uk-margin-small-top"></div>
                                    </div>
                                </div>

                                <div class="uk-grid-small" uk-grid>
                                    <div class="uk-width-1-1 uk-margin-small-bottom">
                                        <label class="fw6">Ödeme Yöntemi</label>
                                        <div class="uk-flex uk-flex-wrap uk-margin-small-top">
                                            <label class="uk-margin-right">
                                                <input type="radio" name="o_y_ds" value="krediKarti" class="uk-radio" checked onclick="togglePaymentMethod()"> Kredi Kartı
                                            </label>
                                            <label>
                                                <input type="radio" name="o_y_ds" value="bankaHavalesi" class="uk-radio" onclick="togglePaymentMethod()"> Banka Havalesi
                                            </label>
                                        </div>
                                    </div>
                                    <div id="krediKartiForm">
                                        <!-- Taksit seçenekleri modal -->
                                        <div id="taksitModal" class="uk-modal-full" uk-modal>
                                            <div class="uk-modal-dialog uk-modal-body uk-padding-remove">
                                                <button class="uk-modal-close-full uk-close-large" type="button" uk-close></button>
                                                <div class="uk-padding">
                                                    <div class="uk-container uk-container-expand">
                                                        <h2 class="uk-modal-title uk-text-center uk-margin-medium-bottom">
                                                            <span class="uk-text-gradient">Tüm Bankaların Taksit Seçenekleri</span>
                                                        </h2>
                                                        <div class="uk-text-center uk-text-muted uk-margin-medium-bottom">
                                                            Seçtiğiniz paket için tüm bankaların taksit seçeneklerini inceleyebilirsiniz.
                                                        </div>
                                                        <div id="tumTaksitSecenekleri" class="uk-margin-medium-top">
                                                            <div class="uk-text-center">
                                                                <div uk-spinner></div>
                                                                <div class="uk-margin-small-top">Taksit seçenekleri yükleniyor...</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="uk-grid-small" uk-grid>
                                            <div class="active uk-width-1-2@l uk-margin-medium-bottom">
                                                <div class="uk-grid-small" uk-grid>
                                                    <div class="uk-width-1-1">
                                                        <label>Kart Numarası</label>
                                                        <input placeholder="**** **** **** ****" class="border-input" id="kartno" type="text" name="kart_no">
                                                    </div>
                                                    <div class="uk-width-1-1">
                                                        <label>Kart Üzerindeki İsim</label>
                                                        <input placeholder="Kart Üzerindeki İsim" id="kartisim" class="border-input" type="text" name="kart_sahibi">
                                                    </div>
                                                    <div class="uk-width-1-2">
                                                        <label>Son Kullanma Tarihi</label>
                                                        <input placeholder="AA/YY" maxlength="7" class="border-input" type="text" id="kartskt" name="kart_son_kullanma">
                                                    </div>
                                                    <div class="uk-width-1-2">
                                                        <label>CVC</label>
                                                        <input placeholder="***" class="border-input" type="text" name="kart_cvc" id="kartcvc">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="uk-width-1-2@s uk-visible@l">
                                                <div class="card-wrapper uk-margin-small-top"></div>
                                            </div>
                                            <div class="uk-width-1-1 uk-margin-small-top fw5">
                                                <label>Taksit Seçenekleri</label>
                                                <div id="taksitSecenekleri" class="uk-margin-small-top">
                                                    <p class="uk-text-muted">Taksit seçeneklerini görmek için lütfen kart numaranızı giriniz.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="bankaHavalesiForm" class="uk-width-1-1@l uk-margin-medium-bottom" style="display: none;">
                                        <div class="uk-grid-small mr-50" uk-grid>
                                            <div class="uk-width-1-1">
                                                <label class="fw6">Banka Bilgileri</label>

                                                <?php
                                                // Aktif banka hesaplarını getir
                                                $bankaHesaplariSor = $db->prepare("SELECT * FROM banka_hesaplari WHERE durum = 1 ORDER BY sira ASC");
                                                $bankaHesaplariSor->execute();
                                                $bankaHesaplari = $bankaHesaplariSor->fetchAll(PDO::FETCH_ASSOC);

                                                if (count($bankaHesaplari) > 0) {
                                                    foreach ($bankaHesaplari as $banka) {
                                                ?>
                                                        <div class="banka-hesap-kutusu uk-margin-medium-bottom gradient-border">
                                                            <div class="uk-grid-small" uk-grid>
                                                                <?php if (!empty($banka['logo'])) { ?>
                                                                    <div class="uk-width-auto uk-flex uk-flex-middle">
                                                                        <img src="<?php echo $banka['logo']; ?>" alt="<?php echo $banka['banka_adi']; ?>" width="80" class="uk-border-circle">
                                                                    </div>
                                                                <?php } ?>
                                                                <div class="uk-width-expand">
                                                                    <h4 class="uk-margin-remove-bottom uk-text-white"><?php echo $banka['banka_adi']; ?></h4>
                                                                    <div class="uk-text-muted">Hesap Adı: <?php echo $banka['hesap_adi']; ?></div>
                                                                    <?php if (!empty($banka['sube_adi'])) { ?>
                                                                        <div class="uk-text-muted">Şube: <?php echo $banka['sube_adi']; ?> (<?php echo $banka['sube_kodu']; ?>)</div>
                                                                    <?php } ?>
                                                                    <div class="uk-flex uk-flex-middle uk-margin-small-top">
                                                                        <div class="uk-text-white uk-text-bold">IBAN: <?php echo $banka['iban']; ?></div>
                                                                        <span uk-icon="icon: copy" onclick="copyIBAN('<?php echo $banka['iban']; ?>')" class="uk-margin-small-left uk-text-white" style="cursor: pointer;"></span>
                                                                    </div>
                                                                    <?php if (!empty($banka['aciklama'])) { ?>
                                                                        <div class="uk-margin-small-top uk-text-muted">
                                                                            <span class="uk-text-warning">NOT : </span> <?php echo $banka['aciklama']; ?>
                                                                        </div>
                                                                    <?php } ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                <?php
                                                    }
                                                } else {
                                                    echo '<div class="custom-alert-box error-alert uk-width-1-1">
                <div class="alert-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M15 9L9 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M9 9L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="alert-content">
                    <strong>Bilgi!</strong>
                    <p>Şu anda banka hesap bilgilerimiz güncellenme aşamasındadır. Lütfen daha sonra tekrar deneyiniz.</p>
                </div>
            </div>';
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <script>
                                    function togglePaymentMethod() {
                                        var selectedMethod = document.querySelector('input[name="o_y_ds"]:checked').value;
                                        var krediKartiForm = document.getElementById('krediKartiForm');
                                        var bankaHavalesiForm = document.getElementById('bankaHavalesiForm');

                                        if (selectedMethod === 'krediKarti') {
                                            krediKartiForm.style.display = 'block';
                                            bankaHavalesiForm.style.display = 'none';
                                        } else {
                                            krediKartiForm.style.display = 'none';
                                            bankaHavalesiForm.style.display = 'block';
                                        }
                                    }
                                </script>
                            </div>

                            <div class="uk-margin-small-top">
                                <form id="odemeForm" method="POST" action="/islemler/session_odeme_tamamla.php">
                                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                    <?php if ($tekSessionSatisi && $direktSessionId) { ?>
                                        <input type="hidden" name="t_s_i" value="<?php echo $direktSessionId; ?>">
                                        <input type="hidden" name="t_s_s" value="true">
                                    <?php } ?>
                                    <input type="hidden" name="e_k" value="<?php echo $eksikKredi; ?>">
                                    <input type="hidden" name="t_g_k" id="toplam_gerekli_kredi" value="<?php echo $toplamGerekliKredi; ?>">
                                    <input type="hidden" name="a_s_i" id="aradaki_session_ids" value='<?php echo json_encode($aradakiSessionIds); ?>'>
                                    <input type="hidden" name="s_s_c" id="selected_session_count" value="<?php echo count($sessionAciklamalari); ?>">
                                    <input type="hidden" name="m_s_i" id="mevcut_session_id" value="<?php echo isset($seciliGrup) ? $seciliGrup['grup_id'] : ''; ?>">
                                    <input type="hidden" name="m_s_e" id="mevcut_session_eksik" value="<?php echo $mevcutSessionIcinEksikKredi; ?>">
                                    <?php if ($tekSessionSatisi && !empty($gruptakiGoremedigiDersIdleri)) { ?>
                                        <input type="hidden" name="g_d_i" value='<?php echo json_encode($gruptakiGoremedigiDersIdleri); ?>'>
                                    <?php } ?>
                                    <input type="hidden" name="i_k" id="hidden_indirim_kodu" value="">
                                    <input type="hidden" name="k_o" id="komisyon_orani" value="<?php echo ayarGetir("komisyon_orani") ?: '0'; ?>">
                                    <input type="hidden" name="k_t" id="komisyon_tutari" value="">
                                    <input type="hidden" name="o_t" id="odenecek_tutar" value="">
                                    <input type="hidden" name="p_i" id="hidden_paket_id" value="">
                                    
                                    <!-- Mesafeli Satış Sözleşmesi Onayı -->
                                    <div class="uk-margin-medium-top">
                                        <div class="uk-margin uk-grid-small uk-child-width-auto uk-grid fs13">
                                            <label><input class="uk-checkbox border-white" type="checkbox" id="sozlesmeOnay" required> <a href="#" class="text-link fw5" onclick="sozlesmeGoster('mesafeli_satis')">Mesafeli Satış Sözleşmesi</a>'ni okudum, onaylıyorum.</label>
                                        </div>
                                    </div>
                                    
                                    <button type="button" id="odemeButonu" class="uk-button gradient-button uk-width-1-1 hover-top uk-margin-medium-top">Ödemeyi Tamamla</button>
                                </form>
                            </div>

                            <!-- Taksit seçenekleri butonu -->
                            <div class="uk-width-1-1 uk-margin-medium-top">
                                <button class="uk-button uk-width-1-1 uk-border-rounded fw5" type="button" uk-toggle="target: #taksitModal" style="background: linear-gradient(145deg, #6cf32a 0%, #c0f32a 100%); color: black;">
                                    <span uk-icon="icon: credit-card" class="uk-margin-small-right"></span>
                                    Tüm Bankaların Taksit Seçeneklerini Görüntüle
                                </button>
                            </div>


                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>




<style>
    .jp-card .jp-card-front,
    .jp-card .jp-card-back {
        background: linear-gradient(145deg, rgba(165, 89, 204, 1) 0%, rgba(246, 135, 230, 1) 100%);
    }

    /* Paket seçim kutularındaki radio buttonları gizle */
    .packet-label input[type="radio"] {
        position: absolute;
        opacity: 0;
        visibility: hidden;
    }

    /* Seçili paket için özel stil */
    .packet-label.secili .packet-label-content {
        border: 2px solid #6cf32a;
        box-shadow: 0 0 10px rgba(108, 243, 42, 0.5);
    }
</style>

<script src="/js/card.js"></script>
<script>
    $('code').each(function(i, e) {
        hljs.highlightBlock(e)
    });
    var card = new Card({
        form: '.active',
        container: '.card-wrapper'
    })

    document.addEventListener('DOMContentLoaded', function() {
        // Paket seçim fonksiyonu
        const paketSecimler = document.querySelectorAll('.paket-secim');
        paketSecimler.forEach(paket => {
            // Varsayılan seçili olan paketi işaretle
            if (paket.querySelector('input[type="radio"]').checked) {
                paket.classList.add('secili');

                // İlk yüklemede seçili paket için form değerlerini güncelle
                const radioInput = paket.querySelector('input[type="radio"]');
                const kredi = radioInput.getAttribute('data-kredi');
                const fiyat = radioInput.getAttribute('data-fiyat');
                const sessionCount = radioInput.getAttribute('data-sessioncount');

                // Paket ID'sini güncelle
                document.getElementById('hidden_paket_id').value = radioInput.value;

                // Kredi sayısını güncelle
                if (kredi) {
                    document.getElementById('toplam_gerekli_kredi').value = kredi;
                }

                // Session sayısını ve IDs'i güncelle
                if (sessionCount) {
                    document.getElementById('selected_session_count').value = sessionCount;

                    // Aradaki session IDs'i güncelle - seçilen session sayısına göre
                    const allSessionIds = <?php echo json_encode($aradakiSessionIds); ?>;
                    const mevcutSessionId = <?php echo isset($seciliGrup) ? $seciliGrup['grup_id'] : 'null'; ?>;
                    const mevcutSessionEksikKredi = <?php echo $mevcutSessionIcinEksikKredi ?: 0; ?>;

                    // Küçük paket için mevcut session ID'sini de ekleyelim
                    let selectedIds = [];

                    // Küçük paket (tek session) için
                    if (parseInt(sessionCount) === 1) {
                        // Eğer mevcut sessionda eksik kredi varsa, mevcut session ID'sini ekle
                        if (mevcutSessionEksikKredi > 0 && mevcutSessionId) {
                            selectedIds.push(mevcutSessionId);
                        }

                        // Sonraki session ID'sini ekle (varsa ve mevcut session değilse)
                        if (allSessionIds.length > 0 && allSessionIds[0] !== mevcutSessionId) {
                            selectedIds.push(allSessionIds[0]);
                        }
                    } else {
                        // Büyük paketler için tüm session ID'lerini ekle
                        selectedIds = [...allSessionIds];

                        // Eğer mevcut sessionda eksik kredi varsa ve listede yoksa ekle
                        if (mevcutSessionEksikKredi > 0 && mevcutSessionId && !selectedIds.includes(mevcutSessionId)) {
                            selectedIds.unshift(mevcutSessionId); // Başa ekle
                        }
                    }

                    document.getElementById('aradaki_session_ids').value = JSON.stringify(selectedIds);
                }
            }

            // Paket seçimi için click event listener
            paket.addEventListener('click', function(event) {
                // Eğer zaten seçili ise işlem yapma (çift tetiklemeyi önlemek için)
                if (this.classList.contains('secili')) {
                    return;
                }

                // Önce tüm paketlerden secili sınıfını kaldır
                paketSecimler.forEach(item => {
                    item.classList.remove('secili');
                    item.querySelector('input[type="radio"]').checked = false;
                });

                // Seçilen paketi işaretle
                this.classList.add('secili');
                const radioInput = this.querySelector('input[type="radio"]');
                radioInput.checked = true;

                // Paket değiştiğinde form değerlerini güncelle
                const kredi = radioInput.getAttribute('data-kredi');
                const fiyat = radioInput.getAttribute('data-fiyat');
                const sessionCount = radioInput.getAttribute('data-sessioncount');

                // Paket ID'sini güncelle
                document.getElementById('hidden_paket_id').value = radioInput.value;

                // Kredi sayısını güncelle
                if (kredi) {
                    document.getElementById('toplam_gerekli_kredi').value = kredi;
                }

                // Session sayısını ve IDs'i güncelle
                if (sessionCount) {
                    document.getElementById('selected_session_count').value = sessionCount;

                    // Aradaki session IDs'i güncelle - seçilen session sayısına göre
                    const allSessionIds = <?php echo json_encode($aradakiSessionIds); ?>;
                    const mevcutSessionId = <?php echo isset($seciliGrup) ? $seciliGrup['grup_id'] : 'null'; ?>;
                    const mevcutSessionEksikKredi = <?php echo $mevcutSessionIcinEksikKredi ?: 0; ?>;

                    // Küçük paket için mevcut session ID'sini de ekleyelim
                    let selectedIds = [];

                    // Küçük paket (tek session) için
                    if (parseInt(sessionCount) === 1) {
                        // Eğer mevcut sessionda eksik kredi varsa, mevcut session ID'sini ekle
                        if (mevcutSessionEksikKredi > 0 && mevcutSessionId) {
                            selectedIds.push(mevcutSessionId);
                        }

                        // Sonraki session ID'sini ekle (varsa ve mevcut session değilse)
                        if (allSessionIds.length > 0 && allSessionIds[0] !== mevcutSessionId) {
                            selectedIds.push(allSessionIds[0]);
                        }
                    } else {
                        // Büyük paketler için tüm session ID'lerini ekle
                        selectedIds = [...allSessionIds];

                        // Eğer mevcut sessionda eksik kredi varsa ve listede yoksa ekle
                        if (mevcutSessionEksikKredi > 0 && mevcutSessionId && !selectedIds.includes(mevcutSessionId)) {
                            selectedIds.unshift(mevcutSessionId); // Başa ekle
                        }
                    }

                    document.getElementById('aradaki_session_ids').value = JSON.stringify(selectedIds);
                }

                // İndirim kodu uygulanmışsa, yeni paket için indirim kodunu tekrar uygula
                const indirimKodu = document.getElementById('hidden_indirim_kodu').value;
                if (indirimKodu) {
                    // Yeni seçilen paketin fiyatını al
                    const yeniPaketFiyat = parseFloat(fiyat);

                    // İndirim kodunu yeni seçilen paket için uygula
                    const formData = new URLSearchParams();
                    formData.append('indirim_kodu', indirimKodu);
                    formData.append('paket_id', radioInput.value);
                    formData.append('donem_id', '<?php echo $seciliDonemId; ?>');
                    formData.append('tutar', yeniPaketFiyat);
                    formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');

                    // İşlem yapılıyor bildirimi göster
                    const indirimSonucuDiv = document.getElementById('indirimSonucu');
                    indirimSonucuDiv.innerHTML = '<div class="uk-text-center"><span uk-spinner></span> İndirim yeniden hesaplanıyor...</div>';

                    // İndirim kodunu yeni paket için kontrol et
                    fetch('/islemler/indirim_kodu_kontrol.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded'
                            },
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // İndirim kodu gösterim alanını göster
                                document.getElementById('indirimKoduGosterim').style.display = 'flex';
                                document.getElementById('indirimKoduBadge').textContent = indirimKodu;

                                indirimSonucuDiv.innerHTML = `
                                <div class="uk-text-success">
                                    <div>İndirim: ${data.indirim_miktari_formatlı || '0.00'} ₺</div>
                                    <div class="uk-text-bold">Ödenecek Tutar: ${data.indirimli_tutar_formatlı || '0.00'} ₺</div>
                                </div>
                            `;
                            } else {
                                // İndirim kodu bu paket için geçerli değilse bilgilendir
                                indirimSonucuDiv.innerHTML = `
                                <div class="uk-text-danger">
                                    <div>Bu paket için indirim kodu geçerli değil: ${data.message}</div>
                                </div>
                            `;

                                // İndirim kodunu temizle
                                document.getElementById('hidden_indirim_kodu').value = '';
                                document.getElementById('indirimKoduGosterim').style.display = 'none';
                            }

                            // Kartın taksit seçeneklerini güncelle
                            const kartNo = document.getElementById('kartno').value.replace(/\s/g, '');
                            if (kartNo.length >= 6) {
                                fetchTaksitSecenekleri(kartNo.substring(0, 6));
                            }

                            // Ödeme tutarını güncelle
                            odemeTutariniGuncelle();
                        })
                        .catch(error => {
                            console.error('İndirim kodu kontrolü hatası:', error);
                            indirimSonucuDiv.innerHTML = '<div class="uk-text-danger">İndirim kodu kontrolünde bir hata oluştu</div>';

                            // Ödeme tutarını güncelle
                            odemeTutariniGuncelle();
                        });
                } else {
                    // İndirim kodu yoksa direkt ödeme tutarını güncelle
                    const kartNo = document.getElementById('kartno').value.replace(/\s/g, '');
                    if (kartNo.length >= 6) {
                        fetchTaksitSecenekleri(kartNo.substring(0, 6));
                    }

                    // Debug amaçlı alert ve console.log kaldırıldı
                    odemeTutariniGuncelle();
                }
            });
        });

        // Eğer hiçbir paket seçili değilse ve sadece tek paket varsa, onu otomatik seç
        const seciliPaket = document.querySelector('.paket-secim.secili');
        if (!seciliPaket && paketSecimler.length === 1) {
            const tekPaket = paketSecimler[0];
            const radioInput = tekPaket.querySelector('input[type="radio"]');

            // Paketi seçili yap
            tekPaket.classList.add('secili');
            radioInput.checked = true;

            // Form değerlerini güncelle
            const kredi = radioInput.getAttribute('data-kredi');
            const fiyat = radioInput.getAttribute('data-fiyat');
            const sessionCount = radioInput.getAttribute('data-sessioncount');

            document.getElementById('hidden_paket_id').value = radioInput.value;

            if (kredi) {
                document.getElementById('toplam_gerekli_kredi').value = kredi;
            }

            if (sessionCount) {
                document.getElementById('selected_session_count').value = sessionCount;

                const allSessionIds = <?php echo json_encode($aradakiSessionIds); ?>;
                const mevcutSessionId = <?php echo isset($seciliGrup) ? $seciliGrup['grup_id'] : 'null'; ?>;
                const mevcutSessionEksikKredi = <?php echo $mevcutSessionIcinEksikKredi ?: 0; ?>;

                let selectedIds = [];

                if (parseInt(sessionCount) === 1) {
                    if (mevcutSessionEksikKredi > 0 && mevcutSessionId) {
                        selectedIds.push(mevcutSessionId);
                    }

                    const nextSessionId = allSessionIds.find(id => id > mevcutSessionId);
                    if (nextSessionId && nextSessionId !== mevcutSessionId) {
                        selectedIds.push(nextSessionId);
                    }
                } else {
                    selectedIds = allSessionIds.slice(0, parseInt(sessionCount));
                    if (mevcutSessionEksikKredi > 0 && mevcutSessionId && !selectedIds.includes(mevcutSessionId)) {
                        selectedIds.unshift(mevcutSessionId);
                    }
                }

                document.getElementById('aradaki_session_ids').value = JSON.stringify(selectedIds);
            }

            // Ödeme tutarını güncelle
            setTimeout(odemeTutariniGuncelle, 100);
        }

        // Değişkenler
        let odemeYontemi = 'krediKarti'; // Varsayılan ödeme yöntemi
        let kuponKodu = '';
        let eksikKredi = <?php echo $eksikKredi; ?>; // Mevcut dersler için eksik kredi
        let sonrakiSessionKredi = 10; // Bir sonraki session için gereken kredi
        let toplamGerekliKredi = <?php echo $toplamGerekliKredi; ?>; // Eksik kredi + session kredisi

        // Komisyon oranı
        let komisyonOrani = <?php echo ayarGetir("komisyon_orani") ?: '0'; ?>;
        
        // KDV oranı - paket bazlı olarak dinamik olarak alınacak
        let kdvOrani = 0;

        // Ödeme tutarını ve komisyon bilgilerini güncelleme fonksiyonu
        function odemeTutariniGuncelle() {
            const seciliPaket = document.querySelector('input[name="paket_secim"]:checked');
            if (!seciliPaket) return;

            const seciliPaketDiv = seciliPaket.closest('.paket-secim');
            const paketFiyatElement = seciliPaketDiv.querySelector('b');
            let paketFiyat = parseFloat(paketFiyatElement.innerText.replace(/[^0-9,]/g, '').replace(',', '.'));
            
            // KDV oranını paket seçimine göre güncelle
            const kdvOraniElement = seciliPaketDiv.querySelector('.uk-text-muted');
            if (kdvOraniElement && kdvOraniElement.textContent.includes('KDV')) {
                const kdvMatch = kdvOraniElement.textContent.match(/KDV \(%(\d+(?:\.\d+)?)\)/);
                if (kdvMatch) {
                    kdvOrani = parseFloat(kdvMatch[1]);
                }
            }

            // Fiyat bilgi alanını bul veya oluştur
            let fiyatBilgiDiv = document.getElementById('fiyatBilgiAlani');
            if (!fiyatBilgiDiv) {
                fiyatBilgiDiv = document.createElement('div');
                fiyatBilgiDiv.id = 'fiyatBilgiAlani';
                fiyatBilgiDiv.className = 'uk-margin-top uk-card uk-card-secondary uk-border-rounded uk-box-shadow-small';
                odemeButonu.parentNode.insertBefore(fiyatBilgiDiv, odemeButonu);
            }

            // İndirim kodu uygulandıysa
            const indirimSonucuDiv = document.getElementById('indirimSonucu');
            if (indirimSonucuDiv && indirimSonucuDiv.innerHTML.trim() !== '') {
                const indirimliTutar = indirimSonucuDiv.querySelector('.uk-text-bold');
                if (indirimliTutar) {
                    paketFiyat = parseFloat(indirimliTutar.textContent.replace(/[^0-9,]/g, '').replace(',', '.'));
                }
            }

            let odenecekTutar = paketFiyat;
            let komisyonTutari = 0;

            // Kredi kartı seçiliyse komisyon ekle
            if (odemeYontemi === 'krediKarti') {
                komisyonTutari = (paketFiyat * komisyonOrani) / 100;
                odenecekTutar = paketFiyat + komisyonTutari;

                fiyatBilgiDiv.innerHTML = `
                    <div class="uk-card-body">
                        <h4 class="uk-text-bold uk-margin-remove uk-text-primary">Ödeme Bilgileri</h4>
                        <hr class="uk-margin-small">
                        <div class="uk-grid-small uk-text-medium" uk-grid>
                            <div class="uk-width-expand uk-text-bold uk-light">Paket Tutarı:</div>
                            <div class="uk-width-auto uk-text-bold uk-light">${paketFiyat.toFixed(2).replace('.', ',')} ₺</div>
                        </div>
                        <div class="uk-grid-small uk-text-medium" uk-grid>
                            <div class="uk-width-expand uk-text-bold uk-light">Komisyon (%${komisyonOrani}):</div>
                            <div class="uk-width-auto uk-text-bold uk-text-warning">+${komisyonTutari.toFixed(2).replace('.', ',')} ₺</div>
                        </div>
                        <div class="uk-grid-small uk-text-medium uk-margin-small-top uk-background-secondary uk-padding-small uk-border-rounded" uk-grid>
                            <div class="uk-width-expand uk-text-bold uk-light">Toplam Ödenecek Tutar:</div>
                            <div class="uk-width-auto uk-text-primary uk-text-bold" style="font-size: 1.2em;">${odenecekTutar.toFixed(2).replace('.', ',')} ₺</div>
                        </div>
                        <div class="uk-text-small uk-text-warning uk-margin-small-top uk-flex uk-flex-middle">
                            <span uk-icon="icon: info; ratio: 0.8" class="uk-margin-small-right"></span>
                            <span>Kredi kartı ile ödemelerde seçilen taksit sayısına göre komisyon uygulanmaktadır.</span>
                        </div>
                    </div>
                `;
            } else {
                // Havale/EFT seçiliyse komisyonsuz fiyat
                fiyatBilgiDiv.innerHTML = `
                    <div class="uk-card-body">
                        <h4 class="uk-text-bold uk-margin-remove uk-text-primary">Ödeme Bilgileri</h4>
                        <hr class="uk-margin-small">
                        <div class="uk-grid-small uk-text-medium uk-background-secondary uk-padding-small uk-border-rounded" uk-grid>
                            <div class="uk-width-expand uk-text-bold uk-light">Toplam Ödenecek Tutar:</div>
                            <div class="uk-width-auto uk-text-primary uk-text-bold" style="font-size: 1.2em;">${paketFiyat.toFixed(2).replace('.', ',')} ₺</div>
                        </div>
                        <div class="uk-text-small uk-text-success uk-margin-small-top uk-flex uk-flex-middle">
                            <span uk-icon="icon: check; ratio: 0.8" class="uk-margin-small-right"></span>
                            <span>Havale/EFT ödemelerinde komisyon ücreti uygulanmamaktadır.</span>
                        </div>
                    </div>
                `;
            }

            // Ödeme form alanlarını güncelle
            document.getElementById('komisyon_tutari').value = komisyonTutari.toFixed(2);
            document.getElementById('odenecek_tutar').value = odenecekTutar.toFixed(2);

            return {
                paketFiyat,
                komisyonTutari,
                odenecekTutar
            };
        }

        // Aradaki sessionlar için ID'leri toplayan dizi
        let aradakiSessionIds = <?php echo json_encode($aradakiSessionIds); ?>;

        // Tek session satışı yapılıyorsa
        let tekSessionSatisi = <?php echo $tekSessionSatisi ? 'true' : 'false'; ?>;
        let direktSessionId = <?php echo $direktSessionId ?: '0'; ?>;

        // Form verilerine eksik kredi bilgisini ekle
        function addMissingCreditInfo(formData) {
            formData.append('eksik_kredi', eksikKredi);
            formData.append('toplam_gerekli_kredi', toplamGerekliKredi);
            // Aradaki sessionların ID'lerini ekle
            if (aradakiSessionIds && aradakiSessionIds.length > 0) {
                formData.append('aradaki_session_ids', JSON.stringify(aradakiSessionIds));
            }
            // Tek session satışı yapılıyorsa session ID'yi ekle
            if (tekSessionSatisi && direktSessionId) {
                formData.append('tek_session_id', direktSessionId);
                formData.append('tek_session_satisi', true);
            }
            return formData;
        }

        // Ödeme butonu
        const odemeButonu = document.getElementById('odemeButonu');

        // İndirim kuponu butonuna tıklama
        document.getElementById('applyDiscount').addEventListener('click', function() {
            const indirimKoduInput = document.getElementById('indirimKodu');
            const indirimKodu = indirimKoduInput.value.trim();

            if (!indirimKodu) {
                Swal.fire('Hata', 'Lütfen bir indirim kodu girin', 'error');
                return;
            }

            // Seçili paketin fiyatını al
            const seciliPaket = document.querySelector('input[name="paket_secim"]:checked');
            if (!seciliPaket) {
                Swal.fire('Hata', 'Lütfen bir paket seçiniz', 'error');
                return;
            }

            // Seçili paketin fiyat bilgisini al (KDV'li tutar)
            const paketFiyat = parseFloat(seciliPaket.closest('.paket-secim').querySelector('b').innerText.replace(/[^0-9,]/g, '').replace(',', '.'));
            
            // KDV oranını paket seçimine göre al
            const seciliPaketDiv = seciliPaket.closest('.paket-secim');
            let paketKdvOrani = 0;
            
            // Tüm KDV bilgilerini içeren elementleri bul
            const kdvElements = seciliPaketDiv.querySelectorAll('.uk-text-muted');
            for (let element of kdvElements) {
                if (element.textContent.includes('KDV')) {
                    // KDV (%20.00): +720 ₺ formatından KDV oranını çıkar
                    const kdvMatch = element.textContent.match(/KDV\s*\(%\s*(\d+(?:\.\d+)?)\s*\)/);
                    if (kdvMatch) {
                        paketKdvOrani = parseFloat(kdvMatch[1]);
                        console.log('KDV oranı bulundu:', paketKdvOrani, 'Element:', element.textContent);
                        break;
                    }
                }
            }
            
            // KDV'siz tutarı hesapla - paket fiyatından KDV'yi çıkar
            const kdvsizTutar = paketFiyat / (1 + paketKdvOrani / 100);
            
            // Debug için console.log
            console.log('Paket Fiyat (KDV\'li):', paketFiyat);
            console.log('KDV Oranı:', paketKdvOrani);
            console.log('KDV\'siz Tutar:', kdvsizTutar);

            // İndirim kodu kontrol
            const formData = new URLSearchParams();
                                formData.append('indirim_kodu', indirimKodu);
                    formData.append('paket_id', seciliPaket.value);
                    formData.append('tutar', kdvsizTutar); // KDV'siz tutarı gönder
                    formData.append('donem_id', '<?php echo $seciliDonemId; ?>');
                    formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');

            const paketIdValue = seciliPaket.value;

            fetch('/islemler/indirim_kodu_kontrol.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // İndirim kodu gösterim alanını göster
                        document.getElementById('indirimKoduGosterim').style.display = 'flex';
                        document.getElementById('indirimKoduBadge').textContent = indirimKodu;

                        document.getElementById('indirimSonucu').innerHTML = `
                    <div class="uk-text-success">
                        <div>İndirim: ${data.indirim_miktari_formatlı || '0.00'} ₺</div>
                        <div>KDV'siz Tutar: ${data.kdvsiz_tutar ? data.kdvsiz_tutar.toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '0.00'} ₺</div>
                        <div>KDV (%${data.kdv_orani || '20'}): +${data.kdv_tutari ? data.kdv_tutari.toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '0.00'} ₺</div>
                        <div class="uk-text-bold">Ödenecek Tutar: ${data.indirimli_tutar_formatlı || '0.00'} ₺</div>
                    </div>
                `;
                        kuponKodu = indirimKodu;
                        document.getElementById('hidden_indirim_kodu').value = indirimKodu; // Hidden input güncelleniyor

                        // İndirim uygulandıktan sonra taksit seçeneklerini de güncelle
                        const kartNo = document.getElementById('kartno').value.replace(/\s/g, '');
                        if (kartNo.length >= 6) {
                            fetchTaksitSecenekleri(kartNo.substring(0, 6));
                        } else {
                            // Taksit seçenekleri yoksa sadece ödeme tutarını güncelle
                            odemeTutariniGuncelle();
                        }

                        Swal.fire('Başarılı', data.message, 'success');
                    } else {
                        document.getElementById('indirimSonucu').innerHTML = '';
                        kuponKodu = '';
                        document.getElementById('hidden_indirim_kodu').value = '';
                        Swal.fire('Hata', data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('İndirim kodu kontrol hatası:', error);
                    Swal.fire('Hata', 'İndirim kodu kontrolünde bir hata oluştu', 'error');
                });
        });

        // İndirim kodu kaldırma butonu
        document.getElementById('removeDiscount').addEventListener('click', function() {
            // İndirim kodu input alanını temizle
            document.getElementById('indirimKodu').value = '';

            // İndirim kodu gösterim alanını gizle
            document.getElementById('indirimKoduGosterim').style.display = 'none';

            // İndirim sonuç alanını temizle
            document.getElementById('indirimSonucu').innerHTML = '';

            // İndirim kodu değişkenini sıfırla
            kuponKodu = '';
            document.getElementById('hidden_indirim_kodu').value = '';

            // Taksit seçeneklerini güncelle
            const kartNo = document.getElementById('kartno').value.replace(/\s/g, '');
            if (kartNo.length >= 6) {
                fetchTaksitSecenekleri(kartNo.substring(0, 6));
            }

            // Ödeme bilgilerini güncelle
            odemeTutariniGuncelle();

            // Başarılı mesajı göster
            Swal.fire({
                icon: 'success',
                title: 'İndirim Kodu Kaldırıldı',
                text: 'İndirim kodu başarıyla kaldırıldı.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        });

        // Ödeme yöntemi değişikliğini izle
        document.querySelectorAll('input[name="o_y_ds"]').forEach(radio => {
            radio.addEventListener('change', function() {
                odemeYontemi = this.value;
                odemeTutariniGuncelle();
            });
        });

        // Paket seçimi değiştiğinde ödeme tutarını güncelle
        document.querySelectorAll('input[name="paket_secim"]').forEach(paket => {
            paket.addEventListener('change', function() {
                odemeTutariniGuncelle();
            });
        });

        // Sayfa yüklendiğinde ödeme tutarını hesapla
        setTimeout(odemeTutariniGuncelle, 500);

        // Tüm taksit seçeneklerini getir
        fetch('/islemler/pos_oranlari.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderTumTaksitSecenekleri(data.bankalar);
                } else {
                    document.getElementById('tumTaksitSecenekleri').innerHTML = `
                        <div class="uk-alert uk-alert-warning">
                            <p>Taksit seçenekleri yüklenirken bir hata oluştu.</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Taksit seçenekleri alınırken hata:', error);
                document.getElementById('tumTaksitSecenekleri').innerHTML = `
                    <div class="uk-alert uk-alert-danger">
                        <p>Taksit seçenekleri yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.</p>
                    </div>
                `;
            });

        // Tüm taksit seçeneklerini render et
        function renderTumTaksitSecenekleri(bankalar) {
            // Seçili paketin fiyatını al
            const seciliPaket = document.querySelector('.paket-secim.secili');
            if (!seciliPaket) return;

            const paketFiyatText = seciliPaket.querySelector('b').innerText;
            let paketFiyat = parseFloat(paketFiyatText.replace(/[^0-9,]/g, '').replace(',', '.'));

            // İndirim kodu uygulanmışsa indirimli fiyatı al
            const indirimSonucuDiv = document.getElementById('indirimSonucu');
            if (indirimSonucuDiv && indirimSonucuDiv.innerHTML.trim() !== '') {
                const indirimliTutar = indirimSonucuDiv.querySelector('.uk-text-bold');
                if (indirimliTutar) {
                    paketFiyat = parseFloat(indirimliTutar.textContent.replace(/[^0-9,]/g, '').replace(',', '.'));
                }
            }

            let html = '<div class="uk-grid uk-child-width-1-2@m uk-child-width-1-3@l uk-child-width-1-5@xl uk-grid-match" uk-grid>';

            bankalar.forEach(banka => {
                if (banka.taksit_secenekleri.length > 0) {
                    html += `
                        <div>
                            <div class="banka-taksit-card">
                                <div class="uk-flex uk-flex-middle uk-margin-small-bottom">
                                    ${banka.banka_gorsel ? 
                                      `<img src="${banka.banka_gorsel}" alt="${banka.banka_adi}" height="30" class="uk-margin-small-right">` : 
                                      `<span uk-icon="icon: credit-card" class="uk-margin-small-right"></span>`}
                                    <h5 class="uk-margin-remove">${banka.banka_adi}</h5>
                                </div>
                                <div class="uk-overflow-auto">
                                    <table class="uk-table uk-table-small uk-table-divider uk-margin-remove">
                                        <thead>
                                            <tr>
                                                <th class="uk-text-center">Taksit</th>
                                                <th class="uk-text-center">Aylık</th>
                                                <th class="uk-text-center">Toplam</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                `;

                    banka.taksit_secenekleri.forEach(taksit => {
                        const toplamTutar = paketFiyat * (1 + taksit.komisyon_oran / 100);
                        const taksitTutari = toplamTutar / taksit.taksit_sayisi;

                        html += `
                            <tr>
                                <td class="uk-text-center">${taksit.taksit_sayisi}${taksit.taksit_sayisi === 1 ? ' (Tek Çekim)' : ' Taksit'}</td>
                                <td class="uk-text-center">${taksitTutari.toFixed(2).replace('.', ',')} ₺</td>
                                <td class="uk-text-center">${toplamTutar.toFixed(2).replace('.', ',')} ₺</td>
                            </tr>
                        `;
                    });

                    html += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    `;
                }
            });

            html += '</div>';

            document.getElementById('tumTaksitSecenekleri').innerHTML = html;
        }

        // Paket seçimi değiştiğinde modal içeriğini güncelle
        document.querySelectorAll('input[name="paket_secim"]').forEach(paket => {
            paket.addEventListener('change', function() {
                if (UIkit.modal('#taksitModal').isToggled()) {
                    fetchTumTaksitSecenekleri();
                }
            });
        });

        // İndirim kodu uygulandığında veya kaldırıldığında modal içeriğini güncelle
        document.getElementById('applyDiscount').addEventListener('click', function() {
            setTimeout(() => {
                if (UIkit.modal('#taksitModal').isToggled()) {
                    fetchTumTaksitSecenekleri();
                }
            }, 1000);
        });

        document.getElementById('removeDiscount').addEventListener('click', function() {
            setTimeout(() => {
                if (UIkit.modal('#taksitModal').isToggled()) {
                    fetchTumTaksitSecenekleri();
                }
            }, 500);
        });

        // Ödeme butonuna tıklama
        odemeButonu.addEventListener('click', function(e) {
            // Prevent multiple submissions
            if (this.disabled) {
                return;
            }

            // Sözleşme onayı kontrolü
            const sozlesmeOnay = document.getElementById('sozlesmeOnay');
            if (!sozlesmeOnay.checked) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata!',
                    text: 'Devam etmek için Mesafeli Satış Sözleşmesi\'ni onaylamanız gerekmektedir.',
                    confirmButtonText: 'Tamam'
                });
                return;
            }

            // Seçili ödeme yöntemini al
            odemeYontemi = document.querySelector('input[name="o_y_ds"]:checked').value;

            // Seçili paketin ID'sini al
            const seciliPaket = document.querySelector('input[name="paket_secim"]:checked');
            if (!seciliPaket) {
                Swal.fire('Hata', 'Lütfen bir paket seçiniz', 'error');
                return;
            }
            document.getElementById('hidden_paket_id').value = seciliPaket.value;

            // Ödeme bilgilerini güncelle
            const odemeBilgileri = odemeTutariniGuncelle();

            // Ödeme formuna yöntemi ekle
            const odemeForm = document.getElementById('odemeForm');
            odemeForm.querySelector('input[name="o_y_ds"]')?.remove(); // Varsa önceki input'u kaldır
            const odemeYontemiInput = document.createElement('input');
            odemeYontemiInput.type = 'hidden';
            odemeYontemiInput.name = 'o_y_ds';
            odemeYontemiInput.value = odemeYontemi;
            odemeForm.appendChild(odemeYontemiInput);

            // Taksit sayısını ekle
            let taksitSayisi = 1;
            const seciliTaksit = document.querySelector('input[name="t_s_m_a"]:checked');
            if (seciliTaksit) {
                taksitSayisi = parseInt(seciliTaksit.value);
            }

            let taksitSayisiInput = document.getElementById('taksit_sayisi');
            if (!taksitSayisiInput) {
                taksitSayisiInput = document.createElement('input');
                taksitSayisiInput.type = 'hidden';
                taksitSayisiInput.id = 'taksit_sayisi';
                taksitSayisiInput.name = 't_s_m_a';
                odemeForm.appendChild(taksitSayisiInput);
            }
            taksitSayisiInput.value = taksitSayisi;

            // Disable the button to prevent multiple submissions
            this.disabled = true;
            this.classList.add('uk-disabled');

            if (odemeYontemi === 'krediKarti') {
                // Kredi kartı bilgilerini kontrol et
                const kartNo = document.getElementById('kartno').value.trim();
                const kartIsim = document.getElementById('kartisim').value.trim();
                const kartSkt = document.getElementById('kartskt').value.trim();
                const kartCvc = document.getElementById('kartcvc').value.trim();

                // Kart numarası kontrolü
                if (!kartNo || kartNo.replace(/\s/g, '').length !== 16) {
                    Swal.fire('Hata', 'Geçerli bir kart numarası giriniz', 'error');
                    // Re-enable the button if validation fails
                    this.disabled = false;
                    this.classList.remove('uk-disabled');
                    return;
                }

                if (!kartIsim) {
                    Swal.fire('Hata', 'Kart sahibinin adını giriniz', 'error');
                    // Re-enable the button if validation fails
                    this.disabled = false;
                    this.classList.remove('uk-disabled');
                    return;
                }

                if (!kartSkt || kartSkt.length !== 5) {
                    Swal.fire('Hata', 'Geçerli bir son kullanma tarihi giriniz (AA/YY)', 'error');
                    // Re-enable the button if validation fails
                    this.disabled = false;
                    this.classList.remove('uk-disabled');
                    return;
                }

                if (!kartCvc || kartCvc.length !== 3) {
                    Swal.fire('Hata', 'Geçerli bir CVC kodu giriniz', 'error');
                    // Re-enable the button if validation fails
                    this.disabled = false;
                    this.classList.remove('uk-disabled');
                    return;
                }

                // Kart bilgilerini forma ekle
                addKartBilgileriToForm(odemeForm, kartNo, kartIsim, kartSkt, kartCvc);

                // Kredi kartı için formu direkt olarak submit et
                Swal.fire({
                    title: 'İşleminiz Yapılıyor',
                    text: 'Ödeme işleniyor, lütfen bekleyiniz...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                        setTimeout(() => {
                            odemeForm.submit();
                        }, 500);
                    }
                });
            } else {
                // Banka havalesi için önce onay al
                Swal.fire({
                    title: 'Onay',
                    text: 'Ödemenizi banka havalesi ile yapmak istediğinize emin misiniz? Onayınızdan sonra dekont yükleme ekranına yönlendirileceksiniz.',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Evet, Onaylıyorum',
                    cancelButtonText: 'İptal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Banka havalesi için AJAX işlemi
                        const formData = new FormData(odemeForm);

                        // Ödeme işlemi başladı
                        Swal.fire({
                            title: 'İşleminiz Yapılıyor',
                            text: 'Havale bilgileri kaydediliyor...',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        // Ödeme isteği
                        fetch('/islemler/session_odeme_tamamla.php', {
                                method: 'POST',
                                body: formData
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Havale Bilgileri Kaydedildi',
                                        text: data.message,
                                        confirmButtonText: 'Tamam'
                                    }).then(() => {
                                        // Başarılı işlem sonrası yönlendirme
                                        window.location.href = data.redirect || '/hesabim';
                                    });
                                } else {
                                    // Re-enable the button if the request fails
                                    odemeButonu.disabled = false;
                                    odemeButonu.classList.remove('uk-disabled');

                                    Swal.fire({
                                        icon: 'error',
                                        title: 'İşlem Başarısız',
                                        text: data.message,
                                        confirmButtonText: 'Tamam'
                                    });
                                }
                            })
                            .catch(error => {
                                console.error('İşlem hatası:', error);

                                // Re-enable the button if the request fails
                                odemeButonu.disabled = false;
                                odemeButonu.classList.remove('uk-disabled');

                                Swal.fire({
                                    icon: 'error',
                                    title: 'Sistem Hatası',
                                    text: 'İşlem sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyin.',
                                    confirmButtonText: 'Tamam'
                                });
                            });
                    } else {
                        // User cancelled the operation, re-enable the button
                        odemeButonu.disabled = false;
                        odemeButonu.classList.remove('uk-disabled');
                    }
                });
            }
        });

        // Kart bilgilerini forma ekleyen fonksiyon
        function addKartBilgileriToForm(form, kartNo, kartIsim, kartSkt, kartCvc) {
            // Önceki kart bilgilerini temizle
            form.querySelector('input[name="kart_no"]')?.remove();
            form.querySelector('input[name="kart_sahibi"]')?.remove();
            form.querySelector('input[name="kart_son_kullanma"]')?.remove();
            form.querySelector('input[name="kart_cvc"]')?.remove();

            // Kart bilgilerini form'a ekle
            const kartNoInput = document.createElement('input');
            kartNoInput.type = 'hidden';
            kartNoInput.name = 'kart_no';
            kartNoInput.value = kartNo.replace(/\s/g, ''); // Boşlukları temizle
            form.appendChild(kartNoInput);

            const kartIsimInput = document.createElement('input');
            kartIsimInput.type = 'hidden';
            kartIsimInput.name = 'kart_sahibi';
            kartIsimInput.value = kartIsim;
            form.appendChild(kartIsimInput);

            const kartSktInput = document.createElement('input');
            kartSktInput.type = 'hidden';
            kartSktInput.name = 'kart_son_kullanma';
            kartSktInput.value = kartSkt;
            form.appendChild(kartSktInput);

            const kartCvcInput = document.createElement('input');
            kartCvcInput.type = 'hidden';
            kartCvcInput.name = 'kart_cvc';
            kartCvcInput.value = kartCvc;
            form.appendChild(kartCvcInput);
        }

        // Kart numarası formatlaması - sadece rakamları kabul et ve 16 haneye sınırla
        document.getElementById('kartno').addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length > 16) {
                value = value.substring(0, 16);
            }

            let formattedValue = '';
            for (let i = 0; i < value.length; i++) {
                if (i > 0 && i % 4 === 0) {
                    formattedValue += ' ';
                }
                formattedValue += value[i];
            }

            this.value = formattedValue;
        });

        // Son kullanma tarihi formatlaması - AA/YY formatını uygula
        document.getElementById('kartskt').addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length > 4) {
                value = value.substring(0, 4);
            }

            // Ay kısmı için kontrol
            if (value.length > 0) {
                const firstDigit = parseInt(value[0]);
                if (firstDigit > 1) {
                    // Eğer ilk rakam 1'den büyükse, başına 0 ekle (2-9 için)
                    value = '0' + value;
                }

                if (value.length >= 2) {
                    const month = parseInt(value.substring(0, 2));
                    if (month > 12 || month === 0) {
                        // Ay 1-12 arasında olmalı
                        value = '12' + value.substring(2);
                    }
                }
            }

            if (value.length > 2) {
                value = value.substring(0, 2) + '/' + value.substring(2);
            }

            this.value = value;
        });

        // CVC formatlaması - sadece 3 rakam
        document.getElementById('kartcvc').addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length > 3) {
                value = value.substring(0, 3);
            }

            this.value = value;
        });

        // Paket seçimi değiştiğinde hidden input'u güncelle
        document.querySelectorAll('input[name="paket_secim"]').forEach(paket => {
            paket.addEventListener('change', function() {
                document.getElementById('hidden_paket_id').value = this.value;
                odemeTutariniGuncelle();
            });
        });

        // Sayfa yüklendiğinde seçili paket varsa hidden input'u güncelle
        setTimeout(function() {
            const seciliPaket = document.querySelector('input[name="paket_secim"]:checked');
            if (seciliPaket) {
                document.getElementById('hidden_paket_id').value = seciliPaket.value;
            }
            odemeTutariniGuncelle();
        }, 500);

        // Kart numarası girişi ve BIN sorgulama işlemi
        let binSorgulamaTimeout;
        document.getElementById('kartno').addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length > 16) {
                value = value.substring(0, 16);
            }

            let formattedValue = '';
            for (let i = 0; i < value.length; i++) {
                if (i > 0 && i % 4 === 0) {
                    formattedValue += ' ';
                }
                formattedValue += value[i];
            }

            this.value = formattedValue;

            // Kart numarası 6 haneli olduğunda BIN sorgulama yap
            value = value.replace(/\s/g, '');
            clearTimeout(binSorgulamaTimeout);

            if (value.length >= 6) {
                const bin = value.substring(0, 6);
                document.getElementById('taksitSecenekleri').innerHTML = '<div class="uk-text-center"><span uk-spinner></span> Taksit seçenekleri yükleniyor...</div>';

                binSorgulamaTimeout = setTimeout(() => {
                    fetchTaksitSecenekleri(bin);
                }, 500);
            } else {
                document.getElementById('taksitSecenekleri').innerHTML = '<p class="uk-text-muted">Kredi kartınızın ilk 6 hanesini girin, taksit seçeneklerini görelim.</p>';
            }
        });

        // BIN koduna göre taksit seçeneklerini getir
        function fetchTaksitSecenekleri(bin) {
            // Seçili paketin fiyatını al
            const seciliPaket = document.querySelector('.paket-secim.secili');
            if (!seciliPaket) return;

            const paketFiyatElement = seciliPaket.querySelector('b');
            let paketFiyat = parseFloat(paketFiyatElement.innerText.replace(/[^0-9,]/g, '').replace(',', '.'));

            // İndirim kodu uygulanmışsa indirimli fiyatı al
            const indirimSonucuDiv = document.getElementById('indirimSonucu');
            if (indirimSonucuDiv && indirimSonucuDiv.innerHTML.trim() !== '') {
                const indirimliTutar = indirimSonucuDiv.querySelector('.uk-text-bold');
                if (indirimliTutar) {
                    paketFiyat = parseFloat(indirimliTutar.textContent.replace(/[^0-9,]/g, '').replace(',', '.'));
                }
            }

            fetch('/islemler/taksit_secenekleri.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `bin=${bin}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderTaksitSecenekleri(data.taksitler, data.kart_bilgisi);
                    } else {
                        document.getElementById('taksitSecenekleri').innerHTML = `
                        <div class="uk-alert uk-alert-warning">
                            <p>${data.message || 'Taksit seçenekleri bulunamadı.'}</p>
                        </div>
                    `;
                    }
                })
                .catch(error => {
                    console.error('Taksit seçenekleri alınırken hata:', error);
                    document.getElementById('taksitSecenekleri').innerHTML = `
                    <div class="uk-alert uk-alert-danger">
                        <p>Taksit seçenekleri alınırken bir hata oluştu. Lütfen tekrar deneyin.</p>
                    </div>
                `;
                });
        }

        // Taksit seçeneklerini ekrana render et
        function renderTaksitSecenekleri(taksitler, kartBilgisi) {
            // Seçili paketin fiyatını al
            const seciliPaket = document.querySelector('.paket-secim.secili');
            if (!seciliPaket) return;

            // Önce normal paket fiyatını al
            const paketFiyatText = seciliPaket.querySelector('b').innerText;
            let paketFiyat = parseFloat(paketFiyatText.replace(/[^0-9,]/g, '').replace(',', '.'));

            // İndirim kodu uygulanmışsa indirimli fiyatı al
            const indirimSonucuDiv = document.getElementById('indirimSonucu');
            if (indirimSonucuDiv && indirimSonucuDiv.innerHTML.trim() !== '') {
                const indirimliTutar = indirimSonucuDiv.querySelector('.uk-text-bold');
                if (indirimliTutar) {
                    paketFiyat = parseFloat(indirimliTutar.textContent.replace(/[^0-9,]/g, '').replace(',', '.'));
                }
            }

            let html = '';

            // Kart bilgilerini göster (daha kompakt)
            if (kartBilgisi) {
                html += `
                    <div class="kart-bilgi-mini">
                        <div class="uk-flex uk-flex-middle">
                            ${kartBilgisi.kart_gorseli ? 
                              `<img src="${kartBilgisi.kart_gorseli}" alt="${kartBilgisi.kart_adi}" height="24" class="kart-mini-logo">` : 
                              `<span uk-icon="icon: credit-card; ratio: 0.8" class="kart-mini-icon"></span>`}
                            <span class="kart-mini-text">${kartBilgisi.kart_adi || 'Kredi Kartı'} <span class="kart-mini-banka">${kartBilgisi.banka_adi || ''}</span></span>
                        </div>
                    </div>
                `;
            }

            // Taksit seçeneklerini göster (daha kompakt bir tablo ile)
            if (taksitler && taksitler.length > 0) {
                // Tek çekim seçeneği için sistemden gelen komisyon oranını bul
                const tekCekimTaksit = taksitler.find(t => t.taksit_sayisi === 1);
                const tekCekimKomisyon = tekCekimTaksit ? tekCekimTaksit.komisyon_oran : 0;

                html += `<div class="taksit-tablo">
                    <table class="uk-table uk-table-small uk-table-divider uk-margin-remove">
                        <thead>
                            <tr>
                                <th class="uk-text-center">Taksit</th>
                                <th class="uk-text-center">Tutar</th>
                                <th class="uk-text-center">Toplam</th>
                            </tr>
                        </thead>
                        <tbody>`;

                // Tek çekim seçeneği
                const tekCekimToplamTutar = paketFiyat * (1 + tekCekimKomisyon / 100);

                html += `
                    <tr class="taksit-satir">
                        <td class="uk-text-center fw5">
                            <label class="taksit-radio">
                                <input type="radio" name="t_s_m_a" value="1" class="uk-radio" checked 
                                       data-komisyon="${tekCekimKomisyon}">
                                <span>Tek Çekim</span>
                            </label>
                        </td>
                        <td class="uk-text-center fw5">${tekCekimToplamTutar.toFixed(2).replace('.', ',')} ₺</td>
                        <td class="uk-text-center fw5">${tekCekimToplamTutar.toFixed(2).replace('.', ',')} ₺</td>
                    </tr>`;

                // Diğer taksit seçenekleri
                taksitler.forEach(taksit => {
                    if (taksit.taksit_sayisi > 1) { // Tek çekimi yukarıda gösterdiğimiz için atla
                        const taksitTutari = (paketFiyat * (1 + taksit.komisyon_oran / 100)) / taksit.taksit_sayisi;
                        const toplamTutar = paketFiyat * (1 + taksit.komisyon_oran / 100);

                        html += `
                            <tr class="taksit-satir">
                                <td class="uk-text-center fw5">
                                    <label class="taksit-radio">
                                        <input type="radio" name="t_s_m_a" value="${taksit.taksit_sayisi}" class="uk-radio" 
                                               data-komisyon="${taksit.komisyon_oran}">
                                        <span>${taksit.taksit_sayisi} Taksit</span>
                                    </label>
                                </td>
                                <td class="uk-text-center fw5">${taksitTutari.toFixed(2).replace('.', ',')} ₺ x ${taksit.taksit_sayisi}</td>
                                <td class="uk-text-center fw5">${toplamTutar.toFixed(2).replace('.', ',')} ₺</td>
                            </tr>`;
                    }
                });

                html += `</tbody></table></div>`;
            } else {
                // Taksit seçeneği yoksa - Tek çekim için varsayılan komisyon oranını kullan
                const varsayilanKomisyon = parseFloat(document.getElementById('komisyon_orani').value);
                const toplamTutar = paketFiyat * (1 + varsayilanKomisyon / 100);

                html += `
                    <div class="uk-alert uk-alert-warning uk-margin-small">
                        <p class="uk-margin-remove">Bu kart için taksit seçeneği bulunmamaktadır.</p>
                    </div>
                    <div class="taksit-tablo">
                        <table class="uk-table uk-table-small uk-margin-remove">
                            <tbody>
                                <tr class="taksit-satir">
                                    <td class="uk-text-center">
                                        <label class="taksit-radio">
                                            <input type="radio" name="t_s_m_a" value="1" class="uk-radio" checked 
                                                   data-komisyon="${varsayilanKomisyon}">
                                            <span>Tek Çekim</span>
                                        </label>
                                    </td>
                                    <td class="uk-text-center">${toplamTutar.toFixed(2).replace('.', ',')} ₺</td>
                                    <td class="uk-text-center uk-text-small">${toplamTutar.toFixed(2).replace('.', ',')} ₺</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>`;
            }

            document.getElementById('taksitSecenekleri').innerHTML = html;

            // Taksit satırlarına tıklama eventi ekle - satırın tamamına tıklanabilir özellik
            document.querySelectorAll('.taksit-satir').forEach(satir => {
                satir.style.cursor = 'pointer';
                satir.addEventListener('click', function(e) {
                    // Eğer tıklanan element input ise (radio) işlem yapma
                    if (e.target.tagName === 'INPUT' && e.target.type === 'radio') {
                        return;
                    }
                    // Satırdaki radio inputunu bul ve seç
                    const radioInput = this.querySelector('input[type="radio"]');
                    if (radioInput) {
                        radioInput.checked = true;
                        // Değişiklik olayını manuel olarak tetikle
                        const changeEvent = new Event('change', {
                            bubbles: true
                        });
                        radioInput.dispatchEvent(changeEvent);
                    }
                });
            });

            // Taksit seçeneği değişikliğini dinle
            document.querySelectorAll('input[name="t_s_m_a"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    // Taksit seçildiğinde ödeme tutarını güncelle
                    odemeTutariniGuncelle();
                });
            });

            // Ödeme tutarını güncelle
            odemeTutariniGuncelle();
        }

        // Ödeme tutarını ve komisyon bilgilerini güncelleme fonksiyonu (mevcut fonksiyon)
        function odemeTutariniGuncelle() {
            const seciliPaket = document.querySelector('input[name="paket_secim"]:checked');
            if (!seciliPaket) return;

            const seciliPaketDiv = seciliPaket.closest('.paket-secim');
            const paketFiyatElement = seciliPaketDiv.querySelector('b');
            let paketFiyat = parseFloat(paketFiyatElement.innerText.replace(/[^0-9,]/g, '').replace(',', '.'));

            // Fiyat bilgi alanını bul veya oluştur
            let fiyatBilgiDiv = document.getElementById('fiyatBilgiAlani');
            if (!fiyatBilgiDiv) {
                fiyatBilgiDiv = document.createElement('div');
                fiyatBilgiDiv.id = 'fiyatBilgiAlani';
                fiyatBilgiDiv.className = 'uk-margin-top uk-card uk-card-secondary uk-border-rounded uk-box-shadow-small';
                odemeButonu.parentNode.insertBefore(fiyatBilgiDiv, odemeButonu);
            }

            // İndirim kodu uygulandıysa
            const indirimSonucuDiv = document.getElementById('indirimSonucu');
            if (indirimSonucuDiv && indirimSonucuDiv.innerHTML.trim() !== '') {
                const indirimliTutar = indirimSonucuDiv.querySelector('.uk-text-bold');
                if (indirimliTutar) {
                    paketFiyat = parseFloat(indirimliTutar.textContent.replace(/[^0-9,]/g, '').replace(',', '.'));
                }
            }

            let odenecekTutar = paketFiyat;
            let komisyonTutari = 0;
            let komisyonOrani = 0;
            let taksitSayisi = 1;

            // Kredi kartı seçiliyse komisyon ekle
            if (odemeYontemi === 'krediKarti') {
                // Seçili taksit sayısını ve komisyon oranını al
                const seciliTaksit = document.querySelector('input[name="t_s_m_a"]:checked');
                if (seciliTaksit) {
                    taksitSayisi = parseInt(seciliTaksit.value);
                    komisyonOrani = seciliTaksit.dataset.komisyon ? parseFloat(seciliTaksit.dataset.komisyon) : 0;
                } else {
                    komisyonOrani = parseFloat(document.getElementById('komisyon_orani').value);
                }

                komisyonTutari = (paketFiyat * komisyonOrani) / 100;
                odenecekTutar = paketFiyat + komisyonTutari;

                // Taksit bilgisi içeren ödeme detayı
                let taksitDetayi = '';
                if (taksitSayisi > 1) {
                    const taksitTutari = odenecekTutar / taksitSayisi;
                    taksitDetayi = `
                        <div class="uk-grid-small uk-text-medium" uk-grid>
                            <div class="uk-width-expand uk-text-bold uk-light">Taksit Tutarı:</div>
                            <div class="uk-width-auto uk-text-bold uk-text-warning">${taksitTutari.toFixed(2).replace('.', ',')} ₺ x ${taksitSayisi} Taksit</div>
                        </div>
                    `;
                }

                fiyatBilgiDiv.innerHTML = `
                    <div class="uk-card-body">
                        <h4 class="uk-text-bold uk-margin-remove uk-text-primary">Ödeme Bilgileri</h4>
                        <hr class="uk-margin-small">
                        <div class="uk-grid-small uk-text-medium" uk-grid>
                            <div class="uk-width-expand uk-text-bold uk-light">Paket Tutarı:</div>
                            <div class="uk-width-auto uk-text-bold uk-light">${paketFiyat.toFixed(2).replace('.', ',')} ₺</div>
                        </div>
                        <div class="uk-grid-small uk-text-medium" uk-grid>
                            <div class="uk-width-expand uk-text-bold uk-light">Komisyon (%${komisyonOrani}):</div>
                            <div class="uk-width-auto uk-text-bold uk-text-warning">+${komisyonTutari.toFixed(2).replace('.', ',')} ₺</div>
                        </div>
                        ${taksitDetayi}
                        <div class="uk-grid-small uk-text-medium uk-margin-small-top uk-background-secondary uk-padding-small uk-border-rounded" uk-grid>
                            <div class="uk-width-expand uk-text-bold uk-light">Toplam Ödenecek Tutar:</div>
                            <div class="uk-width-auto uk-text-primary uk-text-bold" style="font-size: 1.2em;">${odenecekTutar.toFixed(2).replace('.', ',')} ₺</div>
                        </div>
                        <div class="uk-text-small uk-text-warning uk-margin-small-top uk-flex uk-flex-middle">
                            <span uk-icon="icon: info; ratio: 0.8" class="uk-margin-small-right"></span>
                            <span>Kredi kartı ile ödemelerde seçilen taksit sayısına göre komisyon uygulanmaktadır.</span>
                        </div>
                    </div>
                `;
            } else {
                // Havale/EFT seçiliyse komisyonsuz fiyat
                fiyatBilgiDiv.innerHTML = `
                    <div class="uk-card-body">
                        <h4 class="uk-text-bold uk-margin-remove uk-text-primary">Ödeme Bilgileri</h4>
                        <hr class="uk-margin-small">
                        <div class="uk-grid-small uk-text-medium uk-background-secondary uk-padding-small uk-border-rounded" uk-grid>
                            <div class="uk-width-expand uk-text-bold uk-light">Toplam Ödenecek Tutar:</div>
                            <div class="uk-width-auto uk-text-primary uk-text-bold" style="font-size: 1.2em;">${paketFiyat.toFixed(2).replace('.', ',')} ₺</div>
                        </div>
                        <div class="uk-text-small uk-text-success uk-margin-small-top uk-flex uk-flex-middle">
                            <span uk-icon="icon: check; ratio: 0.8" class="uk-margin-small-right"></span>
                            <span>Havale/EFT ödemelerinde komisyon ücreti uygulanmamaktadır.</span>
                        </div>
                    </div>
                `;
            }

            // Form içindeki değerleri güncelle
            document.getElementById('komisyon_tutari').value = komisyonTutari.toFixed(2);
            document.getElementById('odenecek_tutar').value = odenecekTutar.toFixed(2);

            // Taksit sayısını ve komisyon oranını forma ekle/güncelle
            let taksitSayisiInput = document.getElementById('taksit_sayisi');
            if (!taksitSayisiInput) {
                taksitSayisiInput = document.createElement('input');
                taksitSayisiInput.type = 'hidden';
                taksitSayisiInput.id = 'taksit_sayisi';
                taksitSayisiInput.name = 't_s_m_a';
                document.getElementById('odemeForm').appendChild(taksitSayisiInput);
            }
            taksitSayisiInput.value = taksitSayisi;

            let komisyonOraniInput = document.getElementById('komisyon_orani');
            if (komisyonOraniInput) {
                komisyonOraniInput.value = komisyonOrani;
            }

            return {
                paketFiyat,
                komisyonTutari,
                odenecekTutar,
                taksitSayisi,
                komisyonOrani
            };
        }


            // Disable the button to prevent multiple submissions
            this.disabled = true;
            this.classList.add('uk-disabled');

            if (odemeYontemi === 'krediKarti') {
                // Kredi kartı bilgilerini kontrol et
                const kartNo = document.getElementById('kartno').value.trim();
                const kartIsim = document.getElementById('kartisim').value.trim();
                const kartSkt = document.getElementById('kartskt').value.trim();
                const kartCvc = document.getElementById('kartcvc').value.trim();

                // Kart numarası kontrolü
                if (!kartNo || kartNo.replace(/\s/g, '').length !== 16) {
                    Swal.fire('Hata', 'Geçerli bir kart numarası giriniz', 'error');
                    // Re-enable the button if validation fails
                    this.disabled = false;
                    this.classList.remove('uk-disabled');
                    return;
                }

                if (!kartIsim) {
                    Swal.fire('Hata', 'Kart sahibinin adını giriniz', 'error');
                    // Re-enable the button if validation fails
                    this.disabled = false;
                    this.classList.remove('uk-disabled');
                    return;
                }

                if (!kartSkt || kartSkt.length !== 5) {
                    Swal.fire('Hata', 'Geçerli bir son kullanma tarihi giriniz (AA/YY)', 'error');
                    // Re-enable the button if validation fails
                    this.disabled = false;
                    this.classList.remove('uk-disabled');
                    return;
                }

                if (!kartCvc || kartCvc.length !== 3) {
                    Swal.fire('Hata', 'Geçerli bir CVC kodu giriniz', 'error');
                    // Re-enable the button if validation fails
                    this.disabled = false;
                    this.classList.remove('uk-disabled');
                    return;
                }

                // Kart bilgilerini forma ekle
                addKartBilgileriToForm(odemeForm, kartNo, kartIsim, kartSkt, kartCvc);

                // Kredi kartı için formu direkt olarak submit et
                Swal.fire({
                    title: 'İşleminiz Yapılıyor',
                    text: 'Ödeme işleniyor, lütfen bekleyiniz...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                        setTimeout(() => {
                            odemeForm.submit();
                        }, 500);
                    }
                });
            } else {
                // Banka havalesi için AJAX işlemi
                const formData = new FormData(odemeForm);

                // Ödeme işlemi başladı
                Swal.fire({
                    title: 'İşleminiz Yapılıyor',
                    text: 'Havale bilgileri kaydediliyor...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Ödeme isteği
                fetch('/islemler/session_odeme_tamamla.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Havale Bilgileri Kaydedildi',
                                text: data.message,
                                confirmButtonText: 'Tamam'
                            }).then(() => {
                                // Başarılı işlem sonrası yönlendirme
                                window.location.href = data.redirect || '/hesabim';
                            });
                        } else {
                            // Re-enable the button if the request fails
                            odemeButonu.disabled = false;
                            odemeButonu.classList.remove('uk-disabled');

                            Swal.fire({
                                icon: 'error',
                                title: 'İşlem Başarısız',
                                text: data.message,
                                confirmButtonText: 'Tamam'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('İşlem hatası:', error);

                        // Re-enable the button if the request fails
                        odemeButonu.disabled = false;
                        odemeButonu.classList.remove('uk-disabled');

                        Swal.fire({
                            icon: 'error',
                            title: 'Sistem Hatası',
                            text: 'İşlem sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyin.',
                            confirmButtonText: 'Tamam'
                        });
                    });
            }
        });
</script>


<?php
$_SESSION['toplam_gerekli_kredi'] = $toplamGerekliKredi;
$_SESSION['eksik_kredi'] = $eksikKredi;
$_SESSION['aradaki_session_ids'] = json_encode($aradakiSessionIds);
?>



<!-- TC Kimlik Modal -->
<div id="tcKimlikModal" class="uk-flex-top" uk-modal="esc-close: false; bg-close: false">
    <div class="uk-modal-dialog uk-modal-body uk-margin-auto-vertical modal-seviye-width">
        <div class="fw9 modal-seviye mb0">TC Kimlik Numarası Gerekli</div>
        <div class="fw3 modal-seviye2 fw5">Ödeme işlemi için TC Kimlik numaranızı girmeniz gerekmektedir.</div>
        <hr class="modalhr">
        <div class="modal-seviye-icerik madde4">
            <form id="tcKimlikForm">
                <div class="uk-margin">
                    <label class="uk-form-label fw6" for="modal-tc-kimlik">TC Kimlik Numaranız *</label>
                    <div class="uk-form-controls">
                        <input class="uk-input" type="text" id="modal-tc-kimlik" name="tc_kimlik" placeholder="TC Kimlik Numaranızı Giriniz" maxlength="11" required>
                    </div>
                </div>
                <div class="uk-text-right uk-margin-top">
                    <button class="uk-button uk-button-primary br10" type="submit">Kaydet ve Devam Et</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// TC kimlik eksikse modal'ı göster
<?php if ($tc_kimlik_eksik): ?>
document.addEventListener('DOMContentLoaded', function() {
    // Sayfa yüklendiğinde TC kimlik modal'ını göster
    UIkit.modal('#tcKimlikModal').show();
});
<?php endif; ?>

// TC kimlik modal form submit
document.getElementById('tcKimlikForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const tcKimlik = document.getElementById('modal-tc-kimlik').value.trim();
    
    // TC kimlik kontrolü
    if (!tcKimlik) {
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'TC Kimlik numarası boş bırakılamaz.',
            confirmButtonText: 'Tamam'
        });
        return;
    }
    
    if (tcKimlik.length !== 11) {
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'TC Kimlik numarası 11 haneli olmalıdır.',
            confirmButtonText: 'Tamam'
        });
        return;
    }
    
    // AJAX ile TC kimlik güncelle
    const formData = new FormData();
    formData.append('tc_kimlik', tcKimlik);
    formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');
    
    fetch('/islemler/tc_kimlik_guncelle.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            UIkit.modal('#tcKimlikModal').hide();
            Swal.fire({
                icon: 'success',
                title: 'Başarılı!',
                text: 'TC Kimlik numaranız kaydedildi.',
                confirmButtonText: 'Tamam'
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Hata!',
                text: data.message,
                confirmButtonText: 'Tamam'
            });
        }
    })
    .catch(error => {
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'Bir hata oluştu. Lütfen tekrar deneyin.',
            confirmButtonText: 'Tamam'
        });
    });
});

// TC kimlik input'una sadece rakam girişi
document.getElementById('modal-tc-kimlik').addEventListener('input', function(e) {
    let value = this.value.replace(/\D/g, '');
    if (value.length > 11) {
        value = value.substring(0, 11);
    }
    this.value = value;
});

// Mesafeli Satış Sözleşmesi Modal
function sozlesmeGoster(tip) {
    // Kullanıcı bilgilerini al
    const uyeId = <?php echo $_SESSION['uye_id']; ?>;
    const paketSecimi = document.querySelector('input[name="paket_secim"]:checked');
    
    if (!paketSecimi) {
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'Lütfen önce bir paket seçin.',
            confirmButtonText: 'Tamam'
        });
        return;
    }

    const modal = UIkit.modal('#sozlesme-modal');
    const icerik = document.getElementById('sozlesme-icerik');

    // Yükleniyor mesajı göster
    icerik.innerHTML = '<div class="uk-text-center"><div class="uk-margin"><div uk-spinner="ratio: 2"></div></div><p>Sözleşme yükleniyor...</p></div>';
    modal.show();

    // AJAX isteği gönder
    const formData = new FormData();
    formData.append('sozlesme_tipi', tip);
    formData.append('uye_id', uyeId);
    formData.append('paket_id', paketSecimi.value);
    formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');

    fetch('/sozlesmeler/sozlesme_yukle.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            icerik.innerHTML = data.content;
        } else {
            icerik.innerHTML = `
            <div class="uk-text-center uk-text-danger">
                <p>${data.message || 'Sözleşme yüklenemedi'}</p>
                <button class="uk-button uk-button-default uk-margin-small-top" onclick="sozlesmeGoster('${tip}')">
                    Tekrar Dene
                </button>
            </div>
        `;
        }
    })
    .catch(error => {
        console.error('Sözleşme yüklenirken hata oluştu:', error);
        icerik.innerHTML = `
        <div class="uk-text-center uk-text-danger">
            <p>Sözleşme yüklenirken bir hata oluştu.</p>
            <p>Lütfen sayfayı yenileyip tekrar deneyin.</p>
            <button class="uk-button uk-button-default uk-margin-small-top" onclick="sozlesmeGoster('${tip}')">
                Tekrar Dene
            </button>
        </div>
    `;
    });
}


</script>

<!-- Sözleşme Modal -->
<div id="sozlesme-modal" class="uk-modal-full" uk-modal>
    <div class="uk-modal-dialog uk-modal-body uk-padding-remove">
        <button class="uk-modal-close-full uk-close-large" type="button" uk-close></button>
        <div class="uk-padding">
            <div class="uk-container uk-container-expand">
                <div id="sozlesme-icerik" class="uk-margin-medium-top">
                    <!-- Sözleşme içeriği buraya yüklenecek -->
                </div>
            </div>
        </div>
    </div>
</div>

<?php foot(); ?>