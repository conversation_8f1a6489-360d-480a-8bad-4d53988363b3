<?php
require 'view.php';
require_once 'db/kredi_fonksiyonlari.php';

// Oturum kontrolü
if (!oturumKontrol()) {
    header('Location: /giris-yap');
    exit;
}

// Kullanıcı bilgilerini al
$stmt = $db->prepare("SELECT * FROM uyeler WHERE id = ?");
$stmt->execute([$_SESSION['uye_id']]);
$uye = $stmt->fetch(PDO::FETCH_ASSOC);

if ($uye["durum"] == 0) {
    header('Location: /odeme');
    exit;
}

if ($uye["durum"] == 2) {
    header('Location: /odeme-bekliyor');
    exit;
}

// Eğer kullanıcı doğrulanmamışsa doğrulama sayfasına yönlendir
if ($uye['dogrulama_durumu'] == 0) {
    header('Location: /dogrulama');
    exit;
}

// Ücretsiz üye kontrolü - sadece ücretli üyeler görebilir
if (UyeBilgi("ucretsiz_uye") == 1) {
    header('Location: /');
    exit;
}

$uye_id = $_SESSION['uye_id'];

// Seçili dönemi al
$secilen_donem_id = seciliDonem($_SESSION['uye_id']);
$seciliGrup = getSeciliDonemGrubu($_SESSION['uye_id'], $secilen_donem_id);

// Grup bilgilerini ayarla
if (!$seciliGrup) {
    $grupBilgi = [
        'donem_adi' => 'Grup Yok',
        'grup_adi' => 'Grup Yok',
        'donem_id' => '',
        'grup_id' => '',
        'offline' => 0,
        'ilk_toplanti_link' => ''
    ];
    $secilen_donem_id = null;
} else {
    $grupBilgi = [
        'donem_adi' => $seciliGrup['donem_adi'],
        'grup_adi' => $seciliGrup['grup_adi'],
        'donem_id' => $seciliGrup['donem_id'],
        'grup_id' => $seciliGrup['grup_id'],
        'offline' => $seciliGrup['offline'],
        'whatsapp_link' => $seciliGrup['whatsapp_link'],
        'whatsapp_link2' => $seciliGrup['whatsapp_link2'],
        'ilk_toplanti_link' => $seciliGrup['ilk_toplanti_link'],
        'kirk_paket_durum' => $seciliGrup['kirk_paket_durum']
    ];
    $secilen_donem_id = $seciliGrup['donem_id'];
}

// Bilgilendirme içeriklerini al
$bilgilendirme_icerikleri = [];
$stmt = $db->prepare("
    SELECT baslik_$dil as baslik, icerik_$dil as icerik
    FROM aciliricerik 
    WHERE menuid = 179 AND sayfatipi = 4 
    ORDER BY id ASC
");
$stmt->execute();
$bilgilendirme_icerikleri = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Sınav durumu hesaplama fonksiyonu - SADECE BİR KEZ ÇALIŞACAK
function sinavDurumuHesapla($sinav, $uye_id)
{
    $simdi = new DateTime();
    $atama_tarihi = new DateTime($sinav['atama_tarihi']);

    // 1. sınav: atama tarihinden 1 hafta
    $birinci_sinav_baslangic = clone $atama_tarihi;
    $birinci_sinav_bitis = clone $atama_tarihi;
    $birinci_sinav_bitis->add(new DateInterval('P7D'));

    // 2. sınav: 1. sınav bittikten 1 hafta sonra başlar, 1 hafta sürer
    $ikinci_sinav_baslangic = clone $birinci_sinav_bitis;
    $ikinci_sinav_baslangic->add(new DateInterval('P7D'));
    $ikinci_sinav_bitis = clone $ikinci_sinav_baslangic;
    $ikinci_sinav_bitis->add(new DateInterval('P7D'));

    // Sınav sonuçlarını al
    $birinci_sinav_puan = $sinav['birinci_sinav_puan'];
    $ikinci_sinav_puan = $sinav['ikinci_sinav_puan'];

    // Hangi sınav aktif?
    $birinci_sinav_aktif = ($simdi >= $birinci_sinav_baslangic && $simdi <= $birinci_sinav_bitis && $birinci_sinav_puan === null);
    $ikinci_sinav_aktif = ($simdi >= $ikinci_sinav_baslangic && $simdi <= $ikinci_sinav_bitis && $ikinci_sinav_puan === null);

    // Başarı durumu
    $basarili = ($birinci_sinav_puan >= 70) || ($ikinci_sinav_puan >= 70);
    $basarisiz = ($birinci_sinav_puan !== null && $birinci_sinav_puan < 70) && ($ikinci_sinav_puan !== null && $ikinci_sinav_puan < 70);

    return [
        'birinci_sinav_baslangic' => $birinci_sinav_baslangic,
        'birinci_sinav_bitis' => $birinci_sinav_bitis,
        'ikinci_sinav_baslangic' => $ikinci_sinav_baslangic,
        'ikinci_sinav_bitis' => $ikinci_sinav_bitis,
        'birinci_sinav_aktif' => $birinci_sinav_aktif,
        'ikinci_sinav_aktif' => $ikinci_sinav_aktif,
        'birinci_sinav_puan' => $birinci_sinav_puan,
        'ikinci_sinav_puan' => $ikinci_sinav_puan,
        'basarili' => $basarili,
        'basarisiz' => $basarisiz,
        'aktif' => $birinci_sinav_aktif || $ikinci_sinav_aktif
    ];
}

// Seviye sınavlarını al
$seviye_sinavlari = [];
if ($secilen_donem_id && $grupBilgi['grup_id']) {
    $stmt = $db->prepare("
        SELECT s.*, 
               ssa.grup_id,
               ssa.ders_id,
               ssa.atama_tarihi,
               g.grup_adi,
               d.ders_adi,
               sss1.puan as birinci_sinav_puan,
               sss2.puan as ikinci_sinav_puan
        FROM seviye_sinavlari s
        INNER JOIN seviye_sinav_atamalari ssa ON s.id = ssa.sinav_id
        INNER JOIN gruplar g ON ssa.grup_id = g.id
        INNER JOIN dersler d ON ssa.ders_id = d.id
        LEFT JOIN seviye_sinav_sonuclari sss1 ON s.id = sss1.sinav_id AND sss1.uye_id = ? AND sss1.sinav_denemesi = 1
        LEFT JOIN seviye_sinav_sonuclari sss2 ON s.id = sss2.sinav_id AND sss2.uye_id = ? AND sss2.sinav_denemesi = 2
        WHERE ssa.donem_id = ? 
        AND ssa.grup_id <= ? 
        AND ssa.durum = 'aktif'
        AND s.durum = 'aktif'
        ORDER BY ssa.atama_tarihi DESC
    ");
    $stmt->execute([$uye_id, $uye_id, $secilen_donem_id, $grupBilgi['grup_id']]);
    $seviye_sinavlari = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

$title = "Seviye Sınavları";
$description = "İngilizce seviye sınavları ve sertifika sistemi";

head();
?>

<link rel="stylesheet" href="assets/css/seviye-sinavlari-v3.css">

<div class="uk-position-relative circle-background">
    <div class="uk-position-absolute uk-visible@l" id="gradients-container">
        <div class="gradient-mask"></div>
        <div class="gradient-bg">
            <svg xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <filter id="goo">
                        <feGaussianBlur in="SourceGraphic" stdDeviation="10" result="blur" />
                        <feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -8" result="goo" />
                        <feBlend in="SourceGraphic" in2="goo" />
                    </filter>
                </defs>
            </svg>
            <div class="gradients-container">
                <div class="g1"></div>
                <div class="g2"></div>
                <div class="g3"></div>
                <div class="g4"></div>
                <div class="interactive"></div>
            </div>
        </div>
    </div>
    <div class="uk-container uk-container-large uk-position-relative z-9 icsayfa-padding-account">
        <div class="uk-grid-small login-mh-1" uk-grid>
            <div class="uk-width-expand" uk-scrollspy="cls: uk-animation-slide-bottom-medium; delay: 100; repeat: false">
                <div class="uk-grid uk-grid-large uk-margin-large-top">
                    <div class="uk-width-expand uk-visible@m" uk-scrollspy="cls: uk-animation-slide-bottom-medium; delay: 100; repeat: false">
                        <div class="hesabim-menu" uk-sticky="end: #cnt; offset: 200;media: @m">
                            <?php echo getSidebar(); ?>
                        </div>
                    </div>
                    <div class="uk-width-expand uk-hidden@m uk-margin-small-bottom" uk-scrollspy="cls: uk-animation-slide-bottom-medium; delay: 100; repeat: false">
                        <?php echo getMobileSidebar(); ?>
                    </div>

                    <div class="uk-width-3-4@m uk-position-relative" uk-scrollspy="cls: uk-animation-slide-bottom-medium; delay: 100; repeat: false" id="cnt">

                        <div class="account-form-container">
                            <!-- Sayfa Başlığı -->
                            <div class="uk-margin-large-bottom">
                                <div class="uk-flex uk-flex-middle uk-flex-wrap">
                                    <div class="uk-width-expand">
                                        <h1 class="uk-text-white fw7 fs40-mb"><?php echo sayfalar(186, "baslik_$dil") ?></h1>
                                        <p class="uk-text-muted fw5 fs20-mb"><?php echo sayfalar(186, "baslik1_$dil") ?></p>
                                    </div>
                                </div>

                                <!-- Aktif Sınav Uyarısı -->
                                <?php
                                $aktif_sinav = null;
                                if (!empty($seviye_sinavlari)) {
                                    foreach ($seviye_sinavlari as $sinav) {
                                        $durum = sinavDurumuHesapla($sinav, $uye_id);

                                        // Sadece 1. sınav veya 2. sınav aktifse göster (dinlenme zamanında gösterme)
                                        if (($durum['birinci_sinav_aktif'] || $durum['ikinci_sinav_aktif']) && !$durum['basarili']) {
                                            $aktif_sinav = $sinav;
                                            $aktif_sinav['durum'] = $durum;

                                            // Hangi sınav aktifse ona göre son giriş tarihini hesapla
                                            if ($durum['birinci_sinav_aktif']) {
                                                $aktif_sinav['son_giris_tarihi'] = $durum['birinci_sinav_bitis']->format('d.m.Y H:i');
                                                $aktif_sinav['sinav_tipi'] = '1. Sınav';
                                            } elseif ($durum['ikinci_sinav_aktif']) {
                                                $aktif_sinav['son_giris_tarihi'] = $durum['ikinci_sinav_bitis']->format('d.m.Y H:i');
                                                $aktif_sinav['sinav_tipi'] = '2. Sınav';
                                            }

                                            break; // İlk aktif sınavı bul
                                        }
                                    }
                                }
                                ?>

                                <?php if ($aktif_sinav): ?>
                                    <div class="uk-margin-medium-top">
                                        <div class="active-exam-alert uk-card uk-card-default uk-card-body">
                                            <div class="uk-flex uk-flex-middle uk-flex-wrap">
                                                <div class="uk-width-auto uk-margin-small-right uk-hidden uk-visible@m">
                                                    <span class="alert-icon">🎯</span>
                                                </div>
                                                <div class="uk-width-expand">
                                                    <h4 class="uk-text-black fw7 fs40 uk-text-center"> <?= htmlspecialchars($aktif_sinav['sinav_adi']) ?> / <?= $aktif_sinav['sinav_tipi'] ?> Aktif!</h4>

                                                    <!-- Sınav Detayları -->
                                                    <div class=" uk-margin-small-top">
                                                        <div class="uk-grid-small" uk-grid>
                                                            <div class="uk-width-1-2 uk-width-1-4@m">
                                                                <div class="detail-item">
                                                                    <span class="detail-icon">📝</span>
                                                                    <span class="detail-label">Soru Sayısı:</span>
                                                                    <span class="detail-value"><?= ($aktif_sinav['coktan_secmeli_soru_sayisi'] + $aktif_sinav['cumle_soru_sayisi']) ?></span>
                                                                </div>
                                                            </div>
                                                            <div class="uk-width-1-2 uk-width-1-4@m">
                                                                <div class="detail-item">
                                                                    <span class="detail-icon">⏱️</span>
                                                                    <span class="detail-label">Süre:</span>
                                                                    <span class="detail-value"><?= $aktif_sinav['sure_dakika'] ?> dk</span>
                                                                </div>
                                                            </div>
                                                            <div class="uk-width-1-2 uk-width-1-4@m">
                                                                <div class="detail-item">
                                                                    <span class="detail-icon">📅</span>
                                                                    <span class="detail-label">Son Giriş:</span>
                                                                    <span class="detail-value"><?= $aktif_sinav['son_giris_tarihi'] ?? 'Belirtilmemiş' ?></span>
                                                                </div>
                                                            </div>
                                                            <div class="uk-width-1-2 uk-width-1-4@m">
                                                                <div class="detail-item">
                                                                    <span class="detail-icon">🎯</span>
                                                                    <span class="detail-label">Geçme Notu:</span>
                                                                    <span class="detail-value">%70</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="uk-margin-small-top uk-flex uk-flex-center">
                                                        <a href="/seviye-sinav/<?= $aktif_sinav['id'] ?>" class="exam-btn">
                                                            ▶️ Sınavı Başlat
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Bilgilendirme Accordion -->
                                <?php if (!empty($bilgilendirme_icerikleri)): ?>
                                    <div class="uk-margin-medium-top">
                                        <div class="sss">
                                            <?php $i = 0; ?>
                                            <ul uk-accordion="duration:400;" uk-scrollspy="target: > li; cls: uk-animation-slide-bottom; delay: 50; repeat:false">
                                                <?php foreach ($bilgilendirme_icerikleri as $ai) { ?>
                                                    <li class="<?php if ($i == 0) {
                                                                    echo "uk-open";
                                                                } ?> mb10 uk-position-relative">
                                                        <a class="uk-accordion-title" href><?php echo $ai["baslik"] ?></a>
                                                        <div class="uk-accordion-content madde4 fw5">
                                                            <div><?php echo $ai["icerik"] ?></div>
                                                        </div>
                                                    </li>
                                                <?php $i++;
                                                } ?>
                                            </ul>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>


                            <!-- Sınav Listesi -->
                            <div class="uk-margin-large-bottom">

                                <img src="/images/footer-solid.webp" alt="Solid">

                                <div class="exam-section-header uk-flex uk-flex-middle uk-flex-between uk-margin-medium-bottom uk-margin-medium-top" style="background: linear-gradient(to right, #f988e7, #9f56ca); border-radius: 18px; padding: 24px 32px;">
                                    <div class="uk-flex uk-flex-middle">
                                        <span class="uk-icon-button" style="background:#fff; color:#ee3131; margin-right:18px;" uk-icon="icon: list; ratio: 1.4"></span>
                                        <h2 class="fw7 fs30-mb uk-margin-remove uk-text-white" style="letter-spacing: 0.5px;">
                                            Seviye Sınavlarım <span style="color:#fff; font-weight:400;">+ (Varsa 2. Sınav Hakkım)</span>
                                        </h2>
                                    </div>
                                </div>

                                <?php if (empty($seviye_sinavlari)): ?>
                                    <div class="empty-state-box p5" style="background: linear-gradient(135deg, #2f3967 0%, #ee3131 100%); border-radius: 16px; box-shadow: 0 4px 24px rgba(47,57,103,0.10); min-height: 100px; color: #fff;">
                                        <h3 class="empty-state-title fw7" style="color:#fff;">
                                            <?php if (!$secilen_donem_id): ?>
                                                Seçili Dönem Yok
                                            <?php elseif (!$grupBilgi['grup_id']): ?>
                                                Seçili Grup Yok
                                            <?php else: ?>
                                                <span uk-icon="icon: warning; ratio: 1.5" style="color: #ee3131; background: #fff; border-radius: 50%; padding: 4px; box-shadow: 0 2px 8px rgba(238,49,49,0.08);"></span> Henüz Sınav Bulunmuyor
                                            <?php endif; ?>
                                        </h3>
                                        <p class="empty-state-text fw4" style="color:#fff; font-size:18px;">
                                            <?php if (!$secilen_donem_id): ?>
                                                Aktif bir döneminiz bulunmamaktadır.
                                            <?php elseif (!$grupBilgi['grup_id']): ?>
                                                Bu dönemde aktif bir grubunuz bulunmamaktadır.
                                            <?php else: ?>
                                                Bu dönem için henüz seviye sınavı bulunmamaktadır.
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                <?php else: ?>
                                    <div class="uk-grid-small uk-grid-match" uk-grid>
                                        <?php foreach ($seviye_sinavlari as $sinav): ?>
                                            <?php $durum = sinavDurumuHesapla($sinav, $uye_id); ?>

                                            <div class="uk-width-1-1 uk-width-1-2@m uk-margin-medium-bottom">
                                                <div class="exam-card <?= $durum['aktif'] ? 'active-exam' : '' ?> <?= $durum['basarili'] ? 'success-exam' : '' ?> <?= $durum['basarisiz'] ? 'failed-exam' : '' ?>">

                                                    <!-- Sınav Başlığı -->
                                                    <div class="exam-header">
                                                        <h3 class="exam-title"><?= htmlspecialchars($sinav['sinav_adi']) ?></h3>
                                                    </div>

                                                    <!-- Sekme Navigasyonu -->
                                                    <div class="exam-tabs">
                                                        <ul class="uk-subnav uk-subnav-pill" uk-switcher="animation: uk-animation-fade">
                                                            <li class="<?= ($durum['ikinci_sinav_aktif'] || $durum['ikinci_sinav_puan'] !== null) ? '' : 'uk-active' ?>">
                                                                <a href="#" class="exam-tab-link">1. Sınav</a>
                                                            </li>
                                                            <?php if ($durum['birinci_sinav_puan'] === null || $durum['birinci_sinav_puan'] < 70): ?>
                                                                <li class="<?= ($durum['ikinci_sinav_aktif'] || $durum['ikinci_sinav_puan'] !== null) ? 'uk-active' : '' ?>">
                                                                    <a href="#" class="exam-tab-link">2. Sınav</a>
                                                                </li>
                                                            <?php endif; ?>
                                                        </ul>
                                                    </div>

                                                    <!-- Sekme İçerikleri -->
                                                    <div class="uk-switcher uk-margin">

                                                        <!-- 1. Sınav Sekmesi -->
                                                        <div class="exam-tab-content <?= ($durum['ikinci_sinav_aktif'] || $durum['ikinci_sinav_puan'] !== null) ? '' : 'uk-active' ?>">
                                                            <div class="exam-meta">
                                                                <div class="exam-meta-item">
                                                                    <span class="exam-meta-icon">📅</span>
                                                                    <span class="exam-meta-text">Başlangıç: <?= $durum['birinci_sinav_baslangic']->format('d.m.Y H:i') ?></span>
                                                                </div>
                                                                <div class="exam-meta-item">
                                                                    <span class="exam-meta-icon">⏰</span>
                                                                    <span class="exam-meta-text">Bitiş: <?= $durum['birinci_sinav_bitis']->format('d.m.Y H:i') ?></span>
                                                                </div>
                                                                <div class="exam-meta-item">
                                                                    <span class="exam-meta-icon">📝</span>
                                                                    <span class="exam-meta-text">Soru: <?= ($sinav['coktan_secmeli_soru_sayisi'] + $sinav['cumle_soru_sayisi']) ?></span>
                                                                </div>
                                                                <div class="exam-meta-item">
                                                                    <span class="exam-meta-icon">⏱️</span>
                                                                    <span class="exam-meta-text">Süre: <?= $sinav['sure_dakika'] ?> dk</span>
                                                                </div>
                                                                <?php if ($durum['birinci_sinav_puan'] !== null): ?>
                                                                    <div class="exam-meta-item">
                                                                        <span class="exam-meta-icon">📊</span>
                                                                        <span class="exam-meta-text">Puan: %<?= $durum['birinci_sinav_puan'] ?></span>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>

                                                            <!-- Açıklayıcı Metin -->
                                                            <div class="exam-description uk-margin-small-top">
                                                                <?php if ($durum['birinci_sinav_puan'] !== null && $durum['birinci_sinav_puan'] >= 70): ?>
                                                                    <p class="uk-text-success uk-text-small fw5">🎉 Tebrikler! 1. sınavda başarılı oldunuz!</p>
                                                                <?php elseif ($durum['birinci_sinav_puan'] !== null && $durum['birinci_sinav_puan'] < 70): ?>
                                                                    <p class="uk-text-danger uk-text-small fw5">❌ 1. sınavda başarısız oldunuz. 2. sınav hakkınız var.</p>
                                                                <?php elseif ($durum['birinci_sinav_aktif']): ?>
                                                                    <p class="uk-text-black uk-text-small fw5">✅ 1. sınav aktif! Sınava girebilirsiniz.</p>
                                                                <?php elseif (new DateTime() < $durum['birinci_sinav_baslangic']): ?>
                                                                    <p class="uk-text-black uk-text-small fw5">⏳ 1. sınav <?= $durum['birinci_sinav_baslangic']->format('d.m.Y H:i') ?> tarihinde başlayacak ve <?= $durum['birinci_sinav_bitis']->format('d.m.Y H:i') ?> tarihinde bitecek.</p>
                                                                <?php else: ?>
                                                                    <p class="uk-text-black uk-text-small fw5">⏰ 1. sınav <?= $durum['birinci_sinav_baslangic']->format('d.m.Y H:i') ?> tarihinde başladı ve <?= $durum['birinci_sinav_bitis']->format('d.m.Y H:i') ?> tarihinde giriş süresi doldu.</p>
                                                                <?php endif; ?>
                                                            </div>

                                                            <!-- Buton -->
                                                            <div class="uk-margin-small-top uk-flex uk-flex-center">
                                                                <?php if ($durum['birinci_sinav_puan'] !== null && $durum['birinci_sinav_puan'] >= 70): ?>
                                                                    <div class="uk-grid-small uk-child-width-1-2 uk-grid-match" uk-grid>
                                                                        <div>
                                                                            <button class="exam-btn certificate uk-text-center uk-flex uk-flex-center uk-width-100" onclick="sertifikaGoster(<?= $sinav['id'] ?>)">
                                                                                Sertifikayı Görüntüle
                                                                            </button>
                                                                        </div>
                                                                        <div>
                                                                            <button class="exam-btn pink uk-text-center uk-flex uk-flex-center uk-width-100" onclick="sonuclariGoster(<?= $sinav['id'] ?>, 1)">
                                                                                Sonuçları Görüntüle
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                <?php elseif ($durum['birinci_sinav_puan'] !== null): ?>
                                                                    <button class="exam-btn pink uk-text-center uk-flex uk-flex-center uk-width-100" onclick="sonuclariGoster(<?= $sinav['id'] ?>, 1)">
                                                                        Sonuçları Görüntüle
                                                                    </button>
                                                                <?php elseif ($durum['birinci_sinav_aktif']): ?>
                                                                    <a href="/seviye-sinav/<?= $sinav['id'] ?>" class="exam-btn uk-text-center uk-flex uk-flex-center uk-width-100">
                                                                        Sınavı Başlat
                                                                    </a>
                                                                <?php else: ?>
                                                                    <button class="exam-btn disabled uk-text-center uk-flex uk-flex-center uk-width-100" disabled>
                                                                        Sınava Giriş Kapalı
                                                                    </button>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>

                                                        <!-- 2. Sınav Sekmesi -->
                                                        <?php if ($durum['birinci_sinav_puan'] === null || $durum['birinci_sinav_puan'] < 70): ?>
                                                            <div class="exam-tab-content <?= ($durum['ikinci_sinav_aktif'] || $durum['ikinci_sinav_puan'] !== null) ? 'uk-active' : '' ?>">
                                                                <div class="exam-meta">
                                                                    <div class="exam-meta-item">
                                                                        <span class="exam-meta-icon">📅</span>
                                                                        <span class="exam-meta-text">Başlangıç: <?= $durum['ikinci_sinav_baslangic']->format('d.m.Y H:i') ?></span>
                                                                    </div>
                                                                    <div class="exam-meta-item">
                                                                        <span class="exam-meta-icon">⏰</span>
                                                                        <span class="exam-meta-text">Bitiş: <?= $durum['ikinci_sinav_bitis']->format('d.m.Y H:i') ?></span>
                                                                    </div>
                                                                    <div class="exam-meta-item">
                                                                        <span class="exam-meta-icon">📝</span>
                                                                        <span class="exam-meta-text">Soru: <?= ($sinav['coktan_secmeli_soru_sayisi'] + $sinav['cumle_soru_sayisi']) ?></span>
                                                                    </div>
                                                                    <div class="exam-meta-item">
                                                                        <span class="exam-meta-icon">⏱️</span>
                                                                        <span class="exam-meta-text">Süre: <?= $sinav['sure_dakika'] ?> dk</span>
                                                                    </div>
                                                                    <?php if ($durum['ikinci_sinav_puan'] !== null): ?>
                                                                        <div class="exam-meta-item">
                                                                            <span class="exam-meta-icon">📊</span>
                                                                            <span class="exam-meta-text">Puan: %<?= $durum['ikinci_sinav_puan'] ?></span>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>

                                                                <!-- Açıklayıcı Metin -->
                                                                <div class="exam-description uk-margin-small-top">
                                                                    <?php if ($durum['ikinci_sinav_puan'] !== null && $durum['ikinci_sinav_puan'] >= 70): ?>
                                                                        <p class="uk-text-success uk-text-small fw5">🎉 Tebrikler! 2. sınavda başarılı oldunuz!</p>
                                                                    <?php elseif ($durum['ikinci_sinav_puan'] !== null && $durum['ikinci_sinav_puan'] < 70): ?>
                                                                        <p class="uk-text-danger uk-text-small fw5">❌ 2. sınavda başarısız oldunuz.</p>
                                                                    <?php elseif ($durum['ikinci_sinav_aktif']): ?>
                                                                        <p class="uk-text-black uk-text-small fw5">✅ 2. sınav aktif! Sınava girebilirsiniz.</p>
                                                                    <?php elseif (new DateTime() < $durum['ikinci_sinav_baslangic']): ?>
                                                                        <p class="uk-text-black uk-text-small fw5">⏳ 2. sınav <?= $durum['ikinci_sinav_baslangic']->format('d.m.Y H:i') ?> tarihinde başlayacak ve <?= $durum['ikinci_sinav_bitis']->format('d.m.Y H:i') ?> tarihinde bitecek.</p>
                                                                    <?php else: ?>
                                                                        <p class="uk-text-black uk-text-small fw5">⏰ 2. sınav <?= $durum['ikinci_sinav_baslangic']->format('d.m.Y H:i') ?> tarihinde başladı ve <?= $durum['ikinci_sinav_bitis']->format('d.m.Y H:i') ?> tarihinde giriş süresi doldu.</p>
                                                                    <?php endif; ?>
                                                                </div>

                                                                <!-- Buton -->
                                                                <div class="uk-margin-small-top uk-flex uk-flex-center">
                                                                    <?php if ($durum['ikinci_sinav_puan'] !== null && $durum['ikinci_sinav_puan'] >= 70): ?>
                                                                        <div class="uk-grid-small uk-child-width-1-2 uk-grid-match" uk-grid>
                                                                            <div>
                                                                                <button class="exam-btn certificate uk-text-center uk-flex uk-flex-center uk-width-100" onclick="sertifikaGoster(<?= $sinav['id'] ?>)">
                                                                                    Sertifikayı Görüntüle
                                                                                </button>
                                                                            </div>
                                                                            <div>
                                                                                <button class="exam-btn pink uk-text-center uk-flex uk-flex-center uk-width-100" onclick="sonuclariGoster(<?= $sinav['id'] ?>, 2)">
                                                                                    Sonuçları Görüntüle
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                    <?php elseif ($durum['ikinci_sinav_puan'] !== null): ?>
                                                                        <button class="exam-btn pink uk-text-center uk-flex uk-flex-center uk-width-100" onclick="sonuclariGoster(<?= $sinav['id'] ?>, 2)">
                                                                            Sonuçları Görüntüle
                                                                        </button>
                                                                    <?php elseif ($durum['ikinci_sinav_aktif']): ?>
                                                                        <a href="/seviye-sinav/<?= $sinav['id'] ?>" class="exam-btn uk-text-center uk-flex uk-flex-center uk-width-100">
                                                                            Sınavı Başlat
                                                                        </a>
                                                                    <?php else: ?>
                                                                        <button class="exam-btn disabled uk-text-center uk-flex uk-flex-center uk-width-100" disabled>
                                                                            Sınava Giriş Kapalı
                                                                        </button>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sertifika Modal -->
<div id="sertifikaModal" class="uk-modal-container" uk-modal>
    <div class="uk-modal-dialog uk-modal-body uk-margin-auto-vertical">
        <button class="uk-modal-close-default fw7 close-circle-button" type="button">x</button>
        <div class="modal-body" id="sertifikaModalBody">
            <div class="uk-flex uk-flex-center uk-flex-middle uk-margin-bottom">
                <div class="uk-button-group">
                    <a id="sertifikaIndirBtn" href="#" target="_blank" class="uk-button gradient-small-button hover-top uk-text-decoration-none">
                        <span uk-icon="download"></span> Sertifikayı İndir
                    </a>
                </div>
            </div>

            <!-- PDF.js Viewer Container -->
            <div id="pdfViewerContainer" style="border: 2px solid #e5e5e5; border-radius: 8px; overflow: hidden; position: relative;">
                <div id="pdfViewer" style="width: 100%; height: 100%; min-height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- Sonuç Modal -->
<div id="sonucModal" class="uk-modal-container" uk-modal>
    <div class="uk-modal-dialog uk-modal-large uk-modal-body uk-margin-auto-vertical">
        <button class="uk-modal-close-default fw7 close-circle-button" type="button">x</button>
        <div class="modal-body" id="sonucModalBody">
            <div class="uk-text-center uk-margin-medium-bottom">
                <h3 class="uk-modal-title fw5 uk-text-black" id="sonucModalTitle">Sınav Sonuçları</h3>
            </div>

            <!-- Sonuç Özeti -->
            <div class="uk-grid-small uk-child-width-1-2@s uk-child-width-1-4@m uk-text-center uk-margin-medium-bottom" uk-grid>
                <div>
                    <div class="sonuc-ozet-kutu">
                        <h4 class="sonuc-ozet-baslik">Toplam Soru</h4>
                        <p class="sonuc-ozet-deger" id="modal-toplam-soru">0</p>
                    </div>
                </div>
                <div>
                    <div class="sonuc-ozet-kutu">
                        <h4 class="sonuc-ozet-baslik">Doğru Cevap</h4>
                        <p class="sonuc-ozet-deger" id="modal-dogru-sayisi">0</p>
                    </div>
                </div>
                <div>
                    <div class="sonuc-ozet-kutu">
                        <h4 class="sonuc-ozet-baslik">Yanlış Cevap</h4>
                        <p class="sonuc-ozet-deger" id="modal-yanlis-sayisi">0</p>
                    </div>
                </div>
                <div>
                    <div class="sonuc-ozet-kutu">
                        <h4 class="sonuc-ozet-baslik">Başarı Yüzdesi</h4>
                        <p class="sonuc-ozet-deger" id="modal-basari-orani">%0</p>
                    </div>
                </div>
            </div>



            <!-- Soru Sonuçları -->
            <div id="modal-soru-sonuclari" class="uk-margin-medium-top">
                <!-- Soru sonuçları burada dinamik olarak gösterilecek -->
            </div>
        </div>
    </div>
</div>

<!-- PDF.js Kütüphanesi -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script>
    // PDF.js worker'ını ayarla
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

    // Sertifika gösterme fonksiyonu
    function sertifikaGoster(sinavId) {
        window.currentSinavId = sinavId;
        UIkit.modal('#sertifikaModal').show();

        document.getElementById('pdfViewer').innerHTML = `
            <div style="text-align:center; padding:50px;">
                <div uk-spinner="ratio: 2"></div>
                <h3 style="margin-top:20px; color:#000; font-weight: 500;">Lütfen bekleyin...</h3>
                <p style="color:#000; font-weight: 600; margin-top:10px;">Sertifika yükleniyor</p>
            </div>
        `;

        loadSertifikaWithPDFJS(sinavId);

        setTimeout(() => {
            const sertifikaIndirBtn = document.getElementById('sertifikaIndirBtn');
            if (sertifikaIndirBtn) {
                sertifikaIndirBtn.href = `/sertifika-indir?sinav_id=${sinavId}&token=<?= $_SESSION['csrf_token'] ?? '' ?>`;
            }
        }, 100);
    }

    // Sonuçları gösterme fonksiyonu
    function sonuclariGoster(sinavId, sinavDenemesi) {
        window.currentSinavId = sinavId;
        window.currentSinavDenemesi = sinavDenemesi;

        // Modal başlığını güncelle
        document.getElementById('sonucModalTitle').textContent = `${sinavDenemesi}. Sınav Sonuçları`;

        // Loading göster
        document.getElementById('modal-soru-sonuclari').innerHTML = `
            <div style="text-align:center; padding:50px;">
                <div uk-spinner="ratio: 2"></div>
                <h3 style="margin-top:20px; color:#fff; font-weight: 500;">Lütfen bekleyin...</h3>
                <p style="color:#fff; font-weight: 600; margin-top:10px;">Sonuçlar yükleniyor</p>
            </div>
        `;

        UIkit.modal('#sonucModal').show();

        // Sonuçları yükle
        loadSonuclar(sinavId, sinavDenemesi);
    }

    // Sonuçları yükleme fonksiyonu
    function loadSonuclar(sinavId, sinavDenemesi) {
        fetch('/islemler/seviye_sinav_kullanici_islemleri.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    islem: 'sinav_sonuclari_getir',
                    sinav_id: sinavId,
                    sinav_denemesi: sinavDenemesi,
                    csrf_token: '<?= $_SESSION['csrf_token'] ?? '' ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Sonuç özetini güncelle
                    document.getElementById('modal-toplam-soru').textContent = data.toplam_soru;
                    document.getElementById('modal-dogru-sayisi').textContent = data.dogru_sayisi;
                    document.getElementById('modal-yanlis-sayisi').textContent = data.yanlis_sayisi;
                    document.getElementById('modal-basari-orani').textContent = '%' + data.puan;



                    // Soru sonuçlarını göster
                    showSoruSonuclari(data.sorular);
                } else {
                    document.getElementById('modal-soru-sonuclari').innerHTML = `
                    <div class="uk-alert uk-alert-warning">
                        <p><strong>Hata:</strong> ${data.message}</p>
                    </div>
                `;
                }
            })
            .catch(error => {
                console.error('Sonuç yükleme hatası:', error);
                document.getElementById('modal-soru-sonuclari').innerHTML = `
                <div class="uk-alert uk-alert-danger">
                    <p><strong>Hata:</strong> Sonuçlar yüklenirken bir hata oluştu.</p>
                </div>
            `;
            });
    }

    // Soru sonuçlarını gösterme fonksiyonu
    function showSoruSonuclari(sorular) {
        const container = document.getElementById('modal-soru-sonuclari');

        if (!sorular || sorular.length === 0) {
            container.innerHTML = `
                <div class="uk-alert uk-alert-warning">
                    <p><strong>Detaylı soru sonuçları henüz mevcut değil.</strong></p>
                </div>
            `;
            return;
        }

        let html = '';
        sorular.forEach((soru, index) => {
            const isDogru = soru.dogru_mu == 1;
            const badgeClass = isDogru ? 'dogru-badge' : 'yanlis-badge';
            const badgeText = isDogru ? 'DOĞRU' : 'YANLIŞ';
            const soruClass = isDogru ? 'dogru' : 'yanlis';

            html += `
                <div class="soru-sonuc ${soruClass} uk-margin-medium-bottom">
                    <div class="soru-sonuc-baslik uk-flex uk-flex-between uk-flex-middle">
                        <span class="soru-no fw6">${index + 1}.</span>
                        <span class="sonuc-badge ${badgeClass}">${badgeText}</span>
                    </div>
                    <div class="soru-metin fw5 uk-margin-small-top">${soru.soru}</div>
                    <div class="cevap-bilgi uk-margin-small-top">
                        <div class="uk-grid-small" uk-grid>
                            <div class="uk-width-1-2 fw5">
                                <strong>Sizin Cevabınız:</strong><br>
                                <span class="uk-text-black">${soru.ogrenci_cevabi || 'Cevap verilmedi'}</span>
                            </div>
                            <div class="uk-width-1-2 fw5">
                                <strong>Doğru Cevap:</strong><br>
                                <span class="uk-text-dogru">${soru.dogru_cevap}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    // PDF.js ile sertifika yükle
    async function loadSertifikaWithPDFJS(sinavId) {
        const pdfViewer = document.getElementById('pdfViewer');

        try {
            const response = await fetch('/islemler/seviye_sinav_kullanici_islemleri.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    islem: 'sertifika_goster',
                    sinav_id: sinavId,
                    csrf_token: '<?= $_SESSION['csrf_token'] ?? '' ?>'
                })
            });

            const data = await response.json();

            if (data.success && data.sertifika_dosya_yolu) {
                const pdfUrl = '/sertifika-indir?dosya=' + encodeURIComponent(data.sertifika_dosya_yolu) + '&preview=1';
                const loadingTask = pdfjsLib.getDocument(pdfUrl);
                const pdf = await loadingTask.promise;
                const page = await pdf.getPage(1);

                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                const viewport = page.getViewport({
                    scale: 1.5
                });
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                const renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };

                await page.render(renderContext).promise;
                pdfViewer.innerHTML = '';
                pdfViewer.appendChild(canvas);

            } else {
                pdfViewer.innerHTML = '<div style="text-align:center;padding:50px;"><h3>Sertifika bulunamadı</h3><p>Sertifika dosyası henüz oluşturulmamış.</p></div>';
            }

        } catch (error) {
            console.error('PDF yükleme hatası:', error);
            pdfViewer.innerHTML = '<div style="text-align:center;padding:50px;"><h3>Hata</h3><p>Sertifika yüklenirken bir hata oluştu.</p></div>';
        }
    }

    // Sertifika indirme fonksiyonu
    function sertifikaIndir() {
        fetch('/islemler/seviye_sinav_kullanici_islemleri.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    islem: 'sertifika_goster',
                    sinav_id: window.currentSinavId || 0,
                    csrf_token: '<?= $_SESSION['csrf_token'] ?? '' ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.sertifika_dosya_yolu) {
                    window.open('/sertifika-indir?dosya=' + encodeURIComponent(data.sertifika_dosya_yolu), '_blank');
                } else {
                    alert('Sertifika dosyası bulunamadı.');
                }
            })
            .catch(error => {
                alert('Sertifika bilgileri alınırken bir hata oluştu.');
            });
    }

    // Aktif sınavlar için yanıp sönen efekt
    document.addEventListener('DOMContentLoaded', function() {
        setInterval(function() {
            const activeExams = document.querySelectorAll('.active-exam');
            activeExams.forEach(function(exam) {
                exam.classList.toggle('pulse');
            });
        }, 2000);

        // Sekme değiştirme işlemi
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('exam-tab-link')) {
                e.preventDefault();

                const allTabs = e.target.closest('.exam-tabs').querySelectorAll('.exam-tab-link');
                allTabs.forEach(tab => {
                    tab.parentElement.classList.remove('uk-active');
                });

                e.target.parentElement.classList.add('uk-active');

                const tabIndex = Array.from(allTabs).indexOf(e.target);
                const switcher = e.target.closest('.exam-card').querySelector('.uk-switcher');
                const contents = switcher.querySelectorAll('.exam-tab-content');

                contents.forEach((content, index) => {
                    if (index === tabIndex) {
                        content.style.display = 'block';
                        content.classList.add('uk-active');
                    } else {
                        content.style.display = 'none';
                        content.classList.remove('uk-active');
                    }
                });
            }
        });
    });
</script>

<?php foot(); ?>