/* Modern Dictation Styling */

/* Sweet<PERSON>lert Z-Index Fix */
.swal2-container {
    z-index: 99999 !important;
}

.swal2-popup {
    z-index: 99999 !important;
}

.swal2-backdrop-show {
    z-index: 99998 !important;
}

/* <PERSON><PERSON><PERSON><PERSON> özelleştirmeleri */
.swal2-popup.swal2-modal {
    border-radius: 16px !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4) !important;
}

.swal2-title {
    font-weight: 700 !important;
    color: #1e293b !important;
}

.swal2-content {
    color: #374151 !important;
    font-size: 1rem !important;
}

.swal2-confirm {
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    padding: 10px 24px !important;
}

.swal2-confirm:hover {
    background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%) !important;
    transform: translateY(-1px) !important;
}

.swal2-high-z-index {
    z-index: 99999 !important;
}

/* Modal Backdrop Fix - Koyu Tema */
.uk-modal {
    background: rgba(0, 0, 0, 0.85) !important;
    backdrop-filter: blur(20px) !important;
    z-index: 9999 !important;
}

/* Modal Dialog Styling - Koyu Tema */
.uk-modal-dialog {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%) !important;
    border-radius: 16px !important;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.6),
        0 10px 30px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(148, 163, 184, 0.2) !important;
    overflow: hidden !important;
    max-width: 1200px !important;
    width: 90% !important;
    color: #f1f5f9 !important;
}

/* Modal Header - Koyu Tema */
.uk-modal-header {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%) !important;
    color: #f8fafc !important;
    border-bottom: 1px solid rgba(148, 163, 184, 0.2) !important;
    padding: 20px 25px !important;
}

.uk-modal-title {
    color: #f8fafc !important;
    font-weight: 700 !important;
    font-size: 1.4rem !important;
    margin: 0 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.uk-modal-close-default,
.result-modal-close {
    background: rgba(239, 68, 68, 0.15) !important;
    border: 1px solid rgba(239, 68, 68, 0.3) !important;
    color: #fca5a5 !important;
    border-radius: 8px !important;
    width: 32px !important;
    height: 32px !important;
    transition: all 0.3s ease !important;
    position: absolute !important;
    top: 15px !important;
    right: 15px !important;
    z-index: 10 !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    outline: none !important;
}

.uk-modal-close-default::before,
.result-modal-close::before {
    content: "✕" !important;
    font-size: 18px !important;
    font-weight: 900 !important;
    line-height: 1 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.uk-modal-close-default:hover,
.result-modal-close:hover {
    background: rgba(239, 68, 68, 0.35) !important;
    color: #ffffff !important;
    transform: scale(1.1) !important;
    border-color: rgba(239, 68, 68, 0.5) !important;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4) !important;
}

.uk-modal-close-default:active,
.result-modal-close:active {
    transform: scale(0.95) !important;
    background: rgba(239, 68, 68, 0.5) !important;
}

/* Modal Body - Koyu Tema */
.uk-modal-body {
    padding: 0 !important;
    background: transparent !important;
    color: #e2e8f0 !important;
}

/* Dictation Form Container - Koyu Tema */
.dictation-form-container {
    padding: 25px !important;
    max-height: 70vh !important;
    overflow-y: auto !important;
}

/* Sentence Group Styling - Koyu Tema */
.sentence-group {
    background: rgba(51, 65, 85, 0.3) !important;
    border-radius: 12px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    border: 1px solid rgba(148, 163, 184, 0.2) !important;
    transition: all 0.3s ease !important;
}

.sentence-group:hover {
    border-color: rgba(99, 102, 241, 0.5) !important;
    background: rgba(51, 65, 85, 0.5) !important;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2) !important;
}

.sentence-group:last-child {
    margin-bottom: 0 !important;
}

/* Sentence Header */
.sentence-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 15px !important;
}

.sentence-number {
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    color: #f1f5f9 !important;
    margin: 0 !important;
}

/* Listen Button - Koyu Tema */
.listen-btn {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 8px 16px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3) !important;
    cursor: pointer !important;
}

.listen-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4) !important;
    color: white !important;
}

.listen-btn:focus {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3) !important;
}

/* Dinlenen buton stilleri - Koyu Tema */
.listen-btn.listened {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    color: white !important;
    cursor: not-allowed !important;
    opacity: 0.8 !important;
}

.listen-btn.listened:hover {
    transform: none !important;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3) !important;
}

.listen-btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
}

/* Listen Button Tooltip */
.listen-btn-container {
    position: relative !important;
    display: inline-block !important;
}

.listen-tooltip {
    position: absolute !important;
    bottom: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%) !important;
    color: #f9fafb !important;
    padding: 8px 12px !important;
    border-radius: 8px !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 1000 !important;
    margin-bottom: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(148, 163, 184, 0.2) !important;
}

.listen-tooltip::after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    border: 6px solid transparent !important;
    border-top-color: #374151 !important;
}

.listen-btn-container:hover .listen-tooltip {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Sentence Input Wrapper */
.sentence-input-wrapper {
    position: relative !important;
}

/* Sentence Textarea - Koyu Tema */
.sentence-textarea {
    width: 100% !important;
    background: white !important;
    border: 1px solid rgba(148, 163, 184, 0.3) !important;
    border-radius: 8px !important;
    padding: 15px !important;
    font-size: 1rem !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    line-height: 1.5 !important;
    color: rgb(16 24 43) !important;
    transition: all 0.3s ease !important;
    resize: vertical !important;
    min-height: 60px !important;
    box-sizing: border-box !important;
}


.sentence-textarea:focus {
    outline: none !important;
    border-color: #6366f1 !important;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2) !important;
}

.sentence-textarea::placeholder {
    color: #94a3b8 !important;
    font-style: italic !important;
}

/* Submit Section - Koyu Tema */
.submit-section {
    text-align: center !important;
    padding-top: 20px !important;
    border-top: 1px solid rgba(148, 163, 184, 0.2) !important;
    margin-top: 30px !important;
}

/* FIX IT Button - Koyu Tema */
.fix-it-btn {
    background: linear-gradient(135deg, #81ff35 0%, #67f71e 100%) !important;
    color: black !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 14px 28px !important;
    font-weight: 700 !important;
    font-size: 1rem !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    box-shadow: 0 4px 20px rgba(249, 115, 22, 0.4) !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 10px !important;
    cursor: pointer !important;
}

.fix-it-btn:hover {
    background: linear-gradient(135deg, #5dd413 0%, #48c708 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 30px rgba(249, 115, 22, 0.6) !important;
    color: black !important;
}

/* Submit Button - Eski Buton İçin Geri Uyumluluk */
.submit-dictation-btn {
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 14px 28px !important;
    font-weight: 700 !important;
    text-align: center !important;
    font-size: 1rem !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    box-shadow: 0 4px 20px rgba(249, 115, 22, 0.4) !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.submit-dictation-btn:hover {
    background: linear-gradient(135deg, #7bda0e 0%, #34b90c 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 30px rgba(249, 115, 22, 0.6) !important;
    color: black !important;
}

.submit-dictation-btn:disabled {
    opacity: 0.7 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

.submit-dictation-btn:focus {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.3) !important;
}

/* Custom Scrollbar for Modal */
.dictation-form-container::-webkit-scrollbar {
    width: 8px !important;
}

.dictation-form-container::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 10px !important;
}

.dictation-form-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 10px !important;
}

.dictation-form-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
}

/* Card Styling */
.dictation-card {
    transition: all 0.3s ease !important;
    border-radius: 16px !important;
    border: none !important;
    background: white !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
    overflow: hidden !important;
}

.dictation-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
    color: white !important;
    border-radius: 20px !important;
    padding: 25px !important;
    text-align: center !important;
    border: none !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

.stats-number {
    font-size: 2.8rem !important;
    font-weight: 700 !important;
    margin-bottom: 8px !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

.stats-label {
    font-size: 1rem !important;
    opacity: 0.95 !important;
    font-weight: 500 !important;
}

/* Alert Styling */
.alert-card {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
    color: white !important;
    border-radius: 16px !important;
    border: none !important;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3) !important;
}

/* Dictation Items */
.dictation-item {
    background: white !important;
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05) !important;
    transition: all 0.3s ease !important;
    margin-bottom: 15px !important;
    overflow: hidden !important;
    position: relative !important;
}

.dictation-item::before {
    content: '' !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    height: 100% !important;
    width: 4px !important;
    background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
}

.dictation-item:hover {
    transform: translateX(5px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.12) !important;
}

/* Status Badges */
.dictation-status {
    padding: 6px 16px !important;
    border-radius: 25px !important;
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.status-pending {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
    color: #8b4513 !important;
}

.status-completed {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
    color: #2d5016 !important;
}

/* Buttons */
.btn-solve {
    background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 25px !important;
    padding: 10px 25px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.btn-solve:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
    color: white !important;
}

/* Form Styling */
.sentence-input {
    border: 2px solid #e1e5e9 !important;
    border-radius: 12px !important;
    padding: 15px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: #f8f9fa !important;
    width: 100% !important;
    margin-bottom: 15px !important;
}

.sentence-input:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    background: white !important;
    outline: none !important;
}

/* Progress Ring */
.progress-ring {
    transform: rotate(-90deg) !important;
}

.progress-ring-circle {
    transition: stroke-dasharray 0.35s !important;
    transform-origin: 50% 50% !important;
}

/* Instructions Styling */
.dictation-instructions {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-radius: 12px !important;
    padding: 20px !important;
    margin-bottom: 25px !important;
    border-left: 4px solid #667eea !important;
}

.dictation-instructions h4 {
    color: #495057 !important;
    margin-bottom: 15px !important;
    font-weight: 600 !important;
}

.dictation-instructions ul {
    margin: 0 !important;
    padding-left: 20px !important;
}

.dictation-instructions li {
    margin-bottom: 8px !important;
    color: #6c757d !important;
    line-height: 1.5 !important;
}

/* Submit Button */
.submit-dictation-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 25px !important;
    padding: 15px 40px !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
    margin-top: 20px !important;
}

.submit-dictation-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4) !important;
    color: white !important;
}

/* Responsive */
@media (max-width: 768px) {
    .uk-modal-dialog {
        margin: 10px !important;
        max-width: calc(100% - 20px) !important;
        width: calc(100% - 20px) !important;
    }
    
    .uk-modal-header {
        padding: 15px 20px !important;
    }
    
    .dictation-form-container {
        padding: 20px !important;
        max-height: 75vh !important;
    }
    
    .sentence-group {
        padding: 15px !important;
        margin-bottom: 15px !important;
    }
    
    .sentence-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
    }
    
    .sentence-number {
        font-size: 1rem !important;
    }
    
    .listen-btn {
        padding: 6px 12px !important;
        font-size: 0.85rem !important;
        align-self: flex-end !important;
    }

    .listen-tooltip {
        font-size: 0.7rem !important;
        padding: 6px 10px !important;
        max-width: 150px !important;
        white-space: normal !important;
        text-align: center !important;
    }
    
    .sentence-textarea {
        padding: 12px 15px !important;
        font-size: 0.95rem !important;
    }
    
    .submit-dictation-btn {
        padding: 14px 30px !important;
        font-size: 1rem !important;
        min-width: 150px !important;
    }
    
    .stats-number {
        font-size: 2.2rem !important;
    }
    
    .dictation-item:hover {
        transform: none !important;
    }
}

/* Animation for modal appearance */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.uk-modal-dialog {
    animation: modalSlideIn 0.3s ease-out !important;
}

/* Dictation Result Styling */
.dictation-result-container {
    background: white !important;
    color: #333 !important;
}


.result-score {
    font-size: 3rem !important;
    font-weight: 800 !important;
    margin: 10px 0 !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
}

.result-message {
    font-size: 1.2rem !important;
    opacity: 0.95 !important;
    margin-bottom: 0 !important;
}

.result-stats {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
    gap: 15px !important;
    padding: 25px !important;
    background: #f8f9fa !important;
}

.stat-item {
    background: white !important;
    padding: 20px !important;
    border-radius: 12px !important;
    text-align: center !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05) !important;
    transition: all 0.3s ease !important;
}

.stat-item:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.stat-value {
    font-size: 1.8rem !important;
    font-weight: 700 !important;
    color: #667eea !important;
    margin-bottom: 5px !important;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 500 !important;
}

.result-analysis {
    padding: 25px !important;
}

.analysis-title {
    font-size: 1.3rem !important;
    font-weight: 700 !important;
    color: #495057 !important;
    margin-bottom: 20px !important;
    text-align: center !important;
}

.sentence-analysis {
    margin-bottom: 25px !important;
    background: white !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05) !important;
    transition: all 0.3s ease !important;
}

.sentence-analysis:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}


.sentence-number {
    background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
    color: white !important;
    border-radius: 50% !important;
    width: 30px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 700 !important;
    font-size: 0.9rem !important;
}

.sentence-status {
    padding: 4px 12px !important;
    border-radius: 20px !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.status-correct {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
    color: #155724 !important;
}

.status-partial {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    color: #856404 !important;
}

.status-incorrect {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
    color: #721c24 !important;
}

.sentence-content {
    padding: 20px !important;
}

.sentence-row {
    margin-bottom: 15px !important;
}

.sentence-label {
    font-weight: 600 !important;
    color: #495057 !important;
    margin-bottom: 8px !important;
    font-size: 0.9rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.sentence-text {
    background: #f8f9fa !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    line-height: 1.5 !important;
    border-left: 4px solid #dee2e6 !important;
}

.correct-text {
    border-left-color: #28a745 !important;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
    color: #155724 !important;
}

.user-text {
    border-left-color: #dc3545 !important;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
    color: #721c24 !important;
}

.word-comparison {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 4px !important;
    margin-top: 10px !important;
}

.word {
    padding: 4px 8px !important;
    border-radius: 6px !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.word-correct {
    color: #155724 !important;
}

.word-incorrect {
    background: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
    animation: shake 0.5s ease-in-out !important;
}


.word-extra {
    color: #ff74fa !important; /* Turuncu renk */
    font-weight: 500;
    text-decoration: line-through;
}

.word-missing {
    color: #6b7280 !important; /* Gri renk */
    font-weight: 500;
    font-style: italic;
}

.word-wrong {
    color: #ef4444 !important; /* Kırmızı renk */
    /* text-decoration: line-through; */
    font-weight: 500;
}

.word-correct {
    color: #22c55e !important; /* Yeşil renk */
    font-weight: 500;
}

/* Animations */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes countUp {
    from { opacity: 0; transform: scale(0.5); }
    to { opacity: 1; transform: scale(1); }
}

.sentence-analysis {
    animation: fadeInUp 0.5s ease-out !important;
}

.sentence-analysis:nth-child(1) { animation-delay: 0.1s !important; }
.sentence-analysis:nth-child(2) { animation-delay: 0.2s !important; }
.sentence-analysis:nth-child(3) { animation-delay: 0.3s !important; }
.sentence-analysis:nth-child(4) { animation-delay: 0.4s !important; }
.sentence-analysis:nth-child(5) { animation-delay: 0.5s !important; }

.result-score {
    animation: countUp 0.8s ease-out !important;
}

.stat-value {
    animation: countUp 0.6s ease-out !important;
}

.result-actions {
    padding: 25px !important;
    background: #f8f9fa !important;
    border-radius: 0 0 20px 20px !important;
    text-align: center !important;
}

.action-button {
    background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 10px !important;
    padding: 12px 30px !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
    margin: 0 10px !important;
}

.action-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
    color: white !important;
}

.action-button.secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
}

.action-button.secondary:hover {
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4) !important;
}

/* Custom scrollbar for modal */
.uk-modal-body::-webkit-scrollbar {
    width: 8px;
}

.uk-modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.uk-modal-body::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

.uk-modal-body::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Responsive */
@media (max-width: 768px) {
    .result-stats {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 10px !important;
        padding: 20px !important;
    }

    .result-score {
        font-size: 2.5rem !important;
    }

    .sentence-header {
        flex-direction: column !important;
        gap: 10px !important;
        text-align: center !important;
    }

    .word-comparison {
        justify-content: center !important;
    }

    .action-button {
        display: block !important;
        width: 100% !important;
        margin: 5px 0 !important;
    }
}

/* Sentence Comparison Styles */
.sentences-grid {
    display: grid;
    gap: 20px;
    grid-template-columns: 1fr;
    max-width: 100%;
}

@media (min-width: 968px) {
    .sentences-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
    }
}

@media (min-width: 1400px) {
    .sentences-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 14px;
    }
    
    .sentence-comparison {
        max-width: 280px;
        font-size: 0.85rem;
    }
    
    .sentence-text-content {
        font-size: 0.8rem;
        padding: 6px;
    }
}

.sentence-comparison {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    height: fit-content;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    max-width: 100%;
    overflow: hidden;
}

.sentence-comparison:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.sentence-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f3f4f6;
}

.sentence-number {
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

.error-count {
    font-size: 0.8rem;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
    background: #fef3c7;
    color: #92400e;
}

.error-count.perfect {
    background: #d1fae5;
    color: #065f46;
}

.sentence-texts {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.sentence-text-section {
    flex: 1;
}

.sentence-text-label {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.correct-label {
    color: #059669;
}

.user-label {
    color: #0284c7;
}

.sentence-text-content {
    background: #f8fafc;
    border-radius: 8px;
    padding: 8px;
    min-height: 40px;
    line-height: 1.4;
    font-size: 0.9rem;
    border: 1px solid #e5e7eb;
    align-items: center;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    max-width: 100%;
}

.correct-sentence {
    background: #f0fdf4;
    border-color: #bbf7d0;
}

.user-sentence {
    background: #eff6ff;
    border-color: #bfdbfe;
}

/* Word highlighting styles */
.word-highlight {
    display: inline;
    margin: 0 1px;
    padding: 1px 2px;
    border-radius: 3px;
    word-break: break-word;
}





    /* My Dictations Sayfası Özel Tasarımı */

    /* Başlık */
    .fs20.fw3.border-bottom-green {
        color: #333 !important;
        font-size: 1.8rem !important;
        font-weight: 700 !important;
        margin-bottom: 25px !important;
        padding-bottom: 15px !important;
        border-bottom: 3px solid #667eea !important;
    }

    /* İstatistik kartları */
    .stat-card {
        background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
        color: white !important;
        border-radius: 16px !important;
        padding: 25px !important;
        text-align: center !important;
        border: none !important;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
        transition: all 0.3s ease !important;
        margin-bottom: 20px !important;
    }

    .stat-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4) !important;
    }

    .stat-number {
        font-size: 2.5rem !important;
        font-weight: 800 !important;
        color: white !important;
        line-height: 1 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    }

    .stat-label {
        font-size: 1rem;
        margin-top: 8px !important;
        font-weight: 500 !important;
    }

    /* Dictation bölümleri */
    .dictation-section {
        background: white !important;
        border-radius: 16px !important;
        padding: 25px !important;
        margin-bottom: 25px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
        border: none !important;
    }

    .dictation-section h4 {
        color: #333 !important;
        font-weight: 700 !important;
        margin-bottom: 20px !important;
        font-size: 1.3rem !important;
    }

    /* Dictation listesi */
    .dictation-list {
        max-height: 310px !important;
        overflow-y: auto !important;
        padding-right: 10px !important;
    }

    .dictation-item {
        background: #f8f9fa !important;
        border-radius: 12px !important;
        padding: 20px !important;
        margin-bottom: 15px !important;
        border: 2px solid transparent !important;
        transition: all 0.3s ease !important;
        position: relative !important;
    }

    .dictation-item::before {
        content: '' !important;
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        height: 100% !important;
        width: 4px !important;
        background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
        border-radius: 12px 0 0 12px !important;
    }

    .dictation-item:hover {
        background: white !important;
        border-color: #667eea !important;
        transform: translateX(5px) !important;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15) !important;
    }

    .dictation-item h5 {
        color: #333 !important;
        font-weight: 700 !important;
        margin-bottom: 10px !important;
        font-size: 1.1rem !important;
    }

    .dictation-item p {
        color: #6c757d !important;
        margin-bottom: 8px !important;
        font-size: 0.9rem !important;
    }

    .dictation-item .uk-text-small {
        color: #6c757d !important;
        font-size: 0.85rem !important;
    }

    /* Butonlar */
    .pink-btn,
    .btn-solve {
        background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 10px !important;
        padding: 12px 25px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
        text-decoration: none !important;
    }

    .pink-btn:hover,
    .btn-solve:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
        color: white !important;
    }

    /* Modern Dictation Notification */
    .dictation-notification-container {
        position: relative !important;
        overflow: hidden !important;
    }

    .notification-wrapper {
        background: linear-gradient(135deg, #ff9800 0%, #ff6b35 50%, #ff7600 100%) !important;
        border-radius: 16px !important;
        padding: 24px !important;
        box-shadow: 0 10px 30px rgba(255, 154, 86, 0.4) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        position: relative !important;
        overflow: hidden !important;
        animation: slideInFromTop 0.6s ease-out !important;
    }

    .notification-wrapper::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%) !important;
        pointer-events: none !important;
    }

    .notification-wrapper {
        display: flex !important;
        align-items: flex-start !important;
        gap: 20px !important;
    }

    .notification-icon-area {
        position: relative !important;
        flex-shrink: 0 !important;
    }

    .notification-icon-circle {
        width: 36px !important;
        height: 36px !important;
        background: rgba(255, 255, 255, 0.2) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        backdrop-filter: blur(10px) !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        position: relative !important;
        z-index: 2 !important;
    }

    .notification-pulse {
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        width: 36px !important;
        height: 36px !important;
        border-radius: 50% !important;
        background: rgba(255, 255, 255, 0.4) !important;
        animation: pulse 2s infinite !important;
        z-index: 1 !important;
    }

    .notification-content-area {
        flex: 1 !important;
        color: white !important;
    }

    .notification-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: flex-start !important;
        margin-bottom: 12px !important;
    }

    .notification-title {
        font-size: 1.25rem !important;
        font-weight: 700 !important;
        color: white !important;
        margin: 0 !important;
        line-height: 1.3 !important;
    }

    .notification-close-btn {
        background: rgba(255, 255, 255, 0.2) !important;
        border: none !important;
        border-radius: 8px !important;
        width: 32px !important;
        height: 32px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        backdrop-filter: blur(10px) !important;
    }

    .notification-close-btn:hover {
        background: rgba(255, 255, 255, 0.3) !important;
        transform: scale(1.1) !important;
    }

    .notification-message {
        margin-bottom: 20px !important;
    }

    .notification-message p {
        font-size: 1rem !important;
        color: rgba(255, 255, 255, 0.9) !important;
        margin: 0 !important;
        line-height: 1.5 !important;
    }

    .notification-actions {
        display: flex !important;
        gap: 12px !important;
        flex-wrap: wrap !important;
    }

    .notification-primary-btn {
        background: #6cf32a;
        color: black !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 12px 24px !important;
        font-weight: 600 !important;
        font-size: 0.95rem !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        box-shadow: 0 4px 20px rgba(0, 255, 136, 0.4) !important;
        position: relative !important;
        overflow: hidden !important;
    }


    .notification-primary-btn:hover {
        background: #50ed03;
        transform: translateY(-3px) !important;
        box-shadow: 0 8px 30px rgba(0, 255, 136, 0.6) !important;
    }


    .notification-secondary-btn {
        background: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        border-radius: 12px !important;
        padding: 10px 20px !important;
        font-weight: 500 !important;
        font-size: 0.95rem !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        backdrop-filter: blur(10px) !important;
    }

    .notification-secondary-btn:hover {
        background: rgba(255, 255, 255, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.5) !important;
        transform: translateY(-1px) !important;
    }

    .btn-icon {
        font-size: 1.1rem !important;
    }

    .btn-text {
        font-weight: inherit !important;
    }

    /* Animasyonlar */
    @keyframes slideInFromTop {
        0% {
            opacity: 0 !important;
            transform: translateY(-30px) !important;
        }

        100% {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }
    }

    @keyframes pulse {
        0% {
            transform: translate(-50%, -50%) scale(1) !important;
            opacity: 0.7 !important;
        }

        50% {
            transform: translate(-50%, -50%) scale(1.2) !important;
            opacity: 0.3 !important;
        }

        100% {
            transform: translate(-50%, -50%) scale(1.4) !important;
            opacity: 0 !important;
        }
    }

    @keyframes neonGlow {

        0%,
        100% {
            background-size: 200% 200% !important;
            background-position: 0% 50% !important;
        }

        50% {
            background-size: 200% 200% !important;
            background-position: 100% 50% !important;
        }
    }

    /* Accordion */
    .uk-accordion-title {
        background: #f8f9fa !important;
        color: #333 !important;
        border-radius: 8px !important;
        margin-bottom: 5px !important;
        border: 1px solid #dee2e6 !important;
        font-weight: 600 !important;
    }

    .uk-accordion-title:hover {
        background: #e9ecef !important;
    }

    .uk-accordion-content {
        background: white !important;
        color: #333 !important;
        border-radius: 0 0 8px 8px !important;
        border: 1px solid #dee2e6 !important;
        border-top: none !important;
    }

    /* Filtre */
    .uk-select {
        background: white !important;
        color: #333 !important;
        border: 2px solid #e1e5e9 !important;
        border-radius: 8px !important;
        padding: 8px 12px !important;
    }

    .uk-select:focus {
        border-color: #667eea !important;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    }

    /* Modal özelleştirmeleri */
    .uk-modal-dialog {
        background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    .uk-modal-header {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    .uk-modal-footer {
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    .uk-modal-close-default {
        background-color: black !important;
        border-radius: 50% !important;
        height: 30px !important;
        width: 30px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        font-size: 1.5rem !important;
        position: absolute !important;
        top: 10px !important;
        right: 10px !important;
    }

    /* Scrollbar */
    .dictation-list::-webkit-scrollbar {
        width: 6px !important;
    }

    .dictation-list::-webkit-scrollbar-track {
        background: #f1f1f1 !important;
        border-radius: 10px !important;
    }

    .dictation-list::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
        border-radius: 10px !important;
    }

    .dictation-list::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
    }

    /* Boş durum mesajları */
    .uk-text-center.uk-text-muted {
        color: #6c757d !important;
        font-style: italic !important;
        padding: 40px 20px !important;
    }

    /* Modern Score Badge */
    .score-badge {
        display: inline-flex !important;
        align-items: center !important;
        gap: 8px !important;
        padding: 8px 16px !important;
        border-radius: 20px !important;
        font-weight: 700 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s ease !important;
    }

    .score-badge:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }

    .score-excellent {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
        color: white !important;
    }

    .score-good {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%) !important;
        color: white !important;
    }

    .score-average {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
        color: #333 !important;
    }

    .score-poor {
        background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
        color: white !important;
    }

    .score-icon {
        font-size: 1.2rem !important;
    }

    .score-text {
        display: flex !important;
        align-items: baseline !important;
        gap: 2px !important;
    }

    .score-number {
        font-size: 1.1rem !important;
        font-weight: 800 !important;
    }

    .score-percent {
        font-size: 0.9rem !important;
        opacity: 0.9 !important;
    }

    /* Modern Result Button */
    .modern-result-btn {
        background: linear-gradient(135deg, #f887e6 0%, #a056ca 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 10px 16px !important;
        font-weight: 600 !important;
        font-size: 0.9rem !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3) !important;
        display: inline-flex !important;
        align-items: center !important;
        gap: 6px !important;
        cursor: pointer !important;
    }

    .modern-result-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
        color: white !important;
    }

    .btn-icon {
        font-size: 1rem !important;
    }

    .btn-text {
        font-weight: 600 !important;
    }

    /* Accordion İyileştirmeleri */
    .uk-accordion-title {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        color: #333 !important;
        border-radius: 12px !important;
        margin-bottom: 8px !important;
        border: 2px solid #dee2e6 !important;
        font-weight: 700 !important;
        padding: 15px 20px !important;
        transition: all 0.3s ease !important;
        font-size: 16px;
    }

    .uk-accordion-title:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
        border-color: #667eea !important;
        transform: translateX(3px) !important;
    }

    .uk-accordion-content {
        background: white !important;
        color: #333 !important;
        border-radius: 0 0 12px 12px !important;
        border: 2px solid #dee2e6 !important;
        border-top: none !important;
        padding: 20px !important;
        margin-bottom: 15px !important;
    }

    .uk-accordion-content strong {
        font-weight: 700 !important;
    }


    /* Badge İyileştirmeleri */
    .dictation-section .uk-badge {
        border-radius: 15px !important;
        padding: 20px 12px !important;
        font-weight: 600 !important;
        font-size: 0.8rem !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    .uk-badge-primary {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%) !important;
    }

    .uk-badge-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    }

    .uk-badge-warning {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
        color: #333 !important;
    }

    .uk-badge-danger {
        background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
    }

    /* İlerleme Modal Stilleri */
    .progress-modal-content {
        padding: 10px !important;
    }

    .stats-grid-modal {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
        gap: 15px !important;
        margin-bottom: 25px !important;
    }

    .stat-card-modal {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        border-radius: 12px !important;
        padding: 20px !important;
        text-align: center !important;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
        transition: all 0.3s ease !important;
    }

    .stat-card-modal.excellent {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
    }

    .stat-card-modal.good {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%) !important;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3) !important;
    }

    .stat-card-modal.warning {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3) !important;
        color: #333 !important;
    }

    .stat-card-modal .stat-number {
        font-size: 2rem !important;
        font-weight: 800 !important;
        margin-bottom: 5px !important;
    }

    .stat-card-modal .stat-label {
        font-size: 0.9rem;
        opacity: 0.9 !important;
        font-weight: 500 !important;
    }

    .progress-section-modal {
        background: #f8f9fa !important;
        border-radius: 12px !important;
        padding: 20px !important;
        margin-bottom: 20px !important;
    }

    .section-title-modal {
        color: #333 !important;
        font-weight: 700 !important;
        font-size: 1.2rem !important;
        margin-bottom: 15px !important;
        padding-bottom: 8px !important;
        border-bottom: 2px solid #667eea !important;
    }

    .performance-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)) !important;
        gap: 10px !important;
    }

    .performance-item {
        background: white !important;
        border-radius: 8px !important;
        padding: 15px !important;
        text-align: center !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .performance-item.excellent {
        border-left: 4px solid #28a745 !important;
    }

    .performance-item.good {
        border-left: 4px solid #007bff !important;
    }

    .performance-item.average {
        border-left: 4px solid #ffc107 !important;
    }

    .performance-item.poor {
        border-left: 4px solid #dc3545 !important;
    }

    .performance-number {
        font-size: 1.5rem !important;
        font-weight: 700 !important;
        color: #333 !important;
    }

    .performance-label {
        font-size: 0.8rem !important;
        color: #6c757d !important;
        margin-top: 5px !important;
    }

    .recent-results-list {
        max-height: 300px !important;
        overflow-y: auto !important;
    }

    .recent-item-modal {
        background: white !important;
        border-radius: 8px !important;
        padding: 15px !important;
        margin-bottom: 10px !important;
        border-left: 4px solid #667eea !important;
        transition: all 0.3s ease !important;
    }

    .recent-item-modal:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        transform: translateY(3px) !important;
    }

    .recent-item-modal .recent-score {
        font-weight: 700 !important;
        font-size: 1.1rem !important;
    }

    .recent-item-modal .recent-score.uk-badge-success {
        color: #fff !important;
        padding: 4px 8px;
        border-radius: 10px;
    }

    .recent-item-modal .recent-score.uk-badge-primary {
        color: #fff !important;
        padding: 4px 8px;
        border-radius: 10px;
    }

    .recent-item-modal .recent-score.uk-badge-warning {
        color: #fff !important;
        padding: 4px 8px;
        border-radius: 10px;
    }

    .recent-item-modal .recent-score.uk-badge-danger {
        color: #fff !important;
        padding: 4px 8px;
        border-radius: 10px;
    }

    /* Sınıf İstatistikleri Stilleri */
    .class-stats-container {
        padding: 15px !important;
    }

    .class-stats-section {
        margin-bottom: 20px !important;
    }

    .section-title {
        color: #333 !important;
        font-weight: 700 !important;
        font-size: 1rem !important;
        margin-bottom: 15px !important;
        padding-bottom: 8px !important;
        border-bottom: 2px solid #667eea !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
    }

    .section-title span {
        color: #667eea !important;
    }

    .last-dictation-info {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-radius: 8px !important;
        padding: 12px !important;
        border-left: 4px solid #667eea !important;
    }

    .stat-mini-card {
        background: white !important;
        border-radius: 8px !important;
        padding: 12px !important;
        text-align: center !important;
        border: 1px solid #e9ecef !important;
        transition: all 0.3s ease !important;
        width: 190% !important;
    }

    .stat-mini-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        transform: translateY(-2px) !important;
    }

    .stat-mini-number {
        font-size: 1.4rem !important;
        font-weight: 800 !important;
        line-height: 1 !important;
        margin-bottom: 4px !important;
    }

    .stat-mini-label {
        font-size: 0.75rem !important;
        color: #6c757d !important;
        font-weight: 500 !important;
    }

    .comparison-section,
    .overall-section {
        background: #f8f9fa !important;
        border-radius: 8px !important;
        padding: 15px !important;
        margin-bottom: 15px !important;
    }

    .comparison-row {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 8px 0 !important;
        border-bottom: 1px solid #e9ecef !important;
    }

    .comparison-row:last-child {
        border-bottom: none !important;
        padding-bottom: 0 !important;
    }

    .comparison-label {
        font-size: 0.85rem !important;
        color: #495057 !important;
        font-weight: 500 !important;
    }

    .comparison-value {
        font-size: 0.9rem !important;
        font-weight: 700 !important;
        color: #333 !important;
    }

    .overall-section h6 {
        color: #333 !important;
        font-weight: 700 !important;
        font-size: 1rem !important;
        margin-bottom: 12px !important;
        padding-bottom: 8px !important;
        border-bottom: 2px solid #667eea !important;
    }




    .result-modal-content {
        height: 100% !important;
        padding: 25px !important;
        color: #fff !important;
        overflow: visible !important;
        display: flex !important;
        flex-direction: column !important;
    }

    /* Yeni Sonuç Container */
    .new-result-container {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .result-header {
        padding: 0 0 20px 0 !important;
        border-bottom: 1px solid rgba(148, 163, 184, 0.2) !important;
        margin-bottom: 20px !important;
        text-align: center !important;
    }

    .dictation-title {
        font-size: 1.5rem !important;
        font-weight: 700 !important;
        background: #fff !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        margin: 0 !important;
        letter-spacing: -0.5px !important;
    }

    /* Sonuç Tablosu - Koyu Tema */
    .result-table-container {
        flex: 1 !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
        margin-bottom: 20px !important;
        border-radius: 12px !important;
        background: rgba(51, 65, 85, 0.3) !important;
        border: 1px solid rgba(148, 163, 184, 0.2) !important;
    }

    .result-table {
        width: 100% !important;
        border-collapse: collapse !important;
        table-layout: fixed !important;
    }

    .result-table thead {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%) !important;
        color: #f9fafb !important;
    }

    .result-table th {
        padding: 12px 8px !important;
        font-weight: 600 !important;
        font-size: 0.9rem !important;
        letter-spacing: 0.5px !important;
        border-bottom: 1px solid rgba(148, 163, 184, 0.2) !important;
    }

    .result-table th.col-number {
        width: 50px !important;
        text-align: center !important;
        min-width: 50px !important;
    }

    .result-table th.col-errors {
        width: 70px !important;
        text-align: center !important;
        min-width: 70px !important;
    }

    .result-table th.col-mistakes {
        width: 100px !important;
        text-align: center !important;
        min-width: 100px !important;
    }

    .result-table th.col-correct {
        width: 100px !important;
        text-align: center !important;
        min-width: 100px !important;
    }
    
    .result-table th.col-sentences {
        width: auto !important;
        min-width: 180px !important;
        max-width: none !important;
    }

    .result-table tbody tr {
        border-bottom: 1px solid rgba(148, 163, 184, 0.1) !important;
        transition: all 0.3s ease !important;
    }

    .result-table tbody tr:hover {
        background: rgba(51, 65, 85, 0.4) !important;
    }

    .result-table td {
        padding: 12px 8px !important;
        vertical-align: middle !important;
    }

    .result-table .sentence-number {
        text-align: center !important;
        font-weight: 700 !important;
        color: #fff !important;
        font-size: 1rem !important;
    }

    .sentence-text {
        color: #e2e8f0 !important;
        font-weight: 500 !important;
        line-height: 1.5 !important;
        font-size: 0.95rem !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        white-space: normal !important;
    }

    /* Renkli kelime stilleri - Result Modal'da */
    .result-table .word-correct {
        color: #10b981 !important;
        font-weight: 500 !important;
    }

    .result-table .word-wrong {
        color: #ef4444 !important;
        font-weight: 500 !important;
    }

    .result-table .word-missing {
        color: #f59e0b !important;
        font-weight: 600 !important;
    }

    .error-count {
        text-align: center !important;
        font-weight: 700 !important;
        color: #ef4444 !important;
        font-size: 1rem !important;
        padding: 4px 8px !important;
        background: rgba(239, 68, 68, 0.1) !important;
        border-radius: 6px !important;
        display: inline-block !important;
        min-width: 30px !important;
    }

    .result-table td.col-errors {
        text-align: center !important;
        vertical-align: middle !important;
        display: table-cell !important;
    }
    
    .result-table tbody td.col-errors {
        text-align: center !important;
        vertical-align: middle !important;
    }
    
    .result-table tbody td.col-errors .error-count {
        margin: 0 auto !important;
        display: block !important;
        width: fit-content !important;
        text-align: center !important;
        float: none !important;
        position: relative !important;
        left: auto !important;
        right: auto !important;
        transform: translateX(0) !important;
    }

    /* Büyüteç ve Tooltip */
    .mistake-viewer, .correct-viewer {
        text-align: center !important;
        position: relative !important;
    }

    .mistake-trigger, .correct-trigger {
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 36px !important;
        height: 36px !important;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
        color: white !important;
        border-radius: 50% !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3) !important;
    }

    .correct-trigger {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3) !important;
    }

    .mistake-trigger:hover, .correct-trigger:hover {
        transform: scale(1.1) !important;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4) !important;
    }

    .correct-trigger:hover {
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4) !important;
    }

    .mistake-tooltip, .correct-tooltip {
        position: fixed !important;
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%) !important;
        color: #f9fafb !important;
        padding: 15px !important;
        border-radius: 12px !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8) !important;
        z-index: 99999 !important;
        min-width: 250px !important;
        max-width: 350px !important;
        opacity: 0 !important;
        visibility: hidden !important;
        transition: all 0.3s ease !important;
        border: 1px solid rgba(148, 163, 184, 0.3) !important;
        pointer-events: none !important;
    }

    .correct-tooltip {
        background: linear-gradient(135deg, #065f46 0%, #047857 100%) !important;
        border: 1px solid rgba(16, 185, 129, 0.3) !important;
    }

    .mistake-tooltip.show, .correct-tooltip.show {
        opacity: 1 !important;
        visibility: visible !important;
    }

    .mistake-tooltip::before, .correct-tooltip::before {
        content: '' !important;
        position: absolute !important;
        top: 0% !important;
        left: 18.5% !important;
        border: 8px solid transparent !important;
        border-top-color: #ffffff !important;
    }

    .correct-tooltip::before {
        border-top-color: #065f46 !important;
    }
    
    .mistake-tooltip.tooltip-bottom::before, .correct-tooltip.tooltip-bottom::before {
        top: -16px !important;
        border-top-color: transparent !important;
        border-bottom-color: #374151 !important;
    }

    .correct-tooltip.tooltip-bottom::before {
        border-bottom-color: #065f46 !important;
    }

    /* Hata Detayları */
    .mistake-details {
        display: flex !important;
        flex-direction: column !important;
        gap: 8px !important;
    }

    .mistake-pair {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        padding: 6px 0 !important;
        border-bottom: 1px solid rgba(148, 163, 184, 0.1) !important;
    }

    .mistake-pair:last-child {
        border-bottom: none !important;
    }

    .wrong-word {
        background: #dc2626 !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 4px !important;
        font-weight: 600 !important;
        text-decoration: line-through !important;
    }

    .wrong-word.incomplete {
        background: #f59e0b !important;
        color: #1f2937 !important;
        text-decoration: none !important;
    }

    .wrong-word.extra {
        background: #ff74fa !important;
        color: white !important;
        text-decoration: none !important;
    }

    .correct-word {
        background: #10b981 !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 4px !important;
        font-weight: 600 !important;
    }

    .arrow {
        color: #f59e0b !important;
        font-weight: 700 !important;
    }



    .no-mistakes {
        color: #10b981 !important;
        font-weight: 600 !important;
        text-align: center !important;
        font-style: italic !important;
    }

    .color-legend {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 12px !important;
        padding: 16px !important;
        margin-bottom: 20px !important;
        backdrop-filter: blur(10px) !important;
    }

    .color-legend-title {
        font-size: 0.9rem !important;
        font-weight: 600 !important;
        color: #f1f5f9 !important;
        margin-bottom: 12px !important;
        text-align: center !important;
    }

    .color-legend-items {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 8px !important;
    }

    .color-legend-item {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        font-size: 0.8rem !important;
    }

    .color-sample {
        width: 16px !important;
        height: 16px !important;
        border-radius: 4px !important;
        flex-shrink: 0 !important;
    }

    .color-sample.correct {
        background: #10b981 !important;
    }

    .color-sample.wrong {
        background: #dc2626 !important;
    }

    .color-sample.missing {
        background: #f59e0b !important;
    }

    .color-sample.extra {
        background: #ff74fa !important;
    }

    .color-legend-text {
        color: #cbd5e1 !important;
        font-weight: 500 !important;
    }

    .correct-sentence-text {
        color: #f9fafb !important;
        font-weight: 500 !important;
        line-height: 1.6 !important;
        font-size: 0.95rem !important;
        text-align: center !important;
        padding: 8px !important;
    }

    /* Masaüstünde modal sınırlarını aşan tooltip'ler için özel stil */
    @media (min-width: 769px) {
        .result-modal-content {
            overflow: visible !important;
        }
        
        .result-table-container {
            overflow: visible !important;
        }
        
        .uk-modal-dialog {
            overflow: visible !important;
        }
        
        .uk-modal-body {
            overflow: visible !important;
        }
    }

    /* Sonuç Footer - Koyu Tema */
    .result-footer {
        padding: 20px 25px !important;
        border-top: 1px solid rgba(148, 163, 184, 0.2) !important;
        position: relative !important;
        min-height: 80px !important;
    }

    .total-mistakes {
        font-size: 1.3rem !important;
        color: #2d3748 !important;
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%) !important;

        padding: 15px 25px !important;
        border-radius: 16px !important;
        display: inline-block !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;
        flex-shrink: 0 !important;
        align-self: center !important;
    }

    .total-mistakes strong {
        background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        font-weight: 700 !important;
        font-size: 1.1em !important;
    }

    /* Koyu Header */
    .result-modal-header {
        text-align: center !important;
        margin-bottom: 32px !important;
        padding-bottom: 24px !important;
        border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
        position: relative !important;
    }

    .result-modal-title {
        font-size: 2.2rem !important;
        font-weight: 700 !important;
        color: #f1f5f9 !important;
        margin-bottom: 16px !important;
        letter-spacing: -0.025em !important;
    }

    .result-modal-score {
        font-size: 4rem !important;
        font-weight: 800 !important;
        margin: 20px 0 !important;
        letter-spacing: -0.05em !important;
        background: linear-gradient(135deg, var(--score-color), var(--score-color-light)) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
    }

    .score-excellent {
        --score-color: #059669;
        --score-color-light: #10b981;
    }

    .score-good {
        --score-color: #0284c7;
        --score-color-light: #0ea5e9;
    }

    .score-average {
        --score-color: #d97706;
        --score-color-light: #f59e0b;
    }

    .score-poor {
        --score-color: #dc2626;
        --score-color-light: #ef4444;
    }

    .score-fail {
        --score-color: #b91c1c;
        --score-color-light: #dc2626;
    }

    /* Koyu Stats Grid */
    .result-stats-grid {
        display: grid !important;
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 20px !important;
        margin-bottom: 32px !important;
    }

    .result-stat-card {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 16px !important;
        padding: 24px 16px !important;
        text-align: center !important;
        backdrop-filter: blur(10px) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2) !important;
    }

    .result-stat-card:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
        border-color: rgba(99, 102, 241, 0.5) !important;
    }

    .result-stat-number {
        font-size: 2rem !important;
        font-weight: 700 !important;
        margin-bottom: 8px !important;
        letter-spacing: -0.025em !important;
    }

    .result-stat-label {
        font-size: 0.875rem !important;
        color: #cbd5e1 !important;
        font-weight: 500 !important;
        letter-spacing: 0.025em !important;
        text-transform: uppercase !important;
    }

    /* Grid Cümle Container */
    .sentences-container {
        flex: 1 !important;
        overflow-y: auto !important;
        padding-right: 12px !important;
    }

    .sentences-grid {
        display: grid !important;
        grid-template-columns: 1fr !important;
        /* Mobil: 1 sütun */
        gap: 20px !important;
    }

    /* Tablet boyutu: 2 sütun */
    @media (min-width: 768px) {
        .sentences-grid {
            grid-template-columns: repeat(2, 1fr) !important;
        }
    }

    /* Tablet boyutu: 2 sütun */
    @media (min-width: 968px) {
        .sentences-grid {
            grid-template-columns: repeat(3, 1fr) !important;
        }
    }

    /* Büyük ekranlar: 4 sütun */
    @media (min-width: 1400px) {
        .sentences-grid {
            grid-template-columns: repeat(4, 1fr) !important;
        }
    }

    .sentence-comparison {
        background: rgba(255, 255, 255, 0.03) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 16px !important;
        padding: 20px !important;
        position: relative !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2) !important;
        display: flex !important;
        flex-direction: column !important;
        height: fit-content !important;
    }

    .sentence-comparison:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
        border-color: rgba(99, 102, 241, 0.3) !important;
    }

    .sentence-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 16px !important;
        padding-bottom: 12px !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    }


    .error-count {
        background: rgba(239, 68, 68, 0.2) !important;
        border: 1px solid rgba(239, 68, 68, 0.3) !important;
        color: #fca5a5 !important;
        border-radius: 8px !important;
        padding: 6px 10px !important;
        font-size: 0.8rem !important;
        font-weight: 600 !important;
    }

    .error-count.perfect {
        background: rgba(16, 185, 129, 0.2) !important;
        border: 1px solid rgba(16, 185, 129, 0.3) !important;
        color: #6ee7b7 !important;
    }

    .sentence-texts {
        display: flex !important;
        flex-direction: column !important;
        gap: 16px !important;
    }

    .sentence-text-section {
        background: rgba(255, 255, 255, 0.02) !important;
        border-radius: 12px !important;
        padding: 16px !important;
        border: 1px solid rgba(255, 255, 255, 0.05) !important;
        transition: all 0.3s ease !important;
    }

    .sentence-text-section:hover {
        background: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
    }

    .sentence-text-label {
        font-size: 0.8rem !important;
        font-weight: 600 !important;
        margin-bottom: 10px !important;
        letter-spacing: 0.025em !important;
        text-transform: uppercase !important;
    }

    .correct-label {
        color: #6ee7b7 !important;
    }

    .user-label {
        color: #93c5fd !important;
    }

    .sentence-text-content {
        font-size: 1rem !important;
        line-height: 1.6 !important;
        font-weight: 500 !important;
        color: #f1f5f9 !important;
    }

    /* Net Kelime Vurgulama */
    .word-highlight {
        display: inline-block !important;
        padding: 4px 8px !important;
        margin: 2px !important;
        font-weight: 600 !important;
        border-radius: 6px !important;
        transition: all 0.3s ease !important;
    }

    /* Doğru cevap - tamamen yeşil */
    .correct-sentence .word-highlight {
        background: rgba(34, 197, 94, 0.2) !important;
        color: #22c55e !important;
        border: 1px solid rgba(34, 197, 94, 0.3) !important;
    }

    .word-highlight:hover {
        transform: scale(1.05) !important;
        box-shadow: 0 0 10px rgba(255, 255, 255, 0.2) !important;
    }

    /* Koyu Tema Scrollbar */
    .sentences-container::-webkit-scrollbar {
        width: 6px !important;
    }

    .sentences-container::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05) !important;
        border-radius: 10px !important;
    }

    .sentences-container::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
        border-radius: 10px !important;
    }

    .sentences-container::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .account-form-container {
            margin: 10px !important;
            padding: 3px !important;
            border-radius: 15px !important;
        }

        .stat-number {
            font-size: 2rem !important;
        }

        .dictation-item:hover {
            transform: none !important;
        }

        .dictation-section {
            padding: 20px 10px !important;
        }

        .score-badge {
            padding: 6px 12px !important;
            gap: 6px !important;
        }

        .modern-result-btn {
            padding: 8px 12px !important;
            font-size: 0.85rem !important;
        }

        .uk-accordion-title:hover {
            transform: none !important;
        }

        .stats-grid-modal {
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 10px !important;
        }

        .performance-grid {
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 8px !important;
        }

        .recent-item-modal:hover {
            transform: none !important;
        }

        /* Sınıf İstatistikleri Responsive */
        .class-stats-container {
            padding: 12px !important;
        }

        .stat-mini-number {
            font-size: 1.2rem !important;
        }

        .comparison-row {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 4px !important;
        }

        .comparison-label {
            font-size: 0.8rem !important;
        }

        .comparison-value {
            font-size: 0.85rem !important;
        }


        .result-modal-content {
            padding: 20px !important;
            overflow-x: hidden !important;
        }

        .result-table-container {
            overflow-x: hidden !important;
            word-wrap: break-word !important;
        }

        .dictation-title {
            font-size: 1.3rem !important;
        }

        /* Mobilde tablo yapısını card layout'a çevir */
        .result-table,
        .result-table thead,
        .result-table tbody,
        .result-table th,
        .result-table td,
        .result-table tr {
            display: block !important;
        }

        .result-table thead tr {
            position: absolute !important;
            top: -9999px !important;
            left: -9999px !important;
        }

        .result-table tr {
            background: rgba(51, 65, 85, 0.3) !important;
            border-radius: 12px !important;
            margin-bottom: 15px !important;
            padding: 15px !important;
            border: 1px solid rgba(148, 163, 184, 0.2) !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
        }

        .result-table tr:hover {
            background: rgba(51, 65, 85, 0.5) !important;
        }

        .result-table td {
            border: none !important;
            padding: 8px 0 !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            min-height: 40px !important;
        }

        .result-table td:before {
            content: attr(data-label) !important;
            font-weight: 600 !important;
            color: #fff !important;
            font-size: 0.85rem !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
            min-width: 80px !important;
            flex-shrink: 0 !important;
            padding-right: 10px;
        }

        .result-table td.col-errors {
            text-align: right !important;
        }

        .result-table td.col-errors .error-count {
            font-size: 1.1rem !important;
            font-weight: 700 !important;
        }

        .result-table td.mistake-viewer, .result-table td.correct-viewer {
            justify-content: flex-end !important;
        }

        .result-table td[data-label="Sentence"] {
            display: block !important;
            padding: 12px 0 !important;
            word-wrap: break-word !important;
            word-break: break-word !important;
            white-space: normal !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
        }

        .result-table td[data-label="Sentence"]:before {
            display: block !important;
            margin-bottom: 8px !important;
        }

        .result-table td[data-label="Sentence"] .sentence-text {
            line-height: 1.5 !important;
            word-wrap: break-word !important;
            word-break: break-word !important;
            white-space: normal !important;
            overflow-wrap: break-word !important;
        }

        .sentence-number::before {
            width: 26px !important;
            height: 26px !important;
        }

        .mistake-trigger, .correct-trigger {
            width: 36px !important;
            height: 36px !important;
        }

        .mistake-tooltip, .correct-tooltip {
            min-width: 250px !important;
            max-width: 320px !important;
            font-size: 0.85rem !important;
            padding: 16px !important;
        }

        .sentence-text {
            font-size: 0.85rem !important;
            line-height: 1.4 !important;
        }

        .error-count {
            padding: 4px 8px !important;
            font-size: 0.9rem !important;
        }

        .total-mistakes {
            font-size: 1.1rem !important;
            padding: 12px 20px !important;
            align-self: auto !important;
        }

        .result-modal-content {
            padding: 24px !important;
        }

        .result-modal-title {
            font-size: 1.75rem !important;
        }

        .result-modal-score {
            font-size: 2.8rem !important;
        }

        .result-stats-grid {
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 16px !important;
        }

        .result-stat-card {
            padding: 20px 12px !important;
        }

        .result-stat-number {
            font-size: 1.6rem !important;
        }

        .result-stat-label {
            font-size: 0.8rem !important;
        }

        .sentences-grid {
            grid-template-columns: 1fr !important;
            gap: 16px !important;
        }

        .sentence-texts {
            gap: 12px !important;
        }

        .sentence-comparison {
            padding: 20px !important;
            margin-bottom: 20px !important;
        }

        .sentence-text-content {
            font-size: 1.05rem !important;
        }

        .word-highlight {
            padding: 3px 6px !important;
            margin: 1px !important;
            font-size: 0.9rem !important;
            word-wrap: break-word !important;
            word-break: break-word !important;
            display: inline-block !important;
        }

        /* Notification Responsive */
        .notification-wrapper {
            gap: 16px !important;
            padding: 20px !important;
        }

        .notification-icon-area {
            align-self: center !important;
        }

        .notification-header {
            text-align: left !important;
            gap: 12px !important;
        }

        .notification-title {
            font-size: 1.1rem !important;
        }

        .notification-actions {
            flex-direction: column !important;
            gap: 10px !important;
        }

        .notification-primary-btn,
        .notification-secondary-btn {
            width: 100% !important;
            justify-content: center !important;
            padding: 14px 20px !important;
        }
    }

    /* Gelişmiş Kelime Karşılaştırma Stilleri */
    .word-comparison-container {
        background: #f8f9fa !important;
        border-radius: 12px !important;
        padding: 20px !important;
        margin: 15px 0 !important;
        border: 1px solid #e9ecef !important;
    }

    .comparison-row {
        margin-bottom: 15px !important;
        display: flex !important;
        align-items: flex-start !important;
        gap: 15px !important;
    }

    .row-label {
        font-weight: 700 !important;
        color: #495057 !important;
        min-width: 100px !important;
        font-size: 0.9rem !important;
        padding-top: 8px !important;
    }

    .words-container {
        flex: 1 !important;
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 8px !important;
    }

    .word-item {
        display: inline-flex !important;
        align-items: center !important;
        gap: 4px !important;
        padding: 6px 12px !important;
        border-radius: 20px !important;
        font-size: 0.85rem !important;
        font-weight: 500 !important;
        position: relative !important;
        transition: all 0.3s ease !important;
        cursor: help !important;
    }

    .word-item:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }

    /* .word-text {
        font-family: 'Courier New', monospace !important;
    } */

    .word-status {
        font-size: 0.75rem !important;
        font-weight: 700 !important;
    }



    /* Karşılaştırma özeti */
    .comparison-summary {
        background: white !important;
        border-radius: 8px !important;
        padding: 15px !important;
        margin-top: 20px !important;
        border: 1px solid #dee2e6 !important;
    }

    .summary-title {
        font-weight: 700 !important;
        color: #495057 !important;
        margin-bottom: 12px !important;
        font-size: 1rem !important;
    }

    .summary-stats {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 10px !important;
        margin-bottom: 15px !important;
    }

    .stat-item {
        display: flex !important;
        align-items: center !important;
        gap: 6px !important;
        padding: 6px 12px !important;
        border-radius: 15px !important;
        font-size: 0.8rem !important;
        font-weight: 500 !important;
    }

    .stat-item.perfect {
        background: rgba(40, 167, 69, 0.1) !important;
        color: #28a745 !important;
        border: 1px solid rgba(40, 167, 69, 0.2) !important;
    }

    .stat-item.normalized {
        background: rgba(0, 123, 255, 0.1) !important;
        color: #007bff !important;
        border: 1px solid rgba(0, 123, 255, 0.2) !important;
    }

    .stat-item.wrong {
        background: rgba(253, 126, 20, 0.1) !important;
        color: #fd7e14 !important;
        border: 1px solid rgba(253, 126, 20, 0.2) !important;
    }

    .stat-item.missing {
        background: rgba(220, 53, 69, 0.1) !important;
        color: #dc3545 !important;
        border: 1px solid rgba(220, 53, 69, 0.2) !important;
    }

    .stat-item.extra {
        background: rgba(108, 117, 125, 0.1) !important;
        color: #6c757d !important;
        border: 1px solid rgba(108, 117, 125, 0.2) !important;
    }

    .stat-icon {
        font-weight: 700 !important;
    }

    /* Başarı oranı */
    .success-rate {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 10px 15px !important;
        background: #f8f9fa !important;
        border-radius: 8px !important;
        border: 1px solid #e9ecef !important;
    }

    .rate-label {
        font-weight: 600 !important;
        color: #495057 !important;
        font-size: 0.9rem !important;
    }

    .rate-value {
        font-weight: 700 !important;
        font-size: 1.1rem !important;
        padding: 4px 12px !important;
        border-radius: 12px !important;
    }

    .rate-value.excellent {
        background: #28a745 !important;
        color: white !important;
    }

    .rate-value.good {
        background: #007bff !important;
        color: white !important;
    }

    .rate-value.average {
        background: #ffc107 !important;
        color: #333 !important;
    }

    .rate-value.poor {
        background: #dc3545 !important;
        color: white !important;
    }

    /* Hata durumları */
    .comparison-error,
    .comparison-empty {
        text-align: center !important;
        padding: 20px !important;
        color: #6c757d !important;
        font-style: italic !important;
        background: #f8f9fa !important;
        border-radius: 8px !important;
        border: 1px dashed #dee2e6 !important;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .comparison-row {
            flex-direction: column !important;
            gap: 8px !important;
        }

        .row-label {
            min-width: auto !important;
            padding-top: 0 !important;
        }

        .summary-stats {
            flex-direction: column !important;
        }

        .success-rate {
            flex-direction: column !important;
            gap: 8px !important;
            text-align: center !important;
        }
    }

    /* Sonuç İstatistikleri Paneli */
.result-stats-panel {

}


    .stats-loading {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 20px !important;
        color: #94a3b8 !important;
        gap: 10px !important;
    }

    .stats-loading span {
        font-size: 0.9rem !important;
        font-weight: 500 !important;
    }

    .stats-error {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 20px !important;
        color: #f87171 !important;
        gap: 8px !important;
        font-size: 0.9rem !important;
    }

    .result-stats-content {
        padding: 16px !important;
    }

    .stats-header {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        margin-bottom: 16px !important;
        padding-bottom: 12px !important;
        border-bottom: 1px solid rgba(148, 163, 184, 0.2) !important;
    }

    .stats-header span {
        color: #6366f1 !important;
    }

    .stats-header h6 {
        margin: 0 !important;
        font-size: 1rem !important;
        font-weight: 600 !important;
        color: #f1f5f9 !important;
    }

    .stats-grid {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 12px !important;
        margin-bottom: 16px !important;
    }

    .result-stats-content .stat-item {
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
        padding: 10px !important;
        background: rgba(51, 65, 85, 0.3) !important;
        border-radius: 8px !important;
        border: 1px solid rgba(148, 163, 184, 0.1) !important;
        transition: all 0.3s ease !important;
    }

    .result-stats-content .stat-item:hover {
        background: rgba(51, 65, 85, 0.5) !important;
        border-color: rgba(99, 102, 241, 0.3) !important;
        transform: translateY(-1px) !important;
    }

    .result-stats-content .stat-icon {
        font-size: 1.2rem !important;
        line-height: 1 !important;
    }

    .result-stats-content .stat-details {
        flex: 1 !important;
    }

    .result-stats-content .stat-value {
        font-size: 0.95rem !important;
        font-weight: 700 !important;
        color: #f1f5f9 !important;
        line-height: 1.2 !important;
        margin-bottom: 2px !important;
    }

    .result-stats-content .stat-label {
        font-size: 0.75rem !important;
        color: #94a3b8 !important;
        font-weight: 500 !important;
        line-height: 1 !important;
    }

    /* Stat değer renkleri */
    .stats-score-excellent {
        color: #10b981 !important;
    }

    .stats-score-good {
        color: #3b82f6 !important;
    }

    .stats-score-fair {
        color: #f59e0b !important;
    }

    .stats-score-poor {
        color: #ef4444 !important;
    }

    .stats-correct {
        color: #10b981 !important;
    }

    .stats-average {
        color: #8b5cf6 !important;
    }

    .stats-comparison {
        padding-top: 12px !important;
        border-top: 1px solid rgba(148, 163, 184, 0.2) !important;
    }

    .stats-comparison .comparison-item {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 6px 0 !important;
    }

    .stats-comparison .comparison-item:last-child {
        padding-bottom: 0 !important;
    }

    .stats-comparison .comparison-label {
        font-size: 0.85rem !important;
        color: #94a3b8 !important;
        font-weight: 500 !important;
    }

    .stats-comparison .comparison-value {
        font-size: 0.9rem !important;
        font-weight: 600 !important;
        color: #e2e8f0 !important;
    }

    .stats-better {
        color: #10b981 !important;
    }

    /* Header butonları için genel stil */
    .dictation-header-container {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .dictation-header-buttons .uk-flex {
        gap: 15px !important;
    }

    /* Mobil uyumluluk */
@media (max-width: 768px) {
    .result-footer {
        flex-direction: column !important;
        align-items: center !important;
        text-align: center !important;
    }
    
    .result-stats-panel {
        width: 100% !important;
        margin-top: 20px !important;
        max-width: 320px !important;
    }
        
        .stats-grid {
            grid-template-columns: 1fr !important;
            gap: 8px !important;
        }
        
        .result-stats-content .stat-item {
            padding: 8px !important;
        }
        
        .result-stats-content .stat-value {
            font-size: 0.9rem !important;
        }
        
        .result-stats-content .stat-label {
            font-size: 0.7rem !important;
        }
        
        .stats-comparison .comparison-label {
            font-size: 0.8rem !important;
        }
        
        .stats-comparison .comparison-value {
            font-size: 0.85rem !important;
        }

        /* Mobilde header butonları alt satırda */
        .dictation-header-container {
            display: flex !important;
            flex-direction: column !important;
            gap: 15px !important;
        }

        .dictation-header-title {
            text-align: center !important;
        }

        .dictation-header-buttons {
            display: flex !important;
            justify-content: center !important;
        }

        .dictation-header-buttons .uk-flex {
            gap: 10px !important;
        }

        .dictation-header-buttons .pink-btn {
            font-size: 0.85rem !important;
            padding: 8px 16px !important;
        }
    }

 
    /* Yanlış kelime container ve overlay stilleri */
    .word-container {
        position: relative;
        display: inline-block;
    }

    .word-wrong, .word-missing, .word-extra {
        position: relative;
        z-index: 1;
    }

    .correct-word-overlay {
        position: absolute;
        top: -18px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #1c2639;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        color: #fff;
        white-space: nowrap;
        z-index: 2;
        opacity: 0;
        transition: opacity 0.2s;
    }

    .word-container:hover .correct-word-overlay {
        opacity: 1;
    }

 