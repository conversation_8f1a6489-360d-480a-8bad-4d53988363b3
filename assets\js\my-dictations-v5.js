/**
 * My Dictations JavaScript Fonksiyonları
 * Version: 3.1 - Yeni sistem entegrasyonu
 * Son güncelleme: 2025-01-21
 */

// Namespace kullanarak çakışmaları önle
window.MyDictations = window.MyDictations || {
    dictationTimer: null,
    timeLeft: 0,
    currentDictationData: null,
    startTime: null,
    sentenceTimes: []
};

// Son sonuçları yükle
function loadRecentResults() {
    $.ajax({
        url: 'islemler/dictation_recent_results.php',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                renderRecentResults(response.data);
            } else {
                console.error('Recent results error:', response.message);
                $('#recentResultsList').html('<div class="uk-text-center uk-text-black uk-padding fw4">Henüz sonuç bulunamadı</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Recent results AJAX error:', xhr.responseText);
            $('#recentResultsList').html('<div class="uk-text-center uk-text-danger uk-padding">Yükleme hatası: ' + error + '</div>');
        }
    });
}

// Tüm dictationları yükle
function loadAllDictations() {
    const statusFilter = $('#statusFilter').val();
    
    $.ajax({
        url: 'islemler/dictation_all_list.php',
        method: 'POST',
        data: {
            status: statusFilter
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                renderAllDictations(response.data);
            } else {
                console.error('All dictations error:', response.message);
                $('#allDictationsAccordion').html('<li class="uk-text-center uk-text-black uk-padding">Dictation bulunamadı</li>');
            }
        },
        error: function(xhr, status, error) {
            console.error('All dictations AJAX error:', xhr.responseText);
            $('#allDictationsAccordion').html('<li class="uk-text-center uk-text-danger uk-padding">Yükleme hatası: ' + error + '</li>');
        }
    });
}

// Son sonuçları render et
function renderRecentResults(results) {
    let html = '';

    if (results.length === 0) {
        html = '<div class="uk-text-center uk-text-black uk-padding fw4">Henüz sonuç bulunamadı</div>';
    } else {
        results.forEach(function(result) {
            html += `
                <div class="dictation-item">
                    <div class="uk-flex uk-flex-between uk-flex-middle">
                        <div>
                            <h5>${result.dictation_baslik}</h5>
                            <p class="uk-text-small fw4 uk-margin-remove uk-text-black">${result.formatted_date}</p>
                        </div>
                        <div class="uk-text-right">
                            <div class="uk-margin-small-bottom">
                                <div class="score-badge score-${getScoreLevel(result.overall_score)}">
                                    <div class="score-icon">${getScoreEmoji(result.overall_score)}</div>
                                    <div class="score-text">
                                        <span class="score-number">${result.overall_score}</span>
                                        <span class="score-percent">%</span>
                                    </div>
                                </div>
                            </div>
                            <button class="modern-result-btn" onclick="showResultDetail(${result.submission_id})">
                                <i class="btn-icon">📊</i>
                                <span class="btn-text">Sonuç Görüntüle</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
    }

    $('#recentResultsList').html(html);
}

// Tüm dictationları render et
function renderAllDictations(dictations) {
    let html = '';

    if (dictations.length === 0) {
        html = '<li class="uk-text-center uk-text-black uk-padding">Dictation bulunamadı</li>';
    } else {
        dictations.forEach(function(dictation) {
            const statusClass = dictation.status === 'active' ? 'uk-badge-success' : 
                              dictation.status === 'completed' ? 'uk-badge-primary' : 
                              dictation.status === 'pending' ? 'uk-badge-warning' : 'uk-badge-danger';
            
            const canSolve = dictation.status === 'active';
            const canViewResult = dictation.status === 'completed';
            
            html += `
                <li>
                    <a class="uk-accordion-title" href="#">
                        <div class="uk-flex uk-flex-between uk-flex-middle">
                            <div>
                                <strong>${dictation.baslik}</strong>
                            </div>
                            <div class="uk-text-right">
                                <span class="uk-badge ${statusClass}">${dictation.status_label}</span>
                            </div>
                        </div>
                    </a>
                    <div class="uk-accordion-content">
                        <div class="uk-grid-small" uk-grid>
                            <div class="uk-width-2-3@m">
                                <p class="uk-text-small uk-text-black fw5">
                                    <strong>Cümle Sayısı:</strong> ${dictation.sentence_text}
                                </p>
                                <p class="uk-text-small uk-text-black fw5">
                                    <strong>Dictation Tarihi:</strong> ${dictation.formatted_assignment_date}
                                </p>
                                ${dictation.score !== null ? 
                                    `<div class="uk-margin-small-top">
                                        <span class="score-badge score-${getScoreLevel(dictation.score)}"> Başarı Oranı
                                            <span class="score-number">${dictation.score}</span>
                                            <span class="score-percent">%</span>
                                        </span>
                                    </div>` : ''
                                }
                            </div>
                            <div class="uk-width-1-3@m uk-text-right">
                                ${canSolve ? 
                                    `<button class="btn-solve uk-margin-small-bottom" onclick="dictationCoz(${dictation.id})">
                                        <i class="fe fe-play"></i> Çöz
                                    </button><br>` : ''
                                }
                                ${canViewResult ? 
                                    `<button class="modern-result-btn" onclick="showResultDetail(${dictation.submission_id})">
                                        <i class="btn-icon">📊</i>
                                        <span class="btn-text">Sonuç Görüntüle</span>
                                    </button>` : ''
                                }
                            </div>
                        </div>
                    </div>
                </li>
            `;
        });
    }

    $('#allDictationsAccordion').html(html);
}

// Dictation çöz
function dictationCoz(dictationId) {
    $.ajax({
        url: 'islemler/dictation_start.php',
        method: 'POST',
        data: {
            dictation_id: dictationId,
            csrf_token: window.csrf_token
        },
        dataType: 'json',
        beforeSend: function() {
            showLoading('Dictation hazırlanıyor...');
        },
        success: function(response) {
            hideLoading();
            if (response.success) {
                window.MyDictations.currentDictationData = response.data;
                startDictation();
            } else {
                showError(response.message);
            }
        },
        error: function() {
            hideLoading();
            showError('Dictation başlatılırken hata oluştu');
        }
    });
}

// Dictation başlat
function startDictation() {
    const data = window.MyDictations.currentDictationData;
    
    // Dictation verilerini localStorage'a kaydet
    try {
        localStorage.setItem('currentDictationData', JSON.stringify(data));
    } catch (e) {
        console.error('localStorage kaydetme hatası:', e);
    }
    
    // Modal başlığını ayarla
    $('#dictationTitle').text(data.dictation.baslik);
    
    // Dinleme durumlarını localStorage'dan al veya yeni oluştur
    const storageKey = `dictation_listened_${data.dictation.id}`;
    let listenedSentences = [];
    try {
        const stored = localStorage.getItem(storageKey);
        listenedSentences = stored ? JSON.parse(stored) : [];
    } catch (e) {
        listenedSentences = [];
    }
    
    // İçeriği oluştur
    let content = `
        <div class="dictation-form-container">
            <form id="dictationForm">
                <input type="hidden" name="dictation_id" value="${data.dictation.id}">
    `;
    
    // Cümleleri ekle
    data.sentences.forEach(function(sentence, index) {
        const isListened = listenedSentences.includes(index);
        const buttonClass = isListened ? 'listen-btn listened' : 'listen-btn';
        const buttonText = isListened ? 'Dinlendi' : 'Dinle';
        const buttonDisabled = isListened ? 'disabled' : '';
        
        content += `
            <div class="sentence-group">
                <div class="sentence-header">
                    <span class="sentence-number" style="border-radius: 10px !important; width: 75px !important;">Cümle ${index + 1}</span>
                    <div class="listen-btn-container">
                    <button type="button" class="${buttonClass}" onclick="playSentence(${index})" ${buttonDisabled}>
                           <span uk-icon="icon: play-circle"></span> ${buttonText} 
                    </button>
                        <div class="listen-tooltip">You can listen only once</div>
                    </div>
                </div>  
                <div class="sentence-input-wrapper">
                    <textarea 
                        name="answers[${sentence.id}]" 
                        id="sentence_${index}" 
                        class="sentence-textarea" 
                        rows="2" 
                        placeholder="Dinlediğiniz cümleyi buraya yazın..."
                        oninput="clearValidationError(this); autoConvertToLowercase(this)"
                    ></textarea>
                </div>
            </div>
        `;
    });
    
    content += `
            </form>
            
            <div class="submit-section">
                <button id="submitDictation" class="submit-dictation-btn fix-it-btn" onclick="submitDictation()">
                    <span uk-icon="icon: check"></span> </i> FIX IT
                </button>
            </div>
        </div>
    `;
    
    $('#dictationContent').html(content);
    
    // Zamanlama başlat (sadece ilk açılışta)
    if (!window.MyDictations.startTime) {
        window.MyDictations.startTime = Date.now();
    }
    
    if (!window.MyDictations.sentenceTimes) {
        window.MyDictations.sentenceTimes = data.sentences.map(() => ({ startTime: null, totalTime: 0 }));
    }
    
    // Dinleme durumlarını global olarak sakla
    window.MyDictations.listenedSentences = listenedSentences;
    window.MyDictations.currentStorageKey = storageKey;
    
    // Modal'ı göster
    const dictationModal = UIkit.modal('#dictationModal');
    dictationModal.show();
    
    // Modal açıldığında kaydedilen cümleleri yükle
    setTimeout(() => {
        loadSavedSentences();
    }, 100);
    
    // Modal kapatma event listener'larını ekle (önceki listener'ları temizle)
    $('#dictationModal').off('hidden').on('hidden', function() {
        // Modal kapandığında timer'ı durdur
        if (window.MyDictations.dictationTimer) {
            clearInterval(window.MyDictations.dictationTimer);
            window.MyDictations.dictationTimer = null;
        }
        $(document).off('keydown.dictationModal');
    });
    
    // ESC tuşu ile modal kapatma
    $(document).off('keydown.dictationModal').on('keydown.dictationModal', function(e) {
        if (e.key === 'Escape') {
            dictationModal.hide();
        }
    });
    
    // Kapatma butonuna özel event listener ekle
    $('#dictationModal .uk-modal-close-default').off('click').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        dictationModal.hide();
    });
}

// Cümle dinle - Her cümle için sadece 1 kez (kalıcı)
function playSentence(index) {
    const sentence = window.MyDictations.currentDictationData.sentences[index];
    const button = document.querySelector(`button[onclick="playSentence(${index})"]`);
    
    // Eğer bu cümle daha önce dinlendiyse, butonu devre dışı bırak
    if (button.classList.contains('listened') || window.MyDictations.listenedSentences.includes(index)) {
        return;
    }
    
    // Butonu dinleniyor durumuna getir
    button.disabled = true;
    button.innerHTML = '<i class="fe fe-loader"></i> Dinleniyor...';
    
    // Dinlenen cümleyi kaydet
    function markAsListened() {
        // Butonu pasif hale getir
        button.classList.add('listened');
        button.disabled = true;
        button.innerHTML = '<i class="fe fe-check"></i> Dinlendi';
        
        // localStorage'a kaydet
        if (!window.MyDictations.listenedSentences.includes(index)) {
            window.MyDictations.listenedSentences.push(index);
            try {
                localStorage.setItem(window.MyDictations.currentStorageKey, JSON.stringify(window.MyDictations.listenedSentences));
            } catch (e) {
                console.error('localStorage kaydetme hatası:', e);
            }
        }
    }
    
    if (sentence.audio_url) {
        const audio = new Audio(sentence.audio_url);
        
        audio.addEventListener('ended', function() {
            markAsListened();
        });
        
        audio.addEventListener('error', function() {
            // Hata durumunda butonu eski haline döndür
            button.disabled = false;
            button.innerHTML = '<i class="fe fe-volume-2"></i> Dinle';
            showError('Ses dosyası oynatılamadı');
        });
        
        audio.play().catch(function(error) {
            console.error('Ses oynatılamadı:', error);
            button.disabled = false;
            button.innerHTML = '<i class="fe fe-volume-2"></i> Dinle';
            showError('Ses dosyası oynatılamadı');
        });
    } else {
        // Text-to-speech alternatifi
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(sentence.correct_text);
            utterance.lang = 'en-US';
            utterance.rate = 0.6;
            
            utterance.addEventListener('end', function() {
                markAsListened();
            });
            
            utterance.addEventListener('error', function() {
                // Hata durumunda butonu eski haline döndür
                button.disabled = false;
                button.innerHTML = '<i class="fe fe-volume-2"></i> Dinle';
                showError('Ses özelliği hatası');
            });
            
            speechSynthesis.speak(utterance);
        } else {
            button.disabled = false;
            button.innerHTML = '<i class="fe fe-volume-2"></i> Dinle';
            showError('Ses özelliği bu tarayıcıda desteklenmiyor');
        }
    }
}

// // Cümle zamanlaması
// function trackSentenceTime(index) {
//     const now = Date.now();
//     const sentenceTime = window.MyDictations.sentenceTimes[index];
    
//     if (!sentenceTime.startTime) {
//         sentenceTime.startTime = now;
//     }
// }

// Validasyon hatasını temizle
function clearValidationError(textarea) {
    if (textarea.value.trim() !== '') {
        textarea.style.borderColor = '';
        textarea.style.boxShadow = '';
    }
}

// Cümle yazılırken localStorage'a kaydet
function saveSentenceToStorage(textarea) {
    const sentenceIndex = textarea.id.replace('sentence_', '');
    const dictationId = window.MyDictations.currentDictationData?.dictation?.id;
    
    if (!dictationId) return;
    
    const storageKey = `dictation_sentences_${dictationId}`;
    let savedSentences = {};
    
    try {
        const stored = localStorage.getItem(storageKey);
        savedSentences = stored ? JSON.parse(stored) : {};
    } catch (e) {
        console.error('localStorage okuma hatası:', e);
        savedSentences = {};
    }
    
    // Cümleyi kaydet
    savedSentences[sentenceIndex] = textarea.value;
    
    try {
        localStorage.setItem(storageKey, JSON.stringify(savedSentences));
    } catch (e) {
        console.error('localStorage kaydetme hatası:', e);
    }
}

// Kaydedilen cümleleri localStorage'dan yükle
function loadSavedSentences() {
    const dictationId = window.MyDictations.currentDictationData?.dictation?.id;
    
    if (!dictationId) return;
    
    const storageKey = `dictation_sentences_${dictationId}`;
    let savedSentences = {};
    
    try {
        const stored = localStorage.getItem(storageKey);
        savedSentences = stored ? JSON.parse(stored) : {};
    } catch (e) {
        console.error('localStorage okuma hatası:', e);
        return;
    }
    
    // Her cümle textarea'sını doldur
    Object.keys(savedSentences).forEach(sentenceIndex => {
        const textarea = document.getElementById(`sentence_${sentenceIndex}`);
        if (textarea && savedSentences[sentenceIndex]) {
            textarea.value = savedSentences[sentenceIndex];
            // Validasyon hatasını temizle
            clearValidationError(textarea);
        }
    });
}

// Kaydedilen cümleleri temizle
function clearSavedSentences() {
    const dictationId = window.MyDictations.currentDictationData?.dictation?.id;
    
    if (!dictationId) return;
    
    const storageKey = `dictation_sentences_${dictationId}`;
    
    try {
        localStorage.removeItem(storageKey);
    } catch (e) {
        console.error('localStorage temizleme hatası:', e);
    }
}

// Dictation gönder
function submitDictation(isAutoSubmit = false) {
    // Dictation verilerini al (önce global'dan, sonra localStorage'dan)
    let data = window.MyDictations?.currentDictationData;
    
    if (!data || !data.sentences) {
        // localStorage'dan yüklemeyi dene
        try {
            const storedData = localStorage.getItem('currentDictationData');
            if (storedData) {
                data = JSON.parse(storedData);
                window.MyDictations.currentDictationData = data;
            }
        } catch (e) {
            console.error('localStorage okuma hatası:', e);
        }
    }
    
    if (!data || !data.sentences) {
        showError('Dictation verileri bulunamadı. Lütfen sayfayı yenileyip tekrar deneyin.');
        return;
    }

    // Form validasyonu - Tüm cümle alanlarının doldurulup doldurulmadığını kontrol et
    const form = document.getElementById('dictationForm');
    const textareas = form.querySelectorAll('textarea[name^="answers"]');
    let emptyFields = [];
    let allFieldsFilled = true;

    textareas.forEach(function(textarea, index) {
        const value = textarea.value.trim();
        if (value === '') {
            emptyFields.push(index + 1);
            allFieldsFilled = false;
            // Boş alanı vurgula
            textarea.style.borderColor = '#ef4444';
            textarea.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
        } else {
            // Dolu alanın vurgusunu kaldır
            textarea.style.borderColor = '';
            textarea.style.boxShadow = '';
        }
    });

    // Eğer boş alanlar varsa uyarı göster
    if (!allFieldsFilled) {
        let message;
        if (emptyFields.length === 1) {
            message = `${emptyFields[0]}. cümle alanı boş bırakılamaz. Lütfen tüm cümleleri tamamlayın.`;
        } else if (emptyFields.length === textareas.length) {
            message = 'Hiçbir cümle alanı doldurulmamış. Lütfen tüm cümleleri tamamlayın.';
        } else {
            message = `${emptyFields.join(', ')}. cümle alanları boş bırakılamaz. Lütfen tüm cümleleri tamamlayın.`;
        }
        
        showError(message);
        
        // İlk boş alana odaklan
        const firstEmptyField = form.querySelector(`#sentence_${emptyFields[0] - 1}`);
        if (firstEmptyField) {
            firstEmptyField.focus();
            firstEmptyField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        
        return;
    }

    // Timer durdur
    if (window.MyDictations.dictationTimer) {
        clearInterval(window.MyDictations.dictationTimer);
        window.MyDictations.dictationTimer = null;
    }
    
    // Dictation modal'ını kapat
    UIkit.modal('#dictationModal').hide();
    
    // Sonuç modalını göster (hata hesaplaması yapmadan)
    showManualResultModal();
    
    // Aktif dictation alert'ini kontrol et
    checkActiveDictationAlert();
}

// Manuel sonuç modalını göster
function showManualResultModal() {
    
    // Dictation verilerini al (önce global'dan, sonra localStorage'dan)
    let data = window.MyDictations?.currentDictationData;
    
    if (!data || !data.sentences) {
        // localStorage'dan yüklemeyi dene
        try {
            const storedData = localStorage.getItem('currentDictationData');
            if (storedData) {
                data = JSON.parse(storedData);
                window.MyDictations.currentDictationData = data;
            }
        } catch (e) {
            console.error('localStorage okuma hatası:', e);
        }
    }
    
    // Dictation verilerini kontrol et
    if (!data || !data.sentences) {
        showError('Dictation verileri bulunamadı. Lütfen sayfayı yenileyip tekrar deneyin.');
        return;
    }

    // Dictation başlığını güvenli bir şekilde al
    let dictationTitle = 'Writing Skills Exercise';
    if (data && data.dictation && data.dictation.baslik) {
        dictationTitle = data.dictation.baslik;
    }

    // Yeni modal tasarımı
    let content = `
        <div class="new-result-container">
            <div class="result-header">
                <h2 class="dictation-title">${dictationTitle}</h2>
            </div>

            <div class="result-table-container">
                <table class="result-table">
                    <thead>
                        <tr>
                            <th class="col-number" style="${window.innerWidth <= 768 ? 'display: none !important;' : ''}">#</th>
                            <th class="col-correct-answer">Correct Answer</th>
                            <th class="col-user-answer">Your Answer</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    // Cümleleri tabloya ekle
    data.sentences.forEach(function(sentence, index) {
        const userAnswer = document.getElementById(`sentence_${index}`).value || 'No answer';
        const correctText = sentence.sentence_text || sentence.correct_text || 'No correct answer available';
        const errorCount = calculateSentenceErrors(correctText, userAnswer);
        const userClass = errorCount === 0 ? 'answer-text fw5 correct-answer-green' : 'answer-text fw5 wrong-answer-red';
        content += `
            <tr class="sentence-row">
                <td class="uk-text-center fw5" data-label="#">${index + 1}</td>
                <td class="correct-answer" data-label="Correct Answer">
                    <div class="answer-text fw5 correct">${correctText}</div>
                </td>
                <td class="user-answer" data-label="Your Answer">
                    <div class="${userClass}">${userAnswer}</div>
                </td>
            </tr>
        `;
    });

    content += `
                    </tbody>
                </table>
            </div>

            <div class="result-footer uk-text-center">
                <div class="manual-mistake-section">
                    <div class="mistake-instruction">
                        <p class="fw5 mb3">Cümlelerinizi tamamladıktan sonra kaç adet hatanız varsa hata sayınızı lütfen aşağıdaki boşluğa yazıp uygula butonuna basınız.</p>
                    </div>
                    <div class="mistake-input-section">
                        <div class="uk-flex uk-flex-middle uk-flex-center" style="gap: 15px;">
                            <label for="totalMistakeInput" class="fw5">Toplam Hata Sayınız:</label>
                            <input type="number" class="uk-input mistake-number-input" 
                                   id="totalMistakeInput" 
                                   min="0" 
                                   max="100" 
                                   placeholder="0"
                                   style="width: 120px;">
                            <button class="uk-button uk-button-primary" onclick="applyMistakeCount()">
                                <span uk-icon="icon: check"></span> Uygula
                            </button>
                        </div>
                    </div>
                </div>
                <div class="result-stats-panel" id="resultStatsPanel" style="display: none;">
                    <div class="stats-loading">
                        <div uk-spinner="ratio: 0.8"></div>
                        <span>İstatistikler yükleniyor...</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#resultContent').html(content);
    
    // Modal'ı aç
    const resultModal = UIkit.modal('#resultModal');
    resultModal.show();
    
    // SweetAlert ile bilgilendirme mesajı göster
    setTimeout(function() {
        Swal.fire({
            title: 'Cümlelerinizi Kontrol Edin',
            html: `
                <div style="text-align: center; padding: 20px;">
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.6;">
                        <strong>Önemli:</strong> Bu ekranda cümlelerinizi kontrol edip kaç adet hatanız varsa hata sayınızı girmeniz gerekmektedir.
                    </p>
                </div>
            `,
            icon: 'info',
            confirmButtonText: 'Anladım',
            confirmButtonColor: '#3b82f6',
            background: '#1e293b',
            color: '#f1f5f9',
            customClass: {
                popup: 'swal2-custom-popup',
                title: 'swal2-custom-title',
                content: 'swal2-custom-content'
            },
            showClass: {
                popup: 'animate__animated animate__fadeInDown'
            },
            hideClass: {
                popup: 'animate__animated animate__fadeOutUp'
            },
            didOpen: () => {
                // Title rengi beyaz yapılır
                const titleEl = document.querySelector('.swal2-title');
                if (titleEl) {
                    titleEl.style.color = '#fff';
                }
            }
        });
    }, 500); // Modal açıldıktan 500ms sonra göster
    // Modal kapatma event listener'larını ekle
    $('#resultModal').off('hidden').on('hidden', function() {
        $(document).off('keydown.resultModal');
    });
    
    // ESC tuşu ile modal kapatma
    $(document).off('keydown.resultModal').on('keydown.resultModal', function(e) {
        if (e.key === 'Escape') {
            resultModal.hide();
        }
    });
    
    // Modal dışına tıklayınca kapatma
    $('#resultModal .uk-modal-dialog').off('click').on('click', function(e) {
        e.stopPropagation();
    });
    
    $('#resultModal').off('click').on('click', function(e) {
        if (e.target === this) {
            resultModal.hide();
        }
    });
    
    // Kapatma butonuna özel event listener ekle
    $('#resultModal .uk-modal-close-default, #resultModal .result-modal-close').off('click').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        resultModal.hide();
    });
}

// Hata sayısını uygula
function applyMistakeCount() {
    const totalMistakes = parseInt(document.getElementById('totalMistakeInput').value) || 0;
    
    if (totalMistakes < 0) {
        showError('Hata sayısı negatif olamaz');
        return;
    }
    
    // Dictation verilerini al (önce global'dan, sonra localStorage'dan)
    let data = window.MyDictations?.currentDictationData;
    
    if (!data || !data.sentences) {
        // localStorage'dan yüklemeyi dene
        try {
            const storedData = localStorage.getItem('currentDictationData');
            if (storedData) {
                data = JSON.parse(storedData);
                window.MyDictations.currentDictationData = data;
            }
        } catch (e) {
            console.error('localStorage okuma hatası:', e);
        }
    }
    
    if (!data || !data.sentences) {
        showError('Dictation verileri bulunamadı. Lütfen sayfayı yenileyip tekrar deneyin.');
        return;
    }
    
    // İstatistik panelini göster
    $('#resultStatsPanel').show();
    
    const formData = new FormData();
    
    // Kullanıcı cevaplarını topla
    const userAnswers = {};
    data.sentences.forEach(function(sentence, index) {
        const userAnswer = document.getElementById(`sentence_${index}`).value || '';
        userAnswers[sentence.id] = userAnswer;
    });
    
    // Timing verilerini hazırla
    const currentTime = Date.now();
    const startTime = window.MyDictations.startTime || currentTime;
    const totalTime = currentTime - startTime;
    
    formData.append('dictation_id', data.dictation.id);
    formData.append('csrf_token', window.csrf_token);
    formData.append('user_answers', JSON.stringify(userAnswers));
    formData.append('total_mistakes', totalMistakes);
    formData.append('timing_data', JSON.stringify(window.MyDictations.sentenceTimes || []));
    formData.append('start_time', new Date(startTime).toISOString());
    
    // Backend'e gönder
    $.ajax({
        url: 'islemler/dictation_manual_submit.php',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            if (response.success) {

                // İstatistikleri yükle
                if (response.class_stats_data) {
                    renderResultStats(response.class_stats_data, response.analysis, response.analysis.wrong_words || response.analysis.total_mistakes || totalMistakes);
                } else {
                    loadResultStats(response.analysis, response.analysis.wrong_words || response.analysis.total_mistakes || totalMistakes);
                }
                
                // Hata giriş bölümünü gizle ve düzeltme butonu göster
                $('.manual-mistake-section').hide();
                $('#resultStatsPanel').before(`
                    <div class="mistake-correction-section uk-margin-medium-top">
                        <button class="uk-button uk-button-secondary" onclick="showMistakeCorrection()">
                            <span uk-icon="icon: pencil"></span> Hata Sayısını Düzelt
                        </button>
                    </div>
                `);
                
            } else {
                showError(response.message);
            }
        },
        error: function() {
            showError('Hata sayısı kaydedilirken bir hata oluştu');
        }
    });
}

// Hata sayısını düzeltme fonksiyonu
function showMistakeCorrection() {
    // Düzeltme butonunu gizle
    $('.mistake-correction-section').hide();
    
    // Hata giriş bölümünü tekrar göster
    $('.manual-mistake-section').show();
    
    // Input'u temizle
    $('#totalMistakeInput').val('');
    
    // İstatistik panelini gizle
    $('#resultStatsPanel').hide();
}

// Sonuç istatistiklerini yükle
function loadResultStats(analysis, totalErrors) {
    $.ajax({
        url: 'islemler/dictation_class_stats.php',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data.last_dictation) {
                renderResultStats(response.data, analysis, totalErrors);
            } else {
                $('#resultStatsPanel').html(`
                    <div class="stats-error">
                        <span uk-icon="icon: info"></span>
                        <span>İstatistik verisi bulunamadı</span>
                    </div>
                `);
            }
        },
        error: function() {
            $('#resultStatsPanel').html(`
                <div class="stats-error">
                    <span uk-icon="icon: warning"></span>
                    <span>İstatistikler yüklenemedi</span>
                </div>
            `);
        }
    });
}

// Sonuç istatistiklerini render et
function renderResultStats(data, analysis, totalErrors) {
    
    const lastDictation = data.last_dictation;
    const classStats = data.class_stats;
    const studentOverall = data.student_overall_stats;
    
    // Veritabanından gelen wrong_words değerini kullan
    const actualMistakes = analysis.wrong_words || totalErrors || 0;
    
    // Sıralama ikonu
    let rankIcon = '🏆';
    if (classStats.student_rank <= 3) {
        rankIcon = classStats.student_rank === 1 ? '🥇' : classStats.student_rank === 2 ? '🥈' : '🥉';
    } else if (classStats.student_rank <= 5) {
        rankIcon = '⭐';
    } else {
        rankIcon = '📈';
    }
    
    // Başarı durumu rengi
    let scoreClass = 'stats-score-excellent';
    if (lastDictation.student_score < 50) {
        scoreClass = 'stats-score-poor';
    } else if (lastDictation.student_score < 70) {
        scoreClass = 'stats-score-fair';
    } else if (lastDictation.student_score < 85) {
        scoreClass = 'stats-score-good';
    }
    
    // Doğru kelime sayısını hesapla
    const totalWords = analysis.total_words || 0;
    const correctWords = Math.max(0, totalWords - actualMistakes);
    
    const statsHtml = `
        <div class="result-stats-content">
            
            <div class="total-mistakes mb3">
                <strong>Total Mistake Number: ${actualMistakes}</strong>
            </div>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-icon">${rankIcon}</div>
                    <div class="stat-details">
                        <div class="stat-value">${classStats.student_rank}/${classStats.total_students}</div>
                        <div class="stat-label">Sınıf Sıralaması</div>
                    </div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-icon">📊</div>
                    <div class="stat-details">
                        <div class="stat-value ${scoreClass}">${lastDictation.student_score}%</div>
                        <div class="stat-label">Bu Dictaion İçin Başarı Puanı</div>
                    </div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-icon">✅</div>
                    <div class="stat-details">
                        <div class="stat-value stats-correct">${correctWords}</div>
                        <div class="stat-label">Doğru Kelime Sayısı</div>
                    </div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-icon">📈</div>
                    <div class="stat-details">
                        <div class="stat-value stats-average">${studentOverall.average_score}%</div>
                        <div class="stat-label">Genel Dictation Ortalamanız</div>
                    </div>
                </div>
            </div>
            
            <div class="stats-comparison">
                <div class="comparison-item">
                    <span class="comparison-label">Sınıf Ortalaması:</span>
                    <span class="comparison-value">${classStats.class_average_score}%</span>
                </div>
                <div class="comparison-item">
                    <span class="comparison-label">Sınıf Ortalama Hata Sayısı:</span>
                    <span class="comparison-value">${classStats.class_average_mistakes}</span>
                </div>
            </div>
        </div>
    `;
    
    $('#resultStatsPanel').html(statsHtml);
}

// Metin normalizasyonu - boşluk ve noktalama düzenleme
function normalizeText(text) {
    if (typeof text !== 'string') text = String(text || '');
    
    // 1. Noktalama işaretlerinden sonra boşluk ekle (eğer yoksa)
    text = text.replace(/([.,!?;:])([^\s])/g, '$1 $2');
    
    // 2. Çoklu boşlukları tek boşluğa çevir
    text = text.replace(/\s+/g, ' ');
    
    // 3. Baş ve sondaki boşlukları temizle
    text = text.trim();
    
    return text;
}


// Global normalize fonksiyonu - Türkçe İ/ı ve combining characters için
function normalizeWord(word) {
    if (typeof word !== 'string') word = String(word || '');
    
    let normalized = word.toLowerCase();
    
    // Unicode normalizasyonu (combining characterları düzelt)
    normalized = normalized.normalize('NFD');
    
    // Tüm İ/ı/I varyasyonlarını i'ye çevir
    normalized = normalized.replace(/[ıİI]/g, 'i');
    
    // Combining dot above karakterini kaldır (U+0307)
    normalized = normalized.replace(/\u0307/g, '');
    
    // Unicode normalizasyonu geri al (NFC)
    normalized = normalized.normalize('NFC');
    
    return normalized.trim();
}

// Kullanıcının girdiği metni otomatik olarak normalize etme (Türkçe İ/ı ve combining characters problemi çözümü)
function autoConvertToLowercase(textarea) {
    const cursorPosition = textarea.selectionStart;
    const cursorEndPosition = textarea.selectionEnd;

    // Metni kelime kelime normalize et, boşlukları koru
    const originalValue = textarea.value;
    const convertedValue = originalValue
        .split(/(\s+)/) // Boşlukları da diziye dahil et
        .map(part => part.trim() === '' ? part : normalizeWord(part))
        .join('');

    if (originalValue !== convertedValue) {
        textarea.value = convertedValue;
        textarea.setSelectionRange(cursorPosition, cursorEndPosition);
    }

    // Her karakter girişinde localStorage'a kaydet
    saveSentenceToStorage(textarea);
}

// Noktalama işaretlerini kaldıran yardımcı fonksiyon (apostrophe hariç)
function removePunctuation(str) {
    return (str || '')
        .replace(/[''′'‘‛’`]/g, "'")  // Tüm tek tırnak işaretlerini standart apostrophe'a çevir
        .replace(/["“”″„]/g, '"') // Tüm çift tırnak işaretlerini standart çift tırnağa çevir
        .replace(/[^\w\s'\"]/g, ''); // Apostrophe ve çift tırnak hariç diğer noktalama işaretlerini kaldır
}

// Cümleyi renkli olarak göster (doğru kelimeler yeşil, hatalı kelimeler kırmızı)
// generateEnhancedMistakeDetails ile aynı mantığı kullanır - DÜZELTİLMİŞ VERSİYON
function generateColoredSentence(correctText, userText) {
    if (typeof correctText !== 'string') correctText = String(correctText || '');
    if (typeof userText !== 'string') userText = String(userText || '');

    if (!correctText.trim()) return correctText;
    if (!userText.trim()) {
        // Kullanıcı hiçbir şey yazmamışsa, tüm kelimeleri eksik olarak göster
        return correctText.trim().split(/\s+/).map(word => 
            `<span class="word-container">
                <span class="word-missing">${word}</span>
                <span class="correct-word-overlay">incomplete</span>
            </span>`
        ).join(' ');
    }

    // Metinleri normalize et (boşluk düzenleme)
    const normalizedCorrectText = normalizeText(correctText);
    const normalizedUserText = normalizeText(userText);
    
    // Noktalama işaretlerini kaldırarak kelimeleri al (karşılaştırma için)
    const correctWords = removePunctuation(normalizedCorrectText).split(/\s+/).filter(w => w);
    const userWords = removePunctuation(normalizedUserText).split(/\s+/).filter(w => w);
    
    // Normalize fonksiyonu - sadece I/i farkını yok sayar
    const normalizeWord = (word) => {
        return word.toLowerCase().replace(/[ıİI]/g, 'i');
    };
    
    // Kelime sayılarını hesapla (normalize edilmiş hallerle)
    const correctWordCount = {};
    const userWordCount = {};
    const matchedUserWords = {};
    const matchedCorrectWords = {};
    
    correctWords.forEach((word, index) => {
        const normalized = normalizeWord(word);
        correctWordCount[normalized] = (correctWordCount[normalized] || 0) + 1;
    });
    
    userWords.forEach((word, index) => {
        const normalized = normalizeWord(word);
        userWordCount[normalized] = (userWordCount[normalized] || 0) + 1;
    });
    
    // 1. Tam eşleşmeleri bul (normalize edilmiş kelimelerle)
    for (const normalizedWord in correctWordCount) {
        const userCount = userWordCount[normalizedWord] || 0;
        if (userCount > 0) {
            const matchedCount = Math.min(correctWordCount[normalizedWord], userCount);
            matchedUserWords[normalizedWord] = (matchedUserWords[normalizedWord] || 0) + matchedCount;
            matchedCorrectWords[normalizedWord] = (matchedCorrectWords[normalizedWord] || 0) + matchedCount;
        }
    }
    
    // 2. Yazım hatalarını bul
    for (const correctNormalized in correctWordCount) {
        const matchedExact = matchedCorrectWords[correctNormalized] || 0;
        let remainingCorrect = correctWordCount[correctNormalized] - matchedExact;
        
        if (remainingCorrect > 0) {
            for (const userNormalized in userWordCount) {
                if (userNormalized === correctNormalized) continue; // Tam eşleşmeleri atla
                
                const remainingUser = userWordCount[userNormalized] - (matchedUserWords[userNormalized] || 0);
                if (remainingUser > 0 && isFuzzyMatch(correctNormalized, userNormalized)) {
                    // Sadece I/i farkı mı kontrol et
                    const originalCorrectWord = correctWords.find(w => normalizeWord(w.replace(/[.,]/g, '')) === correctNormalized);
                    const originalUserWord = userWords.find(w => normalizeWord(w.replace(/[.,]/g, '')) === userNormalized);
                    
                    const onlyIiDifference = originalCorrectWord && originalUserWord && 
                        originalCorrectWord.toLowerCase().replace(/[ıİI]/g, 'i') === originalUserWord.toLowerCase().replace(/[ıİI]/g, 'i');
                    
                    if (!onlyIiDifference) {
                        const spellingErrorCount = Math.min(remainingCorrect, remainingUser);
                        matchedUserWords[userNormalized] = (matchedUserWords[userNormalized] || 0) + spellingErrorCount;
                        matchedCorrectWords[correctNormalized] = (matchedCorrectWords[correctNormalized] || 0) + spellingErrorCount;
                        
                        remainingCorrect -= spellingErrorCount;
                        if (remainingCorrect <= 0) break;
                    }
                }
            }
        }
    }
    
    // 3. HYBRID YAKLAŞIM: Kullanıcının kelime sırasını takip et, eksik kelimeleri doğru pozisyonlarda göster
    const coloredHtmlParts = [];
    
    // Noktalama işaretlerini filtrele
    const filteredUserWords = userWords.filter(word => {
        const cleanWord = word.replace(/[.,]/g, '');
        return cleanWord.trim().length > 0;
    });
    
    const filteredCorrectWords = correctWords.filter(word => {
        const cleanWord = word.replace(/[.,]/g, '');
        return cleanWord.trim().length > 0;
    });
    
    // Eşleşme durumlarını tutmak için
    const tempMatchedUserWords = {...matchedUserWords};
    const tempMatchedCorrectWords = {...matchedCorrectWords};
    
    // Doğru cümledeki her kelimeyi işaretlemek için
    const processedCorrectWords = new Array(filteredCorrectWords.length).fill(false);
    
    // Kullanıcının her kelimesini işle
    for (let userIndex = 0; userIndex < filteredUserWords.length; userIndex++) {
        const userWord = filteredUserWords[userIndex];
        const userNormalized = normalizeWord(userWord.replace(/[.,]/g, ''));
        
        let matchType = null;
        let matchedCorrectWord = null;
        let correctWordIndex = -1;
        
        // 1. Bu kelimeden önce eksik olan kelimeleri ekle
        for (let correctIndex = 0; correctIndex < filteredCorrectWords.length; correctIndex++) {
            if (processedCorrectWords[correctIndex]) continue;
            
            const correctWord = filteredCorrectWords[correctIndex];
            const correctNormalized = normalizeWord(correctWord.replace(/[.,]/g, ''));
            
            // Bu doğru kelime için kullanıcının cümlesinde karşılık var mı kontrol et
            let hasMatchInRemainingWords = false;
            
            // Kalan kullanıcı kelimelerinde bu doğru kelime var mı?
            for (let remainingIndex = userIndex; remainingIndex < filteredUserWords.length; remainingIndex++) {
                const remainingUserWord = filteredUserWords[remainingIndex];
                const remainingUserNormalized = normalizeWord(remainingUserWord.replace(/[.,]/g, ''));
                
                if (remainingUserNormalized === correctNormalized && (tempMatchedUserWords[correctNormalized] || 0) > 0) {
                    hasMatchInRemainingWords = true;
                    break;
                }
                
                // Yazım hatası kontrolü
                if ((tempMatchedCorrectWords[correctNormalized] || 0) > 0 && 
                    isFuzzyMatch(remainingUserNormalized, correctNormalized)) {
                    const onlyIiDifference = correctWord && remainingUserWord && 
                        correctWord.toLowerCase().replace(/[ıİI]/g, 'i') === remainingUserWord.toLowerCase().replace(/[ıİI]/g, 'i');
                    if (!onlyIiDifference) {
                        hasMatchInRemainingWords = true;
                        break;
                    }
                }
            }
            
            // Eğer kalan kelimeler arasında karşılık yoksa, bu kelime eksik
            if (!hasMatchInRemainingWords) {
                coloredHtmlParts.push(`
                    <span class="word-container">
                        <span class="word-missing">${correctWord}</span>
                        <span class="correct-word-overlay">incomplete</span>
                    </span>
                `);
                processedCorrectWords[correctIndex] = true;
            } else {
                // Karşılık var, bu eksik değil, döngüyü kır
                break;
            }
        }
        
        // 2. Şimdi kullanıcının bu kelimesini işle
        // Tam eşleşme kontrolü
        for (let correctIndex = 0; correctIndex < filteredCorrectWords.length; correctIndex++) {
            if (processedCorrectWords[correctIndex]) continue;
            
            const correctWord = filteredCorrectWords[correctIndex];
            const correctNormalized = normalizeWord(correctWord.replace(/[.,]/g, ''));
            
            if (userNormalized === correctNormalized) {
                matchType = 'correct';
                correctWordIndex = correctIndex;
                break;
            }
        }
        
        // Yazım hatası kontrolü
        if (!matchType) {
            for (let correctIndex = 0; correctIndex < filteredCorrectWords.length; correctIndex++) {
                if (processedCorrectWords[correctIndex]) continue;
                
                const correctWord = filteredCorrectWords[correctIndex];
                const correctNormalized = normalizeWord(correctWord.replace(/[.,]/g, ''));
                
                if (userNormalized !== correctNormalized && 
                    isFuzzyMatch(userNormalized, correctNormalized)) {
                    
                    const onlyIiDifference = correctWord && userWord && 
                        correctWord.toLowerCase().replace(/[ıİI]/g, 'i') === userWord.toLowerCase().replace(/[ıİI]/g, 'i');
                    
                    if (!onlyIiDifference) {
                        matchType = 'wrong';
                        matchedCorrectWord = correctWord;
                        correctWordIndex = correctIndex;
                        break;
                    }
                }
            }
        }
        
        // Eğer hiç eşleşme yoksa fazla kelime
        if (!matchType) {
            matchType = 'extra';
        }
        
        // İşlenen doğru kelimeyi işaretle
        if (correctWordIndex >= 0) {
            processedCorrectWords[correctWordIndex] = true;
        }
        
        // HTML'e ekle
        switch (matchType) {
                case 'correct':
                coloredHtmlParts.push(`<span class="word-correct">${userWord}</span>`);
                    break;
                case 'wrong':
                    coloredHtmlParts.push(`
                        <span class="word-container">
                        <span class="word-wrong">${userWord}</span>
                        <span class="correct-word-overlay">${matchedCorrectWord}</span>
                        </span>
                    `);
                    break;
            case 'extra':
                    coloredHtmlParts.push(`
                        <span class="word-container">
                        <span class="word-extra">${userWord}</span>
                        <span class="correct-word-overlay">extra</span>
                        </span>
                    `);
                    break;
        }
    }
    
    // 3. Son olarak, işlenmemiş kalan eksik kelimeleri ekle
    for (let correctIndex = 0; correctIndex < filteredCorrectWords.length; correctIndex++) {
        if (!processedCorrectWords[correctIndex]) {
            const correctWord = filteredCorrectWords[correctIndex];
                    coloredHtmlParts.push(`
                        <span class="word-container">
                    <span class="word-missing">${correctWord}</span>
                    <span class="correct-word-overlay">incomplete</span>
                        </span>
                    `);
        }
    }

    return coloredHtmlParts.join(' ');
}

// Gelişmiş hata detaylarını oluştur (noktalama hatalarını göster, I/i hatalarını gizle)
function generateEnhancedMistakeDetails(correctText, userText) {
    if (typeof correctText !== 'string') correctText = String(correctText || '');
    if (typeof userText !== 'string') userText = String(userText || '');
    
    if (!correctText.trim()) return '<div class="no-mistakes">Doğru cümle bulunamadı</div>';
    if (!userText.trim()) return '<div class="no-mistakes">Cümle yazılmamış</div>';
    
    // Metinleri normalize et (boşluk düzenleme)
    const normalizedCorrectText = normalizeText(correctText);
    const normalizedUserText = normalizeText(userText);
    
    // Noktalama işaretlerini kaldırarak kelimeleri al (karşılaştırma için)
    const correctWords = removePunctuation(normalizedCorrectText).split(/\s+/).filter(w => w);
    const userWords = removePunctuation(normalizedUserText).split(/\s+/).filter(w => w);
    
    // Normalize fonksiyonu - sadece I/i farkını yok sayar
    const normalizeWord = (word) => {
        return word.toLowerCase().replace(/[ıİI]/g, 'i');
    };
    
    // Kelime sayılarını hesapla (normalize edilmiş hallerle)
    const correctWordCount = {};
    const userWordCount = {};
    const matchedUserWords = {};
    const matchedCorrectWords = {};
    
    correctWords.forEach((word, index) => {
        const normalized = normalizeWord(word);
        correctWordCount[normalized] = (correctWordCount[normalized] || 0) + 1;
    });
    
    userWords.forEach((word, index) => {
        const normalized = normalizeWord(word);
        userWordCount[normalized] = (userWordCount[normalized] || 0) + 1;
    });
    
    let mistakeHtml = '<div class="mistake-details">';
    let hasVisibleMistakes = false;
    
    // 1. Tam eşleşmeleri bul (normalize edilmiş kelimelerle)
    for (const normalizedWord in correctWordCount) {
        const userCount = userWordCount[normalizedWord] || 0;
        if (userCount > 0) {
            const matchedCount = Math.min(correctWordCount[normalizedWord], userCount);
            matchedUserWords[normalizedWord] = (matchedUserWords[normalizedWord] || 0) + matchedCount;
            matchedCorrectWords[normalizedWord] = (matchedCorrectWords[normalizedWord] || 0) + matchedCount;
        }
    }
    
    // 2. Yazım hatalarını bul ve göster
    for (const correctNormalized in correctWordCount) {
        const matchedExact = matchedCorrectWords[correctNormalized] || 0;
        let remainingCorrect = correctWordCount[correctNormalized] - matchedExact;
        
        if (remainingCorrect > 0) {
            for (const userNormalized in userWordCount) {
                if (userNormalized === correctNormalized) continue; // Tam eşleşmeleri atla
                
                const remainingUser = userWordCount[userNormalized] - (matchedUserWords[userNormalized] || 0);
                if (remainingUser > 0 && isFuzzyMatch(correctNormalized, userNormalized)) {
                    // Yazım hatası bulundu - orijinal kelimeleri bul (noktalama ile birlikte)
                    const originalCorrectWord = correctWords.find(w => normalizeWord(w.replace(/[.,]/g, '')) === correctNormalized);
                    const originalUserWord = userWords.find(w => normalizeWord(w.replace(/[.,]/g, '')) === userNormalized);
                    
                    // Sadece I/i farkı mı kontrol et
                    const onlyIiDifference = originalCorrectWord && originalUserWord && 
                        originalCorrectWord.toLowerCase().replace(/[ıİI]/g, 'i') === originalUserWord.toLowerCase().replace(/[ıİI]/g, 'i');
                    
                    if (!onlyIiDifference) {
                        mistakeHtml += `
                            <div class="mistake-pair">
                                <span class="wrong-word">${originalUserWord || userNormalized}</span>
                                <span class="arrow">→</span>
                                <span class="correct-word">${originalCorrectWord || correctNormalized}</span>
                            </div>
                        `;
                        hasVisibleMistakes = true;
                    }
                    
                    const spellingErrorCount = Math.min(remainingCorrect, remainingUser);
                    matchedUserWords[userNormalized] = (matchedUserWords[userNormalized] || 0) + spellingErrorCount;
                    matchedCorrectWords[correctNormalized] = (matchedCorrectWords[correctNormalized] || 0) + spellingErrorCount;
                    
                    remainingCorrect -= spellingErrorCount;
                    if (remainingCorrect <= 0) break;
                }
            }
        }
    }
    
    // 3. Eksik kelimeleri göster
    for (const normalizedWord in correctWordCount) {
        const matchedCount = matchedCorrectWords[normalizedWord] || 0;
        if (matchedCount < correctWordCount[normalizedWord]) {
            const missing = correctWordCount[normalizedWord] - matchedCount;
            const originalWord = correctWords.find(w => normalizeWord(w.replace(/[.,]/g, '')) === normalizedWord);
            
            for (let i = 0; i < missing; i++) {
                mistakeHtml += `
                    <div class="mistake-pair">
                        <span class="wrong-word incomplete">incomplete</span>
                        <span class="arrow">→</span>
                        <span class="correct-word">${originalWord || normalizedWord}</span>
                    </div>
                `;
                hasVisibleMistakes = true;
            }
        }
    }
    
    // 4. Fazla kelimeleri göster
    for (const normalizedWord in userWordCount) {
        const matchedCount = matchedUserWords[normalizedWord] || 0;
        if (matchedCount < userWordCount[normalizedWord]) {
            const extra = userWordCount[normalizedWord] - matchedCount;
            const originalWord = userWords.find(w => normalizeWord(w.replace(/[.,]/g, '')) === normalizedWord);
            
            for (let i = 0; i < extra; i++) {
                mistakeHtml += `
                    <div class="mistake-pair">
                        <span class="wrong-word extra">extra</span>
                        <span class="arrow">→</span>
                        <span class="correct-word">${originalWord || normalizedWord}</span>
                    </div>
                `;
                hasVisibleMistakes = true;
            }
        }
    }
    
    mistakeHtml += '</div>';
    
    if (!hasVisibleMistakes) {
        return '<div class="no-mistakes">Doğru Cümle</div>';
    }
    
    return mistakeHtml;
}

// Hata detaylarını oluştur
function generateMistakeDetails(correctText, userText) {
    if (typeof correctText !== 'string') correctText = String(correctText || '');
    if (typeof userText !== 'string') userText = String(userText || '');
    
    if (!correctText.trim()) return '<div class="no-mistakes">Doğru cümle bulunamadı</div>';
    if (!userText.trim()) return '<div class="no-mistakes">Cümle yazılmamış</div>';
    
    const correctWords = removePunctuation(correctText.trim()).split(/\s+/);
    const userWords = removePunctuation(userText.trim()).split(/\s+/);
    
    let mistakeHtml = '<div class="mistake-details">';
    
    // Kelimeleri karşılaştır
    const maxLength = Math.max(correctWords.length, userWords.length);
    
    for (let i = 0; i < maxLength; i++) {
        const correctWord = correctWords[i] || '';
        const userWord = userWords[i] || '';
        
        if (correctWord && userWord) {
            // Her iki kelime de var
            if (correctWord.toLowerCase() !== userWord.toLowerCase()) {
                // Yanlış kelime
                mistakeHtml += `
                    <div class="mistake-pair">
                        <span class="wrong-word">${userWord}</span>
                        <span class="arrow">→</span>
                        <span class="correct-word">${correctWord}</span>
            </div>
                `;
            }
        } else if (correctWord && !userWord) {
            // Eksik kelime
            mistakeHtml += `
                <div class="mistake-pair">
                    <span class="wrong-word incomplete">incomplete</span>
                    <span class="arrow">→</span>
                    <span class="correct-word">${correctWord}</span>
                </div>
            `;
        } else if (!correctWord && userWord) {
            // Fazla kelime
            mistakeHtml += `
                <div class="mistake-pair">
                    <span class="wrong-word extra">extra</span>
                    <span class="arrow">→</span>
                    <span class="correct-word">${userWord}</span>
                </div>
            `;
        }
    }
    
    mistakeHtml += '</div>';
    
    return mistakeHtml || '<div class="no-mistakes">Hata bulunamadı</div>';
}

// Cümledeki hata sayısını hesapla (backend ile aynı mantık)
function calculateSentenceErrors(correctText, userText, debugLog) {
    if (typeof correctText !== 'string') {
        correctText = correctText ? String(correctText) : '';
    }
    if (typeof userText !== 'string') {
        userText = userText ? String(userText) : '';
    }
    if (!correctText || !userText) return correctText ? correctText.split(' ').length : 0;
    const normalizedCorrectText = normalizeText(correctText);
    const normalizedUserText = normalizeText(userText);
    const correctWords = removePunctuation(normalizedCorrectText).split(/\s+/).filter(w => w);
    const userWords = removePunctuation(normalizedUserText).split(/\s+/).filter(w => w);

    // Contraction ve açılım eşdeğerleri (isFuzzyMatch ile aynı olmalı)
    const contractionEquivalents = {
        "i'll": ["i will"], "I'll": ["I will"],
        "you'll": ["you will"], "You'll": ["You will"],
        "he'll": ["he will"], "He'll": ["He will"],
        "she'll": ["she will"], "She'll": ["She will"],
        "we'll": ["we will"], "We'll": ["We will"],
        "they'll": ["they will"], "They'll": ["They will"],
        "i'm": ["i am"], "I'm": ["I am"],
        "you're": ["you are"], "You're": ["You are"],
        "he's": ["he is", "he has"], "He's": ["He is", "He has"],
        "she's": ["she is", "she has"], "She's": ["She is", "She has"],
        "it's": ["it is", "it has"], "It's": ["It is", "It has"],
        "we're": ["we are"], "We're": ["We are"],
        "they're": ["they are"], "They're": ["They are"],
        "i've": ["i have"], "I've": ["I have"],
        "you've": ["you have"], "You've": ["You have"],
        "we've": ["we have"], "We've": ["We have"],
        "they've": ["they have"], "They've": ["They have"],
        "isn't": ["is not"], "Isn't": ["Is not"],
        "aren't": ["are not"], "Aren't": ["Are not"],
        "wasn't": ["was not"], "Wasn't": ["Was not"],
        "weren't": ["were not"], "Weren't": ["Were not"],
        "don't": ["do not"], "Don't": ["Do not"],
        "doesn't": ["does not"], "Doesn't": ["Does not"],
        "didn't": ["did not"], "Didn't": ["Did not"],
        "won't": ["will not"], "Won't": ["Will not"],
        "wouldn't": ["would not"], "Wouldn't": ["Would not"],
        "can't": ["cannot"], "Can't": ["Cannot"],
        "couldn't": ["could not"], "Couldn't": ["Could not"],
        "shouldn't": ["should not"], "Shouldn't": ["Should not"],
        "mustn't": ["must not"], "Mustn't": ["Must not"],
        "shan't": ["shall not"], "Shan't": ["Shall not"],
        "let's": ["let us"], "Let's": ["Let us"]
    };
    // Açılımlardan contraction'a da bakmak için tersini de ekle
    const expansionEquivalents = {};
    Object.entries(contractionEquivalents).forEach(([contraction, expansions]) => {
        expansions.forEach(exp => {
            expansionEquivalents[exp] = expansionEquivalents[exp] || [];
            expansionEquivalents[exp].push(contraction);
        });
    });

    // Normalizasyon fonksiyonu
    const normalizeWord = (word) => {
        return word.toLowerCase().replace(/[ıİI]/g, 'i');
    };

    let i = 0, j = 0;
    let totalErrors = 0;
    if (debugLog) debugLog.push({type: 'init', correctWords, userWords});
    while (i < correctWords.length || j < userWords.length) {
        if (i >= correctWords.length && j >= userWords.length) break;
        if (i >= correctWords.length) { totalErrors++; j++; if (debugLog) debugLog.push({type: 'user_extra', i, j, totalErrors}); continue; }
        if (j >= userWords.length) { totalErrors++; i++; if (debugLog) debugLog.push({type: 'correct_missing', i, j, totalErrors}); continue; }

        const cWord = correctWords[i];
        const uWord = userWords[j];
        const cNorm = normalizeWord(cWord);
        const uNorm = normalizeWord(uWord);

        if (debugLog) debugLog.push({type: 'compare', i, j, cWord, uWord});

        if (cNorm === uNorm || isFuzzyMatch(cWord, uWord)) {
            if (debugLog) debugLog.push({type: 'match', i, j, cWord, uWord});
            i++; j++; continue;
        }

        let matched = false;
        // Doğru cevapta contraction, öğrencide açılım (ör: I'll == I will)
        if (contractionEquivalents[cWord]) {
            for (const expansion of contractionEquivalents[cWord]) {
                const expansionArr = expansion.split(' ');
                const userSlice = userWords.slice(j, j + expansionArr.length).map(w => normalizeWord(w));
                if (userSlice.length === expansionArr.length && userSlice.every((w, idx) => w === normalizeWord(expansionArr[idx]))) {
                    if (debugLog) debugLog.push({type: 'contraction_match', i, j, cWord, userSlice});
                    i++; j += expansionArr.length;
                    matched = true;
                    break;
                }
            }
            if (matched) continue;
        }
        // Öğrenci cevabında contraction, doğru cevapta açılım (ör: I will == I'll)
        for (const expansion in expansionEquivalents) {
            const expansionArr = expansion.split(' ');
            if (i + expansionArr.length - 1 < correctWords.length) {
                const correctSlice = correctWords.slice(i, i + expansionArr.length).map(w => normalizeWord(w));
                if (correctSlice.length === expansionArr.length && correctSlice.every((w, idx) => w === normalizeWord(expansionArr[idx]))) {
                    const contractions = expansionEquivalents[expansion];
                    for (const contraction of contractions) {
                        if (uNorm === normalizeWord(contraction)) {
                            if (debugLog) debugLog.push({type: 'expansion_match', i, j, uWord, correctSlice});
                            i += expansionArr.length;
                            j++;
                            matched = true;
                            break;
                        }
                    }
                }
            }
            if (matched) break;
        }
        if (matched) continue;
        if (debugLog) debugLog.push({type: 'error', i, j, cWord, uWord});
        totalErrors++;
        i++;
        j++;
    }
    if (debugLog) debugLog.push({type: 'result', totalErrors});
    return totalErrors;
}

// Fuzzy matching fonksiyonu - devre dışı
function isFuzzyMatch(word1, word2) {
    return false;
}

// calculateSentenceErrors fonksiyonunda sadece tam eşleşme ve contraction/expansion kontrolü var (zaten var, isFuzzyMatch artık false dönecek)

// Kelimeleri vurgula
function highlightWords(text, compareText, type) {
    // Veri tipi kontrolü
    if (typeof text !== 'string') {
        text = text ? String(text) : '';
    }
    if (typeof compareText !== 'string') {
        compareText = compareText ? String(compareText) : '';
    }
    
    if (!text) return '<span class="word-highlight word-missing">Boş bırakıldı</span>';
    
    const words = text.replace(/[.,]/g, '').split(/\s+/).filter(w => w);
    const compareWords = compareText ? compareText.replace(/[.,]/g, '').split(/\s+/).filter(w => w) : [];
    
    const normalizeWord = (word) => {
        let normalized = word.toLowerCase();
        normalized = normalized.normalize('NFD');
        normalized = normalized.replace(/[ıİI]/g, 'i');
        normalized = normalized.replace(/\u0307/g, '');
        return normalized.normalize('NFC');
    };
    
    if (type === 'correct') {
        // Doğru cevap için: hep yeşil (correct-sentence CSS'i ile)
        return words.map(word => `<span class="word-highlight">${word}</span>`).join(' ');
    }
    
    // Kullanıcı cevabı için gelişmiş analiz
    // Doğru cevaptaki kelime sayılarını say
    const correctWordCounts = {};
    compareWords.forEach(word => {
        const normalized = normalizeWord(word);
        correctWordCounts[normalized] = (correctWordCounts[normalized] || 0) + 1;
    });
    
    // Kullanıcının kullandığı kelime sayılarını takip et
    const usedWordCounts = {};
    
    // Önce kullanıcının yazdığı kelimeleri normal şekilde işle
    let result = words.map((word, index) => {
        let wordClass = 'word-highlight';
        const normalizedWord = normalizeWord(word);
        
        // Bu kelimeyi kaç kez kullandığımızı say
        usedWordCounts[normalizedWord] = (usedWordCounts[normalizedWord] || 0) + 1;
        
        if (correctWordCounts[normalizedWord]) {
            // Bu kelime doğru cevapda var mı ve henüz limitini aştık mı?
            if (usedWordCounts[normalizedWord] <= correctWordCounts[normalizedWord]) {
                // Kelime var ve henüz fazla kullanmadık - yeşil
                wordClass += ' word-correct';
            } else {
                // Bu kelimeyi fazla kullandık - turuncu
                wordClass += ' word-extra';
            }
        } else {
            // Bu kelime doğru cevapın hiçbir yerinde yok
            // Benzer bir kelime var mı kontrol et (yazım hatası)
            const similarWord = compareWords.find(cw => {
                const distance = levenshteinDistance(normalizedWord, normalizeWord(cw));
                return distance <= 2 && Math.abs(word.length - cw.length) <= 2;
            });
            
            if (similarWord) {
                // Yazım hatası - kırmızı
                wordClass += ' word-wrong';
            } else {
                // Toplam hata - turuncu
                wordClass += ' word-extra';
            }
        }
        
        return `<span class="${wordClass}">${word}</span>`;
    });
    
         // Şimdi eksik kelimeleri doğru pozisyonlarına ekle
     // Doğru cevaptaki her kelimeyi kontrol et
     const userNormalizedWords = words.map(w => normalizeWord(w));
     let insertOffset = 0;
     
     compareWords.forEach((correctWord, correctIndex) => {
         const normalizedCorrect = normalizeWord(correctWord);
         const expectedCount = compareWords.filter(w => normalizeWord(w) === normalizedCorrect).length;
         const foundCount = userNormalizedWords.filter(w => w === normalizedCorrect).length;
         
         if (foundCount < expectedCount) {
             // Bu kelime eksik olabilir, ama önce benzer yazılmış kelime var mı kontrol et
             let hasSimilarWord = false;
             
             // Kullanıcının yazdığı kelimelerde benzer olanı ara
             for (let userWord of words) {
                 const distance = levenshteinDistance(normalizeWord(userWord), normalizedCorrect);
                 const maxLength = Math.max(userWord.length, correctWord.length);
                 
                 if (distance <= 2 && distance < maxLength * 0.5) {
                     // Benzer kelime bulundu, bu yüzden eksik değil hatalı yazılmış
                     hasSimilarWord = true;
                     break;
                 }
             }
             
             if (!hasSimilarWord) {
                 // Benzer kelime yok, gerçekten eksik - doğru pozisyonuna ekle
                 const insertPosition = correctIndex + insertOffset;
                 const missingWordHTML = `<span class="word-highlight word-missing" title="Eksik kelime">${correctWord}</span>`;
                 
                 if (insertPosition <= result.length) {
                     result.splice(insertPosition, 0, missingWordHTML);
                     insertOffset++;
                 } else {
                     result.push(missingWordHTML);
                 }
             }
         }
     });
    
    return result.join(' ');
}

// Gelişmiş kelime karşılaştırması oluştur
function generateWordComparison(correctText, userText) {
    if (!correctText) {
        return '<div class="comparison-error">Doğru cevap bulunamadı</div>';
    }

    if (!userText) {
        return '<div class="comparison-empty">Cevap verilmedi</div>';
    }

    // Kelimeleri normalize et (i/ı duyarsız)
    const normalizeWord = (word) => {
        return word.toLowerCase()
            .replace(/[^\w\s]/g, '')
            .replace(/[ıİI]/g, 'i');
    };

    // Sadece noktalama işaretlerini kaldır, harfleri koru
        const correctWords = correctText.replace(/[.,]/g, '').split(/\s+/).filter(w => w);
    const userWords = userText.replace(/[.,]/g, '').split(/\s+/).filter(w => w);
    
    // Kelime eşleştirme matrisi oluştur
    const matchMatrix = createWordMatchMatrix(correctWords, userWords);

    let html = '<div class="word-comparison-container">';

    // Doğru cevap satırı
    html += '<div class="comparison-row correct-row">';
    html += '<div class="row-label">Doğru:</div>';
    html += '<div class="words-container">';

    // Doğru kelimeleri sırayla göster
    correctWords.forEach((word, index) => {
        const matchInfo = matchMatrix.correctMatches[index];
        let wordClass = 'word-item';
        let statusIcon = '';
        let tooltip = '';

        if (matchInfo && matchInfo.matched) {
            // Tüm eşleşmeleri mükemmel olarak göster (i/ı, case-insensitive dahil)
            wordClass += ' word-perfect';
            statusIcon = '✓';
            tooltip = 'Mükemmel! Doğru kelime';
        } else {
            wordClass += ' word-missing';
            statusIcon = '✗';
            tooltip = 'Eksik kelime';
        }

        html += `<span class="${wordClass}" data-position="${index + 1}" title="${tooltip}">
            <span class="word-text">${word}</span>
            <span class="word-status">${statusIcon}</span>
        </span>`;
    });

    html += '</div></div>';

    // Kullanıcı cevabı satırı
    html += '<div class="comparison-row user-row">';
    html += '<div class="row-label">Yazdığınız:</div>';
    html += '<div class="words-container">';

    // Kullanıcı kelimelerini sırayla göster
    userWords.forEach((word, index) => {
        const matchInfo = matchMatrix.userMatches[index];
        let wordClass = 'word-item';
        let statusIcon = '';
        let tooltip = '';

        if (matchInfo && matchInfo.matched) {
            // Tüm eşleşmeleri mükemmel olarak göster
            wordClass += ' word-perfect';
            statusIcon = '✓';
            tooltip = `Mükemmel! "${matchInfo.matchedWord}" ile eşleşiyor`;
        } else if (matchInfo) {
            if (matchInfo.isWrongSpelling) {
                wordClass += ' word-wrong';
                statusIcon = '~';
                tooltip = 'Yanlış yazılmış kelime (benzer bir kelime var)';
            } else {
                wordClass += ' word-extra';
                statusIcon = '+';
                tooltip = 'Fazla kelime (doğru cevapte yok)';
            }
        } else {
            // matchInfo yok - fazla kelime
            wordClass += ' word-extra';
            statusIcon = '+';
            tooltip = 'Fazla kelime (doğru cevapte yok)';
        }

        html += `<span class="${wordClass}" data-position="${index + 1}" title="${tooltip}">
            <span class="word-text">${word}</span>
            <span class="word-status">${statusIcon}</span>
        </span>`;
    });

    html += '</div></div>';

    // Analiz özeti
    html += generateComparisonSummary(matchMatrix, correctWords.length, userWords.length);

    html += '</div>';

    return html;
}

// Kelime eşleştirme matrisi oluştur (akıllı eşleştirme)
function createWordMatchMatrix(correctWords, userWords) {
    // Gelişmiş normalizasyon (i/ı, büyük/küçük harf duyarsız)
    const normalizeWord = (word) => {
        return word.toLowerCase()
            .replace(/[.,]/g, '')  // Noktalama işaretlerini kaldır
            .replace(/[ıİI]/g, 'i')         // Tüm i varyasyonlarını standart i'ye çevir
            .trim();
    };

    const matrix = {
        correctMatches: [],
        userMatches: [],
        exactMatches: 0,
        wrongWords: 0,
        missingWords: 0,
        extraWords: 0
    };

    // Kelime eşleştirme fonksiyonu
    const wordsMatch = (word1, word2) => {
        if (word1 === word2) return 'exact';
        if (word1.toLowerCase() === word2.toLowerCase()) return 'normalized';
        if (normalizeWord(word1) === normalizeWord(word2)) return 'normalized';
        return 'none';
    };

    // Kullanılan indeksleri takip et
    const usedUserIndices = new Set();
    const usedCorrectIndices = new Set();

    // 1. Aşama: Tam eşleşmeleri bul (pozisyon bazlı)
    for (let i = 0; i < Math.min(correctWords.length, userWords.length); i++) {
        const matchType = wordsMatch(correctWords[i], userWords[i]);
        if (matchType !== 'none') {
            matrix.correctMatches[i] = {
                matched: true,
                matchType: matchType,
                matchedWord: userWords[i],
                userIndex: i,
                originalWord: correctWords[i]
            };

            matrix.userMatches[i] = {
                matched: true,
                matchType: matchType,
                matchedWord: correctWords[i],
                correctIndex: i,
                originalWord: userWords[i]
            };

            matrix.exactMatches++;
            usedUserIndices.add(i);
            usedCorrectIndices.add(i);
        }
    }

    // 2. Aşama: Eşleşmemiş doğru kelimeleri yakındaki kullanıcı kelimeleriyle eşleştir
    for (let i = 0; i < correctWords.length; i++) {
        if (usedCorrectIndices.has(i)) continue;

        let bestMatch = null;
        let bestUserIndex = -1;
        let bestMatchType = 'none';

        // Yakındaki kelimeleri kontrol et (±3 pozisyon)
        for (let j = Math.max(0, i - 3); j <= Math.min(userWords.length - 1, i + 3); j++) {
            if (usedUserIndices.has(j)) continue;

            const matchType = wordsMatch(correctWords[i], userWords[j]);
            if (matchType !== 'none') {
                bestMatch = userWords[j];
                bestUserIndex = j;
                bestMatchType = matchType;
                break; // İlk eşleşmeyi al
            }
        }

        if (bestMatch) {
            matrix.correctMatches[i] = {
                matched: true,
                matchType: bestMatchType,
                matchedWord: bestMatch,
                userIndex: bestUserIndex,
                originalWord: correctWords[i]
            };

            matrix.userMatches[bestUserIndex] = {
                matched: true,
                matchType: bestMatchType,
                matchedWord: correctWords[i],
                correctIndex: i,
                originalWord: bestMatch
            };

            matrix.exactMatches++;
            usedUserIndices.add(bestUserIndex);
            usedCorrectIndices.add(i);
        }
    }

    // 3. Aşama: Eşleşmemiş kelimeleri işaretle
    for (let i = 0; i < correctWords.length; i++) {
        if (!usedCorrectIndices.has(i)) {
            matrix.correctMatches[i] = {
                matched: false,
                matchType: 'none',
                matchedWord: null,
                userIndex: -1,
                originalWord: correctWords[i]
            };
            matrix.missingWords++;
        }
    }

    for (let i = 0; i < userWords.length; i++) {
        if (!usedUserIndices.has(i)) {
            // Yanlış yazım kontrolü
            let isWrongSpelling = false;
            for (let j = 0; j < correctWords.length; j++) {
                if (usedCorrectIndices.has(j)) continue;

                const distance = levenshteinDistance(normalizeWord(userWords[i]), normalizeWord(correctWords[j]));
                const maxLength = Math.max(correctWords[j].length, userWords[i].length);
                const similarity = 1 - (distance / maxLength);

                if (similarity >= 0.7 && distance <= 2) {
                    isWrongSpelling = true;
                    break;
                }
            }

            matrix.userMatches[i] = {
                matched: false,
                matchType: 'none',
                matchedWord: null,
                correctIndex: -1,
                originalWord: userWords[i],
                isWrongSpelling: isWrongSpelling
            };

            if (isWrongSpelling) {
                matrix.wrongWords++;
            } else {
                matrix.extraWords++;
            }
        }
    }

    return matrix;
}

// Levenshtein distance hesaplama
function levenshteinDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }

    return matrix[str2.length][str1.length];
}

// Karşılaştırma özeti oluştur
function generateComparisonSummary(matrix, correctCount, userCount) {
    let html = '<div class="comparison-summary">';
    html += '<div class="summary-title">📊 Kelime Analizi</div>';
    html += '<div class="summary-stats">';

    // İstatistikler
    html += `<div class="stat-item perfect">
        <span class="stat-icon">✓</span>
        <span class="stat-text">Mükemmel: ${matrix.exactMatches}</span>
    </div>`;

    if (matrix.wrongWords > 0) {
        html += `<div class="stat-item wrong">
            <span class="stat-icon">~</span>
            <span class="stat-text">Yanlış: ${matrix.wrongWords}</span>
        </div>`;
    }

    if (matrix.missingWords > 0) {
        html += `<div class="stat-item missing">
            <span class="stat-icon">✗</span>
            <span class="stat-text">Eksik: ${matrix.missingWords}</span>
        </div>`;
    }

    if (matrix.extraWords > 0) {
        html += `<div class="stat-item extra">
            <span class="stat-icon">+</span>
            <span class="stat-text">Fazla: ${matrix.extraWords}</span>
        </div>`;
    }

    html += '</div>';

    // Başarı oranı
    const successRate = Math.round((matrix.exactMatches / correctCount) * 100);
    html += `<div class="success-rate">
        <div class="rate-label">Kelime Başarı Oranı:</div>
        <div class="rate-value ${getSuccessClass(successRate)}">${successRate}%</div>
    </div>`;

    html += '</div>';

    return html;
}

// Başarı sınıfı belirle
function getSuccessClass(rate) {
    if (rate >= 90) return 'excellent';
    if (rate >= 80) return 'good';
    if (rate >= 70) return 'average';
    return 'poor';
}

// Yardımcı fonksiyonlar
function getDifficultyBadge(level) {
    const badges = {
        'beginner': '<span class="uk-badge uk-badge-success">Beginner</span>',
        'intermediate': '<span class="uk-badge uk-badge-warning">Intermediate</span>',
        'advanced': '<span class="uk-badge uk-badge-danger">Advanced</span>'
    };
    return badges[level] || '<span class="uk-badge">-</span>';
}

function getStatusBadge(status) {
    const badges = {
        'active': '<span class="uk-badge uk-badge-primary">Aktif</span>',
        'completed': '<span class="uk-badge uk-badge-success">Tamamlandı</span>',
        'pending': '<span class="uk-badge uk-badge-warning">Bekliyor</span>',
        'expired': '<span class="uk-badge">Süresi Doldu</span>'
    };
    return badges[status] || '<span class="uk-badge">-</span>';
}

function getScoreClass(score) {
    if (score >= 90) return 'uk-badge-success';
    if (score >= 80) return 'uk-badge-primary';
    if (score >= 70) return 'uk-badge-warning';
    return 'uk-badge-danger';
}

function getScoreIcon(score) {
    if (score >= 90) return 'trophy';
    if (score >= 80) return 'happy';
    if (score >= 70) return 'check';
    return 'close';
}

function getScoreLevel(score) {
    if (score >= 90) return 'excellent';
    if (score >= 80) return 'good';
    if (score >= 70) return 'average';
    return 'poor';
}

function getScoreEmoji(score) {
    if (score >= 90) return '🏆';
    if (score >= 80) return '⭐';
    if (score >= 70) return '👍';
    return '📈';
}

function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR') + ' ' + date.toLocaleTimeString('tr-TR', {hour: '2-digit', minute: '2-digit'});
}

// Event listeners
$(document).ready(function() {
    // Submit butonu
    $('#submitDictation').click(function() {
        if (confirm('Dictation\'ı göndermek istediğinizden emin misiniz?')) {
            submitDictation();
        }
    });
    
    // Modal kapanırken timer temizle
    UIkit.util.on('#dictationModal', 'hidden', function() {
        if (window.MyDictations.dictationTimer) {
            clearInterval(window.MyDictations.dictationTimer);
            window.MyDictations.dictationTimer = null;
        }
        window.MyDictations.currentDictationData = null;
    });
});



// İlerleme raporu göster
function ilerlemeyiGor() {
    $.ajax({
        url: 'islemler/dictation_progress_api.php',
        method: 'GET',
        dataType: 'json',
        beforeSend: function() {
            showLoading('İlerleme raporu hazırlanıyor...');
        },
        success: function(response) {
            hideLoading();
            if (response.success) {
                renderProgressData(response.data);
                
                // Modal'ı aç
                const progressModal = UIkit.modal('#progressModal');
                progressModal.show();
                
                // Modal kapatma event listener'larını ekle (önceki listener'ları temizle)
                $('#progressModal').off('hidden').on('hidden', function() {
                    // Modal kapandığında temizlik işlemleri
                    $('#progressContent').empty();
                    $(document).off('keydown.progressModal');
                });
                
                // ESC tuşu ile modal kapatma
                $(document).off('keydown.progressModal').on('keydown.progressModal', function(e) {
                    if (e.key === 'Escape') {
                        progressModal.hide();
                    }
                });
                
                // Kapatma butonuna özel event listener ekle
                $('#progressModal .uk-modal-close-default').off('click').on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    progressModal.hide();
                });
            } else {
                showError('İlerleme raporu yüklenemedi: ' + response.message);
            }
        },
        error: function() {
            hideLoading();
            showError('İlerleme raporu yüklenirken hata oluştu');
        }
    });
}

function renderProgressData(data) {
    const stats = data.stats;
    const recentResults = data.recent_results;

    // Başarı seviyesi belirleme
    function getPerformanceClass(score) {
        if (score >= 90) return 'excellent';
        if (score >= 80) return 'good';
        if (score >= 70) return 'average';
        return 'poor';
    }

    function getScoreClass(score) {
        if (score >= 90) return 'uk-badge-success';
        if (score >= 80) return 'uk-badge-primary';
        if (score >= 70) return 'uk-badge-warning';
        return 'uk-badge-danger';
    }

    let content = `
        <div class="progress-modal-content">
            <!-- Genel İstatistikler -->
            <div class="progress-section-modal">
                <h3 class="section-title-modal">📊 Genel İstatistiklerim</h3>
                <div class="stats-grid-modal">
                    <div class="stat-card-modal ${getPerformanceClass(stats.average_score)}">
                        <div class="stat-number">${stats.total_attempts}</div>
                        <div class="stat-label">Toplam Deneme</div>
                    </div>
                    <div class="stat-card-modal ${getPerformanceClass(stats.average_score)}">
                        <div class="stat-number">${stats.average_score}%</div>
                        <div class="stat-label">Ortalama Skor</div>
                    </div>
                    <div class="stat-card-modal excellent">
                        <div class="stat-number">${stats.best_score}%</div>
                        <div class="stat-label">En İyi Skor</div>
                    </div>
                </div>
            </div>

            <!-- Performans Dağılımı -->
            <div class="progress-section-modal">
                <h3 class="section-title-modal">🎯 Performans Dağılımım</h3>
                <div class="performance-grid">
                    <div class="performance-item excellent">
                        <div class="performance-number fw5">${stats.excellent_count}</div>
                        <div class="performance-label fw5">Mükemmel<br>(90-100%)</div>
                    </div>
                    <div class="performance-item good">
                        <div class="performance-number fw5">${stats.good_count}</div>
                        <div class="performance-label fw5">Çok İyi<br>(80-89%)</div>
                    </div>
                    <div class="performance-item average">
                        <div class="performance-number fw5">${stats.average_count}</div>
                        <div class="performance-label fw5">İyi<br>(70-79%)</div>
                    </div>
                    <div class="performance-item poor">
                        <div class="performance-number fw5">${stats.poor_count}</div>
                        <div class="performance-label fw5">Geliştirilmeli<br>(0-69%)</div>
                    </div>
                </div>
            </div>

            <!-- Son Dictation Sonuçları -->
            <div class="progress-section-modal">
                <h3 class="section-title-modal">📈 Son Dictation Sonuçlarım</h3>
                <div class="recent-results-list">
    `;

    if (recentResults.length === 0) {
        content += `
            <div class="uk-text-center uk-padding">
                <p class="uk-text-black fw5">Henüz dictation çözmediniz. İlk dictation'ınızı çözerek ilerlemenizi takip etmeye başlayın!</p>
            </div>
        `;
    } else {
        recentResults.forEach(function(result) {
            const scoreClass = getScoreClass(result.overall_score);
            content += `
                <div class="recent-item-modal">
                    <div class="uk-flex uk-flex-between uk-flex-middle">
                        <div>
                            <h6 class="uk-margin-remove"><strong>${result.dictation_title}</strong></h6>
                            <p class="uk-text-small uk-margin-remove uk-text-black fw5">${result.formatted_date}</p>
                        </div>
                        <div class="recent-score ${scoreClass}">
                            ${result.overall_score}%
                        </div>
                    </div>
                </div>
            `;
        });
    }

    content += `
                </div>
            </div>
        </div>
    `;

    $('#progressContent').html(content);
}

function showResultDetail(submissionId) {
    // AJAX ile sonuç detayını ve sınıf istatistiklerini tek seferde al
    $.ajax({
        url: 'islemler/dictation_result_with_stats.php',
        method: 'GET',
        data: {
            submission_id: submissionId
        },
        dataType: 'json',
        beforeSend: function() {
            showLoading('Sonuç yükleniyor...');
        },
        success: function(response) {
            hideLoading();
            if (response.success) {
                // Eski sonuçları göstermek için eski sistemi kullan
                showOldDictationResult(response);
            } else {
                showError('Sonuç yüklenemedi: ' + response.message);
            }
        },
        error: function() {
            hideLoading();
            showError('Sonuç yüklenirken hata oluştu');
        }
    });
}

// Eski dictation sonuçlarını göstermek için fonksiyon
function showOldDictationResult(response) {
    
    const analysis = response.analysis;
    
    // JSON string'leri parse et
    let correctAnswers = analysis.correct_answers;
    let userAnswers = response.user_answers;
    
    // Eğer string olarak gelmişse parse et
    if (typeof correctAnswers === 'string') {
        try {
            correctAnswers = JSON.parse(correctAnswers);
        } catch (e) {
            console.error('Correct answers parse hatası:', e);
            correctAnswers = [];
        }
    }
    
    if (typeof userAnswers === 'string') {
        try {
            userAnswers = JSON.parse(userAnswers);
        } catch (e) {
            console.error('User answers parse hatası:', e);
            userAnswers = [];
        }
    }
    
    // Veritabanından gelen wrong_words değerini kullan
    const totalErrors = analysis.wrong_words || 0;
    const sentenceErrors = [];
    
    if (correctAnswers && correctAnswers.length > 0) {
        correctAnswers.forEach(function(correctAnswer, index) {
            let correctText, userText;
            
            // Yeni format kontrolü (detaylı analiz verisi)
            if (typeof correctAnswer === 'object' && correctAnswer.correct_text) {
                correctText = correctAnswer.correct_text;
                userText = correctAnswer.user_text || '';
            }
            // Eski format kontrolü (sadece string)
            else if (typeof correctAnswer === 'string') {
                correctText = correctAnswer;
                const userAnswerValues = userAnswers ? Object.values(userAnswers) : [];
                userText = userAnswerValues[index] || '';
            }
            // Fallback
            else {
                correctText = correctAnswer.sentence_text || correctAnswer.correct_text || '';
                userText = '';
            }
            
            sentenceErrors.push({
                correctText: correctText,
                userText: userText,
                mistakes: generateMistakeDetails(correctText, userText)
            });
        });
    }

    // Dictation başlığını güvenli bir şekilde al
    let dictationTitle = 'Dictation Sonuçları';
    
    // Önce response'dan al
    if (response.dictation_title) {
        dictationTitle = response.dictation_title;
    }
    // Sonra analysis'ten al
    else if (analysis && analysis.dictation_title) {
        dictationTitle = analysis.dictation_title;
    }

    // Yeni modal tasarımı
    let content = `
        <div class="new-result-container">
            <div class="result-header">
                <h2 class="dictation-title">${dictationTitle}</h2>
            </div>

            <div class="result-table-container">
                <table class="result-table">
                    <thead>
                        <tr>
                            <th class="col-number">#</th>
                            <th class="col-correct-answer">Correct Answer</th>
                            <th class="col-user-answer">Your Answer</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    // Cümleleri tabloya ekle
    const allDebugLogs = [];
    sentenceErrors.forEach(function(sentence, index) {
        const debugLog = [];
        const errorCount = calculateSentenceErrors(sentence.correctText, sentence.userText, debugLog);
        allDebugLogs.push({index, correctText: sentence.correctText, userText: sentence.userText, debugLog});
        const userClass = errorCount === 0 ? 'answer-text fw5 correct-answer-green' : 'answer-text fw5 wrong-answer-red';
        content += `
            <tr class="sentence-row">
                <td class="uk-text-center fw5" data-label="#">${index + 1}</td>
                <td class="correct-answer" data-label="Correct Answer">
                    <div class="answer-text fw5 correct">${sentence.correctText}</div>
                </td>
                <td class="user-answer" data-label="Your Answer">
                    <div class="${userClass}">${sentence.userText || 'No answer'}</div>
                </td>
            </tr>
        `;
    });
    // Tüm logları tek bir JSON olarak konsola yazdır
    console.log('Dictation Debug Log:', JSON.stringify(allDebugLogs, null, 2));

    content += `
                    </tbody>
                </table>
            </div>

            <div class="result-footer uk-text-center">
                <div class="result-stats-panel" id="resultStatsPanel">
                    <div class="stats-loading">
                        <div uk-spinner="ratio: 0.8"></div>
                        <span>İstatistikler yükleniyor...</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#resultContent').html(content);
    
    // Modal'ı aç
    const resultModal = UIkit.modal('#resultModal');
    resultModal.show();
    
    // İstatistikleri yükle (eğer response'da varsa)
    if (response.class_stats_data) {
        renderResultStats(response.class_stats_data, analysis, totalErrors);
    } else {
        loadResultStats(analysis, totalErrors);
    }
    
    // Modal kapatma event listener'larını ekle
    $('#resultModal').off('hidden').on('hidden', function() {
        $(document).off('keydown.resultModal');
    });
    
    // ESC tuşu ile modal kapatma
    $(document).off('keydown.resultModal').on('keydown.resultModal', function(e) {
        if (e.key === 'Escape') {
            resultModal.hide();
        }
    });
    
    // Modal dışına tıklayınca kapatma
    $('#resultModal .uk-modal-dialog').off('click').on('click', function(e) {
        e.stopPropagation();
    });
    
    $('#resultModal').off('click').on('click', function(e) {
        if (e.target === this) {
            resultModal.hide();
        }
    });
    
    // Kapatma butonuna özel event listener ekle
    $('#resultModal .uk-modal-close-default, #resultModal .result-modal-close').off('click').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        resultModal.hide();
    });
}

// Yardımcı mesaj fonksiyonları
function showSuccess(message) {
    Swal.fire({
        title: 'Başarılı!',
        text: message,
        icon: 'success',
        confirmButtonText: 'Tamam',
        customClass: {
            container: 'swal2-high-z-index'
        },
        didOpen: () => {
            // Z-index'i programatik olarak ayarla
            const swalContainer = document.querySelector('.swal2-container');
            if (swalContainer) {
                swalContainer.style.zIndex = '99999';
            }
        }
    });
}

function showError(message) {
    Swal.fire({
        title: 'Hata!',
        text: message,
        icon: 'error',
        confirmButtonText: 'Tamam',
        customClass: {
            container: 'swal2-high-z-index'
        },
        didOpen: () => {
            // Z-index'i programatik olarak ayarla
            const swalContainer = document.querySelector('.swal2-container');
            if (swalContainer) {
                swalContainer.style.zIndex = '99999';
            }
        }
    });
}

function showLoading(message = 'Yükleniyor...') {

}

function hideLoading() {

}

// ... dosyanın sonuna ekle ...
window.loadRecentResults = loadRecentResults;
window.loadAllDictations = loadAllDictations;

