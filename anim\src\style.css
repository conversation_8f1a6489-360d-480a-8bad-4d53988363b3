.text-container {
  z-index: 100;
  width: 100vw;
  height: 100vh;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  justify-content: center;
  align-items: center;
  font-size: 96px;
  color: white;
  opacity: 0.8;
  user-select: none;
  text-shadow: 1px 1px rgba(0,0,0,0.1);
}

/* SCSS değişkenlerini CSS'de kullanmak için :root tanımladık */
:root {
  --color-bg1: #b32ab8;
  --color-bg2: #002f88;
  --color1: #b32ab8;
  --color2: 221, 74, 255; /*pembe*/
  --color3: 9, 55, 143; /*mavi*/
  --color4: 124, 67, 153; /*kırmızı*/
  --color5: 85, 40, 95; /*acik pembe*/
  
  --color-interactive:  124, 67, 153;
  --circle-size: 75%;
  --blending: hard-light;
}

@keyframes moveInCircle {
  0% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
  100% { transform: rotate(360deg); }
}

@keyframes moveVertical {
  0% { transform: translateY(-50%); }
  50% { transform: translateY(50%); }
  100% { transform: translateY(-50%); }
}

@keyframes moveHorizontal {
  0% { transform: translateX(-50%) translateY(-10%); }
  50% { transform: translateX(50%) translateY(10%); }
  100% { transform: translateX(-50%) translateY(-10%); }
}

.gradient-bg {
  width: 100vw;
  height: 140vh;
  position: relative;
  overflow: hidden;
  background: rgb(45,49,59); background: linear-gradient(0deg, rgba(45,49,59,1) 0%, rgba(0,0,0,1) 80%, rgba(0,0,0,1) 90%);
  top: 0;
  left: 0;
}

.gradient-mask {position: absolute; bottom: 0; width: 100%; height: 250px; background: rgb(110,14,124);background: linear-gradient(180deg, rgba(110,14,124,0) 0%, rgba(45,49,59,1) 100%); z-index: 1;}

.gradient-bg svg {
  position: fixed;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
}

.gradients-container {
  filter: url(#goo) blur(40px);
  width: 100%;
  height: 100%;
}
/* Hareketli renkli alanlar */
.g1, .g2, .g3, .g4, .g5, .g6, .g7, .interactive {
  position: absolute;
  mix-blend-mode: var(--blending);
  width: var(--circle-size);
  height: var(--circle-size);
  top: calc(50% - var(--circle-size) / 2);
  left: calc(50% - var(--circle-size) / 2);
  opacity: 1;
}

.g1 {
  background: radial-gradient(circle at center, rgba(var(--color1), 0.8) 0, rgba(var(--color1), 0) 50%) no-repeat;
  transform-origin: center center;
  animation: moveVertical 30s ease infinite;
}

.g2 {
  background: radial-gradient(circle at center, rgba(var(--color2), 0.8) 0, rgba(var(--color2), 0) 50%) no-repeat;
  transform-origin: calc(50% - 400px);
  animation: moveInCircle 20s reverse infinite;
}

.g3 {
  background: radial-gradient(circle at center, rgba(var(--color3), 0.8) 0, rgba(var(--color3), 0) 50%) no-repeat;
  top: calc(50% - var(--circle-size) / 2 + 200px);
  left: calc(50% - var(--circle-size) / 2 - 500px);
  transform-origin: calc(50% + 400px);
  animation: moveInCircle 40s linear infinite;
}

.g4 {
  background: radial-gradient(circle at center, rgba(var(--color4), 0.8) 0, rgba(var(--color4), 0) 50%) no-repeat;
  transform-origin: calc(50% - 200px);
  animation: moveHorizontal 40s ease infinite;
  opacity: 0.7;
}

.g5 {
  background: radial-gradient(circle at center, rgba(var(--color5), 0.8) 0, rgba(var(--color5), 0) 50%) no-repeat;
  width: calc(var(--circle-size) * 2);
  height: calc(var(--circle-size) * 2);
  top: calc(50% - var(--circle-size));
  left: calc(50% - var(--circle-size));
  transform-origin: calc(50% - 200px) calc(50% + 200px);
  animation: moveInCircle 20s ease infinite;
}

.g6 {
  background: radial-gradient(circle at center, rgba(var(--color5), 0.8) 0, rgba(var(--color5), 0) 50%) no-repeat;
  width: calc(var(--circle-size) * 1.5);
  height: calc(var(--circle-size) * 1.5);
  bottom: calc(50% - var(--circle-size));
  right: calc(50% - var(--circle-size));
  transform-origin: calc(50% + 600px) calc(50% - 200px);
  animation: moveInCircle 45s ease infinite;
}

.g7 {
  background: radial-gradient(circle at center, rgba(var(--color3), 0.8) 0, rgba(var(--color3), 0) 50%) no-repeat;
  width: calc(var(--circle-size) * 1.2);
  height: calc(var(--circle-size) * 1.2);
  top: calc(50% - var(--circle-size));
  left: calc(50% - var(--circle-size));
  transform-origin: calc(50% - 200px) calc(50% + 600px);
  animation: moveVertical 35s ease infinite;
}

.interactive {
  background: radial-gradient(circle at center, rgba(var(--color-interactive), 0.8) 0, rgba(var(--color-interactive), 0) 50%) no-repeat;
  width: 100%;
  height: 100%;
  top: -50%;
  left: -50%;
  opacity: 0.7;
}
