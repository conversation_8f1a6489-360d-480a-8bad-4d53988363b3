{"version": 3, "sources": ["../scss/_variables.scss", "color1.scss"], "names": [], "mappings": "AAKA;AAqCA;AASA;AAeA;AAaA;AAOA;AAQA;AC7FA;EACI,ODKO;;ACJP;EACI,ODGG;;;ACCX;EACI;;;AAIA;EAEI;;;AAKJ;EAEI;;;AAKR;AAEA;EACI;;;AAIA;EAEI;;;AAKJ;EAEI;;;AAIR;EACI;;;AAGJ;EACI;;;AAIA;EAEI;;;AAIR;EACI;;AAEI;AAAA;EAEI;;;AAKZ;EACI;;AAEI;AAAA;EAEI;;;AAMR;EACI,cD7EG;;AC+EP;EACI;EACA;;;AAIR;EACI;EACA;EACA;;AACA;EACI;EACA,kBD3FG;EC4FH,cD5FG;EC6FH;;AAEJ;EAEI;;AAEJ;EAEI;EACA,kBDtGG;ECuGH,cDvGG;;AC0GH;EAEI;EACA,kBD7GD;EC8GC,cD9GD;;;ACmHX;EACI;EACA,kBDrHO;ECsHP,cDtHO;;;AC0HP;EAEI;;;AAIR;EACI;;;AAGJ;EACI,ODrIO;ECsIP;EACA;;AACA;EACI;EACA;EACA,cD3IG;EC4IH;;AAEJ;EAEI;EACA;;;AAIR;EACI;EACA;EACA;;AACA;EACI;EACA,kBD3JG;EC4JH,cD5JG;;AC8JP;EAEI;;AAEJ;EAEI;EACA,kBDrKG;ECsKH,cDtKG;;ACyKH;EAEI;EACA,kBD5KD;EC6KC,cD7KD;;;ACkLX;EACI;EACA,kBDpLO;ECqLP,cDrLO;;;ACyLP;EAEI;;;AAIR;EACI;;;AAGJ;AAAA;EAEI;;;AAGJ;EACI,ODzMO;EC0MP;EACA;EACA,cD5MO;;AC6MP;EACI;EACA,YD/MG;ECgNH,cDhNG;;ACkNP;EAEI;;AAEJ;EAEI,ODxNG;ECyNH;;AAGA;EAEI;EACA,kBD/ND;ECgOC,cDhOD;;;ACqOX;EACI;EACA,kBDvOO;ECwOP,cDxOO;;;AC4OP;EAEI;;;AAIR;EACI;;;AAGJ;EACI,ODvPO;;ACwPP;EACI,ODzPG;;;AC8PP;EAII,ODlQG;;;ACsQX;EACI;;;AAGJ;EACI;EACA,kBD5QO;EC6QP,cD7QO;;;ACiRP;EAGI,kBDpRG;;;ACyRP;EACI,kBD1RG;EC2RH,cD3RG;;AC6RP;EACI,kBD9RG;;;ACkSX;AAAA;EAEI;;;AAGJ;EACI,kBDxSO;;;AC4SP;EAGI,kBD/SG;;;ACoTP;AAAA;EAEI,kBDtTG;;;AC0TX;EACI,OD3TO;;;AC8TX;EACI;EACA,kBDhUO;ECiUP,cDjUO;;;ACsUH;EAEI,kBDxUD;;AC2UP;EACI,kBD5UG;;AC8UP;EACI,eD/UG;;;ACmVX;EACI,YDpVO;;;ACwVP;EAEI,YD1VG;;;AC8VX;EACI,kBD/VO;ECgWP;EACA;;;AAGJ;EACI;;;AAGJ;EACI,ODzWO;;;AC4WX;EACI;;;AAGJ;EACI;;;AAGJ;EACI,ODrXO;ECsXP,kBDpXQ;ECqXR,cDtXQ;;ACuXR;EACI,kBDzXG;;AC2XP;EACI;;;AAIR;EACI,ODjYO;ECkYP;;AAEI;EAEI,ODtYD;ECuYC;;AAEJ;EACI;EACA,kBD3YD;EC4YC,cD5YD;;;ACiZX;EACI,YDlZO;ECmZP;EACA;;;AAGJ;EACI,ODxZO;;;AC4ZP;EAEI,YD9ZG;;;ACkaX;EACI;IACI,YDpaG;;ECqaH;IAEI,YDvaD;;;AC4aX;EACI;EACA;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI,kBDzcO;;;AC4cX;EACI,YD7cO;;AC8cP;EAEI,YDhdG;;;ACodX;EACI;EACA;;;AAGJ;EACI;EACA;;;AAIA;EACI,cDheG;ECieH;EACA,ODleG;ECmeH,YD7dK;;AC+dT;EACI,cDteG;ECueH;EACA,ODxeG;ECyeH;;;AAIR;EACI,YD9eO;;;ACifX;EACI,ODlfO;;;ACqfX;EACI;;;AAGJ;EACI,YD1fO;;AC2fP;EACI;EACA;;;AAIR;EACI;;;AAGJ;AAAA;EAEI,ODvgBO;;;AC0gBX;EACI,ODhfG;;;ACmfP;EACI,YD/gBO;ECghBP;;AACA;EACI,oBDlhBG;;;ACshBX;EACI,YDvhBO;;;AC2hBX;AAEA;EACI;EACA,YD/hBO;;;ACqiBC;EAEI,YDviBL;;AC4iBH;EACI;EACA,YD9iBD;EC+iBC;;AAEJ;EACI;EACA,YDnjBD;ECojBC;;AACA;EACI,YDtjBL;;ACyjBH;EACI,YD1jBD;;;AC+jBX;EACI,YDhkBO;;;ACmkBX;EACI;EACA,kBDpkBQ;;;ACukBZ;EACI,kBDzkBO;;;AC4kBX;EACI,kBD7kBO;EC8kBP,cD9kBO;;;ACklBX;AAEA;EACI,ODrlBO;ECslBP;;;AAGJ;EACI;EACA;;;AAGJ;EACI,YD/lBO;;;ACkmBX;EACI;;;AAGJ;EACI,YDvmBO;ECwmBP;;;AAIA;EACI,cD7mBG;EC8mBH,YD9mBG;;ACgnBP;EACI,YDjnBG;;;ACsnBP;EAEI,YDxnBG;ECynBH,OD9lBD;;ACgmBH;EAEI,YD7nBG;;;ACioBX;EACI;;;AAIA;EACI,ODvoBG;;ACyoBP;EACI;EACA,YD3oBG;;AC4oBH;EACI;;;AAMR;AAAA;EAEI,ODrpBG;;;ACypBX;EACI;IACI,kBDtnBY;;;AC0nBpB;EACI;IACI,kBD5nBY;;;ACgoBpB;EACI,YDtqBO;;;ACyqBX;AAAA;EAEI,YD3qBO;;;AC+qBP;EACI,ODhrBG;;ACmrBH;EAEI,ODrrBD;;;AC0rBX;EACI,OD3rBO;;;AC8rBX;EACI,OD/rBO;;ACgsBP;EACI,ODjsBG;;;ACqsBX;EACI,ODtsBO;;;ACysBX;EACI,YD1sBO;;;AC6sBX;EACI,YD9sBO;EC+sBP;;;AAGJ;EACI,kBDltBQ;;;ACqtBZ;EACI;;;AAGJ;EACI;EACA;;;AAIA;AAAA;EAEI;;;AAKR;AAGI;EACI,OD3uBG;;AC6uBP;EACI;;;AAKR;AAEA;EACI,kBDtvBO;ECuvBP,cDvvBO;;ACwvBP;EACI,cDzvBG;;;AC8vBP;EACI,kBD/vBG;;ACiwBP;AAAA;EAEI,cDnwBG;;;ACwwBX;AAGA;AAAA;AAAA;EAGI,OD9wBO;;;ACmxBH;EAII;;AAIJ;EAEI,OD7xBD;;ACiyBH;EAGI,ODpyBD;;ACuyBP;EACI,ODxyBG;;AC0yBP;EACI;;AAEJ;AAAA;EAEI;;AAEJ;EACI;;;AAKJ;EACI;;AAEJ;EACI;;;AAMA;EAMI;;AAIJ;EAKI,ODh1BD;;;ACu1BH;EACI;;AAEJ;EAOI;;;AAKZ;EACI;;;AAIA;EAKI;;;AAKJ;EACI;;AAEJ;EACI,YDz3BG;EC03BH;;AAGA;EAMI;;AAEJ;EACI,OD32BL;;AC82BH;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;EAEI;;AAEJ;AAAA;AAAA;AAAA;EAII;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;AAAA;EAEI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AACA;EACI;;;AAMR;EAMI;;;AAKJ;AAAA;AAAA;AAAA;EAII;;;AAKJ;AAAA;EAEI,YD18BI", "file": "color1.css", "sourcesContent": ["\r\n$background: #f0f0f5;\r\n$default-color:#282f53;\r\n$border: #e9edf4;\r\n\r\n/*Color variables*/\r\n\r\n$primary-1:var(--primary-bg-color);\r\n$primary-01:var(--primary01);\r\n$primary-02:var(--primary02);\r\n$primary-03:var(--primary03);\r\n$primary-06:var(--primary06);\r\n$primary-09:var(--primary09);\r\n$primary-005:var(--primary005);\r\n$primary-hover:var(--primary-bg-hover);\r\n$primary-border:var(--primary-bg-border);\r\n$primary-transparent:var(--primary-transparentcolor);\r\n$darkprimary-transparent:var(--darkprimary-transparentcolor);\r\n$transparentprimary-transparent:var(--transparentprimary-transparentcolor);\r\n$secondary:#05c3fb;\r\n$pink:#fc5296;\r\n$teal:#1caf9f;\r\n$purple:#8927ec;\r\n$success:#09ad95;\r\n$warning:#f7b731;\r\n$danger:#e82646;\r\n$info:#1170e4;\r\n$orange:#fc7303;\r\n$red:#e73827;\r\n$lime:#7bd235;\r\n$dark:#343a40;\r\n$indigo:#6574cd;\r\n$cyan:#007ea7;\r\n$azure:#45aaf2;\r\n$white:#fff;\r\n$black:#000;\r\n$light:#f2f2f9;\r\n$gray:#5a6970;\r\n$green:#4ecc48;\r\n$blue:#3223f1;\r\n$yellow:#FBB034;\r\n\r\n/*Gradient variables*/\r\n\r\n$primary-gradient-1:linear-gradient(to bottom right, $primary-1 0%, #8e77fa 100%);\r\n$secondary-gradient:linear-gradient(to bottom right, #82cff2 0%, #28b7f9 100%);\r\n$warning-gradient:linear-gradient(to bottom right, #f66b4e 0%, #fbc434 100%);\r\n$info-gradient:linear-gradient(to bottom right, #1e63c3 0%, #00f2fe 100%);\r\n$danger-gradient:linear-gradient(to bottom right, #b51b35 0%, #fd4a68 100%);\r\n$success-gradient:linear-gradient(to bottom right, #1ea38f 0%, #5cf9e2 100%);\r\n\r\n/*white variables*/\r\n\r\n$white-1:rgba(255, 255, 255, 0.1);\r\n$white-2:rgba(255, 255, 255, 0.2);\r\n$white-3:rgba(255, 255, 255, 0.3);\r\n$white-4:rgba(255, 255, 255, 0.4);\r\n$white-5:rgba(255, 255, 255, 0.5);\r\n$white-6:rgba(255, 255, 255, 0.6);\r\n$white-7:rgba(255, 255, 255, 0.7);\r\n$white-8:rgba(255, 255, 255, 0.8);\r\n$white-9:rgba(255, 255, 255, 0.9);\r\n$white-05:rgba(255, 255, 255, 0.05);\r\n$white-08:rgba(255, 255, 255, 0.08);\r\n$white-75:rgba(255, 255, 255, 0.075);\r\n\r\n/*black variables*/\r\n\r\n$black-1:rgba(0, 0, 0, 0.1);\r\n$black-2:rgba(0, 0, 0, 0.2);\r\n$black-3:rgba(0, 0, 0, 0.3);\r\n$black-4:rgba(0, 0, 0, 0.4);\r\n$black-5:rgba(0, 0, 0, 0.5);\r\n$black-6:rgba(0, 0, 0, 0.6);\r\n$black-7:rgba(0, 0, 0, 0.7);\r\n$black-8:rgba(0, 0, 0, 0.8);\r\n$black-9:rgba(0, 0, 0, 0.9);\r\n$black-05:rgba(0, 0, 0, 0.05);\r\n\r\n/*shadow variables*/\r\n\r\n$shadow:0 5px 15px 5px rgba(80, 102, 224, 0.08);\r\n$dark-theme:#1e2448;\r\n$dark-theme2:#16192f;\r\n$dark-theme3:#181d3e;\r\n\r\n/*Dark Theme Variables*/\r\n\r\n$dark-body:#1a1a3c;\r\n$dark-theme-1:#2a2a4a;\r\n$text-color:#dedefd;\r\n$border-dark:rgba(255, 255, 255, 0.1);\r\n$dark-card-shadow:0 3px 9px 0 rgba(28, 28, 51, 0.15);\r\n\r\n/*Transparent variables*/\r\n\r\n$transparent-primary:$primary-1;\r\n$transparent-theme:rgba(0, 0, 0, 0.2);\r\n$transparent-body:var(--transparent-body);\r\n$transparent-border:rgba(255, 255, 255, 0.2);", "@import \"../scss/variables\";\r\na {\r\n    color: $primary-1;\r\n    &:hover {\r\n        color: $primary-1;\r\n    }\r\n}\r\n\r\n.bg-primary {\r\n    background: $primary-1 !important;\r\n}\r\n\r\na.bg-primary {\r\n    &:hover,\r\n    &:focus {\r\n        background-color: $primary-1 !important;\r\n    }\r\n}\r\n\r\nbutton.bg-primary {\r\n    &:hover,\r\n    &:focus {\r\n        background-color: $primary-1 !important;\r\n    }\r\n}\r\n\r\n\r\n/*--- gradient-backgrounds --*/\r\n\r\n.bg-primary-gradient {\r\n    background: $primary-gradient-1 !important;\r\n}\r\n\r\na.bg-primary-gradient {\r\n    &:hover,\r\n    &:focus {\r\n        background-color: $primary-gradient-1 !important;\r\n    }\r\n}\r\n\r\nbutton.bg-primary-gradient {\r\n    &:hover,\r\n    &:focus {\r\n        background-color: $primary-gradient-1 !important;\r\n    }\r\n}\r\n\r\n.border-primary {\r\n    border-color: $primary-1 !important;\r\n}\r\n\r\n.text-primary {\r\n    color: $primary-1 !important;\r\n}\r\n\r\na.text-primary {\r\n    &:hover,\r\n    &:focus {\r\n        color: $primary-1 !important;\r\n    }\r\n}\r\n\r\n.table-primary {\r\n    background-color: #d2cdf9;\r\n    > {\r\n        th,\r\n        td {\r\n            background-color: #d2cdf9;\r\n        }\r\n    }\r\n}\r\n\r\n.table-hover .table-primary:hover {\r\n    background-color: #b7cded;\r\n    > {\r\n        td,\r\n        th {\r\n            background-color: #b7cded;\r\n        }\r\n    }\r\n}\r\n\r\n.select2-container--default {\r\n    &.select2-container--focus .select2-selection--multiple {\r\n        border-color: $primary-1;\r\n    }\r\n    .select2-selection--multiple .select2-selection__choice {\r\n        background-color: $primary-1 !important;\r\n        border: 1px solid $primary-1 !important;\r\n    }\r\n}\r\n\r\n.btn-primary {\r\n    color: $white!important;\r\n    background: $primary-1 !important;\r\n    border-color: $primary-1 !important;\r\n    &:hover {\r\n        color: #fff;\r\n        background-color: $primary-1;\r\n        border-color: $primary-1;\r\n        opacity: 0.8;\r\n    }\r\n    &:focus,\r\n    &.focus {\r\n        opacity: 0.9;\r\n    }\r\n    &.disabled,\r\n    &:disabled {\r\n        color: #fff;\r\n        background-color: $primary-1;\r\n        border-color: $primary-1;\r\n    }\r\n    &:not(:disabled):not(.disabled) {\r\n        &:active,\r\n        &.active {\r\n            color: #fff;\r\n            background-color: $primary-1;\r\n            border-color: $primary-1;\r\n        }\r\n    }\r\n}\r\n\r\n.show>.btn-primary.dropdown-toggle {\r\n    color: #fff;\r\n    background-color: $primary-1;\r\n    border-color: $primary-1;\r\n}\r\n\r\n.btn-primary:not(:disabled):not(.disabled) {\r\n    &:active:focus,\r\n    &.active:focus {\r\n        box-shadow: 0 0 0 2px $primary-09;\r\n    }\r\n}\r\n\r\n.show>.btn-primary.dropdown-toggle:focus {\r\n    box-shadow: 0 0 0 2px $primary-09;\r\n}\r\n\r\n.btn-primary-light {\r\n    color: $primary-1;\r\n    background: $primary-02 !important;\r\n    border-color: $primary-02 !important;\r\n    &:hover {\r\n        color: #fff;\r\n        background-color: $primary-1 !important;\r\n        border-color: $primary-1;\r\n        opacity: 0.9;\r\n    }\r\n    &:focus,\r\n    &.focus {\r\n        box-shadow: 0 0 0 2px $primary-09;\r\n        opacity: 0.9;\r\n    }\r\n}\r\n\r\n.btn-primary-gradient {\r\n    color: #fff;\r\n    background: $primary-1 !important;\r\n    border-color: $primary-1 !important;\r\n    &:hover {\r\n        color: #fff;\r\n        background-color: $primary-1;\r\n        border-color: $primary-1;\r\n    }\r\n    &:focus,\r\n    &.focus {\r\n        box-shadow: 0 0 0 2px $primary-09;\r\n    }\r\n    &.disabled,\r\n    &:disabled {\r\n        color: #fff;\r\n        background-color: $primary-1;\r\n        border-color: $primary-1;\r\n    }\r\n    &:not(:disabled):not(.disabled) {\r\n        &:active,\r\n        &.active {\r\n            color: #fff;\r\n            background-color: $primary-1;\r\n            border-color: $primary-1;\r\n        }\r\n    }\r\n}\r\n\r\n.show>.btn-primary-gradient.dropdown-toggle {\r\n    color: #fff;\r\n    background-color: $primary-1;\r\n    border-color: $primary-1;\r\n}\r\n\r\n.btn-primary-gradient:not(:disabled):not(.disabled) {\r\n    &:active:focus,\r\n    &.active:focus {\r\n        box-shadow: 0 0 0 2px $primary-09;\r\n    }\r\n}\r\n\r\n.show>.btn-primary-gradient.dropdown-toggle:focus {\r\n    box-shadow: 0 0 0 2px $primary-09;\r\n}\r\n\r\n.btn-check:focus+.btn-primary,\r\n.btn-primary:focus {\r\n    box-shadow: none;\r\n}\r\n\r\n.btn-outline-primary {\r\n    color: $primary-1;\r\n    background: transparent;\r\n    background-image: none;\r\n    border-color: $primary-1;\r\n    &:hover {\r\n        color: #fff;\r\n        background: $primary-1;\r\n        border-color: $primary-1;\r\n    }\r\n    &:focus,\r\n    &.focus {\r\n        box-shadow: 0 0 0 2px $primary-01;\r\n    }\r\n    &.disabled,\r\n    &:disabled {\r\n        color: $primary-1;\r\n        background-color: transparent;\r\n    }\r\n    &:not(:disabled):not(.disabled) {\r\n        &:active,\r\n        &.active {\r\n            color: #fff;\r\n            background-color: $primary-1;\r\n            border-color: $primary-1;\r\n        }\r\n    }\r\n}\r\n\r\n.show>.btn-outline-primary.dropdown-toggle {\r\n    color: #fff;\r\n    background-color: $primary-1;\r\n    border-color: $primary-1;\r\n}\r\n\r\n.btn-outline-primary:not(:disabled):not(.disabled) {\r\n    &:active:focus,\r\n    &.active:focus {\r\n        box-shadow: 0 0 0 2px $primary-01;\r\n    }\r\n}\r\n\r\n.show>.btn-outline-primary.dropdown-toggle:focus {\r\n    box-shadow: 0 0 0 2px $primary-01;\r\n}\r\n\r\n.btn-link {\r\n    color: $primary-1;\r\n    &:hover {\r\n        color: $primary-1;\r\n    }\r\n}\r\n\r\n.dropdown-item {\r\n    &:hover,\r\n    &:focus,\r\n    &.active,\r\n    &:active {\r\n        color: $primary-1;\r\n    }\r\n}\r\n\r\n.timeline__item:after {\r\n    border: 6px solid $primary-1;\r\n}\r\n\r\n.custom-control-input:checked~.custom-control-label::before {\r\n    color: #fff;\r\n    background-color: $primary-1;\r\n    border-color: $primary-1;\r\n}\r\n\r\n.custom-checkbox .custom-control-input {\r\n    &:checked~.custom-control-label::before,\r\n    &:indeterminate~.custom-control-label::before,\r\n    &:disabled:checked~.custom-control-label::before {\r\n        background-color: $primary-1;\r\n    }\r\n}\r\n\r\n.custom-radio .custom-control-input {\r\n    &:checked~.custom-control-label::before {\r\n        background-color: $primary-1;\r\n        border-color: $primary-1;\r\n    }\r\n    &:disabled:checked~.custom-control-label::before {\r\n        background-color: $primary-1;\r\n    }\r\n}\r\n\r\n.form-select:focus,\r\n.form-file-input:focus~.form-file-label::after {\r\n    border-color: none;\r\n}\r\n\r\n.form-file-label::after {\r\n    background-color: $primary-1;\r\n}\r\n\r\n.custom-range {\r\n    &::-webkit-slider-thumb,\r\n    &::-moz-range-thumb,\r\n    &::-ms-thumb {\r\n        background-color: $primary-1;\r\n    }\r\n}\r\n\r\n.nav-pills {\r\n    .nav-link.active,\r\n    .show>.nav-link {\r\n        background-color: $primary-1;\r\n    }\r\n}\r\n\r\n.page-link:hover {\r\n    color: $primary-1;\r\n}\r\n\r\n.page-item.active .page-link {\r\n    color: #fff;\r\n    background-color: $primary-1;\r\n    border-color: $primary-1;\r\n}\r\n\r\n.range.range-primary {\r\n    input[type=\"range\"] {\r\n        &::-webkit-slider-thumb,\r\n        &::-moz-slider-thumb {\r\n            background-color: $primary-1;\r\n        }\r\n    }\r\n    output {\r\n        background-color: $primary-1;\r\n    }\r\n    input[type=\"range\"] {\r\n        outline-color: $primary-1;\r\n    }\r\n}\r\n\r\n.panel.price>.panel-heading {\r\n    background: $primary-1;\r\n}\r\n\r\n.nav-tabs .nav-item1 .nav-link {\r\n    &:hover:not(.disabled),\r\n    &.active {\r\n        background: $primary-1;\r\n    }\r\n}\r\n\r\n.heading-primary {\r\n    background-color: $primary-1;\r\n    color: #ffffff;\r\n    padding: 5px;\r\n}\r\n\r\n.breadcrumb-item.active {\r\n    color: #f5f3f9;\r\n}\r\n\r\n.breadcrumb-item1 a {\r\n    color: $primary-1;\r\n}\r\n\r\n.panel-title1 a {\r\n    background: $primary-1 !important;\r\n}\r\n\r\n.btn.dropdown-toggle.btn-primary~.dropdown-menu .dropdown-plus-title {\r\n    border-color: rgb(108, 95, 252) !important;\r\n}\r\n\r\n.alert-primary {\r\n    color: $primary-1;\r\n    background-color: $primary-02;\r\n    border-color: $primary-01;\r\n    hr {\r\n        border-top-color: $primary-1;\r\n    }\r\n    .alert-link {\r\n        color: #1200d7;\r\n    }\r\n}\r\n\r\n.list-group-item-primary {\r\n    color: $primary-1;\r\n    background-color: #cbdbf2;\r\n    &.list-group-item-action {\r\n        &:hover,\r\n        &:focus {\r\n            color: $primary-1;\r\n            background-color: #b7cded;\r\n        }\r\n        &.active {\r\n            color: #fff;\r\n            background-color: $primary-1;\r\n            border-color: $primary-1;\r\n        }\r\n    }\r\n}\r\n\r\n.header-bg {\r\n    background: $primary-1;\r\n    background: -webkit-$primary-1 !important;\r\n    background: $primary-1 !important;\r\n}\r\n\r\n.footer a:not(.btn) {\r\n    color: $primary-1;\r\n}\r\n\r\n.nav-tabs .nav-link {\r\n    &:hover:not(.disabled),\r\n    &.active {\r\n        background: $primary-1;\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .animated-arrow span {\r\n        background: $primary-1;\r\n        &:before,\r\n        &:after {\r\n            background: $primary-1;\r\n        }\r\n    }\r\n}\r\n\r\n.expanel-primary>.expanel-heading {\r\n    color: $white!important;\r\n    background-color: $primary-1 !important;\r\n    border-color: $primary-1 !important;\r\n}\r\n\r\n.login-img {\r\n    position: relative;\r\n    background-image: url(../images/media/bg2.jpg);\r\n}\r\n\r\n.login-img::before {\r\n    content: '';\r\n    background: linear-gradient(to right, $primary-06 0%, #05c3fb91 100%);\r\n    height: 100%;\r\n    width: 100%;\r\n    position: absolute;\r\n}\r\n\r\n.avatar {\r\n    background: $primary-02 no-repeat center/cover;\r\n}\r\n\r\n.spinner,\r\n.spinner-lg,\r\n.double-bounce1,\r\n.double-bounce2,\r\n.cube1,\r\n.cube2 {\r\n    background-color: $primary-1;\r\n}\r\n\r\n.lds-heart div {\r\n    background: $primary-1;\r\n    &:after,\r\n    &:before {\r\n        background: $primary-1;\r\n    }\r\n}\r\n\r\n.lds-ring div {\r\n    border: 6px solid $primary-1;\r\n    border-color: $primary-1 transparent transparent transparent;\r\n}\r\n\r\n.lds-hourglass:after {\r\n    border: 26px solid $primary-1;\r\n    border-color: $primary-1 transparent;\r\n}\r\n\r\n.selectgroup-input {\r\n    &:checked+.selectgroup-button {\r\n        border-color: $primary-1;\r\n        z-index: 1;\r\n        color: $primary-1;\r\n        background: $primary-005;\r\n    }\r\n    &:focus+.selectgroup-button {\r\n        border-color: $primary-1;\r\n        z-index: 2;\r\n        color: $primary-1;\r\n        box-shadow: 0 0 0 2px $primary-02;\r\n    }\r\n}\r\n\r\n.custom-switch-input:checked~.custom-switch-indicator {\r\n    background: $primary-1;\r\n}\r\n\r\n.ui-datepicker .ui-datepicker-title {\r\n    color: $primary-1;\r\n}\r\n\r\n.timeline--horizontal .timeline-divider {\r\n    background: $primary-1 !important;\r\n}\r\n\r\n#back-to-top {\r\n    background: $primary-1;\r\n    &:hover {\r\n        color: $primary-1 !important;\r\n        border: 2px solid $primary-1;\r\n    }\r\n}\r\n\r\n.tabs-menu1 ul li .active {\r\n    border-bottom: 1px solid $primary-1;\r\n}\r\n\r\n.tab-content i,\r\n.tabs-menu2 ul li .active {\r\n    color: $primary-1;\r\n}\r\n\r\n.tab-content .btn i {\r\n    color: $white;\r\n}\r\n\r\n.message-feed:not(.right) .mf-content {\r\n    background: $primary-1;\r\n    color: #fff;\r\n    &:before {\r\n        border-right-color: $primary-1;\r\n    }\r\n}\r\n\r\n.msb-reply button {\r\n    background: $primary-1;\r\n}\r\n\r\n\r\n/* --------Added--------- css*/\r\n\r\n.wizard-card .moving-tab {\r\n    margin-top: 5px;\r\n    background: $primary-1;\r\n}\r\n\r\n.cal1 .clndr {\r\n    .clndr-table {\r\n        tr .day {\r\n            &.today.event,\r\n            &.my-today.event {\r\n                background: $primary-1;\r\n            }\r\n        }\r\n    }\r\n    .clndr-controls .clndr-control-button {\r\n        .clndr-previous-button {\r\n            color: #fff;\r\n            background: $primary-1;\r\n            border-radius: 5px;\r\n        }\r\n        .clndr-next-button {\r\n            color: #fff;\r\n            background: $primary-1;\r\n            border-radius: 5px;\r\n            &:hover {\r\n                background: $primary-1;\r\n            }\r\n        }\r\n        .clndr-previous-button:hover {\r\n            background: $primary-1;\r\n        }\r\n    }\r\n}\r\n\r\n.fc button {\r\n    background: $primary-1;\r\n}\r\n\r\n.fc-event {\r\n    color: $primary-1 !important;\r\n    background-color: $primary-01;\r\n}\r\n\r\n.fc-event-dot {\r\n    background-color: $primary-1;\r\n}\r\n\r\n.form-check-input:checked {\r\n    background-color: $primary-1;\r\n    border-color: $primary-1;\r\n}\r\n\r\n\r\n/* ----Added css----*/\r\n\r\nform.convFormDynamic button.submit {\r\n    color: $primary-1;\r\n    border: 1px solid $primary-1 !important;\r\n}\r\n\r\nform.convFormDynamic button.submit:hover {\r\n    background: $primary-1 !important;\r\n    color: #fff;\r\n}\r\n\r\ndiv.conv-form-wrapper div#messages div.message.from {\r\n    background: $primary-1;\r\n}\r\n\r\n.timeline__item:after {\r\n    border: 6px solid $primary-1;\r\n}\r\n\r\n.accordionjs .acc_section.acc_active>.acc_head {\r\n    background: $primary-1;\r\n    color: $white!important;\r\n}\r\n\r\n.tab_wrapper {\r\n    >ul li.active {\r\n        border-color: $primary-1;\r\n        background: $primary-1;\r\n    }\r\n    &.right_side>ul li.active:after {\r\n        background: $primary-1;\r\n    }\r\n}\r\n\r\n.cal1 .clndr .clndr-table tr .day {\r\n    &.today,\r\n    &.my-today {\r\n        background: $primary-1;\r\n        color: $white;\r\n    }\r\n    &.today:hover,\r\n    &.my-today:hover {\r\n        background: $primary-1;\r\n    }\r\n}\r\n\r\n.primary .pricing-divider {\r\n    background: $white !important;\r\n}\r\n\r\n.product-grid6 {\r\n    .title a:hover {\r\n        color: $primary-1;\r\n    }\r\n    .icons li a {\r\n        color: #fff;\r\n        background: $primary-1;\r\n        &:hover {\r\n            color: #fff;\r\n        }\r\n    }\r\n}\r\n\r\n#user-profile .profile-details {\r\n    a>i,\r\n    i {\r\n        color: $primary-1;\r\n    }\r\n}\r\n\r\n@media (max-width: 767.98px) and (min-width: 576px) {\r\n    .search-show .search-element {\r\n        background-image: $primary-gradient-1;\r\n    }\r\n}\r\n\r\n@media (max-width: 575.98px) {\r\n    .search-show .search-element {\r\n        background-image: $primary-gradient-1;\r\n    }\r\n}\r\n\r\n.text-primary-gradient {\r\n    background: $primary-1;\r\n}\r\n\r\n.chat .msg_head,\r\n.msb-reply button {\r\n    background: $primary-1;\r\n}\r\n\r\n.top-footer {\r\n    p:hover {\r\n        color: $primary-1;\r\n    }\r\n    a {\r\n        address:hover,\r\n        &:hover {\r\n            color: $primary-1;\r\n        }\r\n    }\r\n}\r\n\r\n.footer-payments a:hover {\r\n    color: $primary-1;\r\n}\r\n\r\n.footer-social-list a {\r\n    color: $primary-1;\r\n    &:hover {\r\n        color: $primary-1;\r\n    }\r\n}\r\n\r\n.sw-theme-dots .nav-tabs .nav-link:hover:not(.disabled) {\r\n    color: $primary-1;\r\n}\r\n\r\n.custom-switch-input:checked~.custom-switch-indicator {\r\n    background: $primary-1;\r\n}\r\n\r\n.label-primary {\r\n    background: $primary-1;\r\n    color: #fff;\r\n}\r\n\r\n.bg-primary-transparent {\r\n    background-color: $primary-01;\r\n}\r\n\r\n.text-primary-shadow {\r\n    text-shadow: 0 5px 10px $primary-03;\r\n}\r\n\r\n.chart-dropshadow-primary {\r\n    -webkit-filter: drop-shadow((-6px) 12px 4px $primary-02);\r\n    filter: drop-shadow((-6px) 12px 4px $primary-02);\r\n}\r\n\r\n.fc-toolbar {\r\n    .fc-state-active,\r\n    .ui-state-active {\r\n        background: #6155e2;\r\n    }\r\n}\r\n\r\n\r\n/*-- Sidemenu --*/\r\n\r\n.side-menu__item {\r\n    &.active {\r\n        color: $primary-1;\r\n    }\r\n    &.active .side-menu__icon {\r\n        color: $primary-1 !important;\r\n    }\r\n}\r\n\r\n\r\n/*-- Horizontal-menu --*/\r\n\r\n.onoffswitch-checkbox:checked+.onoffswitch-label {\r\n    background-color: $primary-1;\r\n    border-color: $primary-1;\r\n    &:before {\r\n        border-color: $primary-1;\r\n    }\r\n}\r\n\r\n.onoffswitch2-checkbox:checked+ {\r\n    .onoffswitch2-label {\r\n        background-color: $primary-1;\r\n    }\r\n    .onoffswitch-label2,\r\n    .onoffswitch2-label:before {\r\n        border-color: $primary-1;\r\n    }\r\n}\r\n\r\n\r\n/*--Dark-mode---*/\r\n\r\n\r\n.dropdown-item:focus,\r\n.dark-mode .dropdown-item.active,\r\n.dropdown-item:active {\r\n    color: $primary-1;\r\n}\r\n\r\n.dark-mode {\r\n    .side-menu__item {\r\n        &.active,\r\n        &:hover,\r\n        &:focus,\r\n        &.active .side-menu__icon {\r\n            color: $primary-1 !important;\r\n        }\r\n    }\r\n    .side-menu__item {\r\n        &:hover,\r\n        &:focus {\r\n            color: $primary-1;\r\n        }\r\n    }\r\n    &.dark-menu .side-menu__item {\r\n        &.active .side-menu__icon,\r\n        &:hover .side-menu__icon,\r\n        &:focus .side-menu__icon {\r\n            color: $primary-1;\r\n        }\r\n    }\r\n    .footer a:not(.btn) {\r\n        color: $primary-1;\r\n    }\r\n    .text-primary {\r\n        color: $primary-1 !important;\r\n    }\r\n    .panel-default>.panel-heading,\r\n    .panel-tabs a {\r\n        color: #dedefd;\r\n    }\r\n    .sidebar-icon {\r\n        color: $primary-1 !important;\r\n    }\r\n}\r\n\r\n.dark-mode .side-menu__item {\r\n    &.active {\r\n        color: #ffffff;\r\n    }\r\n    &.active .side-menu__icon {\r\n        color: #ffffff;\r\n    }\r\n}\r\n\r\n.light-menu {\r\n    &.dark-mode .side-menu__item {\r\n        &.active .side-menu__icon,\r\n        &:hover .side-menu__icon,\r\n        &:focus .side-menu__icon,\r\n        &.active,\r\n        &:hover,\r\n        &:focus {\r\n            color: #8061ce;\r\n        }\r\n    }\r\n    .side-menu__item {\r\n        &:hover .side-menu__icon,\r\n        &:focus .side-menu__icon,\r\n        &.active,\r\n        &:hover,\r\n        &:focus {\r\n            color: $primary-1;\r\n        }\r\n    }\r\n}\r\n\r\n.transparent-mode.light-menu {\r\n    .side-menu__item {\r\n        &.active .side-menu__icon {\r\n            color: $primary-1 !important;\r\n        }\r\n        &:hover .side-menu__icon,\r\n        &:hover .side-menu__label,\r\n        &:focus .side-menu__icon,\r\n        &:focus .side-menu__label,\r\n        &.active,\r\n        &:hover,\r\n        &:focus {\r\n            color: $primary-1 !important;\r\n        }\r\n    }\r\n}\r\n\r\n.default-body .app-sidebar {\r\n    background: $primary-1 !important;\r\n}\r\n\r\n.dark-menu .side-menu__item {\r\n    &.active .side-menu__icon,\r\n    &:hover .side-menu__icon,\r\n    &:focus .side-menu__icon,\r\n    &.active,\r\n    &:hover {\r\n        color: #5b8bf1;\r\n    }\r\n}\r\n\r\n.color-menu {\r\n    .app-sidebar {\r\n        background: $primary-1 !important;\r\n    }\r\n    .side-header {\r\n        background: $primary-1;\r\n        border-bottom: 1px solid $white-1;\r\n    }\r\n    .side-menu__item {\r\n        &.active .side-menu__icon,\r\n        &:hover .side-menu__icon,\r\n        &:focus .side-menu__icon,\r\n        &.active,\r\n        &:hover,\r\n        &:focus {\r\n            color: $white !important;\r\n        }\r\n        &.active {\r\n            color: $white;\r\n        }\r\n    }\r\n    .side-menu .side-menu__item.active::after {\r\n        background: #fff;\r\n    }\r\n    .side-menu .side-menu__item:hover::after {\r\n        background: #fff;\r\n    }\r\n    .side-menu h3 {\r\n        color: rgba(255, 255, 255, 0.4) !important;\r\n    }\r\n    .side-menu .side-menu__icon,\r\n    .side-menu .side-menu__item {\r\n        color: $white-7 !important;\r\n    }\r\n    .side-menu__item:hover,\r\n    .side-menu__item:focus {\r\n        color: $white!important;\r\n    }\r\n    .side-menu__item:hover .side-menu__icon,\r\n    .side-menu__item:hover .side-menu__label,\r\n    .side-menu__item:focus .side-menu__icon,\r\n    .side-menu__item:focus .side-menu__label {\r\n        color: #ffffff !important;\r\n    }\r\n    .slide.is-expanded a {\r\n        color: #e7eefb;\r\n    }\r\n    .slide-item {\r\n        color: #e7eefb;\r\n    }\r\n    .slide-item:hover,\r\n    .slide-item:focus {\r\n        color: $white!important;\r\n    }\r\n    .slide-menu li .slide-item:before {\r\n        color: #e7eefb;\r\n    }\r\n    .side-menu__item {\r\n        color: #e7eefb;\r\n    }\r\n    .side-menu-label1 {\r\n        border-bottom-color: 1px solid $border-dark !important;\r\n        a {\r\n            color: $white !important;\r\n        }\r\n    }\r\n}\r\n\r\n.gradient-menu .side-menu__item {\r\n    &.active .side-menu__icon,\r\n    &:hover .side-menu__icon,\r\n    &:focus .side-menu__icon,\r\n    &.active,\r\n    &:hover,\r\n    &:focus {\r\n        color: #ccc9ec !important;\r\n    }\r\n}\r\n\r\n.gradient-menu {\r\n    .side-menu__item:hover .side-menu__icon,\r\n    .side-menu__item:hover .side-menu__label,\r\n    .side-menu__item:focus .side-menu__icon,\r\n    .side-menu__item:focus .side-menu__label {\r\n        color: #ccc9ec;\r\n    }\r\n}\r\n\r\n.color-menu.sidemenu-bgimage {\r\n    .side-header:before,\r\n    .app-sidebar:before {\r\n        background: $primary-09;\r\n    }\r\n}"]}