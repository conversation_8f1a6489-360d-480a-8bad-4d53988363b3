=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 10:06:51
İşlem No: ORDER202509081006508001
Ödeme ID: 4041
Kullanıcı ID: 3706
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4907,25
Taksit: 3

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => fb6ccdf8-407e-4e44-bdd4-e8b656d0274b
            [UCD_HTML] => <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="tr" xmlns="http://www.w3.org/1999/xhtml">
<head>
<script type="text/javascript" src="/ruxitagentjs_ICA27NVfqrux_10263230921131557.js" data-dtconfig="rid=RID_-**********|rpid=-*********|domain=denizbank.com|reportUrl=/rb_d0ac9e6b-b11a-4747-9900-8db5d8d1e8c3|app=600f804e2f46f262|cuc=w2s3jly9|owasp=1|mel=100000|featureHash=ICA27NVfqrux|dpvc=1|lastModification=*************|tp=500,50,0,1|rdnt=1|uxrgce=1|srbbv=2|agentUri=/ruxitagentjs_ICA27NVfqrux_10263230921131557.js"></script></head>
<body>
<form id="form1" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="POST">
<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=fb6ccdf8-407e-4e44-bdd4-e8b656d0274b" />
<script type="text/javascript">
 document.getElementById("form1").submit(); 
</script>
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081006508001
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 10:07:37
İşlem No: ORDER202509081007362081
Ödeme ID: 4042
Kullanıcı ID: 2152
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 14f1104c-c002-462f-9327-e27138ff1e77
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=14f1104c-c002-462f-9327-e27138ff1e77" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081007362081
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 10:08:54
İşlem No: ORDER202509081008533400
Ödeme ID: 4043
Kullanıcı ID: 2152
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => dd224c70-0c5a-4643-afb4-085c7bfcd5c6
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=dd224c70-0c5a-4643-afb4-085c7bfcd5c6" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081008533400
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 10:09:52
İşlem No: ORDER202509081009516192
Ödeme ID: 4044
Kullanıcı ID: 3706
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4907,25
Taksit: 3

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => ********-f09c-4244-b518-0999c578e810
            [UCD_HTML] => <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="tr" xmlns="http://www.w3.org/1999/xhtml">
<head>
<script type="text/javascript" src="/ruxitagentjs_ICA27NVfqrux_10263230921131557.js" data-dtconfig="rid=RID_-**********|rpid=-**********|domain=denizbank.com|reportUrl=/rb_d0ac9e6b-b11a-4747-9900-8db5d8d1e8c3|app=600f804e2f46f262|cuc=w2s3jly9|owasp=1|mel=100000|featureHash=ICA27NVfqrux|dpvc=1|lastModification=*************|tp=500,50,0,1|rdnt=1|uxrgce=1|srbbv=2|agentUri=/ruxitagentjs_ICA27NVfqrux_10263230921131557.js"></script></head>
<body>
<form id="form1" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="POST">
<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=********-f09c-4244-b518-0999c578e810" />
<script type="text/javascript">
 document.getElementById("form1").submit(); 
</script>
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081009516192
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 10:12:43
İşlem No: ORDER202509081012427544
Ödeme ID: 4045
Kullanıcı ID: 3706
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 527351da-35f6-48eb-92ab-8b0eb3a278df
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=527351da-35f6-48eb-92ab-8b0eb3a278df" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081012427544
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 10:20:50
İşlem No: ORDER202509081020499123
Ödeme ID: 4046
Kullanıcı ID: 3908
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => b12ccca7-b9a4-4e6f-a37c-713f4d73b81d
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=b12ccca7-b9a4-4e6f-a37c-713f4d73b81d" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081020499123
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 12:21:00
İşlem No: ORDER202509081220583461
Ödeme ID: 4051
Kullanıcı ID: 3755
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 26076a96-a3e6-481b-90b4-037d9daac88b
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=26076a96-a3e6-481b-90b4-037d9daac88b" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081220583461
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 12:28:41
İşlem No: ORDER202509081228398204
Ödeme ID: 4052
Kullanıcı ID: 4134
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 1bf43abb-3be3-4ea8-bc9b-6d1e3b103a5f
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=1bf43abb-3be3-4ea8-bc9b-6d1e3b103a5f" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081228398204
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 13:11:12
İşlem No: ORDER202509081311115176
Ödeme ID: 4053
Kullanıcı ID: 4523
Paket ID: 1
Paket Adı: 4 Aylık paket
Tutar: 16000,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => c9da901a-9eea-4586-bd20-c96d2b232a3d
            [UCD_HTML] => <html>
 <head>
  <!-- troyStartSuccess.htm -->
  <title>GO</title>
  <meta http-equiv="Content-Language" content="tr">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
  <script type="text/javascript" language="javascript" nonce="vOpOIyfgkHsU2W5jpDFSV3F4KbV0HQ+zmEReRjJq22c=">
	window.onload = function() {
		document.returnform.submit();
	}
</script>
 </head>
 <body>
  <form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post">
   <input type="hidden" name="goreq" value="********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
   <noscript>
    <center>
     Devam etmek icin tiklayiniz.
     <br>
     <br>
     <input type="submit" name="submit" value="Submit" id="btnSbmt">
    </center>
   </noscript>
  </form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=c9da901a-9eea-4586-bd20-c96d2b232a3d" />
 </body>
</html>
            [UCD_MD] => ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081311115176
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 13:13:10
İşlem No: ORDER202509081313081379
Ödeme ID: 4054
Kullanıcı ID: 3050
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 4f858469-523c-47f9-8392-f266b6ca98fa
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=4f858469-523c-47f9-8392-f266b6ca98fa" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081313081379
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 13:16:14
İşlem No: ORDER202509081316137458
Ödeme ID: 4055
Kullanıcı ID: 3050
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => b99d6021-8b2d-46df-afb8-fddf5b74790e
            [UCD_HTML] => <html>
<!-- troyStartSuccess.htm -->
<title>GO</title>
	<meta http-equiv="Content-Language" content="tr">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="now">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
</head>
<body>
	<form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" >
		<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
		<noscript>
		<center>Devam etmek icin tiklayiniz.<br><br>
		<input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
	</noscript>
	</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=b99d6021-8b2d-46df-afb8-fddf5b74790e" />
	<script type="text/javascript" language="javascript">
		(function () {
			const form = document.forms['returnform'] || document.querySelector('form');
			if (form) {
				form.submit();
			}
		})();
	</script>
	</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081316137458
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 13:47:37
İşlem No: ORDER202509081347363545
Ödeme ID: 4056
Kullanıcı ID: 3565
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => fb67372d-5175-4df5-9dce-a6dd6df0f50c
            [UCD_HTML] => <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="tr" xmlns="http://www.w3.org/1999/xhtml">
<head>
<script type="text/javascript" src="/ruxitagentjs_ICA27NVfqrux_10263230921131557.js" data-dtconfig="rid=RID_-**********|rpid=-**********|domain=denizbank.com|reportUrl=/rb_d0ac9e6b-b11a-4747-9900-8db5d8d1e8c3|app=600f804e2f46f262|cuc=w2s3jly9|owasp=1|mel=100000|featureHash=ICA27NVfqrux|dpvc=1|lastModification=*************|tp=500,50,0,1|rdnt=1|uxrgce=1|srbbv=2|agentUri=/ruxitagentjs_ICA27NVfqrux_10263230921131557.js"></script></head>
<body>
<form id="form1" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="POST">
<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=fb67372d-5175-4df5-9dce-a6dd6df0f50c" />
<script type="text/javascript">
 document.getElementById("form1").submit(); 
</script>
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081347363545
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 16:00:59
İşlem No: ORDER202509081600586626
Ödeme ID: 4058
Kullanıcı ID: 3977
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 07afc9ac-bdcb-42f0-ae1c-d5c19b456e49
            [UCD_HTML] => <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="tr" xmlns="http://www.w3.org/1999/xhtml">
<head>
<script type="text/javascript" src="/ruxitagentjs_ICA27NVfqrux_10263230921131557.js" data-dtconfig="rid=RID_-**********|rpid=-*********|domain=denizbank.com|reportUrl=/rb_d0ac9e6b-b11a-4747-9900-8db5d8d1e8c3|app=600f804e2f46f262|cuc=w2s3jly9|owasp=1|mel=100000|featureHash=ICA27NVfqrux|dpvc=1|lastModification=*************|tp=500,50,0,1|rdnt=1|uxrgce=1|srbbv=2|agentUri=/ruxitagentjs_ICA27NVfqrux_10263230921131557.js"></script></head>
<body>
<form id="form1" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="POST">
<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=07afc9ac-bdcb-42f0-ae1c-d5c19b456e49" />
<script type="text/javascript">
 document.getElementById("form1").submit(); 
</script>
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081600586626
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 16:49:28
İşlem No: ORDER202509081649272265
Ödeme ID: 4059
Kullanıcı ID: 2314
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 8c357152-99fd-41f6-b2ad-d8db84283996
            [UCD_HTML] => <html>
<head>
    <title>GO</title>
    <meta http-equiv="Content-Language" content="tr">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="now">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
    <script type="text/javascript" nonce="247gbfhf2vpbb7ral83utstvl">
        window.onload = function () {
            document.returnform.submit();
        }
    </script>
</head>
<body>
<form action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post" name="returnform">
    <input name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" type="hidden"/>
    <noscript>
        <center>Devam etmek icin tiklayiniz.<br><br>
            <input type="submit" name="submit" value="Submit" id="btnSbmt"></center>
    </noscript>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=8c357152-99fd-41f6-b2ad-d8db84283996" />
</body>
</html>
            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081649272265
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 17:43:53
İşlem No: ORDER202509081743526428
Ödeme ID: 4060
Kullanıcı ID: 4667
Paket ID: 2
Paket Adı: Standart paket
Tutar: 5400,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 72a58bd7-a909-4504-9e04-290bd874bee6
            [UCD_HTML] => <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="tr" xmlns="http://www.w3.org/1999/xhtml">
<head>
<script type="text/javascript" src="/ruxitagentjs_ICA27NVfqrux_10263230921131557.js" data-dtconfig="rid=RID_-**********|rpid=-*********|domain=denizbank.com|reportUrl=/rb_d0ac9e6b-b11a-4747-9900-8db5d8d1e8c3|app=600f804e2f46f262|cuc=w2s3jly9|owasp=1|mel=100000|featureHash=ICA27NVfqrux|dpvc=1|lastModification=*************|tp=500,50,0,1|rdnt=1|uxrgce=1|srbbv=2|agentUri=/ruxitagentjs_ICA27NVfqrux_10263230921131557.js"></script></head>
<body>
<form id="form1" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="POST">
<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=72a58bd7-a909-4504-9e04-290bd874bee6" />
<script type="text/javascript">
 document.getElementById("form1").submit(); 
</script>
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081743526428
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 17:47:39
İşlem No: ORDER202509081747379663
Ödeme ID: 4061
Kullanıcı ID: 4667
Paket ID: 2
Paket Adı: Standart paket
Tutar: 5400,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 9c7aa3d7-f566-4320-a556-86868eaafbfc
            [UCD_HTML] => <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="tr" xmlns="http://www.w3.org/1999/xhtml">
<head>
<script type="text/javascript" src="/ruxitagentjs_ICA27NVfqrux_10263230921131557.js" data-dtconfig="rid=RID_-**********|rpid=**********|domain=denizbank.com|reportUrl=/rb_d0ac9e6b-b11a-4747-9900-8db5d8d1e8c3|app=600f804e2f46f262|cuc=w2s3jly9|owasp=1|mel=100000|featureHash=ICA27NVfqrux|dpvc=1|lastModification=*************|tp=500,50,0,1|rdnt=1|uxrgce=1|srbbv=2|agentUri=/ruxitagentjs_ICA27NVfqrux_10263230921131557.js"></script></head>
<body>
<form id="form1" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="POST">
<input type="hidden" name="goreq" value="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
</form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=9c7aa3d7-f566-4320-a556-86868eaafbfc" />
<script type="text/javascript">
 document.getElementById("form1").submit(); 
</script>
</body>
</html>

            [UCD_MD] => ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509081747379663
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
=== PAKET SATIN ALMA ÖDEME İŞLEMİ KAYDI ===
Tarih: 2025-09-08 20:15:19
İşlem No: ORDER202509082015177450
Ödeme ID: 4062
Kullanıcı ID: 4282
Paket ID: 11
Paket Adı: July Camp Speaking Club
Tutar: 4500,00
Taksit: 1

BANKA YANITI:
stdClass Object
(
    [TP_WMD_UCDResult] => stdClass Object
        (
            [Islem_ID] => **********
            [Islem_GUID] => 7d51453e-23b4-42a6-95de-7ea7ad995dbe
            [UCD_HTML] => <html>
 <head>
  <!-- troyStartSuccess.htm -->
  <title>GO</title>
  <meta http-equiv="Content-Language" content="tr">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="Author" content="Uygulama Gelistirme Asseco SEE tarafindan yapilmistir">
  <script type="text/javascript" language="javascript" nonce="EA/qgHMvxQujEmyv+XGiAtr5Sf9qv5Zh9MCaLLcxclg=">
	window.onload = function() {
		document.returnform.submit();
	}
</script>
 </head>
 <body>
  <form name="returnform" action="https://goguvenliodeme.bkm.com.tr/troy/approve" method="post">
   <input type="hidden" name="goreq" value="********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
   <noscript>
    <center>
     Devam etmek icin tiklayiniz.
     <br>
     <br>
     <input type="submit" name="submit" value="Submit" id="btnSbmt">
    </center>
   </noscript>
  </form><img style="display:none" src="https://posws.param.com.tr/api/paramapi/status?SID=7d51453e-23b4-42a6-95de-7ea7ad995dbe" />
 </body>
</html>
            [UCD_MD] => ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            [Sonuc] => 1
            [Sonuc_Str] => İşlem Başarılı
            [Banka_Sonuc_Kod] => -1
            [Siparis_ID] => ORDER202509082015177450
        )

)


ÖDEME BAŞARILI:
Sonuç Kodu: 1
Sonuç Mesajı: İşlem Başarılı
İşlem ID: **********
Dekont ID: 0
